# GoSea Database Schema Analysis - Current User Management System

## Executive Summary

This document provides a comprehensive analysis of the current GoSea database schema for user management, identifying redundancies, inefficiencies, and normalization issues that impact data integrity and system performance.

## Current Schema Structure

### 1. User Table (`users`)
**Purpose**: Core authentication and authorization data
**Key Fields**:
- `id` (String, CUID) - Primary key
- `email` (String, unique) - User identifier
- `role` (UserRole enum) - CUSTOMER, BOAT_OWNER, AFFILIATE_AGENT, ADMIN
- `password` (String, nullable) - Hashed password
- `googleId` (String, nullable, unique) - OAuth integration
- `isActive` (Boolean) - Account status
- `emailVerified` (Boolean) - Email verification status
- `isApproved` (Boolean) - Admin approval for boat owners
- `approvedAt`, `approvedBy`, `rejectionReason` - Approval workflow fields

**Relationships**:
- One-to-one with Profile
- One-to-one with Provider (boat owners only)
- One-to-many with Boats, Bookings, etc.

### 2. Profile Table (`profiles`)
**Purpose**: Personal and business information storage
**Key Fields**:
- `userId` (String, unique) - Foreign key to User
- `firstName`, `lastName` (String) - Personal identification
- `phone` (String, encrypted) - Contact information
- `profilePicture` (String) - Avatar URL
- `dateOfBirth` (DateTime) - Personal data
- `emergencyContact` (String) - Safety information
- **Business Fields** (problematic):
  - `companyName` (String) - Business name
  - `brn` (String) - Business registration number
  - `agencyName` (String) - Agency information
- **Address Fields**:
  - `address1`, `address2`, `city`, `postcode`, `state`
- `language` (String) - User preference
- `profileCompletedAt` (DateTime) - Completion tracking

### 3. Provider Table (`providers`)
**Purpose**: Business entity information for boat owners
**Key Fields**:
- `userId` (String, unique) - Foreign key to User
- `companyName` (String) - **DUPLICATE** of Profile.companyName
- `displayName` (String) - Public business name
- `description` (String) - Business description
- `brn` (String) - **DUPLICATE** of Profile.brn
- `operatingLicense` (String) - Business license
- `contactPhone` (String, plain text) - **CONFLICTS** with Profile.phone
- `contactEmail` (String) - **POTENTIALLY DUPLICATE** of User.email
- `logoUrl`, `coverImageUrl` (String) - Branding assets
- `rating`, `reviewCount` - Business metrics
- `isVerified`, `isActive` - Business status
- `isAutoGenerated` (Boolean) - System flag

## Data Analysis Results

### Current User Data Patterns

**Sample Data Analysis** (from <EMAIL> - BOAT_OWNER):
```
Profile Data:
  Company: "Ayie Sdn Bhd"
  BRN: "**********"
  Phone: [ENCRYPTED]

Provider Data:
  Company: "Ayie Sdn Bhd"      ← DUPLICATE
  BRN: "**********"            ← DUPLICATE  
  Contact Phone: "+***********" ← PLAIN TEXT
  Contact Email: "<EMAIL>" ← DUPLICATE
```

**Statistics**:
- Total Users: 8
- Profiles with company names: 5
- Total providers: 5
- **100% redundancy rate** between Profile and Provider business data

## Identified Issues

### 1. Data Redundancy Issues

#### Critical Redundancies:
- **companyName**: Stored in both Profile and Provider tables
- **brn**: Business registration number duplicated
- **Contact Information**: Phone stored differently in Profile (encrypted) vs Provider (plain text)
- **Email**: User.email potentially duplicated in Provider.contactEmail

#### Impact:
- **Storage Waste**: Duplicate data increases database size
- **Maintenance Overhead**: Updates require multiple table modifications
- **Consistency Risk**: No constraints ensure data synchronization
- **Performance Impact**: Additional storage and indexing overhead

### 2. Normalization Violations

#### Mixed Concerns in Profile Table:
- **Personal Data**: firstName, lastName, dateOfBirth, emergencyContact
- **Business Data**: companyName, brn, agencyName (should be in Provider)
- **Address Data**: Could be personal or business context

#### Violation of Single Responsibility Principle:
- Profile table serves both personal and business data needs
- No clear separation between user types' data requirements

### 3. Data Consistency Issues

#### No Referential Integrity:
- No constraints ensuring Profile.companyName = Provider.companyName
- No validation that Profile.brn = Provider.brn
- contactEmail can differ from User.email without validation

#### Encryption Inconsistency:
- Profile.phone is encrypted
- Provider.contactPhone is plain text
- Inconsistent security handling for similar data types

### 4. Performance Issues

#### Query Complexity:
- **Boat Owner Data**: Requires 3-table JOIN (User + Profile + Provider)
- **Customer Data**: Requires 2-table JOIN (User + Profile)
- **Admin Data**: May only need User table

#### Index Inefficiency:
- Duplicate fields require separate indexing
- Business queries must search across multiple tables

### 5. Access Pattern Analysis

#### Current Data Access Patterns:

**Customer Registration/Login**:
```sql
SELECT u.*, p.firstName, p.lastName, p.phone 
FROM users u 
JOIN profiles p ON u.id = p.userId 
WHERE u.email = ?
```

**Boat Owner Dashboard**:
```sql
SELECT u.*, p.*, pr.* 
FROM users u 
JOIN profiles p ON u.id = p.userId 
JOIN providers pr ON u.id = pr.userId 
WHERE u.email = ?
```

**Admin User Management**:
```sql
SELECT u.*, p.firstName, p.lastName, p.companyName, pr.companyName as provider_company
FROM users u 
LEFT JOIN profiles p ON u.id = p.userId 
LEFT JOIN providers pr ON u.id = pr.userId
```

## Schema Relationship Issues

### Current Relationships:
```
User (1) ←→ (1) Profile
User (1) ←→ (0..1) Provider
```

### Problems:
1. **Mandatory Profile**: All users must have profiles, even when only basic auth is needed
2. **Optional Provider**: Only boat owners have providers, but business data is split between Profile and Provider
3. **No Role-Based Constraints**: No database-level enforcement of which roles can have providers

## Migration Complexity Assessment

### Current Data Dependencies:
- **5 boat owners** with duplicate business data
- **Encrypted personal data** requiring careful handling
- **Foreign key relationships** to boats, bookings, services
- **Application code dependencies** on current structure

### Risk Factors:
- **Data Loss Risk**: Encryption keys must be preserved
- **Application Downtime**: Schema changes require coordinated deployment
- **Data Integrity**: Ensuring consistency during migration
- **Rollback Complexity**: Reverting changes if issues arise

## Recommendations Summary

### Immediate Issues to Address:
1. **Eliminate business data redundancy** between Profile and Provider
2. **Standardize contact information handling** (encryption consistency)
3. **Implement data consistency constraints**
4. **Optimize query patterns** for different user types

### Long-term Improvements:
1. **Role-based schema optimization**
2. **Performance indexing strategy**
3. **Data archival and cleanup procedures**
4. **Audit trail implementation**

---

*This analysis forms the foundation for the optimized schema proposal detailed in `proposal_user_db_schema.md`*
