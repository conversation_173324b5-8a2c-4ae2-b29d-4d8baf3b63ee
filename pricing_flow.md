# GoSea Dynamic Pricing System Documentation

## Overview

The GoSea platform implements a sophisticated dynamic pricing system that supports multiple pricing scenarios based on service configuration. The system automatically detects and displays appropriate pricing options while maintaining a consistent user experience across all scenarios.

## Database Schema

### Core Tables

#### 1. `provider_services` Table
```sql
CREATE TABLE "provider_services" (
    "id" TEXT PRIMARY KEY,
    "providerId" TEXT NOT NULL,
    "serviceTypeId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "basePrice" DECIMAL NOT NULL,
    "agePricing" JSONB,                    -- JSON field for age-based pricing
    "maxCapacity" INTEGER NOT NULL,
    "includedItems" TEXT[],
    "excludedItems" TEXT[] DEFAULT '{}',
    "specialInstruction" TEXT,
    "isActive" BOOLEAN DEFAULT true,
    "createdAt" TIMESTAMP DEFAULT now(),
    "updatedAt" TIMESTAMP NOT NULL
);
```

#### 2. `package_types` Table
```sql
CREATE TABLE "package_types" (
    "id" TEXT PRIMARY KEY,
    "name" TEXT NOT NULL,                  -- Standard, Premium, Luxury
    "code" TEXT UNIQUE NOT NULL,           -- standard, premium, luxury
    "description" TEXT,
    "isDefault" BOOLEAN DEFAULT false,
    "sortOrder" INTEGER DEFAULT 0,
    "isActive" BOOLEAN DEFAULT true
);
```

#### 3. `service_packages` Table
```sql
CREATE TABLE "service_packages" (
    "id" TEXT PRIMARY KEY,
    "serviceId" TEXT NOT NULL,
    "packageTypeId" TEXT NOT NULL,
    "basePrice" DECIMAL NOT NULL,
    "agePricing" JSONB,                    -- Package-specific age pricing
    "priceModifier" DECIMAL DEFAULT 1.0,
    "includedItems" TEXT[],
    "excludedItems" TEXT[],
    "isActive" BOOLEAN DEFAULT true,
    FOREIGN KEY ("serviceId") REFERENCES "provider_services"("id"),
    FOREIGN KEY ("packageTypeId") REFERENCES "package_types"("id")
);
```

#### 4. `age_categories` Table
```sql
CREATE TABLE "age_categories" (
    "id" TEXT PRIMARY KEY,
    "name" TEXT NOT NULL,                  -- Adult, Child, Toddler, Senior, PWD
    "code" TEXT UNIQUE NOT NULL,           -- adult, child, toddler, senior, pwd
    "description" TEXT,
    "minAge" INTEGER,                      -- Minimum age for category
    "maxAge" INTEGER,                      -- Maximum age for category
    "sortOrder" INTEGER DEFAULT 0,
    "isActive" BOOLEAN DEFAULT true
);
```

#### 5. `service_age_pricing` Table
```sql
CREATE TABLE "service_age_pricing" (
    "id" TEXT PRIMARY KEY,
    "serviceId" TEXT NOT NULL,
    "ageCategoryId" TEXT NOT NULL,
    "price" DECIMAL NOT NULL,
    "priceType" TEXT DEFAULT 'FIXED',      -- FIXED, PERCENTAGE, FREE
    "percentageOfBase" DECIMAL,
    "isActive" BOOLEAN DEFAULT true,
    FOREIGN KEY ("serviceId") REFERENCES "provider_services"("id"),
    FOREIGN KEY ("ageCategoryId") REFERENCES "age_categories"("id")
);
```

### Relationships

```mermaid
erDiagram
    provider_services ||--o{ service_packages : "has many"
    provider_services ||--o{ service_age_pricing : "has many"
    package_types ||--o{ service_packages : "defines"
    age_categories ||--o{ service_age_pricing : "applies to"
    
    provider_services {
        string id PK
        decimal basePrice
        jsonb agePricing
        string[] includedItems
        string[] excludedItems
    }
    
    service_packages {
        string id PK
        string serviceId FK
        string packageTypeId FK
        decimal basePrice
        jsonb agePricing
        string[] includedItems
    }
    
    age_categories {
        string id PK
        string code UK
        int minAge
        int maxAge
        int sortOrder
    }
```

## Pricing Scenarios

The system automatically detects and handles 4 distinct pricing scenarios:

### Scenario 1: Basic Pricing
- **Condition**: No packages, no age-based pricing
- **Display**: Single price "RM X per person"
- **Calculation**: `totalPassengers * basePrice`
- **UI**: Simple passenger count field

### Scenario 2: Package-Only Pricing
- **Condition**: Has packages, no age-based pricing
- **Display**: Package selection with uniform pricing per package
- **Calculation**: `totalPassengers * selectedPackage.basePrice`
- **UI**: Package selection + passenger count

### Scenario 3: Age-Based Pricing
- **Condition**: No packages, has age-based pricing
- **Display**: Different prices for each age group
- **Calculation**: `(adults * adultPrice) + (children * childPrice) + ...`
- **UI**: Age group breakdown (Adults, Children, Toddlers, Seniors, PWD)

### Scenario 4: Full Variation Pricing
- **Condition**: Has packages AND age-based pricing
- **Display**: Package selection with age-specific pricing per package
- **Calculation**: Based on selected package's age pricing
- **UI**: Package selection + age group breakdown

## Data Flow Architecture

### 1. Database to API Layer

#### Service Data Retrieval
```javascript
// Backend API endpoint: /api/services/:id
const serviceData = {
  id: "ps_coral_snorkeling_half",
  name: "Coral Reef Snorkeling",
  basePrice: 120.00,
  agePricing: {
    adult: 120.00,
    child: 80.00,
    toddler: 0.00,
    senior: 100.00,
    pwd: 90.00
  },
  packages: [
    {
      id: "sp_service_standard",
      packageType: { code: "standard", name: "Standard" },
      basePrice: 120.00,
      agePricing: {
        adult: 120.00,
        child: 80.00,
        toddler: 0.00,
        senior: 100.00,
        pwd: 90.00
      },
      includedItems: ["Transportation", "Guide", "Equipment"]
    },
    {
      id: "sp_service_premium",
      packageType: { code: "premium", name: "Premium" },
      basePrice: 180.00,
      agePricing: {
        adult: 180.00,
        child: 120.00,
        toddler: 0.00,
        senior: 150.00,
        pwd: 135.00
      },
      includedItems: ["Transportation", "Guide", "Equipment", "Lunch", "Photos"]
    }
  ]
};
```

### 2. Frontend Scenario Detection

#### Pricing Scenario Logic
```javascript
const getPricingScenario = (service) => {
  if (!service) return 'basic';

  const hasPackages = service.packages && service.packages.length > 0;
  const hasAgePricing = service.agePricing ||
    (hasPackages && service.packages.some(pkg => pkg.agePricing));

  if (!hasPackages && !hasAgePricing) return 'basic';           // Scenario 1
  if (hasPackages && !hasAgePricing) return 'packages-only';    // Scenario 2
  if (!hasPackages && hasAgePricing) return 'age-only';         // Scenario 3
  return 'full-variation';                                      // Scenario 4
};
```

### 3. Dynamic Price Calculation

#### Universal Pricing Function
```javascript
const getPriceForAge = (ageGroup, scenario, service, selectedPackage) => {
  const basePrice = parseFloat(service.basePrice);

  switch (scenario) {
    case 'basic':
      return ageGroup === 'toddler' ? 0 : basePrice;

    case 'packages-only':
      if (service.packages && selectedPackage) {
        const pkg = service.packages.find(p => p.id === selectedPackage);
        return ageGroup === 'toddler' ? 0 : parseFloat(pkg.basePrice);
      }
      return ageGroup === 'toddler' ? 0 : basePrice;

    case 'age-only':
      if (service.agePricing && service.agePricing[ageGroup] !== undefined) {
        return parseFloat(service.agePricing[ageGroup]);
      }
      return ageGroup === 'toddler' ? 0 : basePrice;

    case 'full-variation':
      if (service.packages && selectedPackage) {
        const pkg = service.packages.find(p => p.id === selectedPackage);
        if (pkg && pkg.agePricing && pkg.agePricing[ageGroup] !== undefined) {
          return parseFloat(pkg.agePricing[ageGroup]);
        }
      }
      // Fallback to service age pricing
      if (service.agePricing && service.agePricing[ageGroup] !== undefined) {
        return parseFloat(service.agePricing[ageGroup]);
      }
      return ageGroup === 'toddler' ? 0 : basePrice;
  }
};
```

## UI Components

### 1. Service Detail Page (`frontend/src/pages/services/[id].js`)

#### DynamicPricingSection Component
- **Purpose**: Display pricing information on service detail pages
- **Location**: `frontend/src/components/DynamicPricingSection.js`
- **Features**:
  - Automatic scenario detection
  - Package comparison tables
  - Age-based pricing previews
  - "Most Popular" package highlighting
  - Responsive design with overflow handling

#### Key Features:
```javascript
// Package display with popularity indicators
{service.packages.map((pkg, index) => (
  <div key={pkg.id} className={`package-card ${index === 1 ? 'popular' : ''}`}>
    {index === 1 && <div className="popular-badge">Most Popular</div>}
    <h3>{pkg.packageType.name}</h3>
    <div className="price">RM {pkg.basePrice}</div>
    <ul className="included-items">
      {pkg.includedItems.slice(0, 5).map(item => (
        <li key={item}>{item}</li>
      ))}
      {pkg.includedItems.length > 5 && (
        <li className="more-items">+{pkg.includedItems.length - 5} more</li>
      )}
    </ul>
  </div>
))}
```

### 2. Booking Page (`frontend/src/pages/booking.js`)

#### Dynamic Form Rendering
The booking page dynamically renders form fields based on the detected pricing scenario:

##### Scenario 1: Basic Pricing
```javascript
// Single passenger field
<div className="passenger-selection">
  <label>Number of Passengers</label>
  <input
    type="number"
    value={bookingDetails.passengers}
    onChange={(e) => setBookingDetails(prev => ({
      ...prev,
      passengers: parseInt(e.target.value)
    }))}
  />
  <div className="price-display">RM {basePrice} per person</div>
</div>
```

##### Scenario 2: Package-Only Pricing
```javascript
// Package selection + passenger count
<div className="package-selection">
  {service.packages.map(pkg => (
    <div
      key={pkg.id}
      className={`package-option ${selectedPackage === pkg.id ? 'selected' : ''}`}
      onClick={() => selectPackage(pkg.id)}
    >
      <h4>{pkg.packageType.name}</h4>
      <div className="price">RM {pkg.basePrice}</div>
      <div className="includes">
        {pkg.includedItems.map(item => (
          <span key={item} className="include-item">{item}</span>
        ))}
      </div>
    </div>
  ))}
</div>
```

##### Scenario 3: Age-Based Pricing
```javascript
// Age group breakdown
<div className="age-group-selection">
  {['adults', 'children', 'toddlers', 'seniors', 'pwd'].map(ageGroup => (
    <div key={ageGroup} className="age-group">
      <label>{t(ageGroup)}</label>
      <div className="counter">
        <button onClick={() => decrementAge(ageGroup)}>-</button>
        <span>{bookingDetails[ageGroup]}</span>
        <button onClick={() => incrementAge(ageGroup)}>+</button>
      </div>
      <div className="price">
        RM {getPriceForAge(ageGroup)} per person
      </div>
    </div>
  ))}
</div>
```

##### Scenario 4: Full Variation
```javascript
// Package selection + age breakdown
<div className="full-variation-pricing">
  {/* Package Selection */}
  <div className="package-selection">
    {/* Same as Scenario 2 */}
  </div>

  {/* Age Group Selection with Package-Specific Pricing */}
  <div className="age-group-selection">
    {['adults', 'children', 'toddlers', 'seniors', 'pwd'].map(ageGroup => (
      <div key={ageGroup} className="age-group">
        <label>{t(ageGroup)}</label>
        <div className="counter">
          <button onClick={() => decrementAge(ageGroup)}>-</button>
          <span>{bookingDetails[ageGroup]}</span>
          <button onClick={() => incrementAge(ageGroup)}>+</button>
        </div>
        <div className="price">
          RM {getPackageAgePrice(ageGroup, selectedPackage)} per person
        </div>
      </div>
    ))}
  </div>
</div>
```

## Dynamic Age Range Configuration

### Current Age Categories
The system currently supports 5 age categories with fixed ranges:

| Category | Code | Age Range | Default Price Behavior |
|----------|------|-----------|------------------------|
| Adult | `adult` | 18-64 | Full price |
| Child | `child` | 3-17 | Reduced price |
| Toddler | `toddler` | 0-2 | Free |
| Senior | `senior` | 65+ | Discounted price |
| PWD | `pwd` | Any age | Special pricing |

### Proposed Dynamic Age Range System

#### Enhanced Age Categories Table
```sql
ALTER TABLE "age_categories" ADD COLUMN "isConfigurable" BOOLEAN DEFAULT false;
ALTER TABLE "age_categories" ADD COLUMN "defaultMinAge" INTEGER;
ALTER TABLE "age_categories" ADD COLUMN "defaultMaxAge" INTEGER;

-- Service-specific age range overrides
CREATE TABLE "service_age_ranges" (
    "id" TEXT PRIMARY KEY,
    "serviceId" TEXT NOT NULL,
    "ageCategoryId" TEXT NOT NULL,
    "minAge" INTEGER NOT NULL,
    "maxAge" INTEGER,
    "isActive" BOOLEAN DEFAULT true,
    "createdAt" TIMESTAMP DEFAULT now(),
    "updatedAt" TIMESTAMP NOT NULL,
    FOREIGN KEY ("serviceId") REFERENCES "provider_services"("id"),
    FOREIGN KEY ("ageCategoryId") REFERENCES "age_categories"("id"),
    UNIQUE("serviceId", "ageCategoryId")
);
```

#### Implementation Example
```javascript
// Enhanced age category retrieval
const getAgeRangesForService = async (serviceId) => {
  const customRanges = await prisma.serviceAgeRange.findMany({
    where: { serviceId, isActive: true },
    include: { ageCategory: true }
  });

  const defaultRanges = await prisma.ageCategory.findMany({
    where: { isActive: true }
  });

  return defaultRanges.map(category => {
    const customRange = customRanges.find(r => r.ageCategoryId === category.id);
    return {
      ...category,
      minAge: customRange?.minAge ?? category.minAge,
      maxAge: customRange?.maxAge ?? category.maxAge,
      isCustomized: !!customRange
    };
  });
};

// Frontend age validation
const validateAgeInput = (age, ageCategory, serviceAgeRanges) => {
  const range = serviceAgeRanges.find(r => r.code === ageCategory);
  if (!range) return false;

  const ageNum = parseInt(age);
  if (range.minAge && ageNum < range.minAge) return false;
  if (range.maxAge && ageNum > range.maxAge) return false;

  return true;
};
```

#### Admin Interface for Age Range Configuration
```javascript
// Service configuration component
const ServiceAgeRangeConfig = ({ serviceId, ageCategories }) => {
  const [customRanges, setCustomRanges] = useState({});

  const updateAgeRange = (categoryId, minAge, maxAge) => {
    setCustomRanges(prev => ({
      ...prev,
      [categoryId]: { minAge, maxAge }
    }));
  };

  return (
    <div className="age-range-config">
      <h3>Age Range Configuration</h3>
      {ageCategories.map(category => (
        <div key={category.id} className="age-range-item">
          <label>{category.name}</label>
          <div className="range-inputs">
            <input
              type="number"
              placeholder="Min Age"
              value={customRanges[category.id]?.minAge || category.defaultMinAge}
              onChange={(e) => updateAgeRange(
                category.id,
                parseInt(e.target.value),
                customRanges[category.id]?.maxAge
              )}
            />
            <span>to</span>
            <input
              type="number"
              placeholder="Max Age"
              value={customRanges[category.id]?.maxAge || category.defaultMaxAge}
              onChange={(e) => updateAgeRange(
                category.id,
                customRanges[category.id]?.minAge,
                parseInt(e.target.value)
              )}
            />
          </div>
        </div>
      ))}
    </div>
  );
};
```

## Implementation Examples

### Complete Pricing Flow Example

#### 1. Service Creation with Pricing
```javascript
// Backend: Creating a service with full variation pricing
const createServiceWithPricing = async (serviceData) => {
  const service = await prisma.providerService.create({
    data: {
      name: "Premium Snorkeling Adventure",
      basePrice: 150.00,
      agePricing: {
        adult: 150.00,
        child: 100.00,
        toddler: 0.00,
        senior: 120.00,
        pwd: 110.00
      }
    }
  });

  // Create packages
  const packages = await Promise.all([
    prisma.servicePackage.create({
      data: {
        serviceId: service.id,
        packageTypeId: 'pkg_standard',
        basePrice: 150.00,
        agePricing: {
          adult: 150.00,
          child: 100.00,
          toddler: 0.00,
          senior: 120.00,
          pwd: 110.00
        },
        includedItems: ['Equipment', 'Guide', 'Transport']
      }
    }),
    prisma.servicePackage.create({
      data: {
        serviceId: service.id,
        packageTypeId: 'pkg_premium',
        basePrice: 220.00,
        agePricing: {
          adult: 220.00,
          child: 150.00,
          toddler: 0.00,
          senior: 180.00,
          pwd: 165.00
        },
        includedItems: ['Equipment', 'Guide', 'Transport', 'Lunch', 'Photos']
      }
    })
  ]);

  return { service, packages };
};
```

#### 2. Frontend Booking Calculation
```javascript
// Complete booking calculation for all scenarios
const calculateBookingTotal = (bookingDetails, service, scenario) => {
  const { adults, children, toddlers, seniors, pwd, selectedPackage } = bookingDetails;

  let total = 0;

  // Calculate for each age group
  ['adult', 'child', 'toddler', 'senior', 'pwd'].forEach(ageGroup => {
    const count = bookingDetails[ageGroup === 'adult' ? 'adults' :
                                 ageGroup === 'child' ? 'children' :
                                 ageGroup + 's'];
    const price = getPriceForAge(ageGroup, scenario, service, selectedPackage);
    total += count * price;
  });

  return total;
};
```

### Best Practices

#### 1. Data Validation
```javascript
// Validate pricing data integrity
const validatePricingData = (service) => {
  const errors = [];

  // Check base price
  if (!service.basePrice || service.basePrice <= 0) {
    errors.push('Base price must be greater than 0');
  }

  // Validate age pricing if present
  if (service.agePricing) {
    Object.entries(service.agePricing).forEach(([ageGroup, price]) => {
      if (price < 0) {
        errors.push(`${ageGroup} price cannot be negative`);
      }
    });
  }

  // Validate packages
  if (service.packages) {
    service.packages.forEach((pkg, index) => {
      if (!pkg.basePrice || pkg.basePrice <= 0) {
        errors.push(`Package ${index + 1} base price must be greater than 0`);
      }
    });
  }

  return errors;
};
```

#### 2. Performance Optimization
```javascript
// Cache pricing calculations
const pricingCache = new Map();

const getCachedPrice = (cacheKey, calculationFn) => {
  if (pricingCache.has(cacheKey)) {
    return pricingCache.get(cacheKey);
  }

  const result = calculationFn();
  pricingCache.set(cacheKey, result);

  // Clear cache after 5 minutes
  setTimeout(() => pricingCache.delete(cacheKey), 300000);

  return result;
};
```

#### 3. Error Handling
```javascript
// Robust error handling for pricing operations
const safePriceCalculation = (calculationFn, fallbackPrice = 0) => {
  try {
    const result = calculationFn();
    return isNaN(result) ? fallbackPrice : result;
  } catch (error) {
    console.error('Pricing calculation error:', error);
    return fallbackPrice;
  }
};
```

## Summary

The GoSea dynamic pricing system provides:

- ✅ **Flexible Architecture**: Supports 4 distinct pricing scenarios
- ✅ **Database-Driven**: All pricing data stored in normalized tables
- ✅ **Dynamic UI**: Automatically adapts interface based on pricing configuration
- ✅ **Extensible Design**: Easy to add new age categories or pricing models
- ✅ **Performance Optimized**: Efficient calculations with caching
- ✅ **User-Friendly**: Consistent experience across all scenarios
- ✅ **Admin Configurable**: Service providers can customize pricing easily

The system is production-ready and handles complex pricing scenarios while maintaining simplicity for end users.
```
```
