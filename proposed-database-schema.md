# GoSea Platform - Proposed Database Schema Redesign

## Overview
This document outlines a comprehensive database schema redesign to support the complex boat service structure described in the requirements, including provider management, jetty-based search, multiple service types, and flexible destination handling.

## Key Design Principles

### 1. **Provider-Centric Model**
- Each search result represents a boat provider/owner, not individual boats
- Providers can own multiple boats and offer multiple services
- Clear separation between provider, boats, and services
- **Single boat owners automatically become providers** to ensure platform inclusivity

### 2. **Flexible Location System**
- Support for departure jetties (Jetty Kampung Mangkuk, Jetty Merang, Jetty Penarik)
- Multiple destination endpoints for passenger services
- Route-based journey concept (departure → destination)
- **Mandatory destination selection for passenger transport services**

### 3. **Service-First Booking Workflow**
- Service type selection comes first in booking process
- Services are offered by providers, not individual boats
- **Boats are specifically assigned to services** with primary/secondary preferences
- **Seat quota management** through boat-to-service assignments

### 4. **Scalable Service Categories**
- Hierarchical service type structure
- Support for service variations and subcategories
- Extensible for future service types
- **Category-specific business rules** (e.g., passenger transport requirements)

## Proposed Table Structure

### Core Provider Management

#### 1. Providers Table (`providers`) - NEW
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Provider identifier |
| userId | String | FK, UNIQUE, NOT NULL | Reference to users table (boat owner) |
| companyName | String | NOT NULL | Official company name |
| displayName | String | NOT NULL | Display name for search results |
| description | String | NULLABLE | Provider description |
| brn | String | NULLABLE | Business registration number |
| operatingLicense | String | NULLABLE | Marine operating license |
| contactPhone | String | NOT NULL | Primary contact number |
| contactEmail | String | NOT NULL | Primary contact email |
| address | String | NULLABLE | Business address |
| logoUrl | String | NULLABLE | Company logo URL |
| coverImageUrl | String | NULLABLE | Cover image for provider page |
| rating | Decimal | DEFAULT 0 | Average rating |
| reviewCount | Int | DEFAULT 0 | Total review count |
| isVerified | Boolean | DEFAULT false | Verification status |
| isActive | Boolean | DEFAULT true | Active status |
| isAutoGenerated | Boolean | DEFAULT false | Auto-created for single boat owners |
| createdAt | DateTime | DEFAULT now() | Creation timestamp |
| updatedAt | DateTime | AUTO UPDATE | Last update timestamp |

**Business Rules:**
- **Auto-Provider Creation**: When a user with role `BOAT_OWNER` creates their first boat, a provider record is automatically created
- **Single Provider Per User**: Each boat owner can only have one provider record (enforced by UNIQUE constraint on userId)
- **Default Naming**: For auto-generated providers, `companyName` defaults to "{firstName} {lastName} Marine Services" and `displayName` defaults to "{firstName} {lastName}"

**Relationships:**
- Many-to-One: user (users)
- One-to-Many: boats, services, operatingAreas

#### 2. Enhanced Boats Table (`boats`) - MODIFIED
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Boat identifier |
| providerId | String | FK, NOT NULL | Reference to providers table |
| name | String | NOT NULL | Boat name |
| description | String | NULLABLE | Boat description |
| registrationNumber | String | UNIQUE, NOT NULL | Official boat registration |
| capacity | Int | NOT NULL | Maximum passenger capacity |
| length | Decimal | NULLABLE | Boat length in meters |
| engineType | String | NULLABLE | Engine type/power |
| yearBuilt | Int | NULLABLE | Year of construction |
| safetyRating | String | NULLABLE | Safety certification level |
| amenities | Json | NULLABLE | Available amenities |
| status | BoatStatus | DEFAULT DRAFT | Approval status |
| isActive | Boolean | DEFAULT true | Active status |
| createdAt | DateTime | DEFAULT now() | Creation timestamp |
| updatedAt | DateTime | AUTO UPDATE | Last update timestamp |

**Relationships:**
- Many-to-One: provider
- One-to-Many: photos, serviceAssignments, maintenanceRecords
- Many-to-Many: services (through serviceAssignments)

### Location and Route Management

#### 3. Jetties Table (`jetties`) - NEW
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Jetty identifier |
| name | String | NOT NULL | Jetty name |
| code | String | UNIQUE, NOT NULL | Short code (e.g., "JKM", "JM", "JP") |
| fullName | String | NOT NULL | Full official name |
| location | String | NOT NULL | Geographic location |
| coordinates | Json | NULLABLE | GPS coordinates |
| facilities | Json | NULLABLE | Available facilities |
| operatingHours | Json | NULLABLE | Operating hours |
| parkingAvailable | Boolean | DEFAULT false | Parking availability |
| isActive | Boolean | DEFAULT true | Active status |

#### 4. Destinations Table (`destinations`) - NEW
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Destination identifier |
| name | String | NOT NULL | Destination name |
| code | String | UNIQUE, NOT NULL | Short code (e.g., "RED", "PER") |
| fullName | String | NOT NULL | Full destination name |
| description | String | NULLABLE | Destination description |
| coordinates | Json | NULLABLE | GPS coordinates |
| imageUrl | String | NULLABLE | Destination image |
| popularActivities | String[] | NULLABLE | Popular activities |
| isActive | Boolean | DEFAULT true | Active status |

#### 5. Routes Table (`routes`) - NEW
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Route identifier |
| departureJettyId | String | FK, NOT NULL | Reference to jetties table |
| destinationId | String | FK, NOT NULL | Reference to destinations table |
| distance | Decimal | NULLABLE | Distance in nautical miles |
| estimatedDuration | Int | NULLABLE | Duration in minutes |
| difficulty | String | NULLABLE | Route difficulty level |
| seasonalRestrictions | Json | NULLABLE | Seasonal operating restrictions |
| isActive | Boolean | DEFAULT true | Active status |

**Unique Constraint:** (departureJettyId, destinationId)

### Service Management

#### 6. Service Categories Table (`service_categories`) - NEW
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Category identifier |
| name | String | NOT NULL | Category name |
| code | String | UNIQUE, NOT NULL | Category code |
| description | String | NULLABLE | Category description |
| iconUrl | String | NULLABLE | Category icon |
| requiresDestination | Boolean | DEFAULT false | Whether destination selection is mandatory |
| sortOrder | Int | DEFAULT 0 | Display order |
| isActive | Boolean | DEFAULT true | Active status |

**Business Rules:**
- **Passenger Transport Requirement**: Services with category code "PASSENGER_TRANSPORT" must have `requiresDestination = true`
- **Destination Validation**: Services in categories with `requiresDestination = true` must have at least one route defined

**Example Categories:**
- SNORKELING (Snorkeling Tours) - requiresDestination: false
- PASSENGER_TRANSPORT (Passenger Transport) - requiresDestination: true
- ISLAND_HOPPING (Island Hopping) - requiresDestination: true
- FISHING (Fishing Trips) - requiresDestination: false
- SUNSET_CRUISE (Sunset Cruises) - requiresDestination: false

#### 7. Service Types Table (`service_types`) - NEW
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Service type identifier |
| categoryId | String | FK, NOT NULL | Reference to service_categories |
| name | String | NOT NULL | Service type name |
| code | String | UNIQUE, NOT NULL | Service type code |
| description | String | NULLABLE | Service type description |
| defaultDuration | Int | NULLABLE | Default duration in minutes |
| requiresRoute | Boolean | DEFAULT false | Whether service requires route selection |
| allowsMultipleDestinations | Boolean | DEFAULT false | Multiple destinations support |
| isActive | Boolean | DEFAULT true | Active status |

#### 8. Provider Services Table (`provider_services`) - NEW
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Provider service identifier |
| providerId | String | FK, NOT NULL | Reference to providers table |
| serviceTypeId | String | FK, NOT NULL | Reference to service_types table |
| name | String | NOT NULL | Service name |
| description | String | NULLABLE | Service description |
| basePrice | Decimal | NOT NULL | Base price |
| agePricing | Json | NULLABLE | Age-based pricing |
| duration | Int | NULLABLE | Duration in minutes |
| maxCapacity | Int | NOT NULL | Maximum capacity |
| includedItems | String[] | NULLABLE | Included items |
| itinerary | Json | NULLABLE | Service itinerary |
| requirements | Json | NULLABLE | Special requirements |
| isActive | Boolean | DEFAULT true | Active status |
| createdAt | DateTime | DEFAULT now() | Creation timestamp |
| updatedAt | DateTime | AUTO UPDATE | Last update timestamp |

**Business Rules:**
- **Boat Assignment Requirement**: Every service must have at least one boat assigned through `service_assignments`
- **Capacity Validation**: Service `maxCapacity` cannot exceed the total capacity of assigned boats
- **Passenger Transport Routes**: Services with category "PASSENGER_TRANSPORT" must have at least one route defined in `service_routes`
- **Primary Destinations**: Passenger transport services should include routes to "Pulau Perhentian" and "Pulau Redang" as primary options

**Relationships:**
- Many-to-One: provider, serviceType
- One-to-Many: serviceRoutes, serviceSchedules, bookings
- Many-to-Many: boats (through serviceAssignments)

#### 9. Service Routes Table (`service_routes`) - NEW
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Service route identifier |
| serviceId | String | FK, NOT NULL | Reference to provider_services |
| routeId | String | FK, NOT NULL | Reference to routes table |
| priceModifier | Decimal | DEFAULT 1.0 | Price multiplier for this route |
| isActive | Boolean | DEFAULT true | Active status |

**Unique Constraint:** (serviceId, routeId)

#### 10. Service Assignments Table (`service_assignments`) - NEW
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Assignment identifier |
| serviceId | String | FK, NOT NULL | Reference to provider_services |
| boatId | String | FK, NOT NULL | Reference to boats table |
| isPrimary | Boolean | DEFAULT false | Primary boat for service |
| maxCapacityOverride | Int | NULLABLE | Override boat's default capacity for this service |
| isActive | Boolean | DEFAULT true | Assignment status |
| assignedAt | DateTime | DEFAULT now() | Assignment timestamp |

**Business Rules:**
- **Mandatory Assignment**: Every service must have at least one boat assigned
- **Primary Boat Requirement**: Each service must have exactly one primary boat (enforced by application logic)
- **Capacity Control**: `maxCapacityOverride` allows limiting boat capacity for specific services (e.g., premium services with fewer seats)
- **Seat Quota Calculation**: Available seats = MIN(boat.capacity, maxCapacityOverride ?? boat.capacity)
- **Booking Priority**: System assigns primary boat first, then falls back to other assigned boats based on availability

**Unique Constraint:** (serviceId, boatId)

### Operating Areas and Schedules

#### 11. Provider Operating Areas Table (`provider_operating_areas`) - NEW
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Operating area identifier |
| providerId | String | FK, NOT NULL | Reference to providers table |
| jettyId | String | FK, NOT NULL | Reference to jetties table |
| isActive | Boolean | DEFAULT true | Active status |

**Unique Constraint:** (providerId, jettyId)

#### 12. Service Schedules Table (`service_schedules`) - NEW
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Schedule identifier |
| serviceId | String | FK, NOT NULL | Reference to provider_services |
| dayOfWeek | Int | NOT NULL | Day of week (0-6, 0=Sunday) |
| departureTime | String | NOT NULL | Departure time (HH:MM) |
| availableCapacity | Int | NOT NULL | Available capacity |
| isActive | Boolean | DEFAULT true | Active status |

#### 13. Service Availability Table (`service_availability`) - NEW
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Availability identifier |
| serviceId | String | FK, NOT NULL | Reference to provider_services |
| date | Date | NOT NULL | Available date |
| timeSlot | String | NOT NULL | Time slot (HH:MM) |
| availableCapacity | Int | NOT NULL | Available capacity |
| totalCapacity | Int | NOT NULL | Total capacity |
| priceModifier | Decimal | DEFAULT 1.0 | Price modifier for date/time |
| isActive | Boolean | DEFAULT true | Active status |

**Unique Constraint:** (serviceId, date, timeSlot)

### Enhanced Booking Management

#### 14. Enhanced Bookings Table (`bookings`) - MODIFIED
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Booking identifier |
| customerId | String | FK, NOT NULL | Reference to users table |
| providerId | String | FK, NOT NULL | Reference to providers table |
| serviceId | String | FK, NOT NULL | Reference to provider_services |
| routeId | String | FK, NULLABLE | Reference to routes table |
| assignedBoatId | String | FK, NULLABLE | Assigned boat |
| serviceDate | Date | NOT NULL | Service date |
| serviceTime | String | NOT NULL | Service time slot |
| passengerCount | Int | NOT NULL | Total passenger count |
| passengerBreakdown | Json | NULLABLE | Age-based breakdown |
| baseAmount | Decimal | NOT NULL | Base service amount |
| routeAmount | Decimal | DEFAULT 0 | Route-specific amount |
| discountAmount | Decimal | DEFAULT 0 | Discount amount |
| totalAmount | Decimal | NOT NULL | Final total amount |
| discountCode | String | NULLABLE | Applied discount code |
| status | BookingStatus | DEFAULT PENDING | Booking status |
| contactName | String | NOT NULL | Contact person name |
| contactPhone | String | NOT NULL | Contact phone |
| contactEmail | String | NOT NULL | Contact email |
| specialRequests | String | NULLABLE | Special requests |
| affiliateId | String | NULLABLE | Affiliate reference |
| commission | Decimal | NULLABLE | Affiliate commission |
| createdAt | DateTime | DEFAULT now() | Creation timestamp |
| updatedAt | DateTime | AUTO UPDATE | Last update timestamp |

**Relationships:**
- Many-to-One: customer (users), provider, service, route, assignedBoat
- One-to-Many: payments, notifications

## New Enums

### Enhanced ServiceType (replaced by service_categories)
```sql
-- Remove old ServiceType enum
-- Replace with flexible service_categories table
```

### Enhanced Location (replaced by jetties and destinations)
```sql
-- Remove old Location enum  
-- Replace with jetties and destinations tables
```

### New Enums
```sql
enum BoatStatus {
  DRAFT
  PENDING_APPROVAL
  APPROVED
  REJECTED
  INACTIVE
  MAINTENANCE
}

enum BookingStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
  NO_SHOW
}
```

## Business Rules and Constraints

### 1. **Single Boat Owner as Provider Rule**

#### Automatic Provider Creation
When a user with role `BOAT_OWNER` creates their first boat, the system automatically creates a provider record:

```sql
-- Trigger function for automatic provider creation
CREATE OR REPLACE FUNCTION create_provider_for_boat_owner()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if this is the user's first boat and they don't have a provider yet
    IF NOT EXISTS (SELECT 1 FROM providers WHERE userId = NEW.ownerId) THEN
        INSERT INTO providers (
            userId,
            companyName,
            displayName,
            contactPhone,
            contactEmail,
            isAutoGenerated
        )
        SELECT
            NEW.ownerId,
            COALESCE(p.firstName || ' ' || p.lastName || ' Marine Services', 'Marine Services'),
            COALESCE(p.firstName || ' ' || p.lastName, 'Boat Owner'),
            COALESCE(p.phone, u.email),
            u.email,
            true
        FROM users u
        LEFT JOIN profiles p ON p.userId = u.id
        WHERE u.id = NEW.ownerId;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to boats table
CREATE TRIGGER trigger_create_provider_for_boat_owner
    AFTER INSERT ON boats
    FOR EACH ROW
    EXECUTE FUNCTION create_provider_for_boat_owner();
```

#### Provider Naming Convention
- **Company Name**: "{FirstName} {LastName} Marine Services" (e.g., "Ahmad Hassan Marine Services")
- **Display Name**: "{FirstName} {LastName}" (e.g., "Ahmad Hassan")
- **Auto-Generated Flag**: `isAutoGenerated = true` to distinguish from manually created providers

### 2. **Boat-to-Service Assignment Control**

#### Service Assignment Rules
```sql
-- Constraint: Every service must have at least one boat assigned
ALTER TABLE provider_services
ADD CONSTRAINT check_service_has_boats
CHECK (
    EXISTS (
        SELECT 1 FROM service_assignments sa
        WHERE sa.serviceId = id AND sa.isActive = true
    )
);

-- Constraint: Each service must have exactly one primary boat
CREATE UNIQUE INDEX idx_service_primary_boat
ON service_assignments (serviceId)
WHERE isPrimary = true AND isActive = true;
```

#### Seat Quota Calculation
```sql
-- Function to calculate available seats for a service
CREATE OR REPLACE FUNCTION calculate_service_capacity(service_id TEXT)
RETURNS INTEGER AS $$
DECLARE
    total_capacity INTEGER := 0;
    boat_record RECORD;
BEGIN
    FOR boat_record IN
        SELECT
            b.capacity,
            sa.maxCapacityOverride
        FROM service_assignments sa
        JOIN boats b ON b.id = sa.boatId
        WHERE sa.serviceId = service_id
        AND sa.isActive = true
    LOOP
        total_capacity := total_capacity +
            COALESCE(boat_record.maxCapacityOverride, boat_record.capacity);
    END LOOP;

    RETURN total_capacity;
END;
$$ LANGUAGE plpgsql;
```

#### Boat Assignment Priority Logic
1. **Primary Boat**: Always attempt to assign the primary boat first
2. **Secondary Boats**: If primary boat is unavailable, assign from other boats assigned to the service
3. **Capacity Override**: Respect `maxCapacityOverride` for premium services with reduced seating

### 3. **Mandatory Destinations for Passenger Transport**

#### Category-Level Destination Requirements
```sql
-- Constraint: Passenger transport services must have routes
ALTER TABLE provider_services
ADD CONSTRAINT check_passenger_transport_routes
CHECK (
    NOT EXISTS (
        SELECT 1 FROM service_types st
        JOIN service_categories sc ON sc.id = st.categoryId
        WHERE st.id = serviceTypeId
        AND sc.code = 'PASSENGER_TRANSPORT'
    ) OR EXISTS (
        SELECT 1 FROM service_routes sr
        WHERE sr.serviceId = id
    )
);
```

#### Required Destinations Setup
```sql
-- Ensure primary destinations exist
INSERT INTO destinations (name, code, fullName, isActive) VALUES
('Pulau Perhentian', 'PER', 'Pulau Perhentian Islands', true),
('Pulau Redang', 'RED', 'Pulau Redang Island', true)
ON CONFLICT (code) DO NOTHING;

-- Ensure routes exist for passenger transport
INSERT INTO routes (departureJettyId, destinationId, isActive)
SELECT j.id, d.id, true
FROM jetties j
CROSS JOIN destinations d
WHERE j.code IN ('JKM', 'JM', 'JP') -- Main jetties
AND d.code IN ('PER', 'RED') -- Primary destinations
ON CONFLICT (departureJettyId, destinationId) DO NOTHING;
```

#### Booking Validation for Passenger Transport
```sql
-- Function to validate passenger transport bookings
CREATE OR REPLACE FUNCTION validate_passenger_transport_booking()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if this is a passenger transport service
    IF EXISTS (
        SELECT 1 FROM provider_services ps
        JOIN service_types st ON st.id = ps.serviceTypeId
        JOIN service_categories sc ON sc.id = st.categoryId
        WHERE ps.id = NEW.serviceId
        AND sc.code = 'PASSENGER_TRANSPORT'
    ) THEN
        -- Ensure route is specified for passenger transport
        IF NEW.routeId IS NULL THEN
            RAISE EXCEPTION 'Route selection is mandatory for passenger transport services';
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply validation trigger
CREATE TRIGGER trigger_validate_passenger_transport_booking
    BEFORE INSERT OR UPDATE ON bookings
    FOR EACH ROW
    EXECUTE FUNCTION validate_passenger_transport_booking();
```

## Search and Booking Workflow

### 1. **Service-First Search**
```sql
-- Step 1: User selects service type
SELECT sc.*, COUNT(ps.id) as provider_count
FROM service_categories sc
LEFT JOIN service_types st ON st.categoryId = sc.id
LEFT JOIN provider_services ps ON ps.serviceTypeId = st.id
WHERE sc.isActive = true
GROUP BY sc.id
ORDER BY sc.sortOrder;

-- Step 2: User selects departure jetty and destination
SELECT DISTINCT j.*, d.*
FROM jetties j
CROSS JOIN destinations d
WHERE EXISTS (
  SELECT 1 FROM routes r 
  WHERE r.departureJettyId = j.id 
  AND r.destinationId = d.id
  AND r.isActive = true
);
```

### 2. **Provider Search Results**
```sql
-- Search providers offering selected service on selected route
SELECT DISTINCT 
  p.*,
  ps.basePrice,
  ps.duration,
  MIN(ps.basePrice) as startingPrice,
  COUNT(DISTINCT b.id) as boatCount
FROM providers p
JOIN provider_services ps ON ps.providerId = p.id
JOIN service_types st ON st.id = ps.serviceTypeId
JOIN service_routes sr ON sr.serviceId = ps.id
JOIN routes r ON r.id = sr.routeId
LEFT JOIN service_assignments sa ON sa.serviceId = ps.id
LEFT JOIN boats b ON b.id = sa.boatId
WHERE st.categoryId = ? -- selected service category
  AND r.departureJettyId = ? -- selected jetty
  AND r.destinationId = ? -- selected destination
  AND p.isActive = true
  AND ps.isActive = true
GROUP BY p.id, ps.id
ORDER BY p.rating DESC, startingPrice ASC;
```

### 3. **Enhanced Booking Process**

#### For All Services:
1. **Service Category Selection**: User selects service type (e.g., "Snorkeling Tours")
2. **Departure Jetty Selection**: User selects departure point
3. **Provider Selection**: Show providers offering selected service from selected jetty
4. **Service Details**: User selects specific service from chosen provider

#### For Passenger Transport Services (Additional Steps):
5. **Mandatory Destination Selection**: User must select destination (Pulau Perhentian/Redang)
6. **Route Validation**: System validates selected jetty → destination route exists

#### For All Services (Continued):
7. **Date/Time Selection**: Show available slots based on service schedules
8. **Passenger Details**: Collect passenger count and information
9. **Automatic Boat Assignment**: System assigns boat using priority logic:
   - Primary boat (if available)
   - Secondary assigned boats (based on availability)
   - Respect capacity overrides for premium services
10. **Payment**: Process payment and confirm booking

#### Seat Quota Management:
- **Real-time Availability**: Calculate available seats using assigned boats and capacity overrides
- **Overbooking Prevention**: Prevent bookings exceeding total assigned boat capacity
- **Dynamic Assignment**: Assign specific boat at booking time based on availability

## Migration Strategy

### Phase 1: Core Structure
1. Create new tables: providers, jetties, destinations, routes
2. Migrate existing boat owners to providers
3. Set up basic jetty and destination data

### Phase 2: Service Management
1. Create service categories and types
2. Migrate existing boats to new service structure
3. Set up provider services and routes

### Phase 3: Enhanced Features
1. Implement service schedules and availability
2. Update booking workflow
3. Migrate existing bookings to new structure

### Phase 4: Cleanup
1. Remove deprecated fields and tables
2. Update API endpoints
3. Update frontend components

## Benefits of New Design

### 1. **Provider-Centric Search**
- Search results show providers, not individual boats
- Clear company branding and information
- Multiple boats per provider support

### 2. **Flexible Location System**
- Support for any number of jetties and destinations
- Route-based pricing and scheduling
- Easy addition of new locations

### 3. **Service-First Workflow**
- Service type selection prioritized
- Clear service categorization
- Extensible service structure

### 4. **Scalable Architecture**
- Easy addition of new service types
- Flexible pricing models
- Comprehensive availability management

### 5. **Enhanced User Experience**
- Clearer booking workflow
- Better search filtering
- More detailed service information

This redesigned schema provides a robust foundation for the complex boat service structure while maintaining flexibility for future enhancements and scaling.

## Database Relationship Diagram

```mermaid
erDiagram
    Users ||--o{ Providers : owns
    Users ||--o{ Bookings : makes
    Users ||--o| Profiles : has

    Providers ||--o{ Boats : owns
    Providers ||--o{ ProviderServices : offers
    Providers ||--o{ ProviderOperatingAreas : operates_in

    Boats ||--o{ BoatPhotos : contains
    Boats ||--o{ ServiceAssignments : assigned_to

    ServiceCategories ||--o{ ServiceTypes : contains
    ServiceTypes ||--o{ ProviderServices : implements

    ProviderServices ||--o{ ServiceRoutes : serves
    ProviderServices ||--o{ ServiceSchedules : scheduled
    ProviderServices ||--o{ ServiceAvailability : available
    ProviderServices ||--o{ ServiceAssignments : uses
    ProviderServices ||--o{ Bookings : receives

    Jetties ||--o{ Routes : departure_from
    Jetties ||--o{ ProviderOperatingAreas : operates_from
    Destinations ||--o{ Routes : destination_to
    Routes ||--o{ ServiceRoutes : used_by
    Routes ||--o{ Bookings : booked_for

    Bookings ||--o{ Payments : generates
    Bookings }o--|| DiscountCodes : applies

    Providers {
        string id PK
        string userId FK
        string companyName
        string displayName
        decimal rating
        int reviewCount
        boolean isVerified
        boolean isActive
    }

    ProviderServices {
        string id PK
        string providerId FK
        string serviceTypeId FK
        string name
        decimal basePrice
        int maxCapacity
        boolean isActive
    }

    Routes {
        string id PK
        string departureJettyId FK
        string destinationId FK
        decimal distance
        int estimatedDuration
        boolean isActive
    }

    Bookings {
        string id PK
        string customerId FK
        string providerId FK
        string serviceId FK
        string routeId FK
        string assignedBoatId FK
        date serviceDate
        string serviceTime
        int passengerCount
        decimal totalAmount
        BookingStatus status
    }
```

## Sample Data Structure

### Example Service Categories
```json
[
  {
    "id": "cat-001",
    "name": "Snorkeling Tours",
    "code": "SNORKELING",
    "description": "Underwater exploration and marine life viewing",
    "sortOrder": 1
  },
  {
    "id": "cat-002",
    "name": "Passenger Transport",
    "code": "PASSENGER_TRANSPORT",
    "description": "Island transfers and transportation services",
    "sortOrder": 2
  },
  {
    "id": "cat-003",
    "name": "Island Hopping",
    "code": "ISLAND_HOPPING",
    "description": "Multi-destination island exploration tours",
    "sortOrder": 3
  }
]
```

### Example Jetties
```json
[
  {
    "id": "jetty-001",
    "name": "Jetty Kampung Mangkuk",
    "code": "JKM",
    "fullName": "Jetty Kampung Mangkuk, Terengganu",
    "location": "Kampung Mangkuk, Terengganu",
    "coordinates": {"lat": 5.8077, "lng": 102.9734},
    "facilities": ["parking", "restroom", "food_court"],
    "operatingHours": {"open": "06:00", "close": "18:00"},
    "parkingAvailable": true
  },
  {
    "id": "jetty-002",
    "name": "Jetty Merang",
    "code": "JM",
    "fullName": "Jetty Merang, Terengganu",
    "location": "Merang, Terengganu",
    "coordinates": {"lat": 5.8234, "lng": 102.9456},
    "facilities": ["parking", "restroom", "ticket_office"],
    "operatingHours": {"open": "06:00", "close": "18:00"},
    "parkingAvailable": true
  }
]
```

### Example Provider Service
```json
{
  "id": "service-001",
  "providerId": "provider-001",
  "serviceTypeId": "type-001",
  "name": "Half Day Snorkeling Adventure",
  "description": "Explore the vibrant coral reefs and marine life around Redang Island",
  "basePrice": 280.00,
  "agePricing": {
    "adult": 280.00,
    "child": 196.00,
    "infant": 0.00
  },
  "duration": 240,
  "maxCapacity": 12,
  "includedItems": [
    "Snorkeling gear (mask, fins, snorkel)",
    "Life jackets",
    "Professional guide",
    "Light refreshments",
    "Underwater photography"
  ],
  "itinerary": [
    {"time": "09:00", "activity": "Departure from jetty", "duration": "30 min"},
    {"time": "09:30", "activity": "First snorkeling spot", "duration": "90 min"},
    {"time": "11:00", "activity": "Second snorkeling spot", "duration": "90 min"},
    {"time": "12:30", "activity": "Return journey", "duration": "30 min"}
  ]
}
```

## API Endpoint Changes

### New Search Endpoints
```
GET /api/search/service-categories
GET /api/search/jetties
GET /api/search/destinations
GET /api/search/routes?jetty={jettyId}&destination={destinationId}
GET /api/search/providers?serviceCategory={categoryId}&route={routeId}&date={date}
```

### Enhanced Provider Endpoints
```
GET /api/providers/{id}
GET /api/providers/{id}/services
GET /api/providers/{id}/boats
GET /api/providers/{id}/reviews
POST /api/providers/auto-create  // Auto-create provider for boat owner
```

### Service Management Endpoints
```
GET /api/services/{id}
GET /api/services/{id}/availability?date={date}
GET /api/services/{id}/pricing
GET /api/services/{id}/assigned-boats
POST /api/services/{id}/book
POST /api/services/{id}/assign-boat
PUT /api/services/{id}/boat-assignments/{assignmentId}
```

### Booking Validation Endpoints
```
POST /api/bookings/validate-passenger-transport
GET /api/bookings/available-capacity?serviceId={id}&date={date}&time={time}
POST /api/bookings/assign-boat
```

## API Validation Logic

### 1. **Provider Auto-Creation Validation**
```javascript
// POST /api/boats - Create new boat
async function createBoat(req, res) {
  const { ownerId, ...boatData } = req.body;

  // Check if user is boat owner
  const user = await User.findById(ownerId);
  if (user.role !== 'BOAT_OWNER') {
    return res.status(400).json({ error: 'User must be a boat owner' });
  }

  // Auto-create provider if doesn't exist
  let provider = await Provider.findOne({ userId: ownerId });
  if (!provider) {
    const profile = await Profile.findOne({ userId: ownerId });
    provider = await Provider.create({
      userId: ownerId,
      companyName: `${profile.firstName} ${profile.lastName} Marine Services`,
      displayName: `${profile.firstName} ${profile.lastName}`,
      contactPhone: profile.phone || user.email,
      contactEmail: user.email,
      isAutoGenerated: true
    });
  }

  // Create boat with provider reference
  const boat = await Boat.create({
    ...boatData,
    providerId: provider.id
  });

  res.json({ boat, provider });
}
```

### 2. **Service Assignment Validation**
```javascript
// POST /api/services/{id}/assign-boat
async function assignBoatToService(req, res) {
  const { serviceId } = req.params;
  const { boatId, isPrimary, maxCapacityOverride } = req.body;

  // Validate boat belongs to same provider as service
  const service = await ProviderService.findById(serviceId).populate('provider');
  const boat = await Boat.findById(boatId);

  if (boat.providerId !== service.providerId) {
    return res.status(400).json({
      error: 'Boat must belong to the same provider as the service'
    });
  }

  // If setting as primary, remove primary flag from other assignments
  if (isPrimary) {
    await ServiceAssignment.updateMany(
      { serviceId, isPrimary: true },
      { isPrimary: false }
    );
  }

  // Validate capacity override
  if (maxCapacityOverride && maxCapacityOverride > boat.capacity) {
    return res.status(400).json({
      error: 'Capacity override cannot exceed boat capacity'
    });
  }

  const assignment = await ServiceAssignment.create({
    serviceId,
    boatId,
    isPrimary,
    maxCapacityOverride
  });

  res.json(assignment);
}
```

### 3. **Passenger Transport Booking Validation**
```javascript
// POST /api/services/{id}/book
async function bookService(req, res) {
  const { serviceId } = req.params;
  const { routeId, ...bookingData } = req.body;

  // Check if service requires destination (passenger transport)
  const service = await ProviderService.findById(serviceId)
    .populate({
      path: 'serviceType',
      populate: { path: 'category' }
    });

  const requiresDestination = service.serviceType.category.requiresDestination;

  if (requiresDestination && !routeId) {
    return res.status(400).json({
      error: 'Destination selection is mandatory for passenger transport services'
    });
  }

  // Validate route exists for this service
  if (routeId) {
    const serviceRoute = await ServiceRoute.findOne({ serviceId, routeId });
    if (!serviceRoute) {
      return res.status(400).json({
        error: 'Selected route is not available for this service'
      });
    }
  }

  // Calculate available capacity and assign boat
  const availableCapacity = await calculateServiceCapacity(serviceId, bookingData.serviceDate, bookingData.serviceTime);

  if (bookingData.passengerCount > availableCapacity) {
    return res.status(400).json({
      error: 'Insufficient capacity for requested passenger count'
    });
  }

  // Assign boat using priority logic
  const assignedBoat = await assignBoatForBooking(serviceId, bookingData.serviceDate, bookingData.serviceTime);

  const booking = await Booking.create({
    ...bookingData,
    serviceId,
    routeId,
    assignedBoatId: assignedBoat.id
  });

  res.json(booking);
}
```

## Implementation Considerations

### 1. **Data Migration Complexity**
- Existing boats need to be grouped under providers
- Current location enum needs mapping to new jetty/destination system
- Booking history must be preserved during migration

### 2. **Performance Optimization**
- Index on frequently queried fields (providerId, serviceTypeId, routeId)
- Consider materialized views for complex search queries
- Implement caching for static data (jetties, destinations, service categories)

### 3. **Business Logic Changes**
- Boat assignment algorithm for bookings
- Availability calculation across multiple boats per service
- Pricing calculation with route modifiers

### 4. **Frontend Impact**

#### Search Flow Redesign
```javascript
// Enhanced search component with business rules
const BoatSearchForm = () => {
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedJetty, setSelectedJetty] = useState(null);
  const [selectedDestination, setSelectedDestination] = useState(null);
  const [requiresDestination, setRequiresDestination] = useState(false);

  // Update destination requirement based on category
  useEffect(() => {
    if (selectedCategory) {
      setRequiresDestination(selectedCategory.requiresDestination);
      if (!selectedCategory.requiresDestination) {
        setSelectedDestination(null); // Clear destination for non-transport services
      }
    }
  }, [selectedCategory]);

  const handleSearch = () => {
    // Validate passenger transport requirements
    if (requiresDestination && !selectedDestination) {
      alert('Please select a destination for passenger transport services');
      return;
    }

    // Proceed with search
    router.push(`/search?category=${selectedCategory.id}&jetty=${selectedJetty.id}&destination=${selectedDestination?.id || ''}`);
  };

  return (
    <form>
      <ServiceCategorySelect
        value={selectedCategory}
        onChange={setSelectedCategory}
        required
      />

      <JettySelect
        value={selectedJetty}
        onChange={setSelectedJetty}
        required
      />

      {requiresDestination && (
        <DestinationSelect
          value={selectedDestination}
          onChange={setSelectedDestination}
          jettyId={selectedJetty?.id}
          required
          primaryDestinations={['Pulau Perhentian', 'Pulau Redang']}
        />
      )}

      <button onClick={handleSearch}>Search Providers</button>
    </form>
  );
};
```

#### Provider Search Results
```javascript
// Provider-centric search results
const SearchResults = ({ providers }) => {
  return (
    <div className="provider-results">
      {providers.map(provider => (
        <ProviderCard key={provider.id}>
          <div className="provider-header">
            <h3>{provider.displayName}</h3>
            {provider.isAutoGenerated && (
              <span className="badge">Individual Owner</span>
            )}
            <div className="rating">
              ⭐ {provider.rating} ({provider.reviewCount} reviews)
            </div>
          </div>

          <div className="provider-stats">
            <span>{provider.boatCount} boats available</span>
            <span>Starting from RM {provider.startingPrice}</span>
          </div>

          <div className="services-preview">
            {provider.services.map(service => (
              <ServicePreview
                key={service.id}
                service={service}
                assignedBoats={service.assignedBoats}
                availableCapacity={service.availableCapacity}
              />
            ))}
          </div>
        </ProviderCard>
      ))}
    </div>
  );
};
```

#### Enhanced Booking Wizard
```javascript
// Service-first booking wizard with boat assignment
const BookingWizard = ({ serviceId }) => {
  const [service, setService] = useState(null);
  const [selectedRoute, setSelectedRoute] = useState(null);
  const [assignedBoat, setAssignedBoat] = useState(null);
  const [availableCapacity, setAvailableCapacity] = useState(0);

  // Load service details and check requirements
  useEffect(() => {
    loadServiceDetails(serviceId).then(data => {
      setService(data);
      if (data.category.requiresDestination && data.routes.length === 1) {
        setSelectedRoute(data.routes[0]); // Auto-select if only one route
      }
    });
  }, [serviceId]);

  // Calculate available capacity when date/time changes
  const handleDateTimeChange = async (date, time) => {
    const capacity = await fetchAvailableCapacity(serviceId, date, time);
    setAvailableCapacity(capacity);

    // Get assigned boat for this booking
    const boat = await getAssignedBoat(serviceId, date, time);
    setAssignedBoat(boat);
  };

  const handleBooking = async (bookingData) => {
    // Validate passenger transport requirements
    if (service.category.requiresDestination && !selectedRoute) {
      throw new Error('Please select a destination');
    }

    // Validate capacity
    if (bookingData.passengerCount > availableCapacity) {
      throw new Error('Insufficient capacity available');
    }

    // Submit booking with route and assigned boat
    const booking = await createBooking({
      ...bookingData,
      serviceId,
      routeId: selectedRoute?.id,
      assignedBoatId: assignedBoat?.id
    });

    return booking;
  };

  return (
    <div className="booking-wizard">
      <ServiceDetails service={service} />

      {service?.category.requiresDestination && (
        <RouteSelection
          routes={service.routes}
          selected={selectedRoute}
          onChange={setSelectedRoute}
          required
        />
      )}

      <DateTimeSelection onChange={handleDateTimeChange} />

      {assignedBoat && (
        <AssignedBoatInfo
          boat={assignedBoat}
          availableCapacity={availableCapacity}
        />
      )}

      <PassengerDetails
        maxCapacity={availableCapacity}
        onSubmit={handleBooking}
      />
    </div>
  );
};
```

#### Provider Dashboard Enhancements
```javascript
// Enhanced provider dashboard for boat and service management
const ProviderDashboard = () => {
  const [provider, setProvider] = useState(null);
  const [boats, setBoats] = useState([]);
  const [services, setServices] = useState([]);

  return (
    <div className="provider-dashboard">
      <ProviderHeader provider={provider} />

      <div className="management-sections">
        <BoatManagement
          boats={boats}
          onBoatAdd={handleBoatAdd}
          onBoatEdit={handleBoatEdit}
        />

        <ServiceManagement
          services={services}
          boats={boats}
          onServiceCreate={handleServiceCreate}
          onBoatAssignment={handleBoatAssignment}
        />

        <BookingManagement
          services={services}
          onBoatReassignment={handleBoatReassignment}
        />
      </div>
    </div>
  );
};
```

This comprehensive redesign transforms GoSea from a simple boat listing platform into a sophisticated marine service marketplace that can handle complex provider relationships, flexible routing, and scalable service offerings.
