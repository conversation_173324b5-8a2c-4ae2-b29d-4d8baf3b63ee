const { PrismaClient } = require('@prisma/client');
const BusinessRulesService = require('./src/services/businessRulesService');

const prisma = new PrismaClient();

async function testBusinessRules() {
  console.log('🧪 Testing Business Rules Implementation...\n');

  try {
    // Test 1: Auto-Provider Creation
    console.log('1️⃣ Testing Auto-Provider Creation...');
    
    // Create a test user
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        role: 'BOAT_OWNER',
        emailVerified: true,
      },
    });

    // Create a profile for the user
    const testProfile = await prisma.profile.create({
      data: {
        userId: testUser.id,
        firstName: 'John',
        lastName: 'Doe',
        phone: '+***********',
      },
    });

    // Create a boat (this should trigger auto-provider creation)
    const testBoat = await prisma.boat.create({
      data: {
        ownerId: testUser.id,
        name: 'Test Boat',
        description: 'A test boat for business rules',
        capacity: 20,
        basePrice: '150.00',
        location: 'Test Location',
        serviceType: 'PASSENGER_FERRY',
      },
    });

    // Check if provider was auto-created
    const autoProvider = await prisma.provider.findUnique({
      where: { userId: testUser.id },
    });

    if (autoProvider && autoProvider.isAutoGenerated) {
      console.log('✅ Auto-provider creation: PASSED');
      console.log(`   Provider created: ${autoProvider.displayName}`);
    } else {
      console.log('❌ Auto-provider creation: FAILED');
    }

    // Test 2: Provider Service Creation
    console.log('\n2️⃣ Testing Provider Service Creation...');
    
    const testService = await prisma.providerService.create({
      data: {
        providerId: autoProvider.id,
        serviceTypeId: 'st_passenger_ferry',
        name: 'Ferry to Perhentian',
        description: 'Regular ferry service to Pulau Perhentian',
        basePrice: '45.00',
        maxCapacity: 20,
      },
    });

    console.log('✅ Provider service creation: PASSED');
    console.log(`   Service created: ${testService.name}`);

    // Test 3: Service Assignment Validation
    console.log('\n3️⃣ Testing Service Assignment Validation...');
    
    // Valid assignment (same provider)
    const validAssignment = await BusinessRulesService.validateServiceAssignment(
      testBoat.id,
      testService.id,
      true
    );

    if (validAssignment.valid) {
      console.log('✅ Valid service assignment validation: PASSED');
    } else {
      console.log('❌ Valid service assignment validation: FAILED');
      console.log(`   Error: ${validAssignment.error}`);
    }

    // Create the assignment
    const serviceAssignment = await prisma.serviceAssignment.create({
      data: {
        boatId: testBoat.id,
        serviceId: testService.id,
        isPrimary: true,
      },
    });

    console.log('✅ Service assignment creation: PASSED');

    // Test 4: Duplicate Primary Assignment Prevention
    console.log('\n4️⃣ Testing Duplicate Primary Assignment Prevention...');
    
    // Create another service
    const testService2 = await prisma.providerService.create({
      data: {
        providerId: autoProvider.id,
        serviceTypeId: 'st_snorkeling_half_day',
        name: 'Half Day Snorkeling',
        description: 'Half day snorkeling tour',
        basePrice: '80.00',
        maxCapacity: 15,
      },
    });

    // Try to assign the same boat as primary to another service
    const duplicatePrimaryValidation = await BusinessRulesService.validateServiceAssignment(
      testBoat.id,
      testService2.id,
      true
    );

    if (!duplicatePrimaryValidation.valid) {
      console.log('✅ Duplicate primary assignment prevention: PASSED');
      console.log(`   Correctly blocked: ${duplicatePrimaryValidation.error}`);
    } else {
      console.log('❌ Duplicate primary assignment prevention: FAILED');
    }

    // Test 5: Provider Operating Area Validation
    console.log('\n5️⃣ Testing Provider Operating Area Validation...');
    
    const operatingAreaValidation = await BusinessRulesService.validateProviderOperatingArea(
      autoProvider.id,
      'jetty_kampung_mangkuk'
    );

    if (operatingAreaValidation.valid) {
      console.log('✅ Provider operating area validation: PASSED');
      
      // Create the operating area
      const operatingArea = await prisma.providerOperatingArea.create({
        data: {
          providerId: autoProvider.id,
          jettyId: 'jetty_kampung_mangkuk',
        },
      });
      
      console.log(`   Operating area created for jetty: jetty_kampung_mangkuk`);
    } else {
      console.log('❌ Provider operating area validation: FAILED');
      console.log(`   Error: ${operatingAreaValidation.error}`);
    }

    // Test 6: Service Route Assignment
    console.log('\n6️⃣ Testing Service Route Assignment...');
    
    const serviceRoute = await prisma.serviceRoute.create({
      data: {
        serviceId: testService.id,
        routeId: 'route_km_perhentian',
      },
    });

    console.log('✅ Service route assignment: PASSED');
    console.log(`   Route assigned: route_km_perhentian`);

    // Test 7: Passenger Transport Route Validation
    console.log('\n7️⃣ Testing Passenger Transport Route Validation...');
    
    const passengerTransportValidation = await BusinessRulesService.validatePassengerTransportRoutes(
      testService.id
    );

    if (passengerTransportValidation.valid) {
      console.log('✅ Passenger transport route validation: PASSED');
    } else {
      console.log('❌ Passenger transport route validation: FAILED');
      console.log(`   Error: ${passengerTransportValidation.error}`);
    }

    // Test 8: Service Capacity Calculation
    console.log('\n8️⃣ Testing Service Capacity Calculation...');
    
    const capacityInfo = await BusinessRulesService.calculateServiceCapacity(testService.id);
    
    if (capacityInfo.capacity === testBoat.capacity) {
      console.log('✅ Service capacity calculation: PASSED');
      console.log(`   Total capacity: ${capacityInfo.capacity}`);
      console.log(`   Assigned boats: ${capacityInfo.assignments.length}`);
    } else {
      console.log('❌ Service capacity calculation: FAILED');
      console.log(`   Expected: ${testBoat.capacity}, Got: ${capacityInfo.capacity}`);
    }

    // Test 9: Route Validation
    console.log('\n9️⃣ Testing Route Validation...');
    
    const routeValidation = await BusinessRulesService.validateRoute({
      departureJettyId: 'jetty_merang',
      destinationId: 'dest_perhentian',
      distance: 20.0,
      estimatedDuration: 35,
    });

    if (routeValidation.valid) {
      console.log('✅ Route validation: PASSED');
    } else {
      console.log('❌ Route validation: FAILED');
      console.log(`   Error: ${routeValidation.error}`);
    }

    // Test 10: Cross-Provider Assignment Prevention
    console.log('\n🔟 Testing Cross-Provider Assignment Prevention...');
    
    // Create another user and provider
    const testUser2 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        role: 'BOAT_OWNER',
        emailVerified: true,
      },
    });

    const testProvider2 = await prisma.provider.create({
      data: {
        userId: testUser2.id,
        companyName: 'Another Marine Services',
        displayName: 'Another Provider',
        contactEmail: '<EMAIL>',
        isAutoGenerated: true,
      },
    });

    // Try to assign boat from provider1 to service from provider2
    const testService3 = await prisma.providerService.create({
      data: {
        providerId: testProvider2.id,
        serviceTypeId: 'st_passenger_ferry',
        name: 'Another Ferry Service',
        description: 'Ferry service from another provider',
        basePrice: '50.00',
        maxCapacity: 25,
      },
    });

    const crossProviderValidation = await BusinessRulesService.validateServiceAssignment(
      testBoat.id,
      testService3.id,
      false
    );

    if (!crossProviderValidation.valid) {
      console.log('✅ Cross-provider assignment prevention: PASSED');
      console.log(`   Correctly blocked: ${crossProviderValidation.error}`);
    } else {
      console.log('❌ Cross-provider assignment prevention: FAILED');
    }

    console.log('\n🎉 Business Rules Testing Complete!');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  } finally {
    // Clean up test data
    console.log('\n🧹 Cleaning up test data...');
    
    try {
      await prisma.serviceRoute.deleteMany({
        where: { serviceId: { contains: 'test' } },
      });
      await prisma.serviceAssignment.deleteMany({
        where: { boat: { name: 'Test Boat' } },
      });
      await prisma.providerOperatingArea.deleteMany({
        where: { provider: { displayName: { contains: 'Test' } } },
      });
      await prisma.providerService.deleteMany({
        where: { provider: { displayName: { contains: 'Test' } } },
      });
      await prisma.boat.deleteMany({
        where: { name: 'Test Boat' },
      });
      await prisma.provider.deleteMany({
        where: { displayName: { contains: 'Test' } },
      });
      await prisma.profile.deleteMany({
        where: { user: { email: { contains: 'testowner' } } },
      });
      await prisma.user.deleteMany({
        where: { email: { contains: 'testowner' } },
      });
      
      console.log('✅ Test data cleaned up successfully');
    } catch (cleanupError) {
      console.error('⚠️ Error during cleanup:', cleanupError);
    }

    await prisma.$disconnect();
  }
}

// Run the tests
testBusinessRules();
