const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Check if data already exists
  const existingJetties = await prisma.jetty.count();
  if (existingJetties > 0) {
    console.log('🔄 Database already has data, clearing and reseeding...');

    // Clear existing data in reverse dependency order
    await prisma.serviceSchedule.deleteMany();
    await prisma.serviceAgePricing.deleteMany();
    await prisma.serviceAssignment.deleteMany();
    await prisma.serviceRoute.deleteMany();
    await prisma.servicePackage.deleteMany();
    await prisma.providerService.deleteMany();
    await prisma.providerOperatingArea.deleteMany();
    await prisma.boat.deleteMany();
    await prisma.provider.deleteMany();
    await prisma.serviceType.deleteMany();
    await prisma.serviceCategory.deleteMany();
    await prisma.route.deleteMany();
    await prisma.destination.deleteMany();
    await prisma.jetty.deleteMany();
    // Delete sample users (only those with specific IDs)
    await prisma.user.deleteMany({
      where: {
        id: {
          in: [
            'user_ocean_adventures', 'user_island_express', 'user_coral_divers',
            'user_marine_explorer', 'user_tropical_cruises'
          ]
        }
      }
    });

    console.log('🗑️ Existing data cleared');
  }

  // Seed Jetties
  console.log('📍 Seeding jetties...');
  const jetties = await Promise.all([
    prisma.jetty.create({
      data: {
        id: 'jeti_kampung_mangkuk',
        name: 'Jeti Kampung Mangkuk',
        code: 'KM',
        fullName: 'Jeti Kampung Mangkuk, Setiu',
        city: 'Setiu',
        postcode: '22100',
        state: 'Terengganu',
        coordinates: { lat: 5.6667, lng: 102.7333 },
        facilities: ['Parking', 'Restroom', 'Food Stalls'],
        operatingHours: { open: '06:00', close: '18:00' },
        parkingAvailable: true,
      },
    }),
    prisma.jetty.create({
      data: {
        id: 'jeti_merang',
        name: 'Jeti Merang',
        code: 'MR',
        fullName: 'Jeti Merang, Setiu',
        city: 'Setiu',
        postcode: '22100',
        state: 'Terengganu',
        coordinates: { lat: 5.6833, lng: 102.7500 },
        facilities: ['Parking', 'Restroom', 'Ticket Counter'],
        operatingHours: { open: '06:00', close: '18:00' },
        parkingAvailable: true,
      },
    }),
    prisma.jetty.create({
      data: {
        id: 'jeti_penarik',
        name: 'Jeti Penarik',
        code: 'PN',
        fullName: 'Jeti Penarik, Setiu',
        city: 'Setiu',
        postcode: '22100',
        state: 'Terengganu',
        coordinates: { lat: 5.7000, lng: 102.7667 },
        facilities: ['Parking', 'Restroom'],
        operatingHours: { open: '06:00', close: '18:00' },
        parkingAvailable: true,
      },
    }),
    // Add more diverse jetties with different locations
    prisma.jetty.create({
      data: {
        id: 'jeti_kuala_besut',
        name: 'Jeti Kuala Besut',
        code: 'KB',
        fullName: 'Jeti Kuala Besut, Besut',
        city: 'Kuala Besut',
        postcode: '22300',
        state: 'Terengganu',
        coordinates: { lat: 5.8234, lng: 102.5678 },
        facilities: ['Parking', 'Restroom', 'Ticket Counter', 'Waiting Area'],
        operatingHours: { open: '07:00', close: '17:00' },
        parkingAvailable: true,
      },
    }),
    prisma.jetty.create({
      data: {
        id: 'jeti_tok_bali',
        name: 'Jeti Tok Bali',
        code: 'TB',
        fullName: 'Jeti Tok Bali, Pasir Puteh',
        city: 'Pasir Puteh',
        postcode: '16800',
        state: 'Kelantan',
        coordinates: { lat: 5.9456, lng: 102.4321 },
        facilities: ['Parking', 'Restroom', 'Food Court'],
        operatingHours: { open: '06:30', close: '18:30' },
        parkingAvailable: true,
      },
    }),
  ]);

  // Seed Destinations
  console.log('🏝️ Seeding destinations...');
  const destinations = await Promise.all([
    prisma.destination.create({
      data: {
        id: 'dest_perhentian',
        name: 'Pulau Perhentian',
        code: 'PERHENTIAN',
        fullName: 'Pulau Perhentian Besar & Kecil',
        description: 'Beautiful twin islands known for crystal clear waters and vibrant marine life',
        state: 'Terengganu',
        coordinates: { lat: 5.9167, lng: 102.7333 },
        popularActivities: ['Snorkeling', 'Diving', 'Beach Activities', 'Island Hopping'],
      },
    }),
    prisma.destination.create({
      data: {
        id: 'dest_redang',
        name: 'Pulau Redang',
        code: 'REDANG',
        fullName: 'Pulau Redang Marine Park',
        description: 'Pristine island with white sandy beaches and excellent diving spots',
        state: 'Terengganu',
        coordinates: { lat: 5.7667, lng: 103.0167 },
        popularActivities: ['Snorkeling', 'Diving', 'Turtle Watching', 'Beach Activities'],
      },
    }),
    // Add more diverse destinations from different states
    prisma.destination.create({
      data: {
        id: 'dest_lang_tengah',
        name: 'Pulau Lang Tengah',
        code: 'LANGTENGAH',
        fullName: 'Pulau Lang Tengah Marine Park',
        description: 'Secluded island paradise with pristine coral reefs and clear waters',
        state: 'Terengganu',
        coordinates: { lat: 5.8000, lng: 102.8500 },
        popularActivities: ['Snorkeling', 'Diving', 'Beach Relaxation', 'Marine Life Watching'],
      },
    }),
    prisma.destination.create({
      data: {
        id: 'dest_kapas',
        name: 'Pulau Kapas',
        code: 'KAPAS',
        fullName: 'Pulau Kapas Island',
        description: 'Cotton-white sandy beaches with crystal clear waters perfect for snorkeling',
        state: 'Terengganu',
        coordinates: { lat: 5.2167, lng: 103.2667 },
        popularActivities: ['Snorkeling', 'Beach Activities', 'Kayaking', 'Fishing'],
      },
    }),
    prisma.destination.create({
      data: {
        id: 'dest_tioman',
        name: 'Pulau Tioman',
        code: 'TIOMAN',
        fullName: 'Pulau Tioman Marine Park',
        description: 'Legendary island with lush rainforests, pristine beaches, and world-class diving',
        state: 'Pahang',
        coordinates: { lat: 2.8167, lng: 104.1667 },
        popularActivities: ['Diving', 'Snorkeling', 'Jungle Trekking', 'Beach Activities'],
      },
    }),
  ]);

  // Seed Routes
  console.log('🛤️ Seeding routes...');
  const routes = await Promise.all([
    // Kampung Mangkuk routes
    prisma.route.create({
      data: {
        id: 'route_km_perhentian',
        departureJettyId: 'jeti_kampung_mangkuk',
        destinationId: 'dest_perhentian',
        distance: 25.0,
        estimatedDuration: 45,
      },
    }),
    prisma.route.create({
      data: {
        id: 'route_km_redang',
        departureJettyId: 'jeti_kampung_mangkuk',
        destinationId: 'dest_redang',
        distance: 35.0,
        estimatedDuration: 60,
      },
    }),
    // Merang routes
    prisma.route.create({
      data: {
        id: 'route_mr_perhentian',
        departureJettyId: 'jeti_merang',
        destinationId: 'dest_perhentian',
        distance: 20.0,
        estimatedDuration: 35,
      },
    }),
    prisma.route.create({
      data: {
        id: 'route_mr_redang',
        departureJettyId: 'jeti_merang',
        destinationId: 'dest_redang',
        distance: 30.0,
        estimatedDuration: 50,
      },
    }),
    // Penarik routes
    prisma.route.create({
      data: {
        id: 'route_pn_perhentian',
        departureJettyId: 'jeti_penarik',
        destinationId: 'dest_perhentian',
        distance: 30.0,
        estimatedDuration: 50,
      },
    }),
    prisma.route.create({
      data: {
        id: 'route_pn_redang',
        departureJettyId: 'jeti_penarik',
        destinationId: 'dest_redang',
        distance: 40.0,
        estimatedDuration: 70,
      },
    }),
  ]);

  // Seed Service Categories
  console.log('📋 Seeding service categories...');
  const serviceCategories = await Promise.all([
    prisma.serviceCategory.create({
      data: {
        id: 'cat_passenger_transport',
        name: 'Passenger Transport',
        code: 'PASSENGER_TRANSPORT',
        description: 'Ferry and boat transport services to islands and destinations',
        requiresDestination: true,
        sortOrder: 1,
      },
    }),
    prisma.serviceCategory.create({
      data: {
        id: 'cat_snorkeling_tours',
        name: 'Snorkeling Tours',
        code: 'SNORKELING_TOURS',
        description: 'Guided snorkeling experiences and marine life tours',
        requiresDestination: true,
        sortOrder: 2,
      },
    }),
    prisma.serviceCategory.create({
      data: {
        id: 'cat_diving_tours',
        name: 'Diving Tours',
        code: 'DIVING_TOURS',
        description: 'Professional diving tours and certification courses',
        requiresDestination: false,
        sortOrder: 3,
      },
    }),
    prisma.serviceCategory.create({
      data: {
        id: 'cat_island_hopping',
        name: 'Island Hopping',
        code: 'ISLAND_HOPPING',
        description: 'Multi-destination island exploration tours',
        requiresDestination: false,
        sortOrder: 4,
      },
    }),
  ]);

  // Seed Service Types
  console.log('🎯 Seeding service types...');
  const serviceTypes = await Promise.all([
    prisma.serviceType.create({
      data: {
        id: 'st_passenger_ferry',
        categoryId: 'cat_passenger_transport',
        name: 'Passenger Ferry',
        code: 'PASSENGER_FERRY',
        description: 'Regular ferry service for passenger transport',
        defaultDuration: 60,
        requiresRoute: true,
      },
    }),
    prisma.serviceType.create({
      data: {
        id: 'st_snorkeling_half_day',
        categoryId: 'cat_snorkeling_tours',
        name: 'Half Day Snorkeling',
        code: 'SNORKELING_HALF_DAY',
        description: 'Half day snorkeling tour with equipment included',
        defaultDuration: 240,
        requiresRoute: false,
      },
    }),
    prisma.serviceType.create({
      data: {
        id: 'st_snorkeling_full_day',
        categoryId: 'cat_snorkeling_tours',
        name: 'Full Day Snorkeling',
        code: 'SNORKELING_FULL_DAY',
        description: 'Full day snorkeling adventure with lunch included',
        defaultDuration: 480,
        requiresRoute: false,
      },
    }),
  ]);

  // Seed Age Categories (use upsert to avoid conflicts)
  console.log('👥 Seeding age categories...');
  const ageCategories = await Promise.all([
    prisma.ageCategory.upsert({
      where: { id: 'age_adult' },
      update: {},
      create: {
        id: 'age_adult',
        name: 'Adult',
        code: 'ADULT',
        description: 'Adult pricing (18+ years)',
        minAge: 18,
        maxAge: null,
        sortOrder: 1,
      },
    }),
    prisma.ageCategory.upsert({
      where: { id: 'age_child' },
      update: {},
      create: {
        id: 'age_child',
        name: 'Child',
        code: 'CHILD',
        description: 'Child pricing (3-17 years)',
        minAge: 3,
        maxAge: 17,
        sortOrder: 2,
      },
    }),
    prisma.ageCategory.upsert({
      where: { id: 'age_toddler' },
      update: {},
      create: {
        id: 'age_toddler',
        name: 'Toddler',
        code: 'TODDLER',
        description: 'Toddler pricing (0-2 years)',
        minAge: 0,
        maxAge: 2,
        sortOrder: 3,
      },
    }),
    prisma.ageCategory.upsert({
      where: { id: 'age_senior' },
      update: {},
      create: {
        id: 'age_senior',
        name: 'Senior',
        code: 'SENIOR',
        description: 'Senior citizen pricing (65+ years)',
        minAge: 65,
        maxAge: null,
        sortOrder: 4,
      },
    }),
    prisma.ageCategory.upsert({
      where: { id: 'age_pwd' },
      update: {},
      create: {
        id: 'age_pwd',
        name: 'PWD',
        code: 'PWD',
        description: 'Person with Disabilities pricing (special rates)',
        minAge: null,
        maxAge: null,
        sortOrder: 5,
      },
    }),
  ]);

  // Seed Package Types (use upsert to avoid conflicts)
  console.log('📦 Seeding package types...');
  const packageTypes = await Promise.all([
    prisma.packageType.upsert({
      where: { id: 'pkg_basic' },
      update: {},
      create: {
        id: 'pkg_basic',
        name: 'Basic Package',
        code: 'BASIC',
        description: 'Essential service with basic inclusions',
        isDefault: false, // Don't override existing default
        sortOrder: 1,
      },
    }),
    prisma.packageType.upsert({
      where: { id: 'pkg_premium' },
      update: {},
      create: {
        id: 'pkg_premium',
        name: 'Premium Package',
        code: 'PREMIUM',
        description: 'Enhanced service with additional amenities and perks',
        sortOrder: 2,
      },
    }),
    prisma.packageType.upsert({
      where: { id: 'pkg_deluxe' },
      update: {},
      create: {
        id: 'pkg_deluxe',
        name: 'Deluxe Package',
        code: 'DELUXE',
        description: 'Luxury experience with all-inclusive amenities',
        sortOrder: 3,
      },
    }),
  ]);

  // Create sample users for providers first
  console.log('👤 Creating sample users for providers...');
  const providerUsers = await Promise.all([
    prisma.user.create({
      data: {
        id: 'user_ocean_adventures',
        email: '<EMAIL>',
        role: 'BOAT_OWNER',
        emailVerified: true,
        password: '$2a$10$dummy.hash.for.seeding.purposes.only',
        profile: {
          create: {
            firstName: 'Ocean',
            lastName: 'Adventures',
            phone: '+60123456789',
          }
        }
      },
    }),
    prisma.user.create({
      data: {
        id: 'user_island_express',
        email: '<EMAIL>',
        role: 'BOAT_OWNER',
        emailVerified: true,
        password: '$2a$10$dummy.hash.for.seeding.purposes.only',
        profile: {
          create: {
            firstName: 'Island',
            lastName: 'Express',
            phone: '+60198765432',
          }
        }
      },
    }),
    prisma.user.create({
      data: {
        id: 'user_coral_divers',
        email: '<EMAIL>',
        role: 'BOAT_OWNER',
        emailVerified: true,
        password: '$2a$10$dummy.hash.for.seeding.purposes.only',
        profile: {
          create: {
            firstName: 'Coral',
            lastName: 'Divers',
            phone: '+60176543210',
          }
        }
      },
    }),
    prisma.user.create({
      data: {
        id: 'user_marine_explorer',
        email: '<EMAIL>',
        role: 'BOAT_OWNER',
        emailVerified: true,
        password: '$2a$10$dummy.hash.for.seeding.purposes.only',
        profile: {
          create: {
            firstName: 'Marine',
            lastName: 'Explorer',
            phone: '+60134567890',
          }
        }
      },
    }),
    prisma.user.create({
      data: {
        id: 'user_tropical_cruises',
        email: '<EMAIL>',
        role: 'BOAT_OWNER',
        emailVerified: true,
        password: '$2a$10$dummy.hash.for.seeding.purposes.only',
        profile: {
          create: {
            firstName: 'Tropical',
            lastName: 'Cruises',
            phone: '+60145678901',
          }
        }
      },
    }),
  ]);

  // Seed Sample Providers
  console.log('🏢 Seeding sample providers...');
  const providers = await Promise.all([
    prisma.provider.create({
      data: {
        id: 'provider_ocean_adventures',
        userId: 'user_ocean_adventures',
        companyName: 'Ocean Adventures Malaysia',
        displayName: 'Ocean Adventures',
        brn: 'SSM123456789',
        contactEmail: '<EMAIL>',
        contactPhone: '+60123456789',
        description: 'Premier marine tour operator specializing in snorkeling and diving adventures around Terengganu islands.',
        logoUrl: 'https://images.unsplash.com/photo-**********-46a013bb70d5?w=400&h=400&fit=crop&crop=center',
        operatingLicense: 'MOTAC-2015-001',
        rating: 4.8,
        reviewCount: 156,
        isActive: true,
        isVerified: true,
      },
    }),
    prisma.provider.create({
      data: {
        id: 'provider_island_express',
        userId: 'user_island_express',
        companyName: 'Island Express Ferry Services',
        displayName: 'Island Express',
        brn: 'SSM987654321',
        contactEmail: '<EMAIL>',
        contactPhone: '+60198765432',
        description: 'Reliable ferry services connecting mainland to Perhentian and Redang islands with daily scheduled departures.',
        logoUrl: 'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=400&h=400&fit=crop&crop=center',
        operatingLicense: 'MOTAC-2010-002',
        rating: 4.5,
        reviewCount: 89,
        isActive: true,
        isVerified: true,
      },
    }),
    prisma.provider.create({
      data: {
        id: 'provider_coral_divers',
        userId: 'user_coral_divers',
        companyName: 'Coral Divers Terengganu',
        displayName: 'Coral Divers',
        brn: 'SSM456789123',
        contactEmail: '<EMAIL>',
        contactPhone: '+60176543210',
        description: 'Professional diving center offering PADI courses and guided diving tours to pristine coral reefs.',
        logoUrl: 'https://images.unsplash.com/photo-**********-dc66d52bef19?w=400&h=400&fit=crop&crop=center',
        operatingLicense: 'MOTAC-2018-003',
        rating: 4.9,
        reviewCount: 234,
        isActive: true,
        isVerified: true,
      },
    }),
    prisma.provider.create({
      data: {
        id: 'provider_marine_explorer',
        userId: 'user_marine_explorer',
        companyName: 'Marine Explorer Adventures',
        displayName: 'Marine Explorer',
        brn: 'SSM789123456',
        contactEmail: '<EMAIL>',
        contactPhone: '+60134567890',
        description: 'Specialized marine wildlife tours and eco-friendly boat excursions to protected marine areas.',
        logoUrl: 'https://images.unsplash.com/photo-1583212292454-1fe6229603b7?w=400&h=400&fit=crop&crop=center',
        operatingLicense: 'MOTAC-2019-004',
        rating: 4.6,
        reviewCount: 178,
        isActive: true,
        isVerified: true,
      },
    }),
    prisma.provider.create({
      data: {
        id: 'provider_tropical_cruises',
        userId: 'user_tropical_cruises',
        companyName: 'Tropical Cruises Malaysia',
        displayName: 'Tropical Cruises',
        brn: 'SSM123789456',
        contactEmail: '<EMAIL>',
        contactPhone: '+60145678901',
        description: 'Luxury cruise experiences and sunset tours with premium amenities and gourmet dining.',
        logoUrl: 'https://images.unsplash.com/photo-1540946485063-a40da27545f8?w=400&h=400&fit=crop&crop=center',
        operatingLicense: 'MOTAC-2020-005',
        rating: 4.8,
        reviewCount: 312,
        isActive: true,
        isVerified: true,
      },
    }),
  ]);

  // Seed Sample Boats
  console.log('⛵ Seeding sample boats...');
  const boats = await Promise.all([
    // Ocean Adventures boats
    prisma.boat.create({
      data: {
        id: 'boat_sea_explorer',
        providerId: 'provider_ocean_adventures',
        ownerId: 'user_ocean_adventures',
        name: 'Sea Explorer',
        engineType: 'Outboard',
        capacity: 12,
        length: 8.5,
        yearBuilt: 2020,
        description: 'Modern speedboat perfect for snorkeling tours with comfortable seating and safety equipment.',
        galleryImages: [
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['GPS Navigation', 'Safety Equipment', 'Snorkeling Gear', 'First Aid Kit'],
          safetyEquipment: ['Life Jackets', 'Emergency Radio', 'Flares', 'Fire Extinguisher']
        },
        isActive: true,
      },
    }),
    prisma.boat.create({
      data: {
        id: 'boat_wave_rider',
        providerId: 'provider_ocean_adventures',
        ownerId: 'user_ocean_adventures',
        name: 'Wave Rider',
        engineType: 'Twin Outboard',
        capacity: 20,
        length: 12.0,
        yearBuilt: 2019,
        description: 'Stable catamaran ideal for larger groups and full-day adventures.',
        galleryImages: [
          'https://images.unsplash.com/photo-**********-77ef2d0cfc6c?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Shade Cover', 'Comfortable Seating', 'Storage Space', 'Sound System'],
          safetyEquipment: ['Life Jackets', 'Emergency Radio', 'GPS', 'First Aid Kit']
        },
        isActive: true,
      },
    }),
    // Island Express boats
    prisma.boat.create({
      data: {
        id: 'boat_island_hopper',
        providerId: 'provider_island_express',
        ownerId: 'user_island_express',
        name: 'Island Hopper',
        engineType: 'Diesel',
        capacity: 50,
        length: 18.0,
        yearBuilt: 2017,
        description: 'Large passenger ferry with covered seating and luggage storage.',
        galleryImages: [
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Covered Seating', 'Luggage Storage', 'Restroom', 'Air Conditioning'],
          safetyEquipment: ['Life Jackets', 'Emergency Equipment', 'Radio Communication']
        },
        isActive: true,
      },
    }),
    // Coral Divers boats
    prisma.boat.create({
      data: {
        id: 'boat_reef_master',
        providerId: 'provider_coral_divers',
        ownerId: 'user_coral_divers',
        name: 'Reef Master',
        engineType: 'Outboard',
        capacity: 16,
        length: 10.0,
        yearBuilt: 2021,
        description: 'Purpose-built dive boat with dive platform and equipment storage.',
        galleryImages: [
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Dive Platform', 'Equipment Storage', 'Nitrox System', 'Camera Rinse Station'],
          safetyEquipment: ['Life Jackets', 'Emergency Oxygen', 'First Aid Kit', 'Emergency Radio']
        },
        isActive: true,
      },
    }),

    // Additional Ocean Adventures boats
    prisma.boat.create({
      data: {
        id: 'boat_ocean_breeze',
        providerId: 'provider_ocean_adventures',
        ownerId: 'user_ocean_adventures',
        name: 'Ocean Breeze',
        engineType: 'Outboard',
        capacity: 10,
        length: 8.5,
        yearBuilt: 2020,
        description: 'Compact speedboat perfect for small group adventures and fishing trips.',
        galleryImages: [
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['GPS Navigation', 'Fish Finder', 'Cooler Box', 'Shade Canopy'],
          safetyEquipment: ['Life Jackets', 'Emergency Radio', 'Flares', 'First Aid Kit']
        },
        isActive: true,
      },
    }),
    prisma.boat.create({
      data: {
        id: 'boat_blue_horizon',
        providerId: 'provider_ocean_adventures',
        ownerId: 'user_ocean_adventures',
        name: 'Blue Horizon',
        engineType: 'Diesel',
        capacity: 20,
        length: 12.0,
        yearBuilt: 2019,
        description: 'Mid-size vessel ideal for day trips and snorkeling excursions.',
        galleryImages: [
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Covered Deck', 'Snorkel Equipment Storage', 'Fresh Water Shower', 'Sound System'],
          safetyEquipment: ['Life Jackets', 'Emergency Radio', 'Flares', 'Fire Extinguisher']
        },
        isActive: true,
      },
    }),
    prisma.boat.create({
      data: {
        id: 'boat_coral_seeker',
        providerId: 'provider_ocean_adventures',
        ownerId: 'user_ocean_adventures',
        name: 'Coral Seeker',
        engineType: 'Outboard',
        capacity: 14,
        length: 9.5,
        yearBuilt: 2021,
        description: 'Specialized boat for coral reef exploration with underwater viewing windows.',
        galleryImages: [
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Underwater Windows', 'Snorkel Gear', 'Marine Life Guide', 'Photography Equipment'],
          safetyEquipment: ['Life Jackets', 'Emergency Radio', 'Flares', 'First Aid Kit']
        },
        isActive: true,
      },
    }),

    // Additional Island Express boats
    prisma.boat.create({
      data: {
        id: 'boat_swift_current',
        providerId: 'provider_island_express',
        ownerId: 'user_island_express',
        name: 'Swift Current',
        engineType: 'Diesel',
        capacity: 40,
        length: 16.0,
        yearBuilt: 2018,
        description: 'Fast passenger ferry with comfortable seating and luggage space.',
        galleryImages: [
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Air Conditioning', 'Comfortable Seating', 'Luggage Storage', 'Refreshment Bar'],
          safetyEquipment: ['Life Jackets', 'Emergency Equipment', 'Radio Communication', 'GPS']
        },
        isActive: true,
      },
    }),
    prisma.boat.create({
      data: {
        id: 'boat_island_star',
        providerId: 'provider_island_express',
        ownerId: 'user_island_express',
        name: 'Island Star',
        engineType: 'Diesel',
        capacity: 35,
        length: 15.0,
        yearBuilt: 2020,
        description: 'Modern ferry with panoramic windows and premium comfort features.',
        galleryImages: [
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Panoramic Windows', 'Premium Seating', 'WiFi', 'Climate Control'],
          safetyEquipment: ['Life Jackets', 'Emergency Equipment', 'Radio Communication', 'Radar']
        },
        isActive: true,
      },
    }),
    prisma.boat.create({
      data: {
        id: 'boat_sea_shuttle',
        providerId: 'provider_island_express',
        ownerId: 'user_island_express',
        name: 'Sea Shuttle',
        engineType: 'Outboard',
        capacity: 25,
        length: 11.0,
        yearBuilt: 2019,
        description: 'Efficient shuttle boat for quick island transfers and day trips.',
        galleryImages: [
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Quick Boarding', 'Covered Seating', 'Storage Compartments', 'Safety Rails'],
          safetyEquipment: ['Life Jackets', 'Emergency Radio', 'First Aid Kit', 'Flares']
        },
        isActive: true,
      },
    }),
    prisma.boat.create({
      data: {
        id: 'boat_express_liner',
        providerId: 'provider_island_express',
        ownerId: 'user_island_express',
        name: 'Express Liner',
        engineType: 'Diesel',
        capacity: 60,
        length: 20.0,
        yearBuilt: 2017,
        description: 'Large capacity ferry for group transfers and scheduled services.',
        galleryImages: [
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Multiple Decks', 'Restrooms', 'Snack Bar', 'Observation Deck'],
          safetyEquipment: ['Life Jackets', 'Emergency Equipment', 'Radio Communication', 'Life Rafts']
        },
        isActive: true,
      },
    }),

    // Additional Coral Divers boats
    prisma.boat.create({
      data: {
        id: 'boat_dive_explorer',
        providerId: 'provider_coral_divers',
        ownerId: 'user_coral_divers',
        name: 'Dive Explorer',
        engineType: 'Outboard',
        capacity: 12,
        length: 9.0,
        yearBuilt: 2020,
        description: 'Specialized diving boat with advanced dive equipment and safety features.',
        galleryImages: [
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Dive Platform', 'Equipment Storage', 'Nitrox System', 'Camera Station'],
          safetyEquipment: ['Life Jackets', 'Emergency Oxygen', 'First Aid Kit', 'Emergency Radio']
        },
        isActive: true,
      },
    }),
    prisma.boat.create({
      data: {
        id: 'boat_reef_guardian',
        providerId: 'provider_coral_divers',
        ownerId: 'user_coral_divers',
        name: 'Reef Guardian',
        engineType: 'Diesel',
        capacity: 18,
        length: 11.5,
        yearBuilt: 2019,
        description: 'Larger dive boat for group diving expeditions and PADI courses.',
        galleryImages: [
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Large Dive Platform', 'Equipment Rental', 'Classroom Area', 'Refreshment Station'],
          safetyEquipment: ['Life Jackets', 'Emergency Oxygen', 'First Aid Kit', 'Emergency Radio']
        },
        isActive: true,
      },
    }),
    prisma.boat.create({
      data: {
        id: 'boat_coral_voyager',
        providerId: 'provider_coral_divers',
        ownerId: 'user_coral_divers',
        name: 'Coral Voyager',
        engineType: 'Outboard',
        capacity: 14,
        length: 10.5,
        yearBuilt: 2021,
        description: 'Modern dive boat with eco-friendly features and underwater photography support.',
        galleryImages: [
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Eco Engine', 'Photo Equipment', 'Marine Biology Guide', 'Underwater Lights'],
          safetyEquipment: ['Life Jackets', 'Emergency Oxygen', 'First Aid Kit', 'Emergency Radio']
        },
        isActive: true,
      },
    }),
    prisma.boat.create({
      data: {
        id: 'boat_deep_blue',
        providerId: 'provider_coral_divers',
        ownerId: 'user_coral_divers',
        name: 'Deep Blue',
        engineType: 'Diesel',
        capacity: 20,
        length: 12.5,
        yearBuilt: 2018,
        description: 'Professional dive vessel for advanced diving and technical diving courses.',
        galleryImages: [
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Technical Dive Support', 'Trimix System', 'Decompression Chamber Access', 'Advanced Navigation'],
          safetyEquipment: ['Life Jackets', 'Emergency Oxygen', 'First Aid Kit', 'Emergency Radio']
        },
        isActive: true,
      },
    }),

    // Marine Explorer boats
    prisma.boat.create({
      data: {
        id: 'boat_marine_discovery',
        providerId: 'provider_marine_explorer',
        ownerId: 'user_marine_explorer',
        name: 'Marine Discovery',
        engineType: 'Diesel',
        capacity: 22,
        length: 13.0,
        yearBuilt: 2020,
        description: 'Research-grade vessel for marine wildlife observation and eco-tours.',
        galleryImages: [
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Observation Deck', 'Marine Biology Lab', 'Hydrophone System', 'Research Equipment'],
          safetyEquipment: ['Life Jackets', 'Emergency Radio', 'First Aid Kit', 'GPS Tracking']
        },
        isActive: true,
      },
    }),
    prisma.boat.create({
      data: {
        id: 'boat_eco_voyager',
        providerId: 'provider_marine_explorer',
        ownerId: 'user_marine_explorer',
        name: 'Eco Voyager',
        engineType: 'Electric Hybrid',
        capacity: 16,
        length: 10.0,
        yearBuilt: 2022,
        description: 'Eco-friendly hybrid boat for sustainable marine tours and wildlife watching.',
        galleryImages: [
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Silent Operation', 'Solar Panels', 'Binoculars', 'Educational Materials'],
          safetyEquipment: ['Life Jackets', 'Emergency Radio', 'First Aid Kit', 'Emergency Beacon']
        },
        isActive: true,
      },
    }),
    prisma.boat.create({
      data: {
        id: 'boat_ocean_researcher',
        providerId: 'provider_marine_explorer',
        ownerId: 'user_marine_explorer',
        name: 'Ocean Researcher',
        engineType: 'Diesel',
        capacity: 18,
        length: 11.5,
        yearBuilt: 2019,
        description: 'Scientific research vessel equipped for marine conservation studies.',
        galleryImages: [
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Research Lab', 'Sample Collection', 'Underwater Camera', 'Data Recording'],
          safetyEquipment: ['Life Jackets', 'Emergency Radio', 'First Aid Kit', 'Satellite Phone']
        },
        isActive: true,
      },
    }),
    prisma.boat.create({
      data: {
        id: 'boat_wildlife_seeker',
        providerId: 'provider_marine_explorer',
        ownerId: 'user_marine_explorer',
        name: 'Wildlife Seeker',
        engineType: 'Outboard',
        capacity: 12,
        length: 8.5,
        yearBuilt: 2021,
        description: 'Agile boat designed for close wildlife encounters and photography tours.',
        galleryImages: [
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Quiet Engine', 'Photography Platform', 'Spotting Scopes', 'Field Guides'],
          safetyEquipment: ['Life Jackets', 'Emergency Radio', 'First Aid Kit', 'Emergency Flares']
        },
        isActive: true,
      },
    }),
    prisma.boat.create({
      data: {
        id: 'boat_conservation_cruiser',
        providerId: 'provider_marine_explorer',
        ownerId: 'user_marine_explorer',
        name: 'Conservation Cruiser',
        engineType: 'Diesel',
        capacity: 24,
        length: 14.0,
        yearBuilt: 2020,
        description: 'Educational vessel for marine conservation awareness tours and school programs.',
        galleryImages: [
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Education Center', 'Interactive Displays', 'Marine Specimens', 'Presentation Area'],
          safetyEquipment: ['Life Jackets', 'Emergency Radio', 'First Aid Kit', 'Emergency Equipment']
        },
        isActive: true,
      },
    }),

    // Tropical Cruises boats
    prisma.boat.create({
      data: {
        id: 'boat_sunset_princess',
        providerId: 'provider_tropical_cruises',
        ownerId: 'user_tropical_cruises',
        name: 'Sunset Princess',
        engineType: 'Diesel',
        capacity: 40,
        length: 18.0,
        yearBuilt: 2021,
        description: 'Luxury cruise yacht with premium amenities for sunset and dinner cruises.',
        galleryImages: [
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Luxury Seating', 'Full Bar', 'Gourmet Kitchen', 'Entertainment System'],
          safetyEquipment: ['Life Jackets', 'Emergency Radio', 'First Aid Kit', 'Life Rafts']
        },
        isActive: true,
      },
    }),
    prisma.boat.create({
      data: {
        id: 'boat_tropical_star',
        providerId: 'provider_tropical_cruises',
        ownerId: 'user_tropical_cruises',
        name: 'Tropical Star',
        engineType: 'Diesel',
        capacity: 35,
        length: 16.5,
        yearBuilt: 2020,
        description: 'Elegant cruise vessel with panoramic views and fine dining facilities.',
        galleryImages: [
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Panoramic Windows', 'Fine Dining', 'Wine Cellar', 'Live Music Area'],
          safetyEquipment: ['Life Jackets', 'Emergency Radio', 'First Aid Kit', 'Safety Equipment']
        },
        isActive: true,
      },
    }),
    prisma.boat.create({
      data: {
        id: 'boat_paradise_cruiser',
        providerId: 'provider_tropical_cruises',
        ownerId: 'user_tropical_cruises',
        name: 'Paradise Cruiser',
        engineType: 'Diesel',
        capacity: 30,
        length: 15.0,
        yearBuilt: 2019,
        description: 'Mid-size luxury vessel perfect for private charters and special events.',
        galleryImages: [
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Private Deck', 'Catering Kitchen', 'Sound System', 'Mood Lighting'],
          safetyEquipment: ['Life Jackets', 'Emergency Radio', 'First Aid Kit', 'Emergency Beacon']
        },
        isActive: true,
      },
    }),
    prisma.boat.create({
      data: {
        id: 'boat_ocean_elegance',
        providerId: 'provider_tropical_cruises',
        ownerId: 'user_tropical_cruises',
        name: 'Ocean Elegance',
        engineType: 'Diesel',
        capacity: 50,
        length: 20.0,
        yearBuilt: 2018,
        description: 'Large luxury yacht for corporate events and wedding celebrations.',
        galleryImages: [
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Multiple Decks', 'Event Space', 'Professional Kitchen', 'Premium Bar'],
          safetyEquipment: ['Life Jackets', 'Emergency Radio', 'First Aid Kit', 'Life Rafts']
        },
        isActive: true,
      },
    }),
    prisma.boat.create({
      data: {
        id: 'boat_royal_wave',
        providerId: 'provider_tropical_cruises',
        ownerId: 'user_tropical_cruises',
        name: 'Royal Wave',
        engineType: 'Diesel',
        capacity: 25,
        length: 13.5,
        yearBuilt: 2022,
        description: 'Newest addition to the fleet with state-of-the-art amenities and eco-friendly features.',
        galleryImages: [
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center'
        ],
        amenities: {
          features: ['Eco Engine', 'Smart Controls', 'Premium Comfort', 'Advanced Navigation'],
          safetyEquipment: ['Life Jackets', 'Emergency Radio', 'First Aid Kit', 'Modern Safety Systems']
        },
        isActive: true,
      },
    }),
  ]);

  // Seed Provider Services
  console.log('🎯 Seeding provider services...');
  const providerServices = await Promise.all([
    // Ocean Adventures services
    prisma.providerService.create({
      data: {
        id: 'ps_ocean_snorkeling_half',
        providerId: 'provider_ocean_adventures',
        serviceTypeId: 'st_snorkeling_half_day',
        name: 'Half Day Snorkeling Adventure',
        description: 'Explore vibrant coral reefs and marine life in crystal clear waters. Includes snorkeling equipment and refreshments.',
        basePrice: 85.00,
        maxCapacity: 12,
        duration: 240,
        includedItems: ['Snorkeling equipment', 'Life jacket', 'Refreshments', 'Professional guide'],
        excludedItems: ['Underwater camera', 'Towels', 'Transportation to jetty', 'Lunch'],
        specialInstruction: 'Please bring your own towel and arrive 30 minutes before departure. Swimming ability required. Not suitable for pregnant women or people with heart conditions.',
        images: [
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center'
        ],
        agePricing: {
          adult: 85.00,
          child: 59.50,
          toddler: 0.00
        },
        itinerary: [
          { time: '09:00', activity: 'Meet at jetty and safety briefing', location: 'Departure Jetty' },
          { time: '09:30', activity: 'Boat departure to snorkeling site', location: 'En route' },
          { time: '10:00', activity: 'First snorkeling session', location: 'Coral Garden' },
          { time: '11:30', activity: 'Refreshment break on boat', location: 'Boat' },
          { time: '12:00', activity: 'Second snorkeling session', location: 'Marine Park' },
          { time: '13:00', activity: 'Return journey to jetty', location: 'En route' }
        ],
        isActive: true,
      },
    }),
    // SCENARIO 1: Normal Pricing (Single passenger field, no packages, no age breakdown)
    prisma.providerService.create({
      data: {
        id: 'ps_ocean_ferry_normal',
        providerId: 'provider_ocean_adventures',
        serviceTypeId: 'st_passenger_ferry',
        name: 'Standard Island Ferry',
        description: 'Simple and affordable ferry service to island destinations. Single pricing for all passengers.',
        basePrice: 45.00,
        maxCapacity: 50,
        duration: 60,
        includedItems: ['Round trip transport', 'Life jacket', 'Luggage allowance'],
        excludedItems: ['Meals', 'Snorkeling equipment', 'Hotel pickup'],
        specialInstruction: 'Please arrive 15 minutes before departure. Bring valid ID for island entry.',
        images: [
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center'
        ],
        // No agePricing - uses basePrice for all passengers
        isActive: true,
      },
    }),
    // Island Express services
    prisma.providerService.create({
      data: {
        id: 'ps_express_ferry',
        providerId: 'provider_island_express',
        serviceTypeId: 'st_passenger_ferry',
        name: 'Island Ferry Service',
        description: 'Comfortable and reliable ferry service to popular island destinations with scheduled departures.',
        basePrice: 45.00,
        maxCapacity: 50,
        duration: 60,
        includedItems: ['Round trip transport', 'Life jacket', 'Luggage allowance'],
        images: [
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center'
        ],
        isActive: true,
      },
    }),
    // SCENARIO 2: Age-based Pricing (Age breakdown, no packages)
    prisma.providerService.create({
      data: {
        id: 'ps_coral_snorkeling_half',
        providerId: 'provider_coral_divers',
        serviceTypeId: 'st_snorkeling_half_day',
        name: 'Coral Reef Snorkeling',
        description: 'Guided snorkeling tour to pristine coral reefs with marine biology insights from certified guides. Different pricing for adults and children.',
        basePrice: 95.00,
        maxCapacity: 16,
        duration: 240,
        includedItems: ['Professional snorkeling equipment', 'Wetsuit', 'Marine guide', 'Underwater photos'],
        excludedItems: ['Personal fins', 'Prescription mask', 'Lunch', 'Hotel pickup'],
        specialInstruction: 'Minimum age 8 years. Basic swimming skills required. Please inform us of any medical conditions. Bring reef-safe sunscreen only.',
        agePricing: {
          adult: 95.00,
          child: 65.00,
          toddler: 0.00
        },
        images: [
          'https://images.unsplash.com/photo-1583212292454-1fe6229603b7?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center'
        ],
        // No packages - pure age-based pricing
        isActive: true,
      },
    }),

    // SCENARIO 3: Package-based Pricing (Packages available, single passenger field)
    prisma.providerService.create({
      data: {
        id: 'ps_tropical_island_tour',
        providerId: 'provider_tropical_cruises',
        serviceTypeId: 'st_snorkeling_half_day',
        name: 'Tropical Island Tour',
        description: 'Island hopping tour with multiple package options. Choose from Basic, Premium, or Deluxe packages.',
        basePrice: 120.00,
        maxCapacity: 20,
        duration: 360,
        includedItems: ['Boat transport', 'Life jacket', 'Basic refreshments'],
        excludedItems: ['Meals', 'Snorkeling equipment', 'Photography'],
        specialInstruction: 'Package selection required during booking. Different packages include different amenities.',
        images: [
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center'
        ],
        // No agePricing - uses basePrice for all passengers, but packages available
        isActive: true,
      },
    }),

    // Marine Explorer services
    prisma.providerService.create({
      data: {
        id: 'ps_marine_wildlife_tour',
        providerId: 'provider_marine_explorer',
        serviceTypeId: 'st_snorkeling_half_day',
        name: 'Marine Wildlife Discovery Tour',
        description: 'Educational eco-tour focusing on marine wildlife observation and conservation awareness.',
        basePrice: 120.00,
        maxCapacity: 22,
        duration: 300,
        includedItems: ['Marine biologist guide', 'Binoculars', 'Educational materials', 'Refreshments'],
        excludedItems: ['Snorkeling equipment', 'Meals', 'Transportation', 'Souvenirs'],
        specialInstruction: 'Educational tour suitable for all ages. Please wear comfortable clothing and bring a hat. Camera recommended for wildlife photography.',
        images: [
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1583212292454-1fe6229603b7?w=800&h=600&fit=crop&crop=center'
        ],
        isActive: true,
      },
    }),
    prisma.providerService.create({
      data: {
        id: 'ps_marine_research_expedition',
        providerId: 'provider_marine_explorer',
        serviceTypeId: 'st_snorkeling_full_day',
        name: 'Marine Research Expedition',
        description: 'Full-day research expedition with hands-on marine conservation activities and data collection.',
        basePrice: 180.00,
        maxCapacity: 18,
        duration: 480,
        includedItems: ['Research equipment', 'Scientific guide', 'Lunch', 'Certificate of participation'],
        images: [
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1583212292454-1fe6229603b7?w=800&h=600&fit=crop&crop=center'
        ],
        isActive: true,
      },
    }),

    // SCENARIO 4: Age & Package-based Pricing (Both packages and age breakdown)
    prisma.providerService.create({
      data: {
        id: 'ps_marine_premium_expedition',
        providerId: 'provider_marine_explorer',
        serviceTypeId: 'st_snorkeling_full_day',
        name: 'Premium Marine Expedition',
        description: 'Full-day premium marine experience with multiple package options and age-based pricing. Perfect for families and groups.',
        basePrice: 150.00,
        maxCapacity: 18,
        duration: 480,
        includedItems: ['Professional equipment', 'Marine biologist guide', 'Basic lunch'],
        excludedItems: ['Premium photography', 'Private guide', 'Hotel transfers'],
        specialInstruction: 'Package and age selection required. Children under 12 receive discounted rates. Premium packages include additional amenities.',
        agePricing: {
          adult: 150.00,
          child: 105.00,
          toddler: 0.00
        },
        images: [
          'https://images.unsplash.com/photo-1583212292454-1fe6229603b7?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center'
        ],
        // Both agePricing and packages will be available
        isActive: true,
      },
    }),

    prisma.providerService.create({
      data: {
        id: 'ps_marine_eco_snorkeling',
        providerId: 'provider_marine_explorer',
        serviceTypeId: 'st_snorkeling_half_day',
        name: 'Eco-Friendly Snorkeling Adventure',
        description: 'Sustainable snorkeling experience with minimal environmental impact and marine education.',
        basePrice: 100.00,
        maxCapacity: 16,
        duration: 240,
        includedItems: ['Eco-friendly equipment', 'Marine guide', 'Conservation briefing', 'Snacks'],
        images: [
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1583212292454-1fe6229603b7?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center'
        ],
        isActive: true,
      },
    }),

    // Tropical Cruises services
    prisma.providerService.create({
      data: {
        id: 'ps_tropical_sunset_cruise',
        providerId: 'provider_tropical_cruises',
        serviceTypeId: 'st_snorkeling_half_day',
        name: 'Luxury Sunset Cruise',
        description: 'Elegant sunset cruise with gourmet dining, premium beverages, and live entertainment.',
        basePrice: 250.00,
        maxCapacity: 40,
        duration: 180,
        includedItems: ['Gourmet dinner', 'Premium drinks', 'Live music', 'Professional photography'],
        images: [
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center'
        ],
        isActive: true,
      },
    }),
    prisma.providerService.create({
      data: {
        id: 'ps_tropical_dinner_cruise',
        providerId: 'provider_tropical_cruises',
        serviceTypeId: 'st_snorkeling_full_day',
        name: 'Premium Dinner Cruise',
        description: 'Exclusive dinner cruise experience with fine dining and panoramic ocean views.',
        basePrice: 320.00,
        maxCapacity: 35,
        duration: 240,
        includedItems: ['Multi-course dinner', 'Wine pairing', 'Entertainment', 'Welcome cocktail'],
        images: [
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center'
        ],
        isActive: true,
      },
    }),
    prisma.providerService.create({
      data: {
        id: 'ps_tropical_private_charter',
        providerId: 'provider_tropical_cruises',
        serviceTypeId: 'st_snorkeling_half_day',
        name: 'Private Luxury Charter',
        description: 'Exclusive private charter for special occasions with personalized service and custom itinerary.',
        basePrice: 800.00,
        maxCapacity: 30,
        duration: 360,
        includedItems: ['Private crew', 'Custom catering', 'Decoration service', 'Photography package'],
        images: [
          'https://images.unsplash.com/photo-1583212292454-1fe6229603b7?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-46a013bb70d5?w=800&h=600&fit=crop&crop=center',
          'https://images.unsplash.com/photo-**********-dc66d52bef19?w=800&h=600&fit=crop&crop=center'
        ],
        isActive: true,
      },
    }),
  ]);

  // Seed Service Packages (for package-based services)
  console.log('📦 Seeding service packages...');
  const servicePackages = await Promise.all([
    // Packages for Tropical Island Tour (Scenario 3: Package-based)
    prisma.servicePackage.create({
      data: {
        id: 'sp_tropical_basic',
        serviceId: 'ps_tropical_island_tour',
        packageTypeId: 'pkg_basic',
        basePrice: 120.00,
        includedItems: ['Boat transport', 'Life jacket', 'Basic refreshments', 'Island visit'],
        excludedItems: ['Snorkeling equipment', 'Lunch', 'Photography'],
      },
    }),
    prisma.servicePackage.create({
      data: {
        id: 'sp_tropical_premium',
        serviceId: 'ps_tropical_island_tour',
        packageTypeId: 'pkg_premium',
        basePrice: 180.00,
        includedItems: ['Boat transport', 'Life jacket', 'Refreshments', 'Island visit', 'Snorkeling equipment', 'Light lunch'],
        excludedItems: ['Professional photography', 'Private guide'],
      },
    }),
    prisma.servicePackage.create({
      data: {
        id: 'sp_tropical_deluxe',
        serviceId: 'ps_tropical_island_tour',
        packageTypeId: 'pkg_deluxe',
        basePrice: 250.00,
        includedItems: ['Boat transport', 'Life jacket', 'Premium refreshments', 'Island visit', 'Snorkeling equipment', 'Gourmet lunch', 'Professional photography'],
        excludedItems: [],
      },
    }),

    // Packages for Premium Marine Expedition (Scenario 4: Age & Package-based)
    prisma.servicePackage.create({
      data: {
        id: 'sp_marine_basic',
        serviceId: 'ps_marine_premium_expedition',
        packageTypeId: 'pkg_basic',
        basePrice: 150.00,
        agePricing: {
          adult: 150.00,
          child: 105.00,
          toddler: 0.00,
          senior: 120.00,
          pwd: 100.00
        },
        includedItems: ['Professional equipment', 'Marine biologist guide', 'Basic lunch', 'Research materials'],
        excludedItems: ['Premium photography', 'Private guide', 'Hotel transfers'],
      },
    }),
    prisma.servicePackage.create({
      data: {
        id: 'sp_marine_premium',
        serviceId: 'ps_marine_premium_expedition',
        packageTypeId: 'pkg_premium',
        basePrice: 220.00,
        agePricing: {
          adult: 220.00,
          child: 154.00,
          toddler: 0.00,
          senior: 176.00,
          pwd: 154.00
        },
        includedItems: ['Professional equipment', 'Marine biologist guide', 'Premium lunch', 'Research materials', 'Underwater photography', 'Certificate'],
        excludedItems: ['Private guide', 'Hotel transfers'],
      },
    }),
    prisma.servicePackage.create({
      data: {
        id: 'sp_marine_deluxe',
        serviceId: 'ps_marine_premium_expedition',
        packageTypeId: 'pkg_deluxe',
        basePrice: 300.00,
        agePricing: {
          adult: 300.00,
          child: 210.00,
          toddler: 0.00,
          senior: 240.00,
          pwd: 210.00
        },
        includedItems: ['Professional equipment', 'Private marine biologist guide', 'Gourmet lunch', 'Research materials', 'Professional photography', 'Certificate', 'Hotel transfers'],
        excludedItems: [],
      },
    }),
  ]);

  // Seed Service Routes (for ferry services)
  console.log('🛤️ Seeding service routes...');
  const serviceRoutes = await Promise.all([
    // Island Express ferry routes
    prisma.serviceRoute.create({
      data: {
        id: 'sr_express_mr_perhentian',
        serviceId: 'ps_express_ferry',
        routeId: 'route_mr_perhentian',
        isActive: true,
      },
    }),
    prisma.serviceRoute.create({
      data: {
        id: 'sr_express_mr_redang',
        serviceId: 'ps_express_ferry',
        routeId: 'route_mr_redang',
        isActive: true,
      },
    }),
  ]);

  // Seed Service Assignments (boats to services)
  console.log('⚓ Seeding service assignments...');
  const serviceAssignments = await Promise.all([
    // Ocean Adventures assignments
    prisma.serviceAssignment.create({
      data: {
        id: 'sa_sea_explorer_snorkeling_half',
        serviceId: 'ps_ocean_snorkeling_half',
        boatId: 'boat_sea_explorer',
        isPrimary: true,
        isActive: true,
      },
    }),
    prisma.serviceAssignment.create({
      data: {
        id: 'sa_wave_rider_ferry_normal',
        serviceId: 'ps_ocean_ferry_normal',
        boatId: 'boat_wave_rider',
        isPrimary: true,
        isActive: true,
      },
    }),
    // Island Express assignments
    prisma.serviceAssignment.create({
      data: {
        id: 'sa_island_hopper_ferry',
        serviceId: 'ps_express_ferry',
        boatId: 'boat_island_hopper',
        isPrimary: true,
        isActive: true,
      },
    }),
    // Coral Divers assignments
    prisma.serviceAssignment.create({
      data: {
        id: 'sa_reef_master_snorkeling',
        serviceId: 'ps_coral_snorkeling_half',
        boatId: 'boat_reef_master',
        isPrimary: true,
        isActive: true,
      },
    }),

    // Assignments for new services
    // Tropical Island Tour (Scenario 3: Package-based)
    prisma.serviceAssignment.create({
      data: {
        id: 'sa_paradise_cruiser_island_tour',
        serviceId: 'ps_tropical_island_tour',
        boatId: 'boat_paradise_cruiser',
        isPrimary: true,
        isActive: true,
      },
    }),

    // Premium Marine Expedition (Scenario 4: Age & Package-based)
    prisma.serviceAssignment.create({
      data: {
        id: 'sa_marine_discovery_premium_expedition',
        serviceId: 'ps_marine_premium_expedition',
        boatId: 'boat_marine_discovery',
        isPrimary: true,
        isActive: true,
      },
    }),
    prisma.serviceAssignment.create({
      data: {
        id: 'sa_ocean_researcher_premium_expedition',
        serviceId: 'ps_marine_premium_expedition',
        boatId: 'boat_ocean_researcher',
        isPrimary: false,
        isActive: true,
      },
    }),
  ]);

  // Seed Operating Areas (Provider-Jetty associations)
  console.log('🗺️ Seeding provider operating areas...');
  const operatingAreas = await Promise.all([
    // Ocean Adventures - operates from Kampung Mangkuk
    prisma.providerOperatingArea.create({
      data: {
        id: 'oa_ocean_adventures_kampung_mangkuk',
        providerId: 'provider_ocean_adventures',
        jettyId: 'jeti_kampung_mangkuk',
        isActive: true,
      },
    }),
    // Island Express - operates from Merang (main) and Kampung Mangkuk
    prisma.providerOperatingArea.create({
      data: {
        id: 'oa_island_express_merang',
        providerId: 'provider_island_express',
        jettyId: 'jeti_merang',
        isActive: true,
      },
    }),
    prisma.providerOperatingArea.create({
      data: {
        id: 'oa_island_express_kampung_mangkuk',
        providerId: 'provider_island_express',
        jettyId: 'jeti_kampung_mangkuk',
        isActive: true,
      },
    }),
    // Coral Divers - operates from Kampung Mangkuk and Penarik
    prisma.providerOperatingArea.create({
      data: {
        id: 'oa_coral_divers_kampung_mangkuk',
        providerId: 'provider_coral_divers',
        jettyId: 'jeti_kampung_mangkuk',
        isActive: true,
      },
    }),
    prisma.providerOperatingArea.create({
      data: {
        id: 'oa_coral_divers_penarik',
        providerId: 'provider_coral_divers',
        jettyId: 'jeti_penarik',
        isActive: true,
      },
    }),
    // Marine Explorer - operates from all three jetties (research vessel)
    prisma.providerOperatingArea.create({
      data: {
        id: 'oa_marine_explorer_kampung_mangkuk',
        providerId: 'provider_marine_explorer',
        jettyId: 'jeti_kampung_mangkuk',
        isActive: true,
      },
    }),
    prisma.providerOperatingArea.create({
      data: {
        id: 'oa_marine_explorer_merang',
        providerId: 'provider_marine_explorer',
        jettyId: 'jeti_merang',
        isActive: true,
      },
    }),
    prisma.providerOperatingArea.create({
      data: {
        id: 'oa_marine_explorer_penarik',
        providerId: 'provider_marine_explorer',
        jettyId: 'jeti_penarik',
        isActive: true,
      },
    }),
    // Tropical Cruises - operates from Merang and Penarik (luxury locations)
    prisma.providerOperatingArea.create({
      data: {
        id: 'oa_tropical_cruises_merang',
        providerId: 'provider_tropical_cruises',
        jettyId: 'jeti_merang',
        isActive: true,
      },
    }),
    prisma.providerOperatingArea.create({
      data: {
        id: 'oa_tropical_cruises_penarik',
        providerId: 'provider_tropical_cruises',
        jettyId: 'jeti_penarik',
        isActive: true,
      },
    }),
  ]);

  // Seed Service Age Pricing (for services with age-based pricing)
  console.log('💰 Seeding service age pricing...');
  const serviceAgePricing = await Promise.all([
    // Coral Reef Snorkeling - Age-based pricing including senior and PWD
    prisma.serviceAgePricing.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        ageCategoryId: 'age_adult',
        price: 85.00,
        priceType: 'FIXED',
      },
    }),
    prisma.serviceAgePricing.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        ageCategoryId: 'age_child',
        price: 60.00,
        priceType: 'FIXED',
      },
    }),
    prisma.serviceAgePricing.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        ageCategoryId: 'age_toddler',
        price: 0.00,
        priceType: 'FIXED',
      },
    }),
    prisma.serviceAgePricing.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        ageCategoryId: 'age_senior',
        price: 70.00, // 15% discount for seniors
        priceType: 'FIXED',
      },
    }),
    prisma.serviceAgePricing.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        ageCategoryId: 'age_pwd',
        price: 65.00, // 25% discount for PWD
        priceType: 'FIXED',
      },
    }),

    // Premium Marine Expedition - Age-based pricing including senior and PWD
    prisma.serviceAgePricing.create({
      data: {
        serviceId: 'ps_marine_premium_expedition',
        ageCategoryId: 'age_adult',
        price: 150.00,
        priceType: 'FIXED',
      },
    }),
    prisma.serviceAgePricing.create({
      data: {
        serviceId: 'ps_marine_premium_expedition',
        ageCategoryId: 'age_child',
        price: 105.00,
        priceType: 'FIXED',
      },
    }),
    prisma.serviceAgePricing.create({
      data: {
        serviceId: 'ps_marine_premium_expedition',
        ageCategoryId: 'age_toddler',
        price: 0.00,
        priceType: 'FIXED',
      },
    }),
    prisma.serviceAgePricing.create({
      data: {
        serviceId: 'ps_marine_premium_expedition',
        ageCategoryId: 'age_senior',
        price: 120.00, // 20% discount for seniors
        priceType: 'FIXED',
      },
    }),
    prisma.serviceAgePricing.create({
      data: {
        serviceId: 'ps_marine_premium_expedition',
        ageCategoryId: 'age_pwd',
        price: 100.00, // 33% discount for PWD
        priceType: 'FIXED',
      },
    }),
  ]);

  // Seed Service Schedules (recurring weekly schedules)
  console.log('⏰ Seeding service schedules...');
  const serviceSchedules = await Promise.all([
    // Coral Reef Snorkeling - Daily morning and afternoon
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        dayOfWeek: 1, // Monday
        departureTime: '09:00',
        availableCapacity: 16,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        dayOfWeek: 1, // Monday
        departureTime: '14:00',
        availableCapacity: 16,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        dayOfWeek: 2, // Tuesday
        departureTime: '09:00',
        availableCapacity: 16,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        dayOfWeek: 2, // Tuesday
        departureTime: '14:00',
        availableCapacity: 16,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        dayOfWeek: 3, // Wednesday
        departureTime: '09:00',
        availableCapacity: 16,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        dayOfWeek: 3, // Wednesday
        departureTime: '14:00',
        availableCapacity: 16,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        dayOfWeek: 4, // Thursday
        departureTime: '09:00',
        availableCapacity: 16,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        dayOfWeek: 4, // Thursday
        departureTime: '14:00',
        availableCapacity: 16,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        dayOfWeek: 5, // Friday
        departureTime: '09:00',
        availableCapacity: 16,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        dayOfWeek: 5, // Friday
        departureTime: '14:00',
        availableCapacity: 16,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        dayOfWeek: 6, // Saturday
        departureTime: '08:00',
        availableCapacity: 16,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        dayOfWeek: 6, // Saturday
        departureTime: '10:00',
        availableCapacity: 16,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        dayOfWeek: 6, // Saturday
        departureTime: '15:00',
        availableCapacity: 16,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        dayOfWeek: 0, // Sunday
        departureTime: '08:00',
        availableCapacity: 16,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        dayOfWeek: 0, // Sunday
        departureTime: '10:00',
        availableCapacity: 16,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_coral_snorkeling_half',
        dayOfWeek: 0, // Sunday
        departureTime: '15:00',
        availableCapacity: 16,
      },
    }),

    // Premium Marine Expedition - Less frequent, full day
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_marine_premium_expedition',
        dayOfWeek: 2, // Tuesday
        departureTime: '08:00',
        availableCapacity: 40,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_marine_premium_expedition',
        dayOfWeek: 4, // Thursday
        departureTime: '08:00',
        availableCapacity: 40,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_marine_premium_expedition',
        dayOfWeek: 6, // Saturday
        departureTime: '07:30',
        availableCapacity: 40,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_marine_premium_expedition',
        dayOfWeek: 0, // Sunday
        departureTime: '07:30',
        availableCapacity: 40,
      },
    }),

    // Island Ferry Service - Multiple daily departures
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_express_ferry',
        dayOfWeek: 1, // Monday
        departureTime: '08:00',
        availableCapacity: 50,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_express_ferry',
        dayOfWeek: 1, // Monday
        departureTime: '10:00',
        availableCapacity: 50,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_express_ferry',
        dayOfWeek: 1, // Monday
        departureTime: '14:00',
        availableCapacity: 50,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_express_ferry',
        dayOfWeek: 1, // Monday
        departureTime: '16:00',
        availableCapacity: 50,
      },
    }),
    // Repeat for other days...
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_express_ferry',
        dayOfWeek: 6, // Saturday
        departureTime: '07:00',
        availableCapacity: 50,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_express_ferry',
        dayOfWeek: 6, // Saturday
        departureTime: '09:00',
        availableCapacity: 50,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_express_ferry',
        dayOfWeek: 6, // Saturday
        departureTime: '11:00',
        availableCapacity: 50,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_express_ferry',
        dayOfWeek: 6, // Saturday
        departureTime: '15:00',
        availableCapacity: 50,
      },
    }),
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_express_ferry',
        dayOfWeek: 6, // Saturday
        departureTime: '17:00',
        availableCapacity: 50,
      },
    }),

    // Example of "every day" schedule (NULL dayOfWeek means runs every day)
    prisma.serviceSchedule.create({
      data: {
        serviceId: 'ps_marine_premium_expedition',
        dayOfWeek: null, // NULL means every day
        departureTime: '12:00',
        availableCapacity: 40,
      },
    }),
  ]);

  console.log('✅ Database seeding completed successfully!');
  console.log(`👤 Created ${providerUsers.length} provider users`);
  console.log(`📍 Created ${jetties.length} jetties`);
  console.log(`🏝️ Created ${destinations.length} destinations`);
  console.log(`🛤️ Created ${routes.length} routes`);
  console.log(`📋 Created ${serviceCategories.length} service categories`);
  console.log(`🎯 Created ${serviceTypes.length} service types`);
  console.log(`👥 Created ${ageCategories.length} age categories`);
  console.log(`📦 Created ${packageTypes.length} package types`);
  console.log(`🏢 Created ${providers.length} providers`);
  console.log(`⛵ Created ${boats.length} boats`);
  console.log(`🎯 Created ${providerServices.length} provider services`);
  console.log(`📦 Created ${servicePackages.length} service packages`);
  console.log(`🛤️ Created ${serviceRoutes.length} service routes`);
  console.log(`⚓ Created ${serviceAssignments.length} service assignments`);
  console.log(`🗺️ Created ${operatingAreas.length} operating areas`);
  console.log(`💰 Created ${serviceAgePricing.length} service age pricing entries`);
  console.log(`⏰ Created ${serviceSchedules.length} service schedules`);
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
