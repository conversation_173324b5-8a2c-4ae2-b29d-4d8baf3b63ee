-- Migration: Add dynamic pricing packages system
-- This migration creates tables for configurable package types and pricing structures

-- CreateTable: Package Types
CREATE TABLE "package_types" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "description" TEXT,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "package_types_pkey" PRIMARY KEY ("id")
);

-- CreateTable: Service Packages (linking services to package types with pricing)
CREATE TABLE "service_packages" (
    "id" TEXT NOT NULL,
    "serviceId" TEXT NOT NULL,
    "packageTypeId" TEXT NOT NULL,
    "basePrice" DECIMAL(65,30) NOT NULL,
    "agePricing" JSONB,
    "priceModifier" DECIMAL(65,30) NOT NULL DEFAULT 1.0,
    "includedItems" TEXT[],
    "excludedItems" TEXT[],
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "service_packages_pkey" PRIMARY KEY ("id")
);

-- CreateTable: Age Categories (configurable age-based pricing categories)
CREATE TABLE "age_categories" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "description" TEXT,
    "minAge" INTEGER,
    "maxAge" INTEGER,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "age_categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable: Service Age Pricing (configurable age-based pricing per service)
CREATE TABLE "service_age_pricing" (
    "id" TEXT NOT NULL,
    "serviceId" TEXT NOT NULL,
    "ageCategoryId" TEXT NOT NULL,
    "price" DECIMAL(65,30) NOT NULL,
    "priceType" TEXT NOT NULL DEFAULT 'FIXED', -- FIXED, PERCENTAGE, FREE
    "percentageOfBase" DECIMAL(65,30),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "service_age_pricing_pkey" PRIMARY KEY ("id")
);

-- Create unique constraints
CREATE UNIQUE INDEX "package_types_code_key" ON "package_types"("code");
CREATE UNIQUE INDEX "service_packages_serviceId_packageTypeId_key" ON "service_packages"("serviceId", "packageTypeId");
CREATE UNIQUE INDEX "age_categories_code_key" ON "age_categories"("code");
CREATE UNIQUE INDEX "service_age_pricing_serviceId_ageCategoryId_key" ON "service_age_pricing"("serviceId", "ageCategoryId");

-- Add foreign key constraints
ALTER TABLE "service_packages" ADD CONSTRAINT "service_packages_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "provider_services"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "service_packages" ADD CONSTRAINT "service_packages_packageTypeId_fkey" FOREIGN KEY ("packageTypeId") REFERENCES "package_types"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "service_age_pricing" ADD CONSTRAINT "service_age_pricing_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "provider_services"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "service_age_pricing" ADD CONSTRAINT "service_age_pricing_ageCategoryId_fkey" FOREIGN KEY ("ageCategoryId") REFERENCES "age_categories"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Insert default package types
INSERT INTO "package_types" ("id", "name", "code", "description", "isDefault", "sortOrder", "isActive", "createdAt", "updatedAt") VALUES
('pkg_standard', 'Standard Package', 'STANDARD', 'Essential experience with basic inclusions', true, 1, true, NOW(), NOW()),
('pkg_premium', 'Premium Package', 'PREMIUM', 'Enhanced experience with additional amenities and services', false, 2, true, NOW(), NOW()),
('pkg_luxury', 'Luxury Package', 'LUXURY', 'Ultimate experience with premium services and exclusive access', false, 3, true, NOW(), NOW());

-- Insert default age categories
INSERT INTO "age_categories" ("id", "name", "code", "description", "minAge", "maxAge", "sortOrder", "isActive", "createdAt", "updatedAt") VALUES
('age_adult', 'Adult', 'ADULT', 'Adult pricing (18+ years)', 18, NULL, 1, true, NOW(), NOW()),
('age_child', 'Child', 'CHILD', 'Child pricing (3-17 years)', 3, 17, 2, true, NOW(), NOW()),
('age_toddler', 'Toddler', 'TODDLER', 'Toddler pricing (0-2 years)', 0, 2, 3, true, NOW(), NOW()),
('age_senior', 'Senior', 'SENIOR', 'Senior citizen pricing (65+ years)', 65, NULL, 4, true, NOW(), NOW());
