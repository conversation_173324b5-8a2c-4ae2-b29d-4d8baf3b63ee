/*
  Warnings:

  - You are about to drop the column `location` on the `affiliate_links` table. All the data in the column will be lost.
  - You are about to drop the column `serviceType` on the `affiliate_links` table. All the data in the column will be lost.
  - The `serviceType` column on the `boats` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `location` column on the `boats` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `serviceType` column on the `bookings` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- AlterEnum
ALTER TYPE "BoatStatus" ADD VALUE 'MAINTENANCE';

-- AlterEnum
ALTER TYPE "BookingStatus" ADD VALUE 'NO_SHOW';

-- DropForeignKey
ALTER TABLE "bookings" DROP CONSTRAINT "bookings_boatId_fkey";

-- AlterTable
ALTER TABLE "affiliate_links" DROP COLUMN "location",
DROP COLUMN "serviceType",
ADD COLUMN     "jettyId" TEXT,
ADD COLUMN     "serviceTypeId" TEXT;

-- AlterTable
ALTER TABLE "boats" ADD COLUMN     "amenities" JSONB,
ADD COLUMN     "engineType" TEXT,
ADD COLUMN     "length" DECIMAL(65,30),
ADD COLUMN     "providerId" TEXT,
ADD COLUMN     "registrationNumber" TEXT,
ADD COLUMN     "safetyRating" TEXT,
ADD COLUMN     "yearBuilt" INTEGER,
DROP COLUMN "serviceType",
ADD COLUMN     "serviceType" TEXT,
DROP COLUMN "location",
ADD COLUMN     "location" TEXT,
ALTER COLUMN "basePrice" DROP NOT NULL;

-- AlterTable
ALTER TABLE "bookings" ADD COLUMN     "assignedBoatId" TEXT,
ADD COLUMN     "baseAmount" DECIMAL(65,30),
ADD COLUMN     "providerId" TEXT,
ADD COLUMN     "routeAmount" DECIMAL(65,30) NOT NULL DEFAULT 0,
ADD COLUMN     "routeId" TEXT,
ADD COLUMN     "serviceId" TEXT,
ADD COLUMN     "specialRequests" TEXT,
ALTER COLUMN "boatId" DROP NOT NULL,
DROP COLUMN "serviceType",
ADD COLUMN     "serviceType" TEXT;

-- DropEnum
DROP TYPE "Location";

-- DropEnum
DROP TYPE "ServiceType";

-- CreateTable
CREATE TABLE "providers" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "companyName" TEXT NOT NULL,
    "displayName" TEXT NOT NULL,
    "description" TEXT,
    "brn" TEXT,
    "operatingLicense" TEXT,
    "contactPhone" TEXT NOT NULL,
    "contactEmail" TEXT NOT NULL,
    "address" TEXT,
    "logoUrl" TEXT,
    "coverImageUrl" TEXT,
    "rating" DECIMAL(65,30) NOT NULL DEFAULT 0,
    "reviewCount" INTEGER NOT NULL DEFAULT 0,
    "isVerified" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isAutoGenerated" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "providers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "jetties" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "fullName" TEXT NOT NULL,
    "location" TEXT NOT NULL,
    "coordinates" JSONB,
    "facilities" JSONB,
    "operatingHours" JSONB,
    "parkingAvailable" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "jetties_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "destinations" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "fullName" TEXT NOT NULL,
    "description" TEXT,
    "coordinates" JSONB,
    "imageUrl" TEXT,
    "popularActivities" TEXT[],
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "destinations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "routes" (
    "id" TEXT NOT NULL,
    "departureJettyId" TEXT NOT NULL,
    "destinationId" TEXT NOT NULL,
    "distance" DECIMAL(65,30),
    "estimatedDuration" INTEGER,
    "difficulty" TEXT,
    "seasonalRestrictions" JSONB,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "routes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "service_categories" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "description" TEXT,
    "iconUrl" TEXT,
    "requiresDestination" BOOLEAN NOT NULL DEFAULT false,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "service_categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "service_types" (
    "id" TEXT NOT NULL,
    "categoryId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "description" TEXT,
    "defaultDuration" INTEGER,
    "requiresRoute" BOOLEAN NOT NULL DEFAULT false,
    "allowsMultipleDestinations" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "service_types_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "provider_services" (
    "id" TEXT NOT NULL,
    "providerId" TEXT NOT NULL,
    "serviceTypeId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "basePrice" DECIMAL(65,30) NOT NULL,
    "agePricing" JSONB,
    "duration" INTEGER,
    "maxCapacity" INTEGER NOT NULL,
    "includedItems" TEXT[],
    "itinerary" JSONB,
    "requirements" JSONB,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "provider_services_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "service_routes" (
    "id" TEXT NOT NULL,
    "serviceId" TEXT NOT NULL,
    "routeId" TEXT NOT NULL,
    "priceModifier" DECIMAL(65,30) NOT NULL DEFAULT 1.0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "service_routes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "service_assignments" (
    "id" TEXT NOT NULL,
    "serviceId" TEXT NOT NULL,
    "boatId" TEXT NOT NULL,
    "isPrimary" BOOLEAN NOT NULL DEFAULT false,
    "maxCapacityOverride" INTEGER,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "assignedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "service_assignments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "provider_operating_areas" (
    "id" TEXT NOT NULL,
    "providerId" TEXT NOT NULL,
    "jettyId" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "provider_operating_areas_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "service_schedules" (
    "id" TEXT NOT NULL,
    "serviceId" TEXT NOT NULL,
    "dayOfWeek" INTEGER NOT NULL,
    "departureTime" TEXT NOT NULL,
    "availableCapacity" INTEGER NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "service_schedules_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "service_availability" (
    "id" TEXT NOT NULL,
    "serviceId" TEXT NOT NULL,
    "date" DATE NOT NULL,
    "timeSlot" TEXT NOT NULL,
    "availableCapacity" INTEGER NOT NULL,
    "totalCapacity" INTEGER NOT NULL,
    "priceModifier" DECIMAL(65,30) NOT NULL DEFAULT 1.0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "service_availability_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "providers_userId_key" ON "providers"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "jetties_code_key" ON "jetties"("code");

-- CreateIndex
CREATE UNIQUE INDEX "destinations_code_key" ON "destinations"("code");

-- CreateIndex
CREATE UNIQUE INDEX "routes_departureJettyId_destinationId_key" ON "routes"("departureJettyId", "destinationId");

-- CreateIndex
CREATE UNIQUE INDEX "service_categories_code_key" ON "service_categories"("code");

-- CreateIndex
CREATE UNIQUE INDEX "service_types_code_key" ON "service_types"("code");

-- CreateIndex
CREATE UNIQUE INDEX "service_routes_serviceId_routeId_key" ON "service_routes"("serviceId", "routeId");

-- CreateIndex
CREATE UNIQUE INDEX "service_assignments_serviceId_boatId_key" ON "service_assignments"("serviceId", "boatId");

-- CreateIndex
CREATE UNIQUE INDEX "provider_operating_areas_providerId_jettyId_key" ON "provider_operating_areas"("providerId", "jettyId");

-- CreateIndex
CREATE UNIQUE INDEX "service_availability_serviceId_date_timeSlot_key" ON "service_availability"("serviceId", "date", "timeSlot");

-- AddForeignKey
ALTER TABLE "providers" ADD CONSTRAINT "providers_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "routes" ADD CONSTRAINT "routes_departureJettyId_fkey" FOREIGN KEY ("departureJettyId") REFERENCES "jetties"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "routes" ADD CONSTRAINT "routes_destinationId_fkey" FOREIGN KEY ("destinationId") REFERENCES "destinations"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "service_types" ADD CONSTRAINT "service_types_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "service_categories"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "provider_services" ADD CONSTRAINT "provider_services_providerId_fkey" FOREIGN KEY ("providerId") REFERENCES "providers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "provider_services" ADD CONSTRAINT "provider_services_serviceTypeId_fkey" FOREIGN KEY ("serviceTypeId") REFERENCES "service_types"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "service_routes" ADD CONSTRAINT "service_routes_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "provider_services"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "service_routes" ADD CONSTRAINT "service_routes_routeId_fkey" FOREIGN KEY ("routeId") REFERENCES "routes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "service_assignments" ADD CONSTRAINT "service_assignments_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "provider_services"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "service_assignments" ADD CONSTRAINT "service_assignments_boatId_fkey" FOREIGN KEY ("boatId") REFERENCES "boats"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "provider_operating_areas" ADD CONSTRAINT "provider_operating_areas_providerId_fkey" FOREIGN KEY ("providerId") REFERENCES "providers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "provider_operating_areas" ADD CONSTRAINT "provider_operating_areas_jettyId_fkey" FOREIGN KEY ("jettyId") REFERENCES "jetties"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "service_schedules" ADD CONSTRAINT "service_schedules_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "provider_services"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "service_availability" ADD CONSTRAINT "service_availability_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "provider_services"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "boats" ADD CONSTRAINT "boats_providerId_fkey" FOREIGN KEY ("providerId") REFERENCES "providers"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bookings" ADD CONSTRAINT "bookings_providerId_fkey" FOREIGN KEY ("providerId") REFERENCES "providers"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bookings" ADD CONSTRAINT "bookings_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "provider_services"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bookings" ADD CONSTRAINT "bookings_routeId_fkey" FOREIGN KEY ("routeId") REFERENCES "routes"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bookings" ADD CONSTRAINT "bookings_assignedBoatId_fkey" FOREIGN KEY ("assignedBoatId") REFERENCES "boats"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bookings" ADD CONSTRAINT "bookings_boatId_fkey" FOREIGN KEY ("boatId") REFERENCES "boats"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Data Migration: Insert initial jetties
INSERT INTO "jetties" ("id", "name", "code", "fullName", "location", "coordinates", "isActive", "createdAt", "updatedAt") VALUES
('jetty_kampung_mangkuk', 'Jetty Kampung Mangkuk', 'KM', 'Jetty Kampung Mangkuk, Setiu', 'Setiu, Terengganu', '{"lat": 5.6667, "lng": 102.7333}', true, NOW(), NOW()),
('jetty_merang', 'Jetty Merang', 'MR', 'Jetty Merang, Setiu', 'Setiu, Terengganu', '{"lat": 5.6833, "lng": 102.7500}', true, NOW(), NOW()),
('jetty_penarik', 'Jetty Penarik', 'PN', 'Jetty Penarik, Setiu', 'Setiu, Terengganu', '{"lat": 5.7000, "lng": 102.7667}', true, NOW(), NOW());

-- Data Migration: Insert initial destinations
INSERT INTO "destinations" ("id", "name", "code", "fullName", "description", "coordinates", "popularActivities", "isActive", "createdAt", "updatedAt") VALUES
('dest_perhentian', 'Pulau Perhentian', 'PERHENTIAN', 'Pulau Perhentian Besar & Kecil', 'Beautiful twin islands known for crystal clear waters and vibrant marine life', '{"lat": 5.9167, "lng": 102.7333}', ARRAY['Snorkeling', 'Diving', 'Beach Activities', 'Island Hopping'], true, NOW(), NOW()),
('dest_redang', 'Pulau Redang', 'REDANG', 'Pulau Redang Marine Park', 'Pristine island with white sandy beaches and excellent diving spots', '{"lat": 5.7667, "lng": 103.0167}', ARRAY['Snorkeling', 'Diving', 'Turtle Watching', 'Beach Activities'], true, NOW(), NOW());

-- Data Migration: Insert initial routes
INSERT INTO "routes" ("id", "departureJettyId", "destinationId", "distance", "estimatedDuration", "isActive", "createdAt", "updatedAt") VALUES
('route_km_perhentian', 'jetty_kampung_mangkuk', 'dest_perhentian', 25.0, 45, true, NOW(), NOW()),
('route_mr_perhentian', 'jetty_merang', 'dest_perhentian', 20.0, 35, true, NOW(), NOW()),
('route_pn_perhentian', 'jetty_penarik', 'dest_perhentian', 30.0, 50, true, NOW(), NOW()),
('route_km_redang', 'jetty_kampung_mangkuk', 'dest_redang', 35.0, 60, true, NOW(), NOW()),
('route_mr_redang', 'jetty_merang', 'dest_redang', 30.0, 50, true, NOW(), NOW()),
('route_pn_redang', 'jetty_penarik', 'dest_redang', 40.0, 70, true, NOW(), NOW());

-- Data Migration: Insert service categories
INSERT INTO "service_categories" ("id", "name", "code", "description", "requiresDestination", "sortOrder", "isActive", "createdAt", "updatedAt") VALUES
('cat_passenger_transport', 'Passenger Transport', 'PASSENGER_TRANSPORT', 'Ferry and boat transport services to islands and destinations', true, 1, true, NOW(), NOW()),
('cat_snorkeling_tours', 'Snorkeling Tours', 'SNORKELING_TOURS', 'Guided snorkeling experiences and marine life tours', false, 2, true, NOW(), NOW()),
('cat_diving_tours', 'Diving Tours', 'DIVING_TOURS', 'Professional diving tours and certification courses', false, 3, true, NOW(), NOW()),
('cat_island_hopping', 'Island Hopping', 'ISLAND_HOPPING', 'Multi-destination island exploration tours', true, 4, true, NOW(), NOW());

-- Data Migration: Insert service types
INSERT INTO "service_types" ("id", "categoryId", "name", "code", "description", "defaultDuration", "requiresRoute", "isActive", "createdAt", "updatedAt") VALUES
('st_passenger_ferry', 'cat_passenger_transport', 'Passenger Ferry', 'PASSENGER_FERRY', 'Regular ferry service for passenger transport', 60, true, true, NOW(), NOW()),
('st_snorkeling_half_day', 'cat_snorkeling_tours', 'Half Day Snorkeling', 'SNORKELING_HALF_DAY', 'Half day snorkeling tour with equipment included', 240, false, true, NOW(), NOW()),
('st_snorkeling_full_day', 'cat_snorkeling_tours', 'Full Day Snorkeling', 'SNORKELING_FULL_DAY', 'Full day snorkeling adventure with lunch included', 480, false, true, NOW(), NOW());

-- Data Migration: Create providers for existing boat owners
INSERT INTO "providers" ("id", "userId", "companyName", "displayName", "contactPhone", "contactEmail", "isAutoGenerated", "createdAt", "updatedAt")
SELECT
    'provider_' || b."ownerId",
    b."ownerId",
    COALESCE(p."firstName" || ' ' || p."lastName" || ' Marine Services', 'Marine Services'),
    COALESCE(p."firstName" || ' ' || p."lastName", 'Boat Owner'),
    COALESCE(p."phone", u."email"),
    u."email",
    true,
    NOW(),
    NOW()
FROM boats b
JOIN users u ON u.id = b."ownerId"
LEFT JOIN profiles p ON p."userId" = u.id
WHERE NOT EXISTS (SELECT 1 FROM providers pr WHERE pr."userId" = b."ownerId");

-- Data Migration: Update boats to reference providers
UPDATE boats
SET "providerId" = 'provider_' || "ownerId"
WHERE "providerId" IS NULL;
