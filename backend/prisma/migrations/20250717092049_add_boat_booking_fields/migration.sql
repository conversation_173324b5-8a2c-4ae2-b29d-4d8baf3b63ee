/*
  Warnings:

  - You are about to drop the column `timeSlots` on the `boat_availability` table. All the data in the column will be lost.
  - Added the required column `serviceTime` to the `bookings` table without a default value. This is not possible if the table is not empty.
  - Added the required column `serviceType` to the `bookings` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "boat_availability" DROP COLUMN "timeSlots",
ADD COLUMN     "availableSlots" JSONB;

-- AlterTable
ALTER TABLE "boats" ADD COLUMN     "agePricing" JSONB,
ADD COLUMN     "galleryImages" JSONB,
ADD COLUMN     "includedItems" TEXT[],
ADD COLUMN     "itinerary" JSONB,
ADD COLUMN     "packageDetails" JSONB;

-- AlterTable
ALTER TABLE "bookings" ADD COLUMN     "discountAmount" DECIMAL(65,30) NOT NULL DEFAULT 0,
ADD COLUMN     "discountCode" TEXT,
ADD COLUMN     "passengerBreakdown" JSONB,
ADD COLUMN     "serviceTime" TEXT NOT NULL,
ADD COLUMN     "serviceType" "ServiceType" NOT NULL;

-- CreateTable
CREATE TABLE "discount_codes" (
    "id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "discountType" TEXT NOT NULL,
    "discountValue" DECIMAL(65,30) NOT NULL,
    "minAmount" DECIMAL(65,30),
    "maxDiscount" DECIMAL(65,30),
    "validFrom" TIMESTAMP(3),
    "validUntil" TIMESTAMP(3),
    "usageLimit" INTEGER,
    "usedCount" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "discount_codes_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "discount_codes_code_key" ON "discount_codes"("code");

-- AddForeignKey
ALTER TABLE "bookings" ADD CONSTRAINT "bookings_discountCode_fkey" FOREIGN KEY ("discountCode") REFERENCES "discount_codes"("code") ON DELETE SET NULL ON UPDATE CASCADE;
