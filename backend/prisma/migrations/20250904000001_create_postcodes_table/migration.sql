-- CreateTable
CREATE TABLE "postcodes" (
    "id" SERIAL NOT NULL,
    "postcode" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "state_id" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "postcodes_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "postcodes_state_id_idx" ON "postcodes"("state_id");

-- CreateIndex
CREATE INDEX "postcodes_postcode_idx" ON "postcodes"("postcode");

-- AddForeignKey
ALTER TABLE "postcodes" ADD CONSTRAINT "postcodes_state_id_fkey" FOREIGN KEY ("state_id") REFERENCES "states"("id") ON DELETE RESTRICT ON UPDATE CASCADE;