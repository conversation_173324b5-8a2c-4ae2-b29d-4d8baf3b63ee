-- DropFore<PERSON><PERSON>ey
ALTER TABLE "service_age_pricing" DROP CONSTRAINT "service_age_pricing_ageCategoryId_fkey";

-- DropForeignKey
ALTER TABLE "service_age_pricing" DROP CONSTRAINT "service_age_pricing_serviceId_fkey";

-- DropForeignKey
ALTER TABLE "service_age_ranges" DROP CONSTRAINT "service_age_ranges_ageCategoryId_fkey";

-- DropForeignKey
ALTER TABLE "service_age_ranges" DROP CONSTRAINT "service_age_ranges_serviceId_fkey";

-- DropForeignKey
ALTER TABLE "service_packages" DROP CONSTRAINT "service_packages_packageTypeId_fkey";

-- DropForeignKey
ALTER TABLE "service_packages" DROP CONSTRAINT "service_packages_serviceId_fkey";

-- DropIndex
DROP INDEX "service_age_ranges_ageCategoryId_idx";

-- DropIndex
DROP INDEX "service_age_ranges_isActive_idx";

-- DropIndex
DROP INDEX "service_age_ranges_serviceId_idx";

-- AlterTable
ALTER TABLE "service_packages" ADD COLUMN     "description" TEXT;

-- CreateIndex
CREATE INDEX "service_assignments_boatId_idx" ON "service_assignments"("boatId");

-- AddForeignKey
ALTER TABLE "service_packages" ADD CONSTRAINT "service_packages_packageTypeId_fkey" FOREIGN KEY ("packageTypeId") REFERENCES "package_types"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "service_packages" ADD CONSTRAINT "service_packages_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "provider_services"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "service_age_pricing" ADD CONSTRAINT "service_age_pricing_ageCategoryId_fkey" FOREIGN KEY ("ageCategoryId") REFERENCES "age_categories"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "service_age_pricing" ADD CONSTRAINT "service_age_pricing_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "provider_services"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "service_age_ranges" ADD CONSTRAINT "service_age_ranges_ageCategoryId_fkey" FOREIGN KEY ("ageCategoryId") REFERENCES "age_categories"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "service_age_ranges" ADD CONSTRAINT "service_age_ranges_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "provider_services"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
