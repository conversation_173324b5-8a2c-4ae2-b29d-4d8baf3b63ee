/*
  Warnings:

  - You are about to drop the column `address1` on the `profiles` table. All the data in the column will be lost.
  - You are about to drop the column `address2` on the `profiles` table. All the data in the column will be lost.
  - You are about to drop the column `agencyName` on the `profiles` table. All the data in the column will be lost.
  - You are about to drop the column `brn` on the `profiles` table. All the data in the column will be lost.
  - You are about to drop the column `city` on the `profiles` table. All the data in the column will be lost.
  - You are about to drop the column `companyName` on the `profiles` table. All the data in the column will be lost.
  - You are about to drop the column `postcode` on the `profiles` table. All the data in the column will be lost.
  - You are about to drop the column `state` on the `profiles` table. All the data in the column will be lost.
  - You are about to drop the column `contactEmail` on the `providers` table. All the data in the column will be lost.
  - You are about to drop the column `contactPhone` on the `providers` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "profiles" DROP COLUMN "address1",
DROP COLUMN "address2",
DROP COLUMN "agencyName",
DROP COLUMN "brn",
DROP COLUMN "city",
DROP COLUMN "companyName",
DROP COLUMN "postcode",
DROP COLUMN "state",
ADD COLUMN     "personalAddress1" TEXT,
ADD COLUMN     "personalAddress2" TEXT,
ADD COLUMN     "personalCity" TEXT,
ADD COLUMN     "personalPostcode" TEXT,
ADD COLUMN     "personalState" TEXT,
ADD COLUMN     "timezone" TEXT DEFAULT 'Asia/Kuala_Lumpur';

-- AlterTable
ALTER TABLE "providers" DROP COLUMN "contactEmail",
DROP COLUMN "contactPhone",
ADD COLUMN     "agencyName" TEXT,
ADD COLUMN     "brandColors" JSONB,
ADD COLUMN     "businessAddress1" TEXT,
ADD COLUMN     "businessAddress2" TEXT,
ADD COLUMN     "businessCity" TEXT,
ADD COLUMN     "businessEmail" TEXT,
ADD COLUMN     "businessPhone" TEXT,
ADD COLUMN     "businessPostcode" TEXT,
ADD COLUMN     "businessState" TEXT,
ADD COLUMN     "certifications" JSONB,
ADD COLUMN     "licenseExpiryDate" TIMESTAMP(3),
ADD COLUMN     "totalBookings" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "verifiedAt" TIMESTAMP(3),
ADD COLUMN     "verifiedBy" TEXT,
ADD COLUMN     "websiteUrl" TEXT,
ALTER COLUMN "companyName" DROP NOT NULL;

-- CreateIndex
CREATE INDEX "providers_companyName_idx" ON "providers"("companyName");

-- CreateIndex
CREATE INDEX "providers_brn_idx" ON "providers"("brn");

-- CreateIndex
CREATE INDEX "providers_isVerified_isActive_idx" ON "providers"("isVerified", "isActive");
