-- Migration: Add Business Rules and Triggers
-- This migration adds database triggers and constraints to enforce business rules

-- 1. Auto-Provider Creation Trigger
-- Automatically creates a provider when a boat owner doesn't have one

CREATE OR REPLACE FUNCTION create_provider_for_boat_owner()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if provider already exists for this boat owner
    IF NOT EXISTS (SELECT 1 FROM providers WHERE "userId" = NEW."ownerId") THEN
        -- Create auto-generated provider
        INSERT INTO providers (
            id,
            "userId",
            "companyName",
            "displayName",
            "contactPhone",
            "contactEmail",
            "isAutoGenerated",
            "createdAt",
            "updatedAt"
        )
        SELECT 
            'provider_' || NEW."ownerId",
            NEW."ownerId",
            COALESCE(p."firstName" || ' ' || p."lastName" || ' Marine Services', 'Marine Services'),
            COALESCE(p."firstName" || ' ' || p."lastName", 'Boat Owner'),
            COALESCE(p."phone", u."email"),
            u."email",
            true,
            NOW(),
            NOW()
        FROM users u 
        LEFT JOIN profiles p ON p."userId" = u.id 
        WHERE u.id = NEW."ownerId";
        
        -- Update the boat to reference the new provider
        NEW."providerId" = 'provider_' || NEW."ownerId";
    ELSE
        -- Set providerId to existing provider
        SELECT id INTO NEW."providerId" FROM providers WHERE "userId" = NEW."ownerId";
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for boat insertion
DROP TRIGGER IF EXISTS trigger_create_provider_for_boat ON boats;
CREATE TRIGGER trigger_create_provider_for_boat
    BEFORE INSERT ON boats
    FOR EACH ROW
    EXECUTE FUNCTION create_provider_for_boat_owner();

-- Create trigger for boat updates (when ownerId changes)
DROP TRIGGER IF EXISTS trigger_update_provider_for_boat ON boats;
CREATE TRIGGER trigger_update_provider_for_boat
    BEFORE UPDATE OF "ownerId" ON boats
    FOR EACH ROW
    WHEN (OLD."ownerId" IS DISTINCT FROM NEW."ownerId")
    EXECUTE FUNCTION create_provider_for_boat_owner();

-- 2. Service Assignment Validation Function
-- Ensures boats can only be assigned to services from their provider

CREATE OR REPLACE FUNCTION validate_service_assignment()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if the boat belongs to the same provider as the service
    IF NOT EXISTS (
        SELECT 1 
        FROM boats b
        JOIN provider_services ps ON ps.id = NEW."serviceId"
        WHERE b.id = NEW."boatId" 
        AND b."providerId" = ps."providerId"
    ) THEN
        RAISE EXCEPTION 'Boat can only be assigned to services from the same provider';
    END IF;
    
    -- Check if boat is already assigned as primary to another service
    IF NEW."isPrimary" = true AND EXISTS (
        SELECT 1 
        FROM service_assignments sa
        WHERE sa."boatId" = NEW."boatId" 
        AND sa."isPrimary" = true 
        AND sa.id != COALESCE(NEW.id, '')
        AND sa."isActive" = true
    ) THEN
        RAISE EXCEPTION 'Boat is already assigned as primary to another service';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for service assignment validation
DROP TRIGGER IF EXISTS trigger_validate_service_assignment ON service_assignments;
CREATE TRIGGER trigger_validate_service_assignment
    BEFORE INSERT OR UPDATE ON service_assignments
    FOR EACH ROW
    EXECUTE FUNCTION validate_service_assignment();

-- 3. Passenger Transport Destination Validation
-- Ensures passenger transport services have at least one route/destination

CREATE OR REPLACE FUNCTION validate_passenger_transport_routes()
RETURNS TRIGGER AS $$
DECLARE
    category_code TEXT;
BEGIN
    -- Get the service category code
    SELECT sc.code INTO category_code
    FROM service_types st
    JOIN service_categories sc ON sc.id = st."categoryId"
    WHERE st.id = NEW."serviceTypeId";
    
    -- If it's passenger transport, ensure it has routes
    IF category_code = 'PASSENGER_TRANSPORT' THEN
        -- For new services, we'll validate this in the application layer
        -- since routes are typically added after service creation
        NULL;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for passenger transport validation
DROP TRIGGER IF EXISTS trigger_validate_passenger_transport ON provider_services;
CREATE TRIGGER trigger_validate_passenger_transport
    BEFORE INSERT OR UPDATE ON provider_services
    FOR EACH ROW
    EXECUTE FUNCTION validate_passenger_transport_routes();

-- 4. Route Validation Function
-- Ensures routes are valid and jetties/destinations exist

CREATE OR REPLACE FUNCTION validate_route()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if departure jetty exists and is active
    IF NOT EXISTS (
        SELECT 1 FROM jetties 
        WHERE id = NEW."departureJettyId" AND "isActive" = true
    ) THEN
        RAISE EXCEPTION 'Departure jetty does not exist or is not active';
    END IF;
    
    -- Check if destination exists and is active
    IF NOT EXISTS (
        SELECT 1 FROM destinations 
        WHERE id = NEW."destinationId" AND "isActive" = true
    ) THEN
        RAISE EXCEPTION 'Destination does not exist or is not active';
    END IF;
    
    -- Ensure distance and duration are positive
    IF NEW.distance <= 0 THEN
        RAISE EXCEPTION 'Route distance must be positive';
    END IF;
    
    IF NEW."estimatedDuration" <= 0 THEN
        RAISE EXCEPTION 'Route estimated duration must be positive';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for route validation
DROP TRIGGER IF EXISTS trigger_validate_route ON routes;
CREATE TRIGGER trigger_validate_route
    BEFORE INSERT OR UPDATE ON routes
    FOR EACH ROW
    EXECUTE FUNCTION validate_route();

-- 5. Provider Operating Area Validation
-- Ensures providers can only operate from active jetties

CREATE OR REPLACE FUNCTION validate_provider_operating_area()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if jetty exists and is active
    IF NOT EXISTS (
        SELECT 1 FROM jetties 
        WHERE id = NEW."jettyId" AND "isActive" = true
    ) THEN
        RAISE EXCEPTION 'Jetty does not exist or is not active';
    END IF;
    
    -- Check if provider exists and is active
    IF NOT EXISTS (
        SELECT 1 FROM providers 
        WHERE id = NEW."providerId" AND "isActive" = true
    ) THEN
        RAISE EXCEPTION 'Provider does not exist or is not active';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for provider operating area validation
DROP TRIGGER IF EXISTS trigger_validate_provider_operating_area ON provider_operating_areas;
CREATE TRIGGER trigger_validate_provider_operating_area
    BEFORE INSERT OR UPDATE ON provider_operating_areas
    FOR EACH ROW
    EXECUTE FUNCTION validate_provider_operating_area();

-- 6. Service Capacity Validation
-- Ensures service capacity doesn't exceed total boat capacity

CREATE OR REPLACE FUNCTION validate_service_capacity()
RETURNS TRIGGER AS $$
DECLARE
    total_boat_capacity INTEGER := 0;
BEGIN
    -- Calculate total capacity from assigned boats
    SELECT COALESCE(SUM(
        CASE 
            WHEN sa."maxCapacityOverride" IS NOT NULL THEN sa."maxCapacityOverride"
            ELSE b.capacity
        END
    ), 0) INTO total_boat_capacity
    FROM service_assignments sa
    JOIN boats b ON b.id = sa."boatId"
    WHERE sa."serviceId" = NEW."serviceId" 
    AND sa."isActive" = true
    AND sa.id != COALESCE(NEW.id, '');
    
    -- Add current assignment capacity
    IF NEW."isActive" = true THEN
        SELECT total_boat_capacity + COALESCE(NEW."maxCapacityOverride", b.capacity)
        INTO total_boat_capacity
        FROM boats b
        WHERE b.id = NEW."boatId";
    END IF;
    
    -- Update service max capacity if it's less than total boat capacity
    UPDATE provider_services 
    SET "maxCapacity" = GREATEST("maxCapacity", total_boat_capacity)
    WHERE id = NEW."serviceId";
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for service capacity validation
DROP TRIGGER IF EXISTS trigger_validate_service_capacity ON service_assignments;
CREATE TRIGGER trigger_validate_service_capacity
    AFTER INSERT OR UPDATE OR DELETE ON service_assignments
    FOR EACH ROW
    EXECUTE FUNCTION validate_service_capacity();

-- 7. Update existing boats to have providers
-- This ensures all existing boats have associated providers

DO $$
BEGIN
    -- Update boats that don't have providers yet
    UPDATE boats 
    SET "providerId" = 'provider_' || "ownerId" 
    WHERE "providerId" IS NULL;
END $$;
