-- Manual Migration Script: Optimize User Schema Separation
-- This script migrates business data from Profile to Provider table
-- and implements the optimized schema structure

-- =====================================================
-- PHASE 1: ADD NEW COLUMNS TO PROVIDER TABLE
-- =====================================================

-- Add new business fields to Provider table
ALTER TABLE providers 
ADD COLUMN IF NOT EXISTS "agencyName" TEXT,
ADD COLUMN IF NOT EXISTS "licenseExpiryDate" TIMESTAMP(3),
ADD COLUMN IF NOT EXISTS "certifications" JSONB,
ADD COLUMN IF NOT EXISTS "businessPhone" TEXT,
ADD COLUMN IF NOT EXISTS "businessEmail" TEXT,
ADD COLUMN IF NOT EXISTS "websiteUrl" TEXT,
ADD COLUMN IF NOT EXISTS "businessAddress1" TEXT,
ADD COLUMN IF NOT EXISTS "businessAddress2" TEXT,
ADD COLUMN IF NOT EXISTS "businessCity" TEXT,
ADD COLUMN IF NOT EXISTS "businessPostcode" TEXT,
ADD COLUMN IF NOT EXISTS "businessState" TEXT,
ADD COLUMN IF NOT EXISTS "brandColors" JSONB,
ADD COLUMN IF NOT EXISTS "totalBookings" INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS "verifiedAt" TIMESTAMP(3),
ADD COLUMN IF NOT EXISTS "verifiedBy" TEXT;

-- Make existing required fields nullable for individual operators
ALTER TABLE providers ALTER COLUMN "companyName" DROP NOT NULL;
ALTER TABLE providers ALTER COLUMN "contactPhone" DROP NOT NULL;
ALTER TABLE providers ALTER COLUMN "contactEmail" DROP NOT NULL;

-- =====================================================
-- PHASE 2: MIGRATE BUSINESS DATA FROM PROFILE TO PROVIDER
-- =====================================================

-- Migrate business data from Profile to Provider
UPDATE providers 
SET 
  "agencyName" = profiles."agencyName",
  "businessPhone" = profiles.phone,  -- Use encrypted phone from profile
  "businessAddress1" = profiles.address1,
  "businessAddress2" = profiles.address2,
  "businessCity" = profiles.city,
  "businessPostcode" = profiles.postcode,
  "businessState" = profiles.state
FROM profiles 
WHERE providers."userId" = profiles."userId";

-- Verify migration - this should return 0 rows if successful
-- SELECT COUNT(*) as unmigrated_records 
-- FROM providers p 
-- JOIN profiles pr ON p."userId" = pr."userId" 
-- WHERE p."companyName" != pr."companyName" OR p."brn" != pr."brn";

-- =====================================================
-- PHASE 3: ADD NEW PERSONAL ADDRESS COLUMNS TO PROFILE
-- =====================================================

-- Add new personal address columns to Profile table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS "personalAddress1" TEXT,
ADD COLUMN IF NOT EXISTS "personalAddress2" TEXT,
ADD COLUMN IF NOT EXISTS "personalCity" TEXT,
ADD COLUMN IF NOT EXISTS "personalPostcode" TEXT,
ADD COLUMN IF NOT EXISTS "personalState" TEXT,
ADD COLUMN IF NOT EXISTS "timezone" TEXT DEFAULT 'Asia/Kuala_Lumpur';

-- =====================================================
-- PHASE 4: REMOVE BUSINESS COLUMNS FROM PROFILE TABLE
-- =====================================================

-- Remove business-related columns from Profile table
ALTER TABLE profiles 
DROP COLUMN IF EXISTS "companyName",
DROP COLUMN IF EXISTS "brn",
DROP COLUMN IF EXISTS "agencyName";

-- Remove old address columns (data already migrated to Provider)
ALTER TABLE profiles 
DROP COLUMN IF EXISTS "address1",
DROP COLUMN IF EXISTS "address2",
DROP COLUMN IF EXISTS "city",
DROP COLUMN IF EXISTS "postcode",
DROP COLUMN IF EXISTS "state";

-- =====================================================
-- PHASE 5: CLEAN UP PROVIDER TABLE
-- =====================================================

-- Remove old contact columns from Provider table (replaced with business* columns)
ALTER TABLE providers 
DROP COLUMN IF EXISTS "contactPhone",
DROP COLUMN IF EXISTS "contactEmail";

-- =====================================================
-- PHASE 6: ADD PERFORMANCE INDEXES
-- =====================================================

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS "idx_providers_company_name" ON providers("companyName") WHERE "companyName" IS NOT NULL;
CREATE INDEX IF NOT EXISTS "idx_providers_brn" ON providers("brn") WHERE "brn" IS NOT NULL;
CREATE INDEX IF NOT EXISTS "idx_providers_verified_active" ON providers("isVerified", "isActive");
CREATE INDEX IF NOT EXISTS "idx_users_role_active" ON users("role", "isActive", "isApproved");
CREATE INDEX IF NOT EXISTS "idx_profiles_completion" ON profiles("profileCompletedAt") WHERE "profileCompletedAt" IS NOT NULL;

-- =====================================================
-- PHASE 7: DATA VALIDATION QUERIES
-- =====================================================

-- Validation Query 1: Check that all boat owners have Provider records
-- SELECT u.email, u.role, 
--        CASE WHEN pr.id IS NULL THEN 'MISSING PROVIDER' ELSE 'HAS PROVIDER' END as provider_status
-- FROM users u 
-- LEFT JOIN providers pr ON u.id = pr."userId" 
-- WHERE u.role = 'BOAT_OWNER';

-- Validation Query 2: Check for any remaining business data in Profile table
-- SELECT COUNT(*) as profiles_with_business_data
-- FROM profiles 
-- WHERE "companyName" IS NOT NULL OR "brn" IS NOT NULL OR "agencyName" IS NOT NULL;

-- Validation Query 3: Verify Provider business data migration
-- SELECT p."companyName", p."brn", p."agencyName", p."businessPhone", p."businessAddress1"
-- FROM providers p
-- JOIN users u ON p."userId" = u.id
-- WHERE u.role = 'BOAT_OWNER'
-- ORDER BY p."createdAt";

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

-- Update schema version or add migration record if needed
-- INSERT INTO _prisma_migrations (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count)
-- VALUES (
--   gen_random_uuid()::text,
--   'manual_schema_optimization',
--   NOW(),
--   'optimize_user_schema_separation',
--   'Manual migration to separate personal and business data',
--   NULL,
--   NOW(),
--   7
-- );

COMMIT;
