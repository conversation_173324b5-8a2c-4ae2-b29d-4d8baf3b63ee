-- Migration: Add Dynamic Age Range Configuration System
-- Date: 2025-08-14
-- Description: Implement service-specific age range configuration with enhanced age categories

-- Step 1: Add new columns to age_categories table
ALTER TABLE "age_categories" ADD COLUMN "isConfigurable" BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE "age_categories" ADD COLUMN "defaultMinAge" INTEGER;
ALTER TABLE "age_categories" ADD COLUMN "defaultMaxAge" INTEGER;

-- Step 2: Update existing age categories with default ranges
UPDATE "age_categories" SET 
    "defaultMinAge" = 18, 
    "defaultMaxAge" = 64,
    "isConfigurable" = true
WHERE "code" = 'adult';

UPDATE "age_categories" SET 
    "defaultMinAge" = 3, 
    "defaultMaxAge" = 17,
    "isConfigurable" = true
WHERE "code" = 'child';

UPDATE "age_categories" SET 
    "defaultMinAge" = 0, 
    "defaultMaxAge" = 2,
    "isConfigurable" = true
WHERE "code" = 'toddler';

UPDATE "age_categories" SET 
    "defaultMinAge" = 65, 
    "defaultMaxAge" = NULL,
    "isConfigurable" = true
WHERE "code" = 'senior';

UPDATE "age_categories" SET 
    "defaultMinAge" = NULL, 
    "defaultMaxAge" = NULL,
    "isConfigurable" = false
WHERE "code" = 'pwd';

-- Step 3: Create service_age_ranges table for service-specific overrides
CREATE TABLE "service_age_ranges" (
    "id" TEXT NOT NULL,
    "serviceId" TEXT NOT NULL,
    "ageCategoryId" TEXT NOT NULL,
    "minAge" INTEGER NOT NULL,
    "maxAge" INTEGER,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "service_age_ranges_pkey" PRIMARY KEY ("id")
);

-- Step 4: Add foreign key constraints
ALTER TABLE "service_age_ranges" ADD CONSTRAINT "service_age_ranges_serviceId_fkey" 
    FOREIGN KEY ("serviceId") REFERENCES "provider_services"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "service_age_ranges" ADD CONSTRAINT "service_age_ranges_ageCategoryId_fkey" 
    FOREIGN KEY ("ageCategoryId") REFERENCES "age_categories"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Step 5: Add unique constraint to prevent duplicate service-age category combinations
ALTER TABLE "service_age_ranges" ADD CONSTRAINT "service_age_ranges_serviceId_ageCategoryId_key" 
    UNIQUE ("serviceId", "ageCategoryId");

-- Step 6: Add indexes for performance
CREATE INDEX "service_age_ranges_serviceId_idx" ON "service_age_ranges"("serviceId");
CREATE INDEX "service_age_ranges_ageCategoryId_idx" ON "service_age_ranges"("ageCategoryId");
CREATE INDEX "service_age_ranges_isActive_idx" ON "service_age_ranges"("isActive");

-- Step 7: Insert sample service-specific age range overrides for testing
-- Example: Marine Premium Expedition with custom age ranges
INSERT INTO "service_age_ranges" ("id", "serviceId", "ageCategoryId", "minAge", "maxAge", "isActive", "createdAt", "updatedAt")
SELECT 
    'sar_' || ps.id || '_' || ac.code,
    ps.id,
    ac.id,
    CASE 
        WHEN ac.code = 'adult' THEN 21  -- Custom adult age starts at 21 for premium services
        WHEN ac.code = 'child' THEN 5   -- Custom child age starts at 5 for safety
        WHEN ac.code = 'senior' THEN 60 -- Custom senior age starts at 60 for premium services
        ELSE ac."defaultMinAge"
    END,
    CASE 
        WHEN ac.code = 'adult' THEN 59  -- Custom adult age ends at 59
        WHEN ac.code = 'child' THEN 20  -- Custom child age ends at 20
        ELSE ac."defaultMaxAge"
    END,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM "provider_services" ps
CROSS JOIN "age_categories" ac
WHERE ps.name LIKE '%Premium%' 
  AND ac.code IN ('adult', 'child', 'senior')
  AND ac."isConfigurable" = true;

-- Step 8: Add comments for documentation
COMMENT ON TABLE "service_age_ranges" IS 'Service-specific age range overrides for flexible age category configuration';
COMMENT ON COLUMN "service_age_ranges"."minAge" IS 'Minimum age for this category in this service (inclusive)';
COMMENT ON COLUMN "service_age_ranges"."maxAge" IS 'Maximum age for this category in this service (inclusive, NULL means no upper limit)';
COMMENT ON COLUMN "age_categories"."isConfigurable" IS 'Whether this age category can have service-specific overrides';
COMMENT ON COLUMN "age_categories"."defaultMinAge" IS 'Default minimum age when no service-specific override exists';
COMMENT ON COLUMN "age_categories"."defaultMaxAge" IS 'Default maximum age when no service-specific override exists';
