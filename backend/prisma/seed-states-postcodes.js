const { PrismaClient } = require('@prisma/client');
const statesData = require('./seed-states');
const postcodesData = require('./seed-postcodes');

const prisma = new PrismaClient();

async function seedStatesAndPostcodes() {
  try {
    console.log('🌱 Starting states and postcodes seeding...');
    
    // Seed states
    console.log('Creating states...');
    for (const state of statesData) {
      await prisma.state.upsert({
        where: { id: state.id },
        update: { name: state.name },
        create: { id: state.id, name: state.name }
      });
    }
    console.log(`✅ Created/updated ${statesData.length} states`);
    
    // Seed postcodes in batches to avoid memory issues
    console.log('Creating postcodes...');
    const batchSize = 1000;
    for (let i = 0; i < postcodesData.length; i += batchSize) {
      const batch = postcodesData.slice(i, i + batchSize);
      
      // Create postcodes for this batch
      const postcodesToCreate = batch.map(pc => ({
        postcode: pc.postcode,
        city: pc.city,
        stateId: pc.state_id
      }));
      
      await prisma.postcode.createMany({
        data: postcodesToCreate,
        skipDuplicates: true
      });
      
      console.log(`✅ Created ${Math.min(batchSize, postcodesData.length - i)} postcodes (batch ${Math.floor(i/batchSize) + 1})`);
    }
    
    console.log('✅ States and postcodes seeding completed successfully!');
  } catch (error) {
    console.error('Error during seeding:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

seedStatesAndPostcodes().catch((error) => {
  console.error('Error during seeding process:', error);
  process.exit(1);
});