const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function seedDynamicAgeRanges() {
  console.log('🎯 Starting Dynamic Age Range System Seeding...');

  try {
    // Get all services to create different age range scenarios
    const services = await prisma.providerService.findMany({
      where: { isActive: true },
      include: { servicePackages: true }
    });

    console.log(`📊 Found ${services.length} services to configure`);

    // Get all age categories
    const ageCategories = await prisma.ageCategory.findMany({
      where: { isActive: true },
      orderBy: { sortOrder: 'asc' }
    });

    console.log(`👥 Found ${ageCategories.length} age categories`);

    // Scenario 1: Premium services with stricter age requirements
    const premiumServices = services.filter(s => s.name.toLowerCase().includes('premium'));
    
    for (const service of premiumServices) {
      console.log(`⭐ Configuring premium service: ${service.name}`);
      
      // Premium services: Adults 21-59, Children 8-20, Seniors 60+
      const adultCategory = ageCategories.find(ac => ac.code === 'ADULT');
      const childCategory = ageCategories.find(ac => ac.code === 'CHILD');
      const seniorCategory = ageCategories.find(ac => ac.code === 'SENIOR');
      
      if (adultCategory) {
        await prisma.serviceAgeRange.upsert({
          where: {
            serviceId_ageCategoryId: {
              serviceId: service.id,
              ageCategoryId: adultCategory.id
            }
          },
          update: {
            minAge: 21,
            maxAge: 59,
            updatedAt: new Date()
          },
          create: {
            id: `sar_${service.id}_adult`,
            serviceId: service.id,
            ageCategoryId: adultCategory.id,
            minAge: 21,
            maxAge: 59
          }
        });
      }
      
      if (childCategory) {
        await prisma.serviceAgeRange.upsert({
          where: {
            serviceId_ageCategoryId: {
              serviceId: service.id,
              ageCategoryId: childCategory.id
            }
          },
          update: {
            minAge: 8,
            maxAge: 20,
            updatedAt: new Date()
          },
          create: {
            id: `sar_${service.id}_child`,
            serviceId: service.id,
            ageCategoryId: childCategory.id,
            minAge: 8,
            maxAge: 20
          }
        });
      }
      
      if (seniorCategory) {
        await prisma.serviceAgeRange.upsert({
          where: {
            serviceId_ageCategoryId: {
              serviceId: service.id,
              ageCategoryId: seniorCategory.id
            }
          },
          update: {
            minAge: 60,
            maxAge: null,
            updatedAt: new Date()
          },
          create: {
            id: `sar_${service.id}_senior`,
            serviceId: service.id,
            ageCategoryId: seniorCategory.id,
            minAge: 60,
            maxAge: null
          }
        });
      }
    }

    // Scenario 2: Adventure/Extreme services with safety-focused age limits
    const adventureServices = services.filter(s => 
      s.name.toLowerCase().includes('adventure') || 
      s.name.toLowerCase().includes('extreme') ||
      s.name.toLowerCase().includes('diving')
    );
    
    for (const service of adventureServices) {
      console.log(`🏄 Configuring adventure service: ${service.name}`);
      
      // Adventure services: Adults 16-65, Children 12-15, No toddlers, Seniors up to 65
      const adultCategory = ageCategories.find(ac => ac.code === 'ADULT');
      const childCategory = ageCategories.find(ac => ac.code === 'CHILD');
      const seniorCategory = ageCategories.find(ac => ac.code === 'SENIOR');
      
      if (adultCategory) {
        await prisma.serviceAgeRange.upsert({
          where: {
            serviceId_ageCategoryId: {
              serviceId: service.id,
              ageCategoryId: adultCategory.id
            }
          },
          update: {
            minAge: 16,
            maxAge: 65,
            updatedAt: new Date()
          },
          create: {
            id: `sar_${service.id}_adult`,
            serviceId: service.id,
            ageCategoryId: adultCategory.id,
            minAge: 16,
            maxAge: 65
          }
        });
      }
      
      if (childCategory) {
        await prisma.serviceAgeRange.upsert({
          where: {
            serviceId_ageCategoryId: {
              serviceId: service.id,
              ageCategoryId: childCategory.id
            }
          },
          update: {
            minAge: 12,
            maxAge: 15,
            updatedAt: new Date()
          },
          create: {
            id: `sar_${service.id}_child`,
            serviceId: service.id,
            ageCategoryId: childCategory.id,
            minAge: 12,
            maxAge: 15
          }
        });
      }
      
      // No seniors for extreme activities
      if (seniorCategory) {
        await prisma.serviceAgeRange.upsert({
          where: {
            serviceId_ageCategoryId: {
              serviceId: service.id,
              ageCategoryId: seniorCategory.id
            }
          },
          update: {
            minAge: 66,
            maxAge: 66, // Effectively disabled
            updatedAt: new Date()
          },
          create: {
            id: `sar_${service.id}_senior`,
            serviceId: service.id,
            ageCategoryId: seniorCategory.id,
            minAge: 66,
            maxAge: 66
          }
        });
      }
    }

    // Scenario 3: Family-friendly services with extended child ranges
    const familyServices = services.filter(s => 
      s.name.toLowerCase().includes('family') || 
      s.name.toLowerCase().includes('snorkeling') ||
      s.name.toLowerCase().includes('cruise')
    );
    
    for (const service of familyServices.slice(0, 3)) { // Limit to first 3 to avoid too many
      console.log(`👨‍👩‍👧‍👦 Configuring family service: ${service.name}`);
      
      // Family services: Adults 18-70, Children 2-17, Toddlers 0-1, Seniors 70+
      const adultCategory = ageCategories.find(ac => ac.code === 'ADULT');
      const childCategory = ageCategories.find(ac => ac.code === 'CHILD');
      const toddlerCategory = ageCategories.find(ac => ac.code === 'TODDLER');
      const seniorCategory = ageCategories.find(ac => ac.code === 'SENIOR');
      
      if (adultCategory) {
        await prisma.serviceAgeRange.upsert({
          where: {
            serviceId_ageCategoryId: {
              serviceId: service.id,
              ageCategoryId: adultCategory.id
            }
          },
          update: {
            minAge: 18,
            maxAge: 70,
            updatedAt: new Date()
          },
          create: {
            id: `sar_${service.id}_adult`,
            serviceId: service.id,
            ageCategoryId: adultCategory.id,
            minAge: 18,
            maxAge: 70
          }
        });
      }
      
      if (childCategory) {
        await prisma.serviceAgeRange.upsert({
          where: {
            serviceId_ageCategoryId: {
              serviceId: service.id,
              ageCategoryId: childCategory.id
            }
          },
          update: {
            minAge: 2,
            maxAge: 17,
            updatedAt: new Date()
          },
          create: {
            id: `sar_${service.id}_child`,
            serviceId: service.id,
            ageCategoryId: childCategory.id,
            minAge: 2,
            maxAge: 17
          }
        });
      }
      
      if (toddlerCategory) {
        await prisma.serviceAgeRange.upsert({
          where: {
            serviceId_ageCategoryId: {
              serviceId: service.id,
              ageCategoryId: toddlerCategory.id
            }
          },
          update: {
            minAge: 0,
            maxAge: 1,
            updatedAt: new Date()
          },
          create: {
            id: `sar_${service.id}_toddler`,
            serviceId: service.id,
            ageCategoryId: toddlerCategory.id,
            minAge: 0,
            maxAge: 1
          }
        });
      }
      
      if (seniorCategory) {
        await prisma.serviceAgeRange.upsert({
          where: {
            serviceId_ageCategoryId: {
              serviceId: service.id,
              ageCategoryId: seniorCategory.id
            }
          },
          update: {
            minAge: 70,
            maxAge: null,
            updatedAt: new Date()
          },
          create: {
            id: `sar_${service.id}_senior`,
            serviceId: service.id,
            ageCategoryId: seniorCategory.id,
            minAge: 70,
            maxAge: null
          }
        });
      }
    }

    // Count the results
    const totalOverrides = await prisma.serviceAgeRange.count();
    const servicesWithOverrides = await prisma.serviceAgeRange.groupBy({
      by: ['serviceId'],
      _count: { serviceId: true }
    });

    console.log('🎉 Dynamic Age Range System seeded successfully!');
    console.log(`📊 Created ${totalOverrides} age range overrides`);
    console.log(`🏢 Configured ${servicesWithOverrides.length} services with custom age ranges`);
    console.log(`⭐ Premium services: ${premiumServices.length}`);
    console.log(`🏄 Adventure services: ${adventureServices.length}`);
    console.log(`👨‍👩‍👧‍👦 Family services: ${Math.min(familyServices.length, 3)}`);

  } catch (error) {
    console.error('❌ Error seeding dynamic age ranges:', error);
    throw error;
  }
}

// Run the seeder
if (require.main === module) {
  seedDynamicAgeRanges()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

module.exports = { seedDynamicAgeRanges };
