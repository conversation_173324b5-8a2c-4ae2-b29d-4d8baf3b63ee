-- GoSea Platform Database Initialization Script
-- This script runs when the PostgreSQL container starts for the first time

-- Create extensions if they don't exist
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "citext";

-- Create database if it doesn't exist (handled by POSTGRES_DB env var)
-- Additional initialization can be added here

-- Set timezone
SET timezone = 'Asia/Kuala_Lumpur';

-- Create a .gitkeep equivalent for migrations
-- This ensures the migrations directory structure is maintained
