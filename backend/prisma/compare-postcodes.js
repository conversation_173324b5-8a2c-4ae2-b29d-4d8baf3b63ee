const fs = require('fs');

// Read seed-postcodes.js
const jsPostcodes = require('./seed-postcodes.js');

// Parse PostcodeSeeder.php to extract postcode data
const phpContent = fs.readFileSync('/Users/<USER>/project/gosea-project/backend/prisma/PostcodeSeeder.php', 'utf8');

// Extract postcode data from PHP array format
const phpPostcodes = [];
const postcodePattern = /\['postcode'=>'(\d+)','city'=>'([^']+)',\s*'state_id'=>'(\d+)'[^\]]*\]/g;
let match;

while ((match = postcodePattern.exec(phpContent)) !== null) {
    phpPostcodes.push({
        postcode: match[1],
        city: match[2],
        state_id: match[3]
    });
}

// Create a set of existing postcodes in JS file for quick lookup
const jsPostcodeSet = new Set(jsPostcodes.map(item => item.postcode));

// Find missing postcodes in JS file
const missingPostcodes = phpPostcodes.filter(phpItem => !jsPostcodeSet.has(phpItem.postcode));

console.log(`Found ${missingPostcodes.length} missing postcodes.`);

// Generate the JavaScript array format for the missing postcodes
let missingPostcodesArray = [];
missingPostcodes.forEach(item => {
    missingPostcodesArray.push(`  { postcode: '${item.postcode}', city: '${item.city}', state_id: ${item.state_id} }`);
});

// Read the existing seed-postcodes.js file
let seedPostcodesContent = fs.readFileSync('/Users/<USER>/project/gosea-project/backend/prisma/seed-postcodes.js', 'utf8');

// Remove the closing bracket and semicolon
seedPostcodesContent = seedPostcodesContent.trim();
if (seedPostcodesContent.endsWith('];')) {
    seedPostcodesContent = seedPostcodesContent.slice(0, -2);
}

// Add a comma to the last line if it doesn't have one
const lines = seedPostcodesContent.split('\n');
if (!lines[lines.length - 1].trim().endsWith(',')) {
    lines[lines.length - 1] = lines[lines.length - 1].trim() + ',';
}
seedPostcodesContent = lines.join('\n');

// Add the missing postcodes
const updatedContent = seedPostcodesContent + '\n' + missingPostcodesArray.join(',\n') + '\n];';

// Write the updated content back to the file
fs.writeFileSync('/Users/<USER>/project/gosea-project/backend/prisma/seed-postcodes.js', updatedContent);

console.log(`Successfully added ${missingPostcodes.length} missing postcodes to seed-postcodes.js`);