const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedDiscountCodes() {
  console.log('Seeding discount codes...');

  const discountCodes = [
    {
      code: 'WELCOME10',
      discountType: 'percentage',
      discountValue: 10,
      minAmount: 100,
      maxDiscount: 50,
      validFrom: new Date('2024-01-01'),
      validUntil: new Date('2024-12-31'),
      usageLimit: 100,
      isActive: true
    },
    {
      code: 'SUMMER2024',
      discountType: 'percentage',
      discountValue: 15,
      minAmount: 200,
      maxDiscount: 75,
      validFrom: new Date('2024-06-01'),
      validUntil: new Date('2024-08-31'),
      usageLimit: 50,
      isActive: true
    },
    {
      code: 'FIXED20',
      discountType: 'fixed',
      discountValue: 20,
      minAmount: 150,
      validFrom: new Date('2024-01-01'),
      validUntil: new Date('2024-12-31'),
      usageLimit: 200,
      isActive: true
    },
    {
      code: 'EARLYBIRD',
      discountType: 'percentage',
      discountValue: 20,
      minAmount: 300,
      maxDiscount: 100,
      validFrom: new Date('2024-01-01'),
      validUntil: new Date('2024-06-30'),
      usageLimit: 30,
      isActive: true
    },
    {
      code: 'TESTCODE',
      discountType: 'percentage',
      discountValue: 25,
      minAmount: 50,
      maxDiscount: 25,
      validFrom: new Date('2024-01-01'),
      validUntil: new Date('2025-12-31'),
      usageLimit: 1000,
      isActive: true
    }
  ];

  for (const discountCode of discountCodes) {
    try {
      await prisma.discountCode.upsert({
        where: { code: discountCode.code },
        update: discountCode,
        create: discountCode
      });
      console.log(`✓ Created/updated discount code: ${discountCode.code}`);
    } catch (error) {
      console.error(`✗ Failed to create discount code ${discountCode.code}:`, error.message);
    }
  }

  console.log('Discount codes seeding completed!');
}

async function main() {
  try {
    await seedDiscountCodes();
  } catch (error) {
    console.error('Seeding failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
