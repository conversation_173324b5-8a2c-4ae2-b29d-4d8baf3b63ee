const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedBoats() {
  console.log('Seeding boats...');

  // First, we need a user to be the boat owner
  let owner = await prisma.user.findFirst({
    where: { role: 'BOAT_OWNER' }
  });

  if (!owner) {
    // Create a dummy boat owner
    owner = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        role: 'BOAT_OWNER',
        emailVerified: true,
        profile: {
          create: {
            firstName: 'Captain',
            lastName: 'Ahmad',
            phone: '+60123456789'
          }
        }
      }
    });
    console.log('✓ Created dummy boat owner');
  }

  const boatsData = [
    {
      id: 'boat-001',
      name: 'Ocean Explorer',
      description: 'A spacious and comfortable boat perfect for snorkeling adventures around Redang Island. Features modern safety equipment and experienced crew.',
      serviceType: 'SNORKELING',
      location: 'REDANG',
      capacity: 12,
      basePrice: 280,
      status: 'APPROVED',
      isActive: true,
      ownerId: owner.id,
      photos: [
        {
          url: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
          alt: 'Ocean Explorer - Main View',
          order: 0
        },
        {
          url: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
          alt: 'Ocean Explorer - Interior',
          order: 1
        }
      ],
      packages: [
        {
          name: 'Half Day Snorkeling',
          description: 'Perfect introduction to Redang\'s marine life',
          price: 280,
          duration: '4 hours',
          includedItems: 'Snorkeling gear,Life jackets,Refreshments,Professional guide'
        }
      ],
      amenities: [
        { name: 'Air Conditioning', description: 'Climate controlled cabin' },
        { name: 'Safety Equipment', description: 'Life jackets and emergency gear' },
        { name: 'Snorkeling Gear', description: 'Masks, fins, and snorkels provided' }
      ]
    },
    {
      id: 'boat-002',
      name: 'Island Hopper',
      description: 'Fast and reliable passenger boat for island transfers and sightseeing tours around Perhentian Islands.',
      serviceType: 'PASSENGER',
      location: 'PERHENTIAN',
      capacity: 20,
      basePrice: 150,
      status: 'APPROVED',
      isActive: true,
      ownerId: owner.id,
      photos: [
        {
          url: 'https://images.unsplash.com/photo-1569263979104-865ab7cd8d13?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
          alt: 'Island Hopper - Main View',
          order: 0
        }
      ],
      packages: [
        {
          name: 'Island Transfer',
          description: 'Comfortable transfer between islands',
          price: 150,
          duration: '2 hours',
          includedItems: 'Life jackets,Comfortable seating,Professional captain'
        }
      ],
      amenities: [
        { name: 'Comfortable Seating', description: 'Cushioned seats for all passengers' },
        { name: 'Safety Equipment', description: 'Life jackets and emergency gear' }
      ]
    },
    {
      id: 'boat-003',
      name: 'Coral Explorer',
      description: 'Specialized snorkeling boat with glass bottom viewing for the best underwater experience at Redang Island.',
      serviceType: 'SNORKELING',
      location: 'REDANG',
      capacity: 8,
      basePrice: 350,
      status: 'APPROVED',
      isActive: true,
      ownerId: owner.id,
      photos: [
        {
          url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
          alt: 'Coral Explorer - Main View',
          order: 0
        }
      ],
      packages: [
        {
          name: 'Premium Snorkeling',
          description: 'Premium snorkeling experience with glass bottom viewing',
          price: 350,
          duration: '6 hours',
          includedItems: 'Premium snorkeling gear,Glass bottom viewing,Lunch,Professional guide,Underwater camera'
        }
      ],
      amenities: [
        { name: 'Glass Bottom Viewing', description: 'See marine life without getting wet' },
        { name: 'Premium Snorkeling Gear', description: 'High-quality masks, fins, and snorkels' },
        { name: 'Underwater Camera', description: 'Capture your underwater memories' }
      ]
    },
    {
      id: 'boat-004',
      name: 'Perhentian Express',
      description: 'High-speed passenger boat for quick and comfortable transfers to Perhentian Islands.',
      serviceType: 'PASSENGER',
      location: 'PERHENTIAN',
      capacity: 30,
      basePrice: 120,
      status: 'APPROVED',
      isActive: true,
      ownerId: owner.id,
      photos: [
        {
          url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
          alt: 'Perhentian Express - Main View',
          order: 0
        }
      ],
      packages: [
        {
          name: 'Express Transfer',
          description: 'Fast and comfortable island transfer',
          price: 120,
          duration: '1.5 hours',
          includedItems: 'Life jackets,Air conditioning,Professional captain'
        }
      ],
      amenities: [
        { name: 'High Speed', description: 'Fast transfer to save your time' },
        { name: 'Air Conditioning', description: 'Climate controlled cabin' },
        { name: 'Safety Equipment', description: 'Life jackets and emergency gear' }
      ]
    }
  ];

  for (const boatData of boatsData) {
    try {
      // Create boat
      const boat = await prisma.boat.upsert({
        where: { id: boatData.id },
        update: {
          name: boatData.name,
          description: boatData.description,
          serviceType: boatData.serviceType,
          location: boatData.location,
          capacity: boatData.capacity,
          basePrice: boatData.basePrice,
          status: boatData.status,
          isActive: boatData.isActive
        },
        create: {
          id: boatData.id,
          name: boatData.name,
          description: boatData.description,
          serviceType: boatData.serviceType,
          location: boatData.location,
          capacity: boatData.capacity,
          basePrice: boatData.basePrice,
          status: boatData.status,
          isActive: boatData.isActive,
          ownerId: boatData.ownerId
        }
      });

      // Delete existing photos and create new ones
      await prisma.boatPhoto.deleteMany({
        where: { boatId: boat.id }
      });

      // Create photos
      for (const photoData of boatData.photos) {
        await prisma.boatPhoto.create({
          data: {
            boatId: boat.id,
            url: photoData.url,
            caption: photoData.alt,
            order: photoData.order
          }
        });
      }

      // Delete existing packages and create new ones
      await prisma.boatPackage.deleteMany({
        where: { boatId: boat.id }
      });

      // Create packages
      for (const packageData of boatData.packages) {
        await prisma.boatPackage.create({
          data: {
            boatId: boat.id,
            name: packageData.name,
            description: packageData.description,
            includedItems: packageData.includedItems.split(',').map(item => item.trim())
          }
        });
      }

      // Delete existing amenities and create new ones
      await prisma.boatAmenity.deleteMany({
        where: { boatId: boat.id }
      });

      // Create amenities (BoatAmenity doesn't have description field, only name)
      for (const amenityData of boatData.amenities) {
        await prisma.boatAmenity.create({
          data: {
            boatId: boat.id,
            name: amenityData.name
          }
        });
      }

      console.log(`✓ Created/updated boat: ${boat.name}`);
    } catch (error) {
      console.error(`✗ Failed to create boat ${boatData.name}:`, error.message);
    }
  }

  console.log('Boats seeding completed!');
}

async function main() {
  try {
    await seedBoats();
  } catch (error) {
    console.error('Seeding failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
