const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function approveBoatOwner() {
  try {
    // Your user ID from the login response
    const userId = 'cmeyvcr jp00024vbwxmbqhetmr';
    
    console.log('Looking for boat owner with ID:', userId);
    
    // Find the boat owner
    const boatOwner = await prisma.user.findFirst({
      where: {
        id: userId,
        role: 'BOAT_OWNER',
        emailVerified: true
      },
      include: {
        profile: true,
        provider: true
      }
    });

    if (!boatOwner) {
      console.log('Boat owner not found or not email verified');
      return;
    }

    console.log('Found boat owner:', {
      email: boatOwner.email,
      name: `${boatOwner.profile?.firstName} ${boatOwner.profile?.lastName}`,
      isApproved: boatOwner.isApproved
    });

    if (boatOwner.isApproved) {
      console.log('Boat owner is already approved!');
      return;
    }

    // Update user and provider in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Approve user account
      const approvedUser = await tx.user.update({
        where: { id: userId },
        data: {
          isApproved: true,
          approvedAt: new Date(),
          approvedBy: 'system-approval' // Since we don't have an admin user yet
        },
        include: {
          profile: true
        }
      });

      // Update provider if exists
      if (boatOwner.provider) {
        await tx.provider.update({
          where: { id: boatOwner.provider.id },
          data: {
            isActive: true,
            isVerified: true
          }
        });
      }

      return approvedUser;
    });

    console.log('✅ Boat owner approved successfully!');
    console.log('User can now sign in and access the boat owner dashboard');
    
  } catch (error) {
    console.error('❌ Error approving boat owner:', error);
  } finally {
    await prisma.$disconnect();
  }
}

approveBoatOwner();
