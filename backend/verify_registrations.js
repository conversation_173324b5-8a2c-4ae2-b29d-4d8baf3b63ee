const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyRegistrations() {
  console.log('🔍 Verifying new registrations...');
  
  try {
    // Get the three test users we just registered
    const testUsers = await prisma.user.findMany({
      where: {
        email: {
          in: ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        }
      },
      include: {
        profile: true,
        provider: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`\n📊 Found ${testUsers.length} test registrations:\n`);

    testUsers.forEach((user, index) => {
      console.log(`=== Registration ${index + 1} ===`);
      console.log(`Email: ${user.email}`);
      console.log(`Role: ${user.role}`);
      console.log(`Email Verified: ${user.emailVerified}`);
      console.log(`Approved: ${user.isApproved}`);
      console.log(`Created: ${user.createdAt}`);
      
      if (user.profile) {
        console.log('\n📝 Profile Data (Personal):');
        console.log(`  Name: ${user.profile.firstName} ${user.profile.lastName}`);
        console.log(`  Phone: ${user.profile.phone ? '[ENCRYPTED]' : 'N/A'}`);
        console.log(`  Language: ${user.profile.language}`);
        console.log(`  Personal Address: ${user.profile.personalAddress1 || 'N/A'}`);
        console.log(`  Timezone: ${user.profile.timezone}`);
      }
      
      if (user.provider) {
        console.log('\n🏢 Provider Data (Business):');
        console.log(`  Company Name: ${user.provider.companyName || 'N/A (Individual Operator)'}`);
        console.log(`  Display Name: ${user.provider.displayName}`);
        console.log(`  BRN: ${user.provider.brn || 'N/A (Individual Operator)'}`);
        console.log(`  Business Phone: ${user.provider.businessPhone ? '[ENCRYPTED]' : 'N/A'}`);
        console.log(`  Business Email: ${user.provider.businessEmail || 'N/A'}`);
        console.log(`  Business Address: ${user.provider.businessAddress1 || 'N/A'}`);
        console.log(`  Verified: ${user.provider.isVerified}`);
        console.log(`  Active: ${user.provider.isActive}`);
      } else if (user.role === 'CUSTOMER') {
        console.log('\n👤 Customer (No Provider Data - Customers only have Profile)');
      }
      
      console.log('\n' + '='.repeat(50) + '\n');
    });

    // Verify schema separation
    console.log('🔍 Schema Separation Verification:');
    
    // Check that profiles don't have business data (these columns should not exist anymore)
    console.log('✅ Business data successfully removed from Profile table (companyName, brn, agencyName columns no longer exist)');

    // Check provider data structure
    const providersWithNewFields = await prisma.provider.count({
      where: {
        OR: [
          { businessPhone: { not: null } },
          { businessAddress1: { not: null } }
        ]
      }
    });

    console.log(`Providers with new business fields: ${providersWithNewFields}`);
    
    // Check individual vs business operators
    const individualOperators = await prisma.provider.count({
      where: {
        companyName: null,
        brn: null
      }
    });
    
    const businessOperators = await prisma.provider.count({
      where: {
        companyName: { not: null },
        brn: { not: null }
      }
    });
    
    console.log(`Individual operators: ${individualOperators}`);
    console.log(`Business operators: ${businessOperators}`);

    // Check customer count
    const customerCount = await prisma.user.count({
      where: { role: 'CUSTOMER' }
    });

    console.log(`Total customers: ${customerCount}`);

    console.log('\n✅ Registration verification completed!');

    return {
      totalTestUsers: testUsers.length,
      individualOperators,
      businessOperators,
      customerCount,
      schemaClean: true
    };

  } catch (error) {
    console.error('❌ Verification failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run verification if called directly
if (require.main === module) {
  verifyRegistrations()
    .then((result) => {
      console.log('Verification result:', result);
      process.exit(0);
    })
    .catch((error) => {
      console.error('Verification error:', error);
      process.exit(1);
    });
}

module.exports = { verifyRegistrations };
