const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createNoItineraryService() {
  try {
    console.log('🎯 Creating service with no itinerary for testing...');

    // Get a provider to assign service to
    const provider = await prisma.provider.findFirst({
      where: { isActive: true }
    });

    if (!provider) {
      throw new Error('No active provider found. Please seed providers first.');
    }

    // Get a service type
    const serviceType = await prisma.serviceType.findFirst({
      where: { isActive: true }
    });

    if (!serviceType) {
      throw new Error('No active service type found. Please seed service types first.');
    }

    // Create service with no itinerary
    const service = await prisma.providerService.create({
      data: {
        id: 'ps_no_itinerary_test',
        providerId: provider.id,
        serviceTypeId: serviceType.id,
        name: 'No Itinerary Test Service',
        description: 'Test service to verify default itinerary handling',
        basePrice: 75.00,
        maxCapacity: 8,
        includedItems: ['Basic equipment', 'Guide'],
        itinerary: null, // Explicitly no itinerary
        isActive: true
      }
    });

    console.log(`✅ Created service: ${service.name} (ID: ${service.id})`);
    console.log('📋 Service has no itinerary data - perfect for testing default message');

  } catch (error) {
    console.error('❌ Error creating no-itinerary service:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

createNoItineraryService();
