const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createPackageTestServices() {
  try {
    console.log('Creating test services for package scenarios...');

    // Find an existing provider
    const provider = await prisma.provider.findFirst({
      where: { id: 'provider_coral_divers' }
    });

    if (!provider) {
      console.error('Provider not found');
      return;
    }

    // Find service type
    const serviceType = await prisma.serviceType.findFirst({
      where: { code: 'passenger_transport' }
    });

    if (!serviceType) {
      console.error('Service type not found');
      return;
    }

    // Create package types
    const standardPackageType = await prisma.packageType.upsert({
      where: { code: 'STANDARD' },
      update: {},
      create: {
        code: 'STANDARD',
        name: 'Standard Package',
        description: 'Basic service package'
      }
    });

    const premiumPackageType = await prisma.packageType.upsert({
      where: { code: 'PREMIUM' },
      update: {},
      create: {
        code: 'PREMIUM',
        name: 'Premium Package',
        description: 'Enhanced service package'
      }
    });

    // Scenario 2: Package-only service (no age pricing)
    const packageOnlyService = await prisma.providerService.create({
      data: {
        id: 'ps_package_only_test',
        name: 'Package Only Test Service',
        description: 'Test service with packages but no age pricing',
        basePrice: '100',
        maxCapacity: 20,
        duration: 240,
        providerId: provider.id,
        serviceTypeId: serviceType.id,
        servicePackages: {
          create: [
            {
              basePrice: '100',
              packageTypeId: standardPackageType.id,
              includedItems: ['Transportation', 'Professional guide', 'Safety equipment'],
              excludedItems: []
            },
            {
              basePrice: '150',
              packageTypeId: premiumPackageType.id,
              includedItems: ['Transportation', 'Professional guide', 'Safety equipment', 'Lunch', 'Snacks', 'Photos'],
              excludedItems: []
            }
          ]
        }
      }
    });

    // Scenario 4: Full variation service (packages + age pricing)
    const fullVariationService = await prisma.providerService.create({
      data: {
        id: 'ps_full_variation_test',
        name: 'Full Variation Test Service',
        description: 'Test service with both packages and age pricing',
        basePrice: '120',
        maxCapacity: 25,
        duration: 360,
        agePricing: {
          adult: 120,
          child: 84,
          senior: 108,
          toddler: 0
        },
        providerId: provider.id,
        serviceTypeId: serviceType.id,
        servicePackages: {
          create: [
            {
              basePrice: '120',
              agePricing: {
                adult: 120,
                child: 84,
                senior: 108,
                toddler: 0
              },
              packageTypeId: standardPackageType.id,
              includedItems: ['Transportation', 'Professional guide', 'Safety equipment'],
              excludedItems: []
            },
            {
              basePrice: '180',
              agePricing: {
                adult: 180,
                child: 126,
                senior: 162,
                toddler: 0
              },
              packageTypeId: premiumPackageType.id,
              includedItems: ['Transportation', 'Professional guide', 'Safety equipment', 'Lunch', 'Snacks', 'Photos', 'Premium drinks'],
              excludedItems: []
            }
          ]
        }
      }
    });

    console.log('✅ Created test services:');
    console.log(`- Scenario 2 (Package-only): ${packageOnlyService.id}`);
    console.log(`- Scenario 4 (Full variation): ${fullVariationService.id}`);

  } catch (error) {
    console.error('Error creating test services:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createPackageTestServices();
