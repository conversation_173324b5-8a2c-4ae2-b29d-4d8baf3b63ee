const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedPricingPackages() {
  try {
    console.log('🎯 Seeding dynamic pricing packages system...');

    // Insert default package types
    console.log('📦 Creating package types...');
    await prisma.packageType.createMany({
      data: [
        {
          id: 'pkg_standard',
          name: 'Standard Package',
          code: 'STANDARD',
          description: 'Essential experience with basic inclusions',
          isDefault: true,
          sortOrder: 1,
          isActive: true
        },
        {
          id: 'pkg_premium',
          name: 'Premium Package',
          code: 'PREMIUM',
          description: 'Enhanced experience with additional amenities and services',
          isDefault: false,
          sortOrder: 2,
          isActive: true
        },
        {
          id: 'pkg_luxury',
          name: 'Luxury Package',
          code: 'LUXURY',
          description: 'Ultimate experience with premium services and exclusive access',
          isDefault: false,
          sortOrder: 3,
          isActive: true
        }
      ],
      skipDuplicates: true
    });

    // Insert default age categories
    console.log('👥 Creating age categories...');
    await prisma.ageCategory.createMany({
      data: [
        {
          id: 'age_adult',
          name: 'Adult',
          code: 'ADULT',
          description: 'Adult pricing (18+ years)',
          minAge: 18,
          maxAge: null,
          sortOrder: 1,
          isActive: true
        },
        {
          id: 'age_child',
          name: 'Child',
          code: 'CHILD',
          description: 'Child pricing (3-17 years)',
          minAge: 3,
          maxAge: 17,
          sortOrder: 2,
          isActive: true
        },
        {
          id: 'age_toddler',
          name: 'Toddler',
          code: 'TODDLER',
          description: 'Toddler pricing (0-2 years)',
          minAge: 0,
          maxAge: 2,
          sortOrder: 3,
          isActive: true
        },
        {
          id: 'age_senior',
          name: 'Senior',
          code: 'SENIOR',
          description: 'Senior citizen pricing (65+ years)',
          minAge: 65,
          maxAge: null,
          sortOrder: 4,
          isActive: true
        }
      ],
      skipDuplicates: true
    });

    // Get existing services to create packages for
    const services = await prisma.providerService.findMany({
      where: { isActive: true },
      take: 3 // Start with first 3 services
    });

    console.log(`💰 Creating service packages for ${services.length} services...`);

    for (const service of services) {
      // Create Standard Package
      await prisma.servicePackage.create({
        data: {
          id: `sp_${service.id}_standard`,
          serviceId: service.id,
          packageTypeId: 'pkg_standard',
          basePrice: service.basePrice,
          agePricing: service.agePricing,
          priceModifier: 1.0,
          includedItems: service.includedItems || [],
          excludedItems: [],
          isActive: true
        }
      });

      // Create Premium Package (25% more expensive)
      const premiumPrice = parseFloat(service.basePrice) * 1.25;
      const premiumAgePricing = service.agePricing ? {
        adult: parseFloat(service.agePricing.adult || service.basePrice) * 1.25,
        child: parseFloat(service.agePricing.child || service.basePrice * 0.7) * 1.25,
        toddler: service.agePricing.toddler || 0
      } : null;

      await prisma.servicePackage.create({
        data: {
          id: `sp_${service.id}_premium`,
          serviceId: service.id,
          packageTypeId: 'pkg_premium',
          basePrice: premiumPrice,
          agePricing: premiumAgePricing,
          priceModifier: 1.25,
          includedItems: [
            ...(service.includedItems || []),
            'Premium refreshments',
            'Professional photography',
            'Complimentary towels'
          ],
          excludedItems: [],
          isActive: true
        }
      });

      // Create Luxury Package (50% more expensive)
      const luxuryPrice = parseFloat(service.basePrice) * 1.5;
      const luxuryAgePricing = service.agePricing ? {
        adult: parseFloat(service.agePricing.adult || service.basePrice) * 1.5,
        child: parseFloat(service.agePricing.child || service.basePrice * 0.7) * 1.5,
        toddler: service.agePricing.toddler || 0
      } : null;

      await prisma.servicePackage.create({
        data: {
          id: `sp_${service.id}_luxury`,
          serviceId: service.id,
          packageTypeId: 'pkg_luxury',
          basePrice: luxuryPrice,
          agePricing: luxuryAgePricing,
          priceModifier: 1.5,
          includedItems: [
            ...(service.includedItems || []),
            'Luxury refreshments & gourmet meal',
            'Professional photography & videography',
            'Premium equipment',
            'Private guide',
            'Complimentary transport'
          ],
          excludedItems: [],
          isActive: true
        }
      });

      console.log(`✅ Created packages for service: ${service.name}`);
    }

    console.log('🎉 Dynamic pricing packages system seeded successfully!');
    console.log(`📦 Created 3 package types`);
    console.log(`👥 Created 4 age categories`);
    console.log(`💰 Created ${services.length * 3} service packages`);

  } catch (error) {
    console.error('❌ Error seeding pricing packages:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

seedPricingPackages();
