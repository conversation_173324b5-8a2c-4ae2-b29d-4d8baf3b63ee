const { PrismaClient } = require('@prisma/client');
const { verifyToken, sanitizeUserForToken } = require('../utils/auth');
const dataProtection = require('../utils/dataProtection');

const prisma = new PrismaClient();

/**
 * Authentication middleware for GoSea platform
 */

/**
 * Extract token from request headers
 * @param {Object} req - Express request object
 * @returns {string|null} - JWT token or null
 */
function extractToken(req) {
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // TODO: Add cookie support later
  // if (req.cookies && req.cookies.accessToken) {
  //   return req.cookies.accessToken;
  // }
  
  return null;
}

/**
 * Middleware to authenticate JWT token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
async function authenticateToken(req, res, next) {
  try {
    const token = extractToken(req);
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access token required',
        code: 'TOKEN_REQUIRED'
      });
    }

    // Verify the token
    const decoded = verifyToken(token);
    
    // Get user from database to ensure they still exist and are active
    const user = await prisma.user.findUnique({
      where: { id: decoded.id },
      include: {
        profile: true
      }
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated',
        code: 'ACCOUNT_DEACTIVATED'
      });
    }

    // Decrypt profile data before adding to request
    if (user.profile) {
      user.profile = dataProtection.decryptProfileData(user.profile);
    }

    // Add user to request object
    req.user = user;
    req.token = token;

    next();
  } catch (error) {
    if (error.message === 'Token expired') {
      return res.status(401).json({
        success: false,
        message: 'Token expired',
        code: 'TOKEN_EXPIRED'
      });
    } else if (error.message === 'Invalid token') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token',
        code: 'TOKEN_INVALID'
      });
    } else {
      console.error('Authentication error:', error);
      return res.status(500).json({
        success: false,
        message: 'Authentication failed',
        code: 'AUTH_ERROR'
      });
    }
  }
}

/**
 * Middleware to optionally authenticate token (doesn't fail if no token)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
async function optionalAuth(req, res, next) {
  try {
    const token = extractToken(req);
    
    if (!token) {
      req.user = null;
      return next();
    }

    // Verify the token
    const decoded = verifyToken(token);
    
    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: decoded.id },
      include: {
        profile: true
      }
    });

    if (user && user.isActive) {
      // Decrypt profile data before adding to request
      if (user.profile) {
        user.profile = dataProtection.decryptProfileData(user.profile);
      }
      req.user = user;
      req.token = token;
    } else {
      req.user = null;
    }
    
    next();
  } catch (error) {
    // For optional auth, we don't fail on token errors
    req.user = null;
    next();
  }
}

/**
 * Middleware to check if user has required role(s)
 * @param {...string} roles - Required roles
 * @returns {Function} - Express middleware function
 */
function requireRole(...roles) {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: roles,
        current: req.user.role
      });
    }

    next();
  };
}

/**
 * Middleware to check if user is admin
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
function requireAdmin(req, res, next) {
  return requireRole('ADMIN')(req, res, next);
}

/**
 * Middleware to check if user is boat owner
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
function requireBoatOwner(req, res, next) {
  return requireRole('BOAT_OWNER', 'ADMIN')(req, res, next);
}

/**
 * Middleware to check if user is affiliate agent
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
function requireAffiliateAgent(req, res, next) {
  return requireRole('AFFILIATE_AGENT', 'ADMIN')(req, res, next);
}

/**
 * Middleware to check if user owns the resource or is admin
 * @param {string} userIdField - Field name containing user ID (default: 'userId')
 * @returns {Function} - Express middleware function
 */
function requireOwnershipOrAdmin(userIdField = 'userId') {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    // Admin can access everything
    if (req.user.role === 'ADMIN') {
      return next();
    }

    // Check if user owns the resource
    const resourceUserId = req.params[userIdField] || req.body[userIdField];
    
    if (req.user.id !== resourceUserId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied - resource ownership required',
        code: 'OWNERSHIP_REQUIRED'
      });
    }

    next();
  };
}

/**
 * Middleware to check if email is verified
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
function requireEmailVerified(req, res, next) {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required',
      code: 'AUTH_REQUIRED'
    });
  }

  if (!req.user.emailVerified) {
    return res.status(403).json({
      success: false,
      message: 'Email verification required',
      code: 'EMAIL_VERIFICATION_REQUIRED'
    });
  }

  next();
}

/**
 * Middleware to validate session token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
async function validateSession(req, res, next) {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const sessionToken = req.headers['x-session-token'];
    
    if (!sessionToken) {
      return res.status(401).json({
        success: false,
        message: 'Session token required',
        code: 'SESSION_TOKEN_REQUIRED'
      });
    }

    // Check if session exists and is valid
    const session = await prisma.userSession.findUnique({
      where: { sessionToken },
      include: { user: true }
    });

    if (!session) {
      return res.status(401).json({
        success: false,
        message: 'Invalid session',
        code: 'INVALID_SESSION'
      });
    }

    if (session.expiresAt < new Date()) {
      // Clean up expired session
      await prisma.userSession.delete({
        where: { id: session.id }
      });
      
      return res.status(401).json({
        success: false,
        message: 'Session expired',
        code: 'SESSION_EXPIRED'
      });
    }

    if (session.userId !== req.user.id) {
      return res.status(401).json({
        success: false,
        message: 'Session user mismatch',
        code: 'SESSION_USER_MISMATCH'
      });
    }

    // Update last active time
    await prisma.userSession.update({
      where: { id: session.id },
      data: { lastActiveAt: new Date() }
    });

    req.session = session;
    next();
  } catch (error) {
    console.error('Session validation error:', error);
    return res.status(500).json({
      success: false,
      message: 'Session validation failed',
      code: 'SESSION_VALIDATION_ERROR'
    });
  }
}

module.exports = {
  authenticateToken,
  optionalAuth,
  requireRole,
  requireAdmin,
  requireBoatOwner,
  requireAffiliateAgent,
  requireOwnershipOrAdmin,
  requireEmailVerified,
  validateSession,
  extractToken
};
