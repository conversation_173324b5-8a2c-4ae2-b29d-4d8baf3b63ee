const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'GoSea Platform API',
      version: '1.0.0',
      description: 'Comprehensive API documentation for the GoSea boat booking platform',
      contact: {
        name: 'GoSea API Support',
        email: '<EMAIL>',
        url: 'https://gosea.my/support'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: 'http://localhost:5001',
        description: 'Development server'
      },
      {
        url: 'https://api.gosea.my',
        description: 'Production server'
      }
    ],
    tags: [
      {
        name: 'System',
        description: 'System health and information endpoints'
      },
      {
        name: 'Authentication',
        description: 'User authentication and authorization'
      },
      {
        name: 'Services',
        description: 'Boat and marine services management'
      },
      {
        name: 'Bookings',
        description: 'Booking creation and management'
      },
      {
        name: 'Payments',
        description: 'Payment processing and receipts'
      },
      {
        name: 'Boats',
        description: 'Boat listing and details'
      },
      {
        name: 'Providers',
        description: 'Service provider management'
      },
      {
        name: 'Jetties',
        description: 'Jetty and departure point information'
      },
      {
        name: 'Search',
        description: 'Search and filter endpoints'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token obtained from /api/auth/login'
        }
      },
      schemas: {
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Unique user identifier'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'User email address'
            },
            role: {
              type: 'string',
              enum: ['CUSTOMER', 'BOAT_OWNER', 'AFFILIATE_AGENT', 'ADMIN'],
              description: 'User role in the system'
            },
            profile: {
              $ref: '#/components/schemas/UserProfile'
            },
            isEmailVerified: {
              type: 'boolean',
              description: 'Whether user email is verified'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Account creation timestamp'
            }
          }
        },
        UserProfile: {
          type: 'object',
          properties: {
            firstName: {
              type: 'string',
              description: 'User first name'
            },
            lastName: {
              type: 'string',
              description: 'User last name'
            },
            phone: {
              type: 'string',
              description: 'User phone number'
            },
            dateOfBirth: {
              type: 'string',
              format: 'date',
              description: 'User date of birth'
            },
            nationality: {
              type: 'string',
              description: 'User nationality'
            }
          }
        },
        Boat: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Unique boat identifier'
            },
            name: {
              type: 'string',
              description: 'Boat name'
            },
            description: {
              type: 'string',
              description: 'Boat description'
            },
            serviceType: {
              type: 'string',
              enum: ['SNORKELING', 'PASSENGER_BOAT'],
              description: 'Type of service offered'
            },
            location: {
              type: 'string',
              enum: ['REDANG_ISLAND', 'PERHENTIAN_ISLAND'],
              description: 'Operating location'
            },
            capacity: {
              type: 'integer',
              description: 'Maximum passenger capacity'
            },
            basePrice: {
              type: 'number',
              format: 'decimal',
              description: 'Base price per person'
            },
            photos: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/BoatPhoto'
              }
            },
            packages: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/BoatPackage'
              }
            },
            amenities: {
              type: 'array',
              items: {
                type: 'string'
              }
            }
          }
        },
        BoatPhoto: {
          type: 'object',
          properties: {
            id: {
              type: 'string'
            },
            url: {
              type: 'string',
              format: 'uri'
            },
            alt: {
              type: 'string'
            },
            order: {
              type: 'integer'
            }
          }
        },
        BoatPackage: {
          type: 'object',
          properties: {
            id: {
              type: 'string'
            },
            name: {
              type: 'string'
            },
            description: {
              type: 'string'
            },
            price: {
              type: 'number',
              format: 'decimal'
            },
            duration: {
              type: 'string'
            }
          }
        },
        Booking: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Unique booking identifier'
            },
            boatId: {
              type: 'string',
              description: 'Associated boat ID'
            },
            customerId: {
              type: 'string',
              description: 'Customer user ID'
            },
            bookingDate: {
              type: 'string',
              format: 'date',
              description: 'Date of the booking'
            },
            timeSlot: {
              type: 'string',
              description: 'Time slot for the booking'
            },
            adultCount: {
              type: 'integer',
              description: 'Number of adult passengers'
            },
            childCount: {
              type: 'integer',
              description: 'Number of child passengers'
            },
            infantCount: {
              type: 'integer',
              description: 'Number of infant passengers'
            },
            totalAmount: {
              type: 'number',
              format: 'decimal',
              description: 'Total booking amount'
            },
            status: {
              type: 'string',
              enum: ['PENDING', 'CONFIRMED', 'CANCELLED', 'COMPLETED'],
              description: 'Booking status'
            },
            paymentStatus: {
              type: 'string',
              enum: ['PENDING', 'PAID', 'FAILED', 'REFUNDED'],
              description: 'Payment status'
            }
          }
        },
        Error: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false
            },
            message: {
              type: 'string',
              description: 'Error message'
            },
            code: {
              type: 'string',
              description: 'Error code'
            },
            timestamp: {
              type: 'string',
              format: 'date-time'
            }
          }
        },
        SuccessResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true
            },
            message: {
              type: 'string',
              description: 'Success message'
            },
            data: {
              type: 'object',
              description: 'Response data'
            }
          }
        },
        Service: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Unique service identifier'
            },
            name: {
              type: 'string',
              description: 'Service name'
            },
            description: {
              type: 'string',
              description: 'Service description'
            },
            basePrice: {
              type: 'number',
              format: 'decimal',
              description: 'Base price per person'
            },
            agePricing: {
              type: 'object',
              properties: {
                adult: { type: 'number' },
                child: { type: 'number' },
                toddler: { type: 'number' },
                senior: { type: 'number' }
              },
              description: 'Age-based pricing structure'
            },
            duration: {
              type: 'integer',
              description: 'Service duration in minutes'
            },
            maxCapacity: {
              type: 'integer',
              description: 'Maximum capacity'
            },
            includedItems: {
              type: 'array',
              items: { type: 'string' },
              description: 'Items included in service'
            },
            itinerary: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/ItineraryItem'
              },
              description: 'Service itinerary'
            },
            packages: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/ServicePackage'
              },
              description: 'Available pricing packages'
            },
            provider: {
              $ref: '#/components/schemas/Provider'
            },
            serviceType: {
              $ref: '#/components/schemas/ServiceType'
            }
          }
        },
        ItineraryItem: {
          type: 'object',
          properties: {
            time: {
              type: 'string',
              description: 'Time of activity'
            },
            activity: {
              type: 'string',
              description: 'Activity description'
            },
            location: {
              type: 'string',
              description: 'Activity location'
            }
          }
        },
        ServicePackage: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Package identifier'
            },
            packageType: {
              $ref: '#/components/schemas/PackageType'
            },
            basePrice: {
              type: 'number',
              format: 'decimal',
              description: 'Package base price'
            },
            agePricing: {
              type: 'object',
              properties: {
                adult: { type: 'number' },
                child: { type: 'number' },
                toddler: { type: 'number' }
              }
            },
            priceModifier: {
              type: 'number',
              format: 'decimal',
              description: 'Price multiplier'
            },
            includedItems: {
              type: 'array',
              items: { type: 'string' },
              description: 'Items included in package'
            },
            excludedItems: {
              type: 'array',
              items: { type: 'string' },
              description: 'Items excluded from package'
            }
          }
        },
        PackageType: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Package type identifier'
            },
            name: {
              type: 'string',
              description: 'Package type name'
            },
            code: {
              type: 'string',
              enum: ['STANDARD', 'PREMIUM', 'LUXURY'],
              description: 'Package type code'
            },
            description: {
              type: 'string',
              description: 'Package type description'
            },
            isDefault: {
              type: 'boolean',
              description: 'Whether this is the default package'
            }
          }
        },
        Provider: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Provider identifier'
            },
            displayName: {
              type: 'string',
              description: 'Provider display name'
            },
            companyName: {
              type: 'string',
              description: 'Company name'
            },
            rating: {
              type: 'number',
              format: 'float',
              description: 'Provider rating'
            },
            reviewCount: {
              type: 'integer',
              description: 'Number of reviews'
            },
            isVerified: {
              type: 'boolean',
              description: 'Whether provider is verified'
            }
          }
        },
        ServiceType: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Service type identifier'
            },
            name: {
              type: 'string',
              description: 'Service type name'
            },
            category: {
              $ref: '#/components/schemas/ServiceCategory'
            }
          }
        },
        ServiceCategory: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Category identifier'
            },
            name: {
              type: 'string',
              description: 'Category name'
            },
            description: {
              type: 'string',
              description: 'Category description'
            }
          }
        },
        Jetty: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Jetty identifier'
            },
            name: {
              type: 'string',
              description: 'Jetty name'
            },
            location: {
              type: 'string',
              description: 'Jetty location'
            },
            coordinates: {
              type: 'object',
              properties: {
                latitude: { type: 'number' },
                longitude: { type: 'number' }
              }
            },
            facilities: {
              type: 'array',
              items: { type: 'string' },
              description: 'Available facilities'
            }
          }
        },
        Payment: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Payment identifier'
            },
            bookingId: {
              type: 'string',
              description: 'Associated booking ID'
            },
            amount: {
              type: 'number',
              format: 'decimal',
              description: 'Payment amount'
            },
            currency: {
              type: 'string',
              enum: ['MYR', 'USD'],
              description: 'Payment currency'
            },
            status: {
              type: 'string',
              enum: ['PENDING', 'COMPLETED', 'FAILED', 'REFUNDED'],
              description: 'Payment status'
            },
            paymentMethod: {
              type: 'string',
              description: 'Payment method used'
            },
            transactionId: {
              type: 'string',
              description: 'External transaction ID'
            }
          }
        },
        PaymentIntent: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Payment intent identifier'
            },
            clientSecret: {
              type: 'string',
              description: 'Client secret for payment confirmation'
            },
            amount: {
              type: 'number',
              description: 'Payment amount in cents'
            },
            currency: {
              type: 'string',
              description: 'Payment currency'
            }
          }
        },
        PromoCode: {
          type: 'object',
          properties: {
            code: {
              type: 'string',
              description: 'Promo code'
            },
            discountType: {
              type: 'string',
              enum: ['PERCENTAGE', 'FIXED_AMOUNT'],
              description: 'Type of discount'
            },
            discountValue: {
              type: 'number',
              description: 'Discount value'
            },
            isValid: {
              type: 'boolean',
              description: 'Whether the promo code is valid'
            }
          }
        },
        Destination: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Destination identifier'
            },
            name: {
              type: 'string',
              description: 'Destination name'
            },
            description: {
              type: 'string',
              description: 'Destination description'
            },
            coordinates: {
              type: 'object',
              properties: {
                latitude: { type: 'number' },
                longitude: { type: 'number' }
              }
            },
            state: {
              type: 'string',
              description: 'State where destination is located'
            }
          }
        },
        Route: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Route identifier'
            },
            departureJetty: {
              $ref: '#/components/schemas/Jetty'
            },
            destination: {
              $ref: '#/components/schemas/Destination'
            },
            distance: {
              type: 'number',
              description: 'Route distance in kilometers'
            },
            estimatedDuration: {
              type: 'integer',
              description: 'Estimated duration in minutes'
            }
          }
        },
        ServiceAssignment: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Assignment identifier'
            },
            serviceId: {
              type: 'string',
              description: 'Service identifier'
            },
            boatId: {
              type: 'string',
              description: 'Boat identifier'
            },
            isPrimary: {
              type: 'boolean',
              description: 'Whether this is the primary boat for the service'
            },
            isActive: {
              type: 'boolean',
              description: 'Whether the assignment is active'
            },
            assignedAt: {
              type: 'string',
              format: 'date-time',
              description: 'Assignment timestamp'
            }
          }
        },
        ProviderOperatingArea: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Operating area identifier'
            },
            providerId: {
              type: 'string',
              description: 'Provider identifier'
            },
            jettyId: {
              type: 'string',
              description: 'Jetty identifier'
            },
            isActive: {
              type: 'boolean',
              description: 'Whether the operating area is active'
            }
          }
        },
        AgeRange: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Age range identifier'
            },
            category: {
              type: 'string',
              enum: ['ADULT', 'CHILD', 'TODDLER', 'SENIOR'],
              description: 'Age category'
            },
            minAge: {
              type: 'integer',
              description: 'Minimum age'
            },
            maxAge: {
              type: 'integer',
              description: 'Maximum age'
            },
            price: {
              type: 'number',
              format: 'decimal',
              description: 'Price for this age range'
            }
          }
        },
        TimeSlot: {
          type: 'object',
          properties: {
            time: {
              type: 'string',
              description: 'Time slot (e.g., "09:00")',
              example: '09:00'
            },
            available: {
              type: 'boolean',
              description: 'Whether the time slot is available'
            },
            capacity: {
              type: 'integer',
              description: 'Available capacity for this time slot'
            }
          }
        },
        SearchFilters: {
          type: 'object',
          properties: {
            serviceCategories: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/ServiceCategory'
              }
            },
            jetties: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/Jetty'
              }
            },
            destinations: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/Destination'
              }
            },
            priceRange: {
              type: 'object',
              properties: {
                min: { type: 'number' },
                max: { type: 'number' }
              }
            }
          }
        },
        PostcodeResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true
            },
            data: {
              type: 'object',
              properties: {
                city: {
                  type: 'string',
                  description: 'City name',
                  example: 'Alor Setar'
                },
                state: {
                  type: 'string',
                  description: 'State name',
                  example: 'Kedah'
                }
              }
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: [
    './src/routes/*.js',
    './src/index.js'
  ]
};

const specs = swaggerJsdoc(options);

module.exports = {
  specs,
  swaggerUi,
  swaggerOptions: {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'GoSea API Documentation'
  }
};
