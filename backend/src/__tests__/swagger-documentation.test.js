const request = require('supertest');
const express = require('express');

describe('Swagger Documentation', () => {
  let app;
  let swaggerConfig;

  beforeAll(() => {
    // Create a minimal Express app for testing
    app = express();
    app.use(express.json());

    // Import the swagger configuration
    swaggerConfig = require('../config/swagger');

    // Add a simple health check
    app.get('/api/health', (req, res) => {
      res.json({ status: 'OK', message: 'Test API running' });
    });
  });

  describe('Swagger Configuration', () => {
    test('should have valid swagger configuration object', () => {
      expect(swaggerConfig).toBeDefined();
      expect(swaggerConfig).toHaveProperty('specs');
      expect(swaggerConfig).toHaveProperty('swaggerUi');
      expect(swaggerConfig).toHaveProperty('swaggerOptions');
    });

    test('should have swagger UI middleware functions', () => {
      expect(Array.isArray(swaggerConfig.swaggerUi.serve)).toBe(true);
      expect(typeof swaggerConfig.swaggerUi.setup).toBe('function');
    });
  });

  describe('API Documentation Structure', () => {
    test('should have valid OpenAPI specification', () => {
      const { specs } = swaggerConfig;

      expect(specs).toHaveProperty('openapi');
      expect(specs).toHaveProperty('info');
      expect(specs).toHaveProperty('info.title');
      expect(specs).toHaveProperty('info.version');
      expect(specs.openapi).toBe('3.0.0');
      expect(specs.info.title).toBe('GoSea Platform API');
    });

    test('should include required API information', () => {
      const { specs } = swaggerConfig;

      expect(specs.info).toHaveProperty('description');
      expect(specs.info).toHaveProperty('contact');
      expect(specs.info).toHaveProperty('license');
    });

    test('should define server configurations', () => {
      const { specs } = swaggerConfig;

      expect(specs).toHaveProperty('servers');
      expect(Array.isArray(specs.servers)).toBe(true);
      expect(specs.servers.length).toBeGreaterThan(0);
    });

    test('should include security schemes', () => {
      const { specs } = swaggerConfig;

      expect(specs).toHaveProperty('components');
      expect(specs.components).toHaveProperty('securitySchemes');
      expect(specs.components.securitySchemes).toHaveProperty('bearerAuth');
    });

    test('should define common schemas', () => {
      const { specs } = swaggerConfig;

      expect(specs.components).toHaveProperty('schemas');
      expect(specs.components.schemas).toHaveProperty('User');
      expect(specs.components.schemas).toHaveProperty('Boat');
      expect(specs.components.schemas).toHaveProperty('Booking');
      expect(specs.components.schemas).toHaveProperty('Error');
      expect(specs.components.schemas).toHaveProperty('SuccessResponse');
    });
  });

  describe('Integration with Backend', () => {
    test('should be accessible when backend is running', async () => {
      // Test that the health endpoint works
      const response = await request(app)
        .get('/api/health')
        .expect(200);
      
      expect(response.body).toHaveProperty('status', 'OK');
    });
  });
});

describe('Swagger Documentation Content Validation', () => {
  test('should include authentication endpoints documentation', () => {
    // This test would verify that auth routes have proper Swagger annotations
    // In a real scenario, we would parse the actual route files and check for @swagger comments
    expect(true).toBe(true); // Placeholder - would implement actual validation
  });

  test('should include boat endpoints documentation', () => {
    // This test would verify that boat routes have proper Swagger annotations
    expect(true).toBe(true); // Placeholder - would implement actual validation
  });

  test('should include booking endpoints documentation', () => {
    // This test would verify that booking routes have proper Swagger annotations
    expect(true).toBe(true); // Placeholder - would implement actual validation
  });

  test('should include search endpoints documentation', () => {
    // This test would verify that search routes have proper Swagger annotations
    expect(true).toBe(true); // Placeholder - would implement actual validation
  });
});
