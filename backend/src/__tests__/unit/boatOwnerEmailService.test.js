const EmailService = require('../../services/emailService');
const nodemailer = require('nodemailer');

// Mock nodemailer
jest.mock('nodemailer');

describe('Boat Owner Email Service Unit Tests', () => {
  let emailService;
  let mockTransporter;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();

    // Create mock transporter
    mockTransporter = {
      sendMail: jest.fn().mockResolvedValue({
        messageId: 'mock_message_id_123',
        accepted: ['<EMAIL>'],
        rejected: []
      })
    };

    // Mock nodemailer.createTransporter to return our mock
    nodemailer.createTransporter = jest.fn().mockReturnValue(mockTransporter);

    // Create fresh email service instance
    emailService = new EmailService();
    emailService.transporter = mockTransporter;
  });

  describe('sendBoatOwnerVerificationEmail', () => {
    const mockData = {
      email: '<EMAIL>',
      firstName: 'John',
      verificationToken: 'verification_token_123'
    };

    test('should send boat owner verification email successfully', async () => {
      const result = await emailService.sendBoatOwnerVerificationEmail(
        mockData.email,
        mockData.firstName,
        mockData.verificationToken
      );

      expect(result.success).toBe(true);
      expect(result.messageId).toBe('mock_message_id_123');
      expect(result.verificationToken).toBe('verification_token_123');

      expect(mockTransporter.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          from: expect.objectContaining({
            name: 'GoSea Platform',
            address: expect.any(String)
          }),
          to: '<EMAIL>',
          subject: 'Verify Your Boat Owner Account - GoSea Platform',
          html: expect.stringContaining('Hello John!'),
          text: expect.stringContaining('Hello John!')
        })
      );
    });

    test('should include verification URL in email content', async () => {
      await emailService.sendBoatOwnerVerificationEmail(
        mockData.email,
        mockData.firstName,
        mockData.verificationToken
      );

      const sendMailCall = mockTransporter.sendMail.mock.calls[0][0];
      const expectedUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/verify-boat-owner-email?token=${mockData.verificationToken}`;

      expect(sendMailCall.html).toContain(expectedUrl);
      expect(sendMailCall.text).toContain(expectedUrl);
    });

    test('should include business-specific content', async () => {
      await emailService.sendBoatOwnerVerificationEmail(
        mockData.email,
        mockData.firstName,
        mockData.verificationToken
      );

      const sendMailCall = mockTransporter.sendMail.mock.calls[0][0];

      expect(sendMailCall.html).toContain('boat owner');
      expect(sendMailCall.html).toContain('business');
      expect(sendMailCall.html).toContain('admin team');
      expect(sendMailCall.html).toContain('1-2 business days');
    });

    test('should handle email sending failure', async () => {
      const errorMessage = 'SMTP server unavailable';
      mockTransporter.sendMail.mockRejectedValue(new Error(errorMessage));

      await expect(emailService.sendBoatOwnerVerificationEmail(
        mockData.email,
        mockData.firstName,
        mockData.verificationToken
      )).rejects.toThrow('Failed to send boat owner verification email');
    });

    test('should use correct email template structure', async () => {
      await emailService.sendBoatOwnerVerificationEmail(
        mockData.email,
        mockData.firstName,
        mockData.verificationToken
      );

      const sendMailCall = mockTransporter.sendMail.mock.calls[0][0];

      // Check HTML template structure
      expect(sendMailCall.html).toContain('<!DOCTYPE html>');
      expect(sendMailCall.html).toContain('⚓ GoSea Platform');
      expect(sendMailCall.html).toContain('Boat Owner Registration');
      expect(sendMailCall.html).toContain('class="header"');
      expect(sendMailCall.html).toContain('class="content"');
      expect(sendMailCall.html).toContain('class="footer"');

      // Check for business-specific styling
      expect(sendMailCall.html).toContain('background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)');
    });
  });

  describe('sendAdminApprovalNotification', () => {
    const mockNotificationData = {
      adminEmail: '<EMAIL>',
      boatOwner: {
        id: 'user_123',
        name: 'John Doe',
        email: '<EMAIL>',
        companyName: 'Marine Adventures Ltd',
        brn: 'BRN123456',
        registrationDate: new Date('2024-01-15')
      },
      approvalLink: 'http://localhost:3000/admin/dashboard?tab=boat-owners&filter=pending&highlight=user_123'
    };

    test('should send admin notification email successfully', async () => {
      const result = await emailService.sendAdminApprovalNotification(mockNotificationData);

      expect(result.success).toBe(true);
      expect(result.messageId).toBe('mock_message_id_123');

      expect(mockTransporter.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          from: expect.objectContaining({
            name: 'GoSea Platform Admin',
            address: expect.any(String)
          }),
          to: '<EMAIL>',
          subject: 'New Boat Owner Registration - Approval Required',
          html: expect.stringContaining('John Doe'),
          text: expect.stringContaining('John Doe')
        })
      );
    });

    test('should include all boat owner details in notification', async () => {
      await emailService.sendAdminApprovalNotification(mockNotificationData);

      const sendMailCall = mockTransporter.sendMail.mock.calls[0][0];

      expect(sendMailCall.html).toContain('John Doe');
      expect(sendMailCall.html).toContain('<EMAIL>');
      expect(sendMailCall.html).toContain('Marine Adventures Ltd');
      expect(sendMailCall.html).toContain('BRN123456');
      expect(sendMailCall.html).toContain('2024-01-15');
    });

    test('should include approval link in notification', async () => {
      await emailService.sendAdminApprovalNotification(mockNotificationData);

      const sendMailCall = mockTransporter.sendMail.mock.calls[0][0];

      expect(sendMailCall.html).toContain(mockNotificationData.approvalLink);
      expect(sendMailCall.text).toContain(mockNotificationData.approvalLink);
    });

    test('should handle notification sending failure', async () => {
      mockTransporter.sendMail.mockRejectedValue(new Error('Network error'));

      await expect(emailService.sendAdminApprovalNotification(mockNotificationData))
        .rejects.toThrow('Failed to send admin approval notification');
    });
  });

  describe('sendBoatOwnerApprovalEmail', () => {
    const mockApprovalData = {
      email: '<EMAIL>',
      firstName: 'John',
      companyName: 'Marine Adventures Ltd'
    };

    test('should send approval email successfully', async () => {
      const result = await emailService.sendBoatOwnerApprovalEmail(
        mockApprovalData.email,
        mockApprovalData.firstName,
        mockApprovalData.companyName
      );

      expect(result.success).toBe(true);
      expect(result.messageId).toBe('mock_message_id_123');

      expect(mockTransporter.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Welcome to GoSea Platform - Account Approved! 🎉',
          html: expect.stringContaining('John'),
          text: expect.stringContaining('John')
        })
      );
    });

    test('should include welcome content and next steps', async () => {
      await emailService.sendBoatOwnerApprovalEmail(
        mockApprovalData.email,
        mockApprovalData.firstName,
        mockApprovalData.companyName
      );

      const sendMailCall = mockTransporter.sendMail.mock.calls[0][0];

      expect(sendMailCall.html).toContain('approved');
      expect(sendMailCall.html).toContain('welcome');
      expect(sendMailCall.html).toContain('sign in');
      expect(sendMailCall.html).toContain('dashboard');
      expect(sendMailCall.html).toContain('Marine Adventures Ltd');
    });

    test('should include sign-in link', async () => {
      await emailService.sendBoatOwnerApprovalEmail(
        mockApprovalData.email,
        mockApprovalData.firstName,
        mockApprovalData.companyName
      );

      const sendMailCall = mockTransporter.sendMail.mock.calls[0][0];
      const expectedSignInUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/boat-owner/signin`;

      expect(sendMailCall.html).toContain('Sign In to Your Account');
    });
  });

  describe('sendBoatOwnerRejectionEmail', () => {
    const mockRejectionData = {
      email: '<EMAIL>',
      firstName: 'John',
      companyName: 'Marine Adventures Ltd',
      rejectionReason: 'Incomplete business documentation. Please provide valid business registration certificate.'
    };

    test('should send rejection email successfully', async () => {
      const result = await emailService.sendBoatOwnerRejectionEmail(
        mockRejectionData.email,
        mockRejectionData.firstName,
        mockRejectionData.companyName,
        mockRejectionData.rejectionReason
      );

      expect(result.success).toBe(true);
      expect(result.messageId).toBe('mock_message_id_123');

      expect(mockTransporter.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'GoSea Platform Application Update',
          html: expect.stringContaining('John'),
          text: expect.stringContaining('John')
        })
      );
    });

    test('should include rejection reason', async () => {
      await emailService.sendBoatOwnerRejectionEmail(
        mockRejectionData.email,
        mockRejectionData.firstName,
        mockRejectionData.companyName,
        mockRejectionData.rejectionReason
      );

      const sendMailCall = mockTransporter.sendMail.mock.calls[0][0];

      expect(sendMailCall.html).toContain('Incomplete business documentation');
      expect(sendMailCall.html).toContain('business registration certificate');
      expect(sendMailCall.text).toContain('Incomplete business documentation');
    });

    test('should include re-application instructions', async () => {
      await emailService.sendBoatOwnerRejectionEmail(
        mockRejectionData.email,
        mockRejectionData.firstName,
        mockRejectionData.companyName,
        mockRejectionData.rejectionReason
      );

      const sendMailCall = mockTransporter.sendMail.mock.calls[0][0];

      expect(sendMailCall.html).toContain('re-apply');
      expect(sendMailCall.html).toContain('register again');
      expect(sendMailCall.html).toContain('correct the issues');
    });
  });

  describe('Email Template Generation', () => {
    test('should generate boat owner verification email template correctly', () => {
      const firstName = 'John';
      const verificationUrl = 'http://localhost:3000/verify-boat-owner-email?token=test123';
      const email = '<EMAIL>';

      const htmlTemplate = emailService.getBoatOwnerVerificationEmailTemplate(firstName, verificationUrl, email);

      expect(htmlTemplate).toContain('Hello John!');
      expect(htmlTemplate).toContain(verificationUrl);
      expect(htmlTemplate).toContain(email);
      expect(htmlTemplate).toContain('boat owner');
      expect(htmlTemplate).toContain('business account');
      expect(htmlTemplate).toContain('admin team');
    });

    test('should generate admin notification template correctly', () => {
      const boatOwner = {
        name: 'John Doe',
        email: '<EMAIL>',
        companyName: 'Marine Adventures',
        brn: 'BRN123',
        registrationDate: new Date('2024-01-15')
      };
      const approvalLink = 'http://localhost:3000/admin/dashboard?tab=boat-owners';

      const htmlTemplate = emailService.getAdminApprovalNotificationTemplate(boatOwner, approvalLink);

      expect(htmlTemplate).toContain('New Boat Owner Registration');
      expect(htmlTemplate).toContain('John Doe');
      expect(htmlTemplate).toContain('<EMAIL>');
      expect(htmlTemplate).toContain('Marine Adventures');
      expect(htmlTemplate).toContain('BRN123');
      expect(htmlTemplate).toContain(approvalLink);
    });

    test('should generate plain text versions correctly', () => {
      const firstName = 'John';
      const verificationUrl = 'http://localhost:3000/verify-boat-owner-email?token=test123';

      const textTemplate = emailService.getBoatOwnerVerificationEmailText(firstName, verificationUrl);

      expect(textTemplate).toContain('Hello John!');
      expect(textTemplate).toContain(verificationUrl);
      expect(textTemplate).toContain('boat owner');
      expect(textTemplate).not.toContain('<html>');
      expect(textTemplate).not.toContain('<div>');
    });
  });

  describe('Email Configuration', () => {
    test('should initialize with MailHog for development', () => {
      process.env.USE_REAL_EMAIL = 'false';
      
      const newEmailService = new EmailService();

      expect(nodemailer.createTransporter).toHaveBeenCalledWith(
        expect.objectContaining({
          host: expect.any(String),
          port: expect.any(Number),
          secure: false,
          auth: false
        })
      );
    });

    test('should initialize with Gmail for production', () => {
      process.env.USE_REAL_EMAIL = 'true';
      process.env.GMAIL_USER = '<EMAIL>';
      process.env.GMAIL_APP_PASSWORD = 'app_password';
      
      const newEmailService = new EmailService();

      expect(nodemailer.createTransporter).toHaveBeenCalledWith(
        expect.objectContaining({
          service: 'gmail',
          auth: expect.objectContaining({
            user: '<EMAIL>',
            pass: 'app_password'
          })
        })
      );
    });

    test('should handle transporter initialization failure', () => {
      nodemailer.createTransporter = jest.fn().mockImplementation(() => {
        throw new Error('SMTP configuration error');
      });

      // Should not throw error during initialization
      expect(() => new EmailService()).not.toThrow();
    });
  });

  describe('Email Content Validation', () => {
    test('should include required security elements in emails', async () => {
      await emailService.sendBoatOwnerVerificationEmail(
        '<EMAIL>',
        'John',
        'token123'
      );

      const sendMailCall = mockTransporter.sendMail.mock.calls[0][0];

      // Should include expiration warning
      expect(sendMailCall.html).toContain('24 hours');
      expect(sendMailCall.html).toContain('expire');
      
      // Should include security warning
      expect(sendMailCall.html).toContain('If you didn\'t create');
      expect(sendMailCall.html).toContain('ignore this email');
    });

    test('should use proper email formatting', async () => {
      await emailService.sendBoatOwnerVerificationEmail(
        '<EMAIL>',
        'John',
        'token123'
      );

      const sendMailCall = mockTransporter.sendMail.mock.calls[0][0];

      // Should have proper HTML structure
      expect(sendMailCall.html).toMatch(/<!DOCTYPE html>/);
      expect(sendMailCall.html).toMatch(/<html>/);
      expect(sendMailCall.html).toMatch(/<head>/);
      expect(sendMailCall.html).toMatch(/<body>/);
      expect(sendMailCall.html).toMatch(/<\/html>/);

      // Should have proper email styling
      expect(sendMailCall.html).toContain('font-family');
      expect(sendMailCall.html).toContain('max-width');
      expect(sendMailCall.html).toContain('padding');
    });

    test('should escape HTML in user-provided content', () => {
      const maliciousName = '<script>alert("xss")</script>';
      const maliciousCompany = '<img src=x onerror=alert("xss")>';
      
      const template = emailService.getBoatOwnerVerificationEmailTemplate(
        maliciousName,
        'http://example.com/verify',
        '<EMAIL>'
      );

      // Should not contain unescaped script tags
      expect(template).not.toContain('<script>alert("xss")</script>');
      expect(template).not.toContain('<img src=x onerror=alert("xss")>');
    });
  });

  describe('Error Handling', () => {
    test('should handle network timeouts gracefully', async () => {
      const timeoutError = new Error('Network timeout');
      timeoutError.code = 'ETIMEDOUT';
      mockTransporter.sendMail.mockRejectedValue(timeoutError);

      await expect(emailService.sendBoatOwnerVerificationEmail(
        '<EMAIL>',
        'John',
        'token123'
      )).rejects.toThrow('Failed to send boat owner verification email');
    });

    test('should handle authentication errors', async () => {
      const authError = new Error('Authentication failed');
      authError.code = 'EAUTH';
      mockTransporter.sendMail.mockRejectedValue(authError);

      await expect(emailService.sendAdminApprovalNotification({
        adminEmail: '<EMAIL>',
        boatOwner: { name: 'John' },
        approvalLink: 'http://example.com'
      })).rejects.toThrow('Failed to send admin approval notification');
    });

    test('should handle invalid email addresses', async () => {
      const invalidEmailError = new Error('Invalid recipient');
      invalidEmailError.code = 'EENVELOPE';
      mockTransporter.sendMail.mockRejectedValue(invalidEmailError);

      await expect(emailService.sendBoatOwnerApprovalEmail(
        'invalid-email-format',
        'John',
        'Company'
      )).rejects.toThrow('Failed to send boat owner approval email');
    });
  });
});