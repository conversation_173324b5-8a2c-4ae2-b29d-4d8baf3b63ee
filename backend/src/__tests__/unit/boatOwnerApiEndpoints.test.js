const request = require('supertest');
const express = require('express');
const { PrismaClient } = require('@prisma/client');

// Mock the AuthService
const mockAuthService = {
  registerBoatOwner: jest.fn(),
  loginBoatOwner: jest.fn(),
  verifyBoatOwnerEmail: jest.fn(),
};

// Mock the admin routes functions
const mockAdminFunctions = {
  loadPendingBoatOwners: jest.fn(),
  approveBoatOwner: jest.fn(),
  rejectBoatOwner: jest.fn(),
};

// Mock Prisma
jest.mock('@prisma/client');
jest.mock('../../services/authService', () => mockAuthService);
jest.mock('../../middleware/auth', () => ({
  authenticateToken: (req, res, next) => {
    req.user = { id: 'admin_123', role: 'ADMIN' };
    next();
  },
  requireRole: (role) => (req, res, next) => {
    if (req.user.role === role) {
      next();
    } else {
      res.status(403).json({ success: false, message: 'Forbidden' });
    }
  }
}));

describe('Boat Owner API Endpoints Unit Tests', () => {
  let app;
  let authRoutes;
  let adminRoutes;

  beforeAll(() => {
    // Create Express app for testing
    app = express();
    app.use(express.json());
    
    // Import routes
    authRoutes = require('../../routes/auth');
    adminRoutes = require('../../routes/admin');
    
    // Mount routes
    app.use('/api/auth', authRoutes);
    app.use('/api/admin', adminRoutes);
  });

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('POST /api/auth/boat-owner/register', () => {
    const validRegistrationData = {
      email: '<EMAIL>',
      password: 'SecurePass123!',
      firstName: 'John',
      lastName: 'Doe',
      phone: '+60123456789',
      companyName: 'Marine Adventures Ltd',
      brn: 'BRN123456'
    };

    test('should successfully register boat owner with valid data', async () => {
      const mockResponse = {
        success: true,
        user: { id: 'user_123', email: '<EMAIL>', role: 'BOAT_OWNER' },
        message: 'Registration successful'
      };

      mockAuthService.registerBoatOwner.mockResolvedValue(mockResponse);

      const response = await request(app)
        .post('/api/auth/boat-owner/register')
        .send(validRegistrationData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Registration successful');
      expect(mockAuthService.registerBoatOwner).toHaveBeenCalledWith(validRegistrationData);
    });

    test('should return 400 for missing required fields', async () => {
      const incompleteData = {
        email: '<EMAIL>',
        password: 'SecurePass123!'
        // Missing firstName, lastName, phone, companyName, brn
      };

      const response = await request(app)
        .post('/api/auth/boat-owner/register')
        .send(incompleteData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
      expect(mockAuthService.registerBoatOwner).not.toHaveBeenCalled();
    });

    test('should return 400 for invalid email format', async () => {
      const invalidEmailData = {
        ...validRegistrationData,
        email: 'invalid-email-format'
      };

      const response = await request(app)
        .post('/api/auth/boat-owner/register')
        .send(invalidEmailData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toContain('Please provide a valid email address');
    });

    test('should return 400 for weak password', async () => {
      const weakPasswordData = {
        ...validRegistrationData,
        password: '123'
      };

      const response = await request(app)
        .post('/api/auth/boat-owner/register')
        .send(weakPasswordData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toContain('Password must be at least 8 characters long');
    });

    test('should return 400 for invalid Malaysian phone number', async () => {
      const invalidPhoneData = {
        ...validRegistrationData,
        phone: '+1234567890' // US format, not Malaysian
      };

      const response = await request(app)
        .post('/api/auth/boat-owner/register')
        .send(invalidPhoneData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toContain('Please provide a valid Malaysia phone number');
    });

    test('should return 400 when user already exists', async () => {
      mockAuthService.registerBoatOwner.mockRejectedValue(
        new Error('User already exists with this email')
      );

      const response = await request(app)
        .post('/api/auth/boat-owner/register')
        .send(validRegistrationData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('User already exists');
    });

    test('should return 500 for server errors', async () => {
      mockAuthService.registerBoatOwner.mockRejectedValue(
        new Error('Database connection failed')
      );

      const response = await request(app)
        .post('/api/auth/boat-owner/register')
        .send(validRegistrationData)
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('REGISTRATION_ERROR');
    });

    test('should sanitize and normalize email', async () => {
      const emailWithSpaces = {
        ...validRegistrationData,
        email: '  <EMAIL>  '
      };

      mockAuthService.registerBoatOwner.mockResolvedValue({ success: true });

      await request(app)
        .post('/api/auth/boat-owner/register')
        .send(emailWithSpaces);

      expect(mockAuthService.registerBoatOwner).toHaveBeenCalledWith({
        ...validRegistrationData,
        email: '<EMAIL>' // Should be normalized
      });
    });
  });

  describe('POST /api/auth/boat-owner/login', () => {
    const validLoginData = {
      email: '<EMAIL>',
      password: 'SecurePass123!'
    };

    test('should successfully login approved boat owner', async () => {
      const mockResponse = {
        success: true,
        accessToken: 'access_token_123',
        refreshToken: 'refresh_token_123',
        user: { id: 'user_123', email: '<EMAIL>', role: 'BOAT_OWNER' },
        message: 'Login successful'
      };

      mockAuthService.loginBoatOwner.mockResolvedValue(mockResponse);

      const response = await request(app)
        .post('/api/auth/boat-owner/login')
        .send(validLoginData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.accessToken).toBeDefined();
      expect(response.body.user.role).toBe('BOAT_OWNER');
      expect(mockAuthService.loginBoatOwner).toHaveBeenCalledWith(
        validLoginData.email,
        validLoginData.password,
        expect.any(String), // IP address
        expect.any(String)  // User agent
      );
    });

    test('should return 400 for missing credentials', async () => {
      const response = await request(app)
        .post('/api/auth/boat-owner/login')
        .send({ email: '<EMAIL>' }) // Missing password
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
      expect(mockAuthService.loginBoatOwner).not.toHaveBeenCalled();
    });

    test('should return 401 for invalid credentials', async () => {
      mockAuthService.loginBoatOwner.mockRejectedValue(
        new Error('Invalid email or password')
      );

      const response = await request(app)
        .post('/api/auth/boat-owner/login')
        .send(validLoginData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('INVALID_CREDENTIALS');
    });

    test('should return 403 for unverified email', async () => {
      mockAuthService.loginBoatOwner.mockRejectedValue(
        new Error('Please verify your email address first')
      );

      const response = await request(app)
        .post('/api/auth/boat-owner/login')
        .send(validLoginData)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('EMAIL_NOT_VERIFIED');
    });

    test('should return 403 for pending approval', async () => {
      mockAuthService.loginBoatOwner.mockRejectedValue(
        new Error('Your account is pending admin approval')
      );

      const response = await request(app)
        .post('/api/auth/boat-owner/login')
        .send(validLoginData)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('PENDING_APPROVAL');
    });

    test('should return 423 for locked account', async () => {
      mockAuthService.loginBoatOwner.mockRejectedValue(
        new Error('Account is locked. Try again in 15 minutes.')
      );

      const response = await request(app)
        .post('/api/auth/boat-owner/login')
        .send(validLoginData)
        .expect(423);

      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('ACCOUNT_LOCKED');
    });
  });

  describe('POST /api/auth/boat-owner/verify-email', () => {
    const validVerificationData = {
      token: 'verification_token_123'
    };

    test('should successfully verify boat owner email', async () => {
      const mockResponse = {
        success: true,
        message: 'Email verified successfully. Your account is now pending admin approval.'
      };

      mockAuthService.verifyBoatOwnerEmail.mockResolvedValue(mockResponse);

      const response = await request(app)
        .post('/api/auth/boat-owner/verify-email')
        .send(validVerificationData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Email verified successfully');
      expect(mockAuthService.verifyBoatOwnerEmail).toHaveBeenCalledWith('verification_token_123');
    });

    test('should return 400 for missing token', async () => {
      const response = await request(app)
        .post('/api/auth/boat-owner/verify-email')
        .send({}) // Missing token
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('MISSING_TOKEN');
      expect(mockAuthService.verifyBoatOwnerEmail).not.toHaveBeenCalled();
    });

    test('should return 400 for invalid token', async () => {
      mockAuthService.verifyBoatOwnerEmail.mockRejectedValue(
        new Error('Invalid or expired email verification token')
      );

      const response = await request(app)
        .post('/api/auth/boat-owner/verify-email')
        .send(validVerificationData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('INVALID_TOKEN');
    });

    test('should return 400 for already verified token', async () => {
      mockAuthService.verifyBoatOwnerEmail.mockRejectedValue(
        new Error('Email has already been verified')
      );

      const response = await request(app)
        .post('/api/auth/boat-owner/verify-email')
        .send(validVerificationData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('ALREADY_VERIFIED');
    });
  });

  describe('Admin Approval Endpoints', () => {
    describe('GET /api/admin/boat-owners/pending', () => {
      test('should return list of pending boat owners for admin', async () => {
        const mockPendingBoatOwners = [
          {
            id: 'user_123',
            email: '<EMAIL>',
            profile: {
              firstName: 'John',
              lastName: 'Doe',
              companyName: 'Marine Adventures',
              brn: 'BRN123'
            },
            createdAt: new Date(),
            emailVerified: true,
            isApproved: null
          }
        ];

        // Mock Prisma query for admin routes
        const mockPrisma = new PrismaClient();
        mockPrisma.user.findMany = jest.fn().mockResolvedValue(mockPendingBoatOwners);
        mockPrisma.user.count = jest.fn().mockResolvedValue(1);

        const response = await request(app)
          .get('/api/admin/boat-owners/pending')
          .set('Authorization', 'Bearer admin_token')
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.boatOwners).toHaveLength(1);
        expect(response.body.data.boatOwners[0].profile.companyName).toBe('Marine Adventures');
      });

      test('should return 403 for non-admin access', async () => {
        // Override auth middleware to simulate non-admin user
        jest.doMock('../../middleware/auth', () => ({
          authenticateToken: (req, res, next) => {
            req.user = { id: 'user_123', role: 'CUSTOMER' };
            next();
          },
          requireRole: (role) => (req, res, next) => {
            if (req.user.role !== role) {
              return res.status(403).json({ success: false, message: 'Forbidden' });
            }
            next();
          }
        }));

        const response = await request(app)
          .get('/api/admin/boat-owners/pending')
          .set('Authorization', 'Bearer user_token')
          .expect(403);

        expect(response.body.success).toBe(false);
        expect(response.body.message).toBe('Forbidden');
      });
    });

    describe('POST /api/admin/boat-owners/:id/approve', () => {
      test('should successfully approve boat owner', async () => {
        const mockApprovedUser = {
          id: 'user_123',
          email: '<EMAIL>',
          isApproved: true,
          approvedAt: new Date(),
          approvedBy: 'admin_123'
        };

        // Mock Prisma update for approval
        const mockPrisma = new PrismaClient();
        mockPrisma.user.update = jest.fn().mockResolvedValue(mockApprovedUser);
        mockPrisma.provider.update = jest.fn().mockResolvedValue({});

        const response = await request(app)
          .post('/api/admin/boat-owners/user_123/approve')
          .set('Authorization', 'Bearer admin_token')
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.message).toContain('approved successfully');
      });

      test('should return 404 for non-existent boat owner', async () => {
        const mockPrisma = new PrismaClient();
        mockPrisma.user.update = jest.fn().mockRejectedValue(
          new Error('Record to update not found')
        );

        const response = await request(app)
          .post('/api/admin/boat-owners/nonexistent_id/approve')
          .set('Authorization', 'Bearer admin_token')
          .expect(404);

        expect(response.body.success).toBe(false);
        expect(response.body.message).toContain('not found');
      });
    });

    describe('POST /api/admin/boat-owners/:id/reject', () => {
      test('should successfully reject boat owner with reason', async () => {
        const rejectionData = {
          rejectionReason: 'Incomplete business documentation'
        };

        const mockRejectedUser = {
          id: 'user_123',
          email: '<EMAIL>',
          isApproved: false,
          rejectionReason: 'Incomplete business documentation'
        };

        const mockPrisma = new PrismaClient();
        mockPrisma.user.update = jest.fn().mockResolvedValue(mockRejectedUser);

        const response = await request(app)
          .post('/api/admin/boat-owners/user_123/reject')
          .set('Authorization', 'Bearer admin_token')
          .send(rejectionData)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.message).toContain('rejected successfully');
      });

      test('should return 400 for missing rejection reason', async () => {
        const response = await request(app)
          .post('/api/admin/boat-owners/user_123/reject')
          .set('Authorization', 'Bearer admin_token')
          .send({}) // Missing rejectionReason
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors).toBeDefined();
      });
    });
  });

  describe('Rate Limiting', () => {
    test('should enforce rate limits on registration endpoint', async () => {
      // Mock rate limiter to reject after certain number of attempts
      const validData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: 'John',
        lastName: 'Doe',
        phone: '+60123456789',
        companyName: 'Marine Adventures',
        brn: 'BRN123'
      };

      mockAuthService.registerBoatOwner.mockResolvedValue({ success: true });

      // Make multiple rapid requests (this would be limited by actual rate limiter)
      const requests = Array(6).fill().map(() =>
        request(app)
          .post('/api/auth/boat-owner/register')
          .send(validData)
      );

      const responses = await Promise.allSettled(requests);
      
      // Some requests should be rate limited (this is a conceptual test)
      expect(responses.length).toBe(6);
    });
  });

  describe('Input Sanitization', () => {
    test('should sanitize XSS attempts in registration data', async () => {
      const maliciousData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: '<script>alert("xss")</script>',
        lastName: 'Doe',
        phone: '+60123456789',
        companyName: 'Marine Adventures<script>alert("xss")</script>',
        brn: 'BRN123'
      };

      mockAuthService.registerBoatOwner.mockResolvedValue({ success: true });

      const response = await request(app)
        .post('/api/auth/boat-owner/register')
        .send(maliciousData);

      // The service should receive sanitized data
      expect(mockAuthService.registerBoatOwner).toHaveBeenCalledWith(
        expect.objectContaining({
          firstName: expect.not.stringContaining('<script>'),
          companyName: expect.not.stringContaining('<script>')
        })
      );
    });
  });

  describe('Response Headers', () => {
    test('should include security headers in responses', async () => {
      mockAuthService.registerBoatOwner.mockResolvedValue({ success: true });

      const response = await request(app)
        .post('/api/auth/boat-owner/register')
        .send({
          email: '<EMAIL>',
          password: 'SecurePass123!',
          firstName: 'John',
          lastName: 'Doe',
          phone: '+60123456789',
          companyName: 'Marine Adventures',
          brn: 'BRN123'
        });

      // Check for security headers (these would be set by helmet middleware)
      expect(response.headers).toBeDefined();
    });

    test('should return JSON content type', async () => {
      mockAuthService.loginBoatOwner.mockResolvedValue({
        success: true,
        accessToken: 'token',
        user: { role: 'BOAT_OWNER' }
      });

      const response = await request(app)
        .post('/api/auth/boat-owner/login')
        .send({
          email: '<EMAIL>',
          password: 'SecurePass123!'
        });

      expect(response.headers['content-type']).toMatch(/application\/json/);
    });
  });
});