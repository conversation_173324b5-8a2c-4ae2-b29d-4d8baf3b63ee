const AuthService = require('../../services/authService');
const EmailService = require('../../services/emailService');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const { generateEmailVerificationToken } = require('../../utils/auth');

// Mock dependencies
jest.mock('../../services/emailService');
jest.mock('../../utils/auth');
jest.mock('bcryptjs');

// Create a mock Prisma client
const mockPrisma = {
  user: {
    findFirst: jest.fn(),
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  },
  profile: {
    create: jest.fn(),
  },
  provider: {
    create: jest.fn(),
  },
  emailVerificationToken: {
    create: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  userSession: {
    create: jest.fn(),
    deleteMany: jest.fn(),
  },
  $transaction: jest.fn(),
};

// Mock PrismaClient
jest.mock('@prisma/client', () => {
  return {
    PrismaClient: jest.fn(() => mockPrisma),
  };
});

describe('Boat Owner Authentication Unit Tests', () => {
  let authService;
  let emailService;

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    
    // Reset modules to get fresh instances
    jest.resetModules();
    
    // Create fresh instances
    authService = require('../../services/authService');
    emailService = EmailService;
    
    // Mock bcrypt
    bcrypt.hash = jest.fn().mockResolvedValue('hashed_password');
    bcrypt.compare = jest.fn();
    
    // Mock auth utils
    generateEmailVerificationToken.mockReturnValue({
      token: 'mock_token_123',
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
    });
  });

  describe('registerBoatOwner', () => {
    const validBoatOwnerData = {
      email: '<EMAIL>',
      password: 'SecurePass123!',
      firstName: 'John',
      lastName: 'Doe',
      phone: '+60*********',
      companyName: 'Marine Adventures Ltd',
      brn: 'BRN123456'
    };

    test('should successfully register a new boat owner', async () => {
      // Setup mocks
      mockPrisma.user.findFirst.mockResolvedValue(null); // No existing user
      mockPrisma.user.findMany.mockResolvedValue([]); // No Gmail variants
      
      const mockUser = { id: 'user_123', email: '<EMAIL>', role: 'BOAT_OWNER' };
      const mockProfile = { id: 'profile_123', userId: 'user_123', firstName: 'John', lastName: 'Doe' };
      const mockProvider = { id: 'provider_123', userId: 'user_123', companyName: 'Marine Adventures Ltd' };
      const mockToken = { id: 'token_123', userId: 'user_123', token: 'mock_token_123' };

      mockPrisma.$transaction.mockImplementation(async (callback) => {
        mockPrisma.user.create.mockResolvedValue(mockUser);
        mockPrisma.profile.create.mockResolvedValue(mockProfile);
        mockPrisma.provider.create.mockResolvedValue(mockProvider);
        mockPrisma.emailVerificationToken.create.mockResolvedValue(mockToken);
        
        return await callback(mockPrisma);
      });

      emailService.sendBoatOwnerVerificationEmail = jest.fn().mockResolvedValue({ success: true });

      // Execute
      const result = await authService.registerBoatOwner(validBoatOwnerData);

      // Assertions
      expect(result.success).toBe(true);
      expect(result.message).toContain('Registration successful');
      expect(mockPrisma.user.create).toHaveBeenCalledWith({
        data: {
          email: '<EMAIL>',
          password: 'hashed_password',
          role: 'BOAT_OWNER',
          emailVerified: false,
          isApproved: false
        }
      });
      expect(mockPrisma.profile.create).toHaveBeenCalled();
      expect(mockPrisma.provider.create).toHaveBeenCalled();
      expect(emailService.sendBoatOwnerVerificationEmail).toHaveBeenCalled();
    });

    test('should reject registration with existing email', async () => {
      // Setup mock for existing user
      const existingUser = { id: 'existing_123', email: '<EMAIL>' };
      mockPrisma.user.findFirst.mockResolvedValue(existingUser);

      // Execute and expect error
      await expect(authService.registerBoatOwner(validBoatOwnerData))
        .rejects.toThrow('User already exists with this email');
      
      // Ensure no user creation was attempted
      expect(mockPrisma.user.create).not.toHaveBeenCalled();
    });

    test('should reject registration with Gmail variant', async () => {
      // Setup data with Gmail
      const gmailData = { ...validBoatOwnerData, email: '<EMAIL>' };
      
      mockPrisma.user.findFirst.mockResolvedValue(null); // No direct match
      mockPrisma.user.findMany.mockResolvedValue([
        { email: '<EMAIL>' } // Existing variant without dot
      ]);

      // Execute and expect error
      await expect(authService.registerBoatOwner(gmailData))
        .rejects.toThrow('An account with this email address (or its Gmail variant) already exists');
    });

    test('should reject registration with weak password', async () => {
      // Mock password validation to fail
      const { validatePasswordStrength } = require('../../utils/auth');
      jest.doMock('../../utils/auth', () => ({
        ...jest.requireActual('../../utils/auth'),
        validatePasswordStrength: jest.fn().mockReturnValue({
          isValid: false,
          errors: ['Password too weak']
        })
      }));

      const weakPasswordData = { ...validBoatOwnerData, password: '123' };
      mockPrisma.user.findFirst.mockResolvedValue(null);

      // Re-require authService to get updated mock
      const AuthServiceWithMockedValidation = require('../../services/authService');
      
      await expect(AuthServiceWithMockedValidation.registerBoatOwner(weakPasswordData))
        .rejects.toThrow('Password validation failed');
    });

    test('should handle email sending failure gracefully', async () => {
      // Setup successful registration but email failure
      mockPrisma.user.findFirst.mockResolvedValue(null);
      mockPrisma.user.findMany.mockResolvedValue([]);
      
      const mockUser = { id: 'user_123', email: '<EMAIL>', role: 'BOAT_OWNER' };
      const mockProfile = { id: 'profile_123' };
      const mockProvider = { id: 'provider_123' };
      const mockToken = { id: 'token_123' };

      mockPrisma.$transaction.mockImplementation(async (callback) => {
        mockPrisma.user.create.mockResolvedValue(mockUser);
        mockPrisma.profile.create.mockResolvedValue(mockProfile);
        mockPrisma.provider.create.mockResolvedValue(mockProvider);
        mockPrisma.emailVerificationToken.create.mockResolvedValue(mockToken);
        
        return { user: mockUser, profile: mockProfile, provider: mockProvider, verificationToken: mockToken };
      });

      // Mock email service to fail
      emailService.sendBoatOwnerVerificationEmail = jest.fn().mockRejectedValue(new Error('Email service down'));

      // Should still succeed even if email fails
      const result = await authService.registerBoatOwner(validBoatOwnerData);
      expect(result.success).toBe(true);
    });
  });

  describe('loginBoatOwner', () => {
    const loginData = {
      email: '<EMAIL>',
      password: 'SecurePass123!',
      ipAddress: '127.0.0.1',
      userAgent: 'Test Agent'
    };

    test('should successfully login approved boat owner', async () => {
      // Setup mock user
      const mockUser = {
        id: 'user_123',
        email: '<EMAIL>',
        role: 'BOAT_OWNER',
        password: 'hashed_password',
        isActive: true,
        emailVerified: true,
        isApproved: true,
        loginAttempts: 0,
        lockedUntil: null,
        profile: { firstName: 'John', lastName: 'Doe' },
        provider: { id: 'provider_123' }
      };

      mockPrisma.user.findFirst.mockResolvedValue(mockUser);
      bcrypt.compare.mockResolvedValue(true);
      mockPrisma.user.update.mockResolvedValue(mockUser);
      mockPrisma.userSession.create.mockResolvedValue({ id: 'session_123' });

      // Mock session generation
      const { generateSessionData } = require('../../utils/auth');
      jest.doMock('../../utils/auth', () => ({
        generateSessionData: jest.fn().mockReturnValue({
          accessToken: 'access_token_123',
          refreshToken: 'refresh_token_123',
          sessionToken: 'session_token_123',
          expiresAt: new Date(),
          user: mockUser
        })
      }));

      const result = await authService.loginBoatOwner(
        loginData.email,
        loginData.password,
        loginData.ipAddress,
        loginData.userAgent
      );

      expect(result.success).toBe(true);
      expect(result.message).toBe('Login successful');
      expect(mockPrisma.user.update).toHaveBeenCalledWith({
        where: { id: 'user_123' },
        data: {
          loginAttempts: 0,
          lockedUntil: null,
          lastLoginAt: expect.any(Date)
        }
      });
    });

    test('should reject login for non-existent user', async () => {
      mockPrisma.user.findFirst.mockResolvedValue(null);

      await expect(authService.loginBoatOwner(
        loginData.email,
        loginData.password,
        loginData.ipAddress,
        loginData.userAgent
      )).rejects.toThrow('Invalid email or password');
    });

    test('should reject login for unverified email', async () => {
      const mockUser = {
        id: 'user_123',
        email: '<EMAIL>',
        role: 'BOAT_OWNER',
        isActive: true,
        emailVerified: false,
        isApproved: true,
        loginAttempts: 0,
        lockedUntil: null
      };

      mockPrisma.user.findFirst.mockResolvedValue(mockUser);

      await expect(authService.loginBoatOwner(
        loginData.email,
        loginData.password,
        loginData.ipAddress,
        loginData.userAgent
      )).rejects.toThrow('Please verify your email address first');
    });

    test('should reject login for unapproved account', async () => {
      const mockUser = {
        id: 'user_123',
        email: '<EMAIL>',
        role: 'BOAT_OWNER',
        isActive: true,
        emailVerified: true,
        isApproved: false,
        loginAttempts: 0,
        lockedUntil: null
      };

      mockPrisma.user.findFirst.mockResolvedValue(mockUser);

      await expect(authService.loginBoatOwner(
        loginData.email,
        loginData.password,
        loginData.ipAddress,
        loginData.userAgent
      )).rejects.toThrow('Your account is pending admin approval');
    });

    test('should reject login for incorrect password', async () => {
      const mockUser = {
        id: 'user_123',
        email: '<EMAIL>',
        role: 'BOAT_OWNER',
        password: 'hashed_password',
        isActive: true,
        emailVerified: true,
        isApproved: true,
        loginAttempts: 0,
        lockedUntil: null
      };

      mockPrisma.user.findFirst.mockResolvedValue(mockUser);
      bcrypt.compare.mockResolvedValue(false); // Wrong password
      mockPrisma.user.update.mockResolvedValue(mockUser);

      await expect(authService.loginBoatOwner(
        loginData.email,
        loginData.password,
        loginData.ipAddress,
        loginData.userAgent
      )).rejects.toThrow('Invalid email or password');

      // Should increment login attempts
      expect(mockPrisma.user.update).toHaveBeenCalledWith({
        where: { id: 'user_123' },
        data: { loginAttempts: 1 }
      });
    });

    test('should reject login for locked account', async () => {
      const mockUser = {
        id: 'user_123',
        email: '<EMAIL>',
        role: 'BOAT_OWNER',
        isActive: true,
        emailVerified: true,
        isApproved: true,
        loginAttempts: 5,
        lockedUntil: new Date(Date.now() + 60000) // Locked for 1 minute
      };

      mockPrisma.user.findFirst.mockResolvedValue(mockUser);

      await expect(authService.loginBoatOwner(
        loginData.email,
        loginData.password,
        loginData.ipAddress,
        loginData.userAgent
      )).rejects.toThrow('Account is locked');
    });

    test('should reject login for inactive account', async () => {
      const mockUser = {
        id: 'user_123',
        email: '<EMAIL>',
        role: 'BOAT_OWNER',
        isActive: false,
        emailVerified: true,
        isApproved: true,
        loginAttempts: 0,
        lockedUntil: null
      };

      mockPrisma.user.findFirst.mockResolvedValue(mockUser);

      await expect(authService.loginBoatOwner(
        loginData.email,
        loginData.password,
        loginData.ipAddress,
        loginData.userAgent
      )).rejects.toThrow('Account is deactivated');
    });
  });

  describe('verifyBoatOwnerEmail', () => {
    const verificationToken = 'test_token_123';

    test('should successfully verify boat owner email and notify admin', async () => {
      const mockUser = {
        id: 'user_123',
        email: '<EMAIL>',
        role: 'BOAT_OWNER',
        profile: { firstName: 'John', lastName: 'Doe', companyName: 'Marine Adventures' },
        provider: { id: 'provider_123' }
      };

      const mockVerificationToken = {
        id: 'token_123',
        userId: 'user_123',
        token: verificationToken,
        expiresAt: new Date(Date.now() + 60000), // Not expired
        verifiedAt: null,
        user: mockUser
      };

      mockPrisma.emailVerificationToken.findUnique.mockResolvedValue(mockVerificationToken);
      mockPrisma.$transaction.mockImplementation(async (callback) => {
        mockPrisma.user.update.mockResolvedValue(mockUser);
        mockPrisma.emailVerificationToken.update.mockResolvedValue(mockVerificationToken);
        return await callback(mockPrisma);
      });

      // Mock the admin notification method
      authService.notifyAdminOfNewBoatOwner = jest.fn().mockResolvedValue();

      const result = await authService.verifyBoatOwnerEmail(verificationToken);

      expect(result.success).toBe(true);
      expect(result.message).toContain('Email verified successfully');
      expect(mockPrisma.user.update).toHaveBeenCalledWith({
        where: { id: 'user_123' },
        data: {
          emailVerified: true,
          emailVerifiedAt: expect.any(Date)
        }
      });
      expect(authService.notifyAdminOfNewBoatOwner).toHaveBeenCalledWith(
        mockUser,
        mockUser.profile,
        mockUser.provider
      );
    });

    test('should reject invalid verification token', async () => {
      mockPrisma.emailVerificationToken.findUnique.mockResolvedValue(null);

      await expect(authService.verifyBoatOwnerEmail(verificationToken))
        .rejects.toThrow('Invalid or expired email verification token');
    });

    test('should reject expired verification token', async () => {
      const expiredToken = {
        id: 'token_123',
        userId: 'user_123',
        token: verificationToken,
        expiresAt: new Date(Date.now() - 60000), // Expired 1 minute ago
        verifiedAt: null,
        user: { role: 'BOAT_OWNER' }
      };

      mockPrisma.emailVerificationToken.findUnique.mockResolvedValue(expiredToken);
      mockPrisma.emailVerificationToken.delete.mockResolvedValue(expiredToken);

      await expect(authService.verifyBoatOwnerEmail(verificationToken))
        .rejects.toThrow('Email verification token has expired');
      
      expect(mockPrisma.emailVerificationToken.delete).toHaveBeenCalledWith({
        where: { id: 'token_123' }
      });
    });

    test('should reject already verified token', async () => {
      const verifiedToken = {
        id: 'token_123',
        userId: 'user_123',
        token: verificationToken,
        expiresAt: new Date(Date.now() + 60000),
        verifiedAt: new Date(), // Already verified
        user: { role: 'BOAT_OWNER' }
      };

      mockPrisma.emailVerificationToken.findUnique.mockResolvedValue(verifiedToken);

      await expect(authService.verifyBoatOwnerEmail(verificationToken))
        .rejects.toThrow('Email has already been verified');
    });

    test('should reject non-boat-owner verification attempts', async () => {
      const mockToken = {
        id: 'token_123',
        userId: 'user_123',
        token: verificationToken,
        expiresAt: new Date(Date.now() + 60000),
        verifiedAt: null,
        user: { role: 'CUSTOMER' } // Not a boat owner
      };

      mockPrisma.emailVerificationToken.findUnique.mockResolvedValue(mockToken);

      await expect(authService.verifyBoatOwnerEmail(verificationToken))
        .rejects.toThrow('Invalid verification method for this user type');
    });
  });

  describe('notifyAdminOfNewBoatOwner', () => {
    test('should send admin notification email', async () => {
      const mockUser = { id: 'user_123', email: '<EMAIL>', createdAt: new Date() };
      const mockProfile = { firstName: 'John', lastName: 'Doe', companyName: 'Marine Adventures', brn: 'BRN123' };
      const mockProvider = { id: 'provider_123' };

      emailService.sendAdminApprovalNotification = jest.fn().mockResolvedValue({ success: true });

      await authService.notifyAdminOfNewBoatOwner(mockUser, mockProfile, mockProvider);

      expect(emailService.sendAdminApprovalNotification).toHaveBeenCalledWith({
        adminEmail: expect.any(String),
        boatOwner: {
          id: 'user_123',
          name: 'John Doe',
          email: '<EMAIL>',
          companyName: 'Marine Adventures',
          brn: 'BRN123',
          registrationDate: expect.any(Date)
        },
        approvalLink: expect.stringContaining('/admin/dashboard')
      });
    });

    test('should handle email notification failure gracefully', async () => {
      const mockUser = { id: 'user_123', email: '<EMAIL>', createdAt: new Date() };
      const mockProfile = { firstName: 'John', lastName: 'Doe', companyName: 'Marine Adventures', brn: 'BRN123' };
      const mockProvider = { id: 'provider_123' };

      emailService.sendAdminApprovalNotification = jest.fn().mockRejectedValue(new Error('Email service down'));

      // Should not throw error even if email fails
      await expect(authService.notifyAdminOfNewBoatOwner(mockUser, mockProfile, mockProvider))
        .resolves.toBeUndefined();
    });
  });

  describe('Input Validation', () => {
    test('should validate required fields for boat owner registration', async () => {
      const incompleteData = {
        email: '<EMAIL>',
        password: 'password',
        firstName: 'John'
        // Missing lastName, phone, companyName, brn
      };

      // This would depend on how validation is implemented
      // The actual validation might be done at the route level with express-validator
      expect(incompleteData).not.toHaveProperty('lastName');
      expect(incompleteData).not.toHaveProperty('phone');
      expect(incompleteData).not.toHaveProperty('companyName');
      expect(incompleteData).not.toHaveProperty('brn');
    });

    test('should validate email format', () => {
      const invalidEmails = [
        'invalid-email',
        'missing@',
        '@missing-domain.com',
        'spaces <EMAIL>'
      ];

      invalidEmails.forEach(email => {
        // Email format validation would typically be done by express-validator at route level
        expect(email).toMatch(/^[^\s@]+@[^\s@]+\.[^\s@]+$/); // This will fail for invalid emails
      });
    });

    test('should validate Malaysian phone number format', () => {
      const validPhones = ['+60*********', '0*********', '******-345-6789'];
      const invalidPhones = ['*********', '+*********0', 'not-a-phone'];

      // Phone validation would typically be done by express-validator at route level
      const malaysiaPhoneRegex = /^(\+?6?01[0-46-9][-\s]?\d{7,8}|(\+?6?0)?(1[0-46-9][-\s]?\d{7,8}))$/;
      
      validPhones.forEach(phone => {
        expect(phone.replace(/[-\s]/g, '')).toMatch(malaysiaPhoneRegex);
      });
    });
  });
});