const request = require('supertest');
const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const EmailService = require('../../services/emailService');

// Mock the email service to prevent actual email sending during tests
jest.mock('../../services/emailService');

// Mock Prisma Client
const mockPrisma = {
  user: {
    create: jest.fn(),
    findFirst: jest.fn(),
    findUnique: jest.fn(),
    findMany: jest.fn(),
    update: jest.fn(),
    count: jest.fn(),
    deleteMany: jest.fn()
  },
  profile: {
    create: jest.fn(),
    deleteMany: jest.fn()
  },
  provider: {
    create: jest.fn(),
    update: jest.fn(),
    deleteMany: jest.fn()
  },
  emailVerificationToken: {
    create: jest.fn(),
    findFirst: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    deleteMany: jest.fn()
  },
  userSession: {
    create: jest.fn(),
    deleteMany: jest.fn()
  },
  $transaction: jest.fn(),
  $disconnect: jest.fn().mockResolvedValue(undefined)
};

jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn(() => mockPrisma)
}));

// Mock authentication middleware
jest.mock('../../middleware/auth', () => ({
  authenticateToken: (req, res, next) => {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }
    
    try {
      const token = authHeader.substring(7);
      const mockJwt = require('jsonwebtoken');
      const decoded = mockJwt.verify(token, process.env.JWT_SECRET || 'test_secret');
      req.user = decoded;
      next();
    } catch (error) {
      return res.status(401).json({ success: false, message: 'Invalid token' });
    }
  },
  requireRole: (role) => (req, res, next) => {
    if (!req.user || req.user.role !== role) {
      return res.status(403).json({ success: false, message: 'Forbidden' });
    }
    next();
  }
}));

describe('Admin Approval Workflow Integration Tests', () => {
  let app;
  let adminToken;
  let testBoatOwner;
  let emailService;
  let testUserCounter = 1;

  beforeAll(async () => {
    // Create Express app
    app = express();
    app.use(express.json());

    // Import and mount routes
    const authRoutes = require('../../routes/auth');
    const adminRoutes = require('../../routes/admin');
    
    app.use('/api/auth', authRoutes);
    app.use('/api/admin', adminRoutes);

    // Generate admin token
    adminToken = jwt.sign(
      { userId: 'admin_123', role: 'ADMIN' },
      process.env.JWT_SECRET || 'test_secret',
      { expiresIn: '1h' }
    );

    // Initialize email service mock
    emailService = EmailService;
    emailService.sendAdminApprovalNotification = jest.fn().mockResolvedValue({ success: true });
    emailService.sendBoatOwnerApprovalEmail = jest.fn().mockResolvedValue({ success: true });
    emailService.sendBoatOwnerRejectionEmail = jest.fn().mockResolvedValue({ success: true });
    emailService.sendBoatOwnerVerificationEmail = jest.fn().mockResolvedValue({ success: true });
  });

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    testUserCounter++;
    
    // Reset mock implementations
    mockPrisma.user.findFirst.mockResolvedValue(null);
    mockPrisma.user.findMany.mockResolvedValue([]);
    mockPrisma.user.count.mockResolvedValue(0);
    mockPrisma.emailVerificationToken.findFirst.mockResolvedValue(null);
    mockPrisma.$transaction.mockImplementation(async (callback) => {
      return await callback(mockPrisma);
    });
  });

  afterAll(async () => {
    // Clean up mocks
    jest.restoreAllMocks();
  });

  describe('Complete Boat Owner Registration and Approval Flow', () => {
    test('should handle complete workflow: registration → email verification → admin approval', async () => {
      const userId = `user_${testUserCounter}`;
      const profileId = `profile_${testUserCounter}`;
      const providerId = `provider_${testUserCounter}`;
      const tokenId = `token_${testUserCounter}`;

      // Step 1: Setup mocks for boat owner registration
      const registrationData = {
        email: `testowner${testUserCounter}@example.com`,
        password: 'SecurePass123!',
        firstName: 'John',
        lastName: 'Doe',
        phone: '+60123456789',
        companyName: 'Marine Adventures Ltd',
        brn: 'BRN123456'
      };

      const mockUser = {
        id: userId,
        email: registrationData.email,
        role: 'BOAT_OWNER',
        emailVerified: false,
        isApproved: false,
        profile: {
          id: profileId,
          firstName: 'John',
          lastName: 'Doe',
          companyName: 'Marine Adventures Ltd'
        },
        provider: {
          id: providerId,
          companyName: 'Marine Adventures Ltd',
          businessRegistrationNumber: 'BRN123456'
        }
      };

      const mockToken = {
        id: tokenId,
        token: 'verification_token_123',
        userId: userId,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        verifiedAt: null,
        user: mockUser
      };

      // Mock registration flow
      mockPrisma.user.findFirst.mockResolvedValue(null); // No existing user
      mockPrisma.user.findMany.mockResolvedValue([]); // No Gmail variants
      mockPrisma.$transaction.mockImplementation(async (callback) => {
        mockPrisma.user.create.mockResolvedValue(mockUser);
        mockPrisma.profile.create.mockResolvedValue(mockUser.profile);
        mockPrisma.provider.create.mockResolvedValue(mockUser.provider);
        mockPrisma.emailVerificationToken.create.mockResolvedValue(mockToken);
        return { user: mockUser, profile: mockUser.profile, provider: mockUser.provider, verificationToken: mockToken };
      });

      // Step 1: Boat owner registers
      const registrationResponse = await request(app)
        .post('/api/auth/boat-owner/register')
        .send(registrationData)
        .expect(201);

      expect(registrationResponse.body.success).toBe(true);
      expect(registrationResponse.body.message).toContain('Registration successful');
      expect(mockPrisma.user.create).toHaveBeenCalled();
      expect(emailService.sendBoatOwnerVerificationEmail).toHaveBeenCalled();

      // Step 2: Setup mocks for email verification
      mockPrisma.emailVerificationToken.findUnique.mockResolvedValue(mockToken);
      mockPrisma.$transaction.mockImplementation(async (callback) => {
        const verifiedUser = { ...mockUser, emailVerified: true, emailVerifiedAt: new Date() };
        mockPrisma.user.update.mockResolvedValue(verifiedUser);
        mockPrisma.emailVerificationToken.update.mockResolvedValue({ ...mockToken, verifiedAt: new Date() });
        return verifiedUser;
      });

      // Step 2: Verify email
      const emailVerificationResponse = await request(app)
        .post('/api/auth/boat-owner/verify-email')
        .send({ token: mockToken.token })
        .expect(200);

      expect(emailVerificationResponse.body.success).toBe(true);
      expect(emailVerificationResponse.body.message).toContain('Email verified successfully');
      expect(emailService.sendAdminApprovalNotification).toHaveBeenCalled();

      // Step 3: Setup mocks for admin pending list
      const pendingBoatOwner = {
        ...mockUser,
        emailVerified: true,
        isApproved: null,
        createdAt: new Date()
      };

      mockPrisma.user.findMany.mockResolvedValue([pendingBoatOwner]);
      mockPrisma.user.count.mockResolvedValue(1);

      // Step 3: Admin checks pending approvals
      const pendingResponse = await request(app)
        .get('/api/admin/boat-owners/pending')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(pendingResponse.body.success).toBe(true);
      expect(pendingResponse.body.data.boatOwners).toHaveLength(1);
      expect(pendingResponse.body.data.boatOwners[0].id).toBe(userId);

      // Step 4: Setup mocks for approval
      const approvedUser = {
        ...mockUser,
        emailVerified: true,
        isApproved: true,
        approvedAt: new Date(),
        approvedBy: 'admin_123'
      };

      const activatedProvider = {
        ...mockUser.provider,
        isActive: true
      };

      mockPrisma.user.update.mockResolvedValue(approvedUser);
      mockPrisma.provider.update.mockResolvedValue(activatedProvider);
      mockPrisma.user.findUnique.mockResolvedValue(approvedUser);

      // Step 4: Admin approves boat owner
      const approvalResponse = await request(app)
        .post(`/api/admin/boat-owners/${userId}/approve`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(approvalResponse.body.success).toBe(true);
      expect(approvalResponse.body.message).toContain('approved successfully');
      expect(emailService.sendBoatOwnerApprovalEmail).toHaveBeenCalledWith(
        registrationData.email,
        'John',
        'Marine Adventures Ltd'
      );

      // Step 5: Setup mocks for login
      const loginUser = {
        ...approvedUser,
        password: await bcrypt.hash('SecurePass123!', 10),
        isActive: true,
        loginAttempts: 0,
        lockedUntil: null
      };

      mockPrisma.user.findFirst.mockResolvedValue(loginUser);
      mockPrisma.user.update.mockResolvedValue(loginUser);
      mockPrisma.userSession.create.mockResolvedValue({ id: 'session_123' });

      // Step 5: Boat owner can now login successfully
      const loginResponse = await request(app)
        .post('/api/auth/boat-owner/login')
        .send({
          email: registrationData.email,
          password: 'SecurePass123!'
        })
        .expect(200);

      expect(loginResponse.body.success).toBe(true);
      expect(loginResponse.body.accessToken).toBeTruthy();
      expect(loginResponse.body.user.role).toBe('BOAT_OWNER');
    });

    test('should handle complete workflow: registration → email verification → admin rejection', async () => {
      const userId = `user_reject_${testUserCounter}`;
      const profileId = `profile_reject_${testUserCounter}`;
      const providerId = `provider_reject_${testUserCounter}`;
      const tokenId = `token_reject_${testUserCounter}`;
      
      // Setup mocks for rejection workflow
      const registrationData = {
        email: `rejected${testUserCounter}@example.com`,
        password: 'SecurePass123!',
        firstName: 'Jane',
        lastName: 'Smith',
        phone: '+60987654321',
        companyName: 'Ocean Services Ltd',
        brn: 'BRN789012'
      };

      const mockUser = {
        id: userId,
        email: registrationData.email,
        role: 'BOAT_OWNER',
        emailVerified: false,
        isApproved: null,
        profile: {
          id: profileId,
          firstName: 'Jane',
          lastName: 'Smith',
          companyName: 'Ocean Services Ltd'
        },
        provider: {
          id: providerId,
          companyName: 'Ocean Services Ltd',
          businessRegistrationNumber: 'BRN789012'
        }
      };

      const mockToken = {
        id: tokenId,
        token: 'rejection_token_123',
        userId: userId,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        verifiedAt: null,
        user: mockUser
      };

      // Mock registration
      mockPrisma.user.findFirst.mockResolvedValue(null);
      mockPrisma.user.findMany.mockResolvedValue([]);
      mockPrisma.$transaction.mockImplementation(async (callback) => {
        mockPrisma.user.create.mockResolvedValue(mockUser);
        mockPrisma.profile.create.mockResolvedValue(mockUser.profile);
        mockPrisma.provider.create.mockResolvedValue(mockUser.provider);
        mockPrisma.emailVerificationToken.create.mockResolvedValue(mockToken);
        return { user: mockUser, profile: mockUser.profile, provider: mockUser.provider, verificationToken: mockToken };
      });

      // Step 1: Registration
      const registrationResponse = await request(app)
        .post('/api/auth/boat-owner/register')
        .send(registrationData)
        .expect(201);

      expect(registrationResponse.body.success).toBe(true);

      // Mock email verification
      mockPrisma.emailVerificationToken.findUnique.mockResolvedValue(mockToken);
      mockPrisma.$transaction.mockImplementation(async (callback) => {
        const verifiedUser = { ...mockUser, emailVerified: true, emailVerifiedAt: new Date() };
        mockPrisma.user.update.mockResolvedValue(verifiedUser);
        mockPrisma.emailVerificationToken.update.mockResolvedValue({ ...mockToken, verifiedAt: new Date() });
        return verifiedUser;
      });

      // Step 2: Email verification
      await request(app)
        .post('/api/auth/boat-owner/verify-email')
        .send({ token: mockToken.token })
        .expect(200);

      // Step 3: Setup mocks for rejection
      const rejectionReason = 'Incomplete business documentation. Please provide valid business registration certificate and insurance documents.';
      const rejectedUser = {
        ...mockUser,
        emailVerified: true,
        isApproved: false,
        rejectionReason: rejectionReason
      };

      mockPrisma.user.update.mockResolvedValue(rejectedUser);
      mockPrisma.user.findUnique.mockResolvedValue(rejectedUser);

      // Step 3: Admin rejection
      const rejectionResponse = await request(app)
        .post(`/api/admin/boat-owners/${userId}/reject`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ rejectionReason })
        .expect(200);

      expect(rejectionResponse.body.success).toBe(true);
      expect(rejectionResponse.body.message).toContain('rejected successfully');
      expect(emailService.sendBoatOwnerRejectionEmail).toHaveBeenCalledWith(
        registrationData.email,
        'Jane',
        'Ocean Services Ltd',
        rejectionReason
      );

      // Step 4: Setup mocks for failed login
      const loginUser = {
        ...rejectedUser,
        password: await bcrypt.hash('SecurePass123!', 10),
        isActive: true,
        loginAttempts: 0,
        lockedUntil: null
      };

      mockPrisma.user.findFirst.mockResolvedValue(loginUser);

      // Step 4: Login should fail after rejection
      await request(app)
        .post('/api/auth/boat-owner/login')
        .send({
          email: registrationData.email,
          password: 'SecurePass123!'
        })
        .expect(403);
    });
  });

  describe('Admin Authorization and Permissions', () => {
    test('should deny access to non-admin users', async () => {
      // Create a customer token (non-admin)
      const customerToken = jwt.sign(
        { userId: 'customer_123', role: 'CUSTOMER' },
        process.env.JWT_SECRET || 'test_secret',
        { expiresIn: '1h' }
      );

      // Try to access admin endpoints with customer token
      await request(app)
        .get('/api/admin/boat-owners/pending')
        .set('Authorization', `Bearer ${customerToken}`)
        .expect(403);

      await request(app)
        .post('/api/admin/boat-owners/fake_id/approve')
        .set('Authorization', `Bearer ${customerToken}`)
        .expect(403);
    });

    test('should deny access without authentication token', async () => {
      await request(app)
        .get('/api/admin/boat-owners/pending')
        .expect(401);

      await request(app)
        .post('/api/admin/boat-owners/fake_id/approve')
        .expect(401);
    });

    test('should handle invalid or expired tokens', async () => {
      const invalidToken = 'invalid_token_string';
      const expiredToken = jwt.sign(
        { userId: 'fake_id', role: 'ADMIN' },
        process.env.JWT_SECRET || 'test_secret',
        { expiresIn: '-1h' } // Expired 1 hour ago
      );

      await request(app)
        .get('/api/admin/boat-owners/pending')
        .set('Authorization', `Bearer ${invalidToken}`)
        .expect(401);

      await request(app)
        .get('/api/admin/boat-owners/pending')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle approval of non-existent boat owner', async () => {
      const nonExistentId = 'non_existent_user_id';
      
      // Mock to simulate user not found
      const notFoundError = new Error('Record to update not found');
      notFoundError.code = 'P2025';
      mockPrisma.user.update.mockRejectedValue(notFoundError);

      await request(app)
        .post(`/api/admin/boat-owners/${nonExistentId}/approve`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });

    test('should handle rejection without reason', async () => {
      const testUserId = `test_user_${testUserCounter}`;

      // Try to reject without providing a reason
      await request(app)
        .post(`/api/admin/boat-owners/${testUserId}/reject`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({}) // No rejectionReason
        .expect(400);
    });

    test('should handle double approval attempt', async () => {
      const testUserId = `double_approve_${testUserCounter}`;
      
      // Mock user that is already approved
      const alreadyApprovedUser = {
        id: testUserId,
        email: '<EMAIL>',
        role: 'BOAT_OWNER',
        emailVerified: true,
        isApproved: true,
        approvedAt: new Date(),
        approvedBy: 'admin_123'
      };

      mockPrisma.user.findUnique.mockResolvedValue(alreadyApprovedUser);

      // Second approval attempt should handle gracefully
      const secondApprovalResponse = await request(app)
        .post(`/api/admin/boat-owners/${testUserId}/approve`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(400);

      expect(secondApprovalResponse.body.success).toBe(false);
      expect(secondApprovalResponse.body.message).toContain('already been processed');
    });

    test('should handle pagination in pending boat owners list', async () => {
      // Create mock data for pagination test
      const mockBoatOwners = Array.from({ length: 15 }, (_, i) => ({
        id: `user_${i}`,
        email: `testowner${i}@example.com`,
        role: 'BOAT_OWNER',
        emailVerified: true,
        isApproved: null,
        createdAt: new Date(),
        profile: {
          firstName: `Test${i}`,
          lastName: 'Owner',
          companyName: `Company ${i}`
        },
        provider: {
          companyName: `Company ${i}`,
          businessRegistrationNumber: `BRN${i}`
        }
      }));

      // Mock first page (10 items)
      mockPrisma.user.findMany.mockResolvedValue(mockBoatOwners.slice(0, 10));
      mockPrisma.user.count.mockResolvedValue(15);

      // Test first page
      const firstPageResponse = await request(app)
        .get('/api/admin/boat-owners/pending?page=1&limit=10')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(firstPageResponse.body.success).toBe(true);
      expect(firstPageResponse.body.data.boatOwners).toHaveLength(10);
      expect(firstPageResponse.body.data.pagination.total).toBe(15);
      expect(firstPageResponse.body.data.pagination.totalPages).toBe(2);

      // Mock second page (5 items)
      mockPrisma.user.findMany.mockResolvedValue(mockBoatOwners.slice(10, 15));

      // Test second page
      const secondPageResponse = await request(app)
        .get('/api/admin/boat-owners/pending?page=2&limit=10')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(secondPageResponse.body.success).toBe(true);
      expect(secondPageResponse.body.data.boatOwners).toHaveLength(5);
    });
  });

  describe('Database Consistency and Rollback', () => {
    test('should maintain database consistency during approval process', async () => {
      const testUserId = `consistency_${testUserCounter}`;
      
      // Mock initial state
      const initialUser = {
        id: testUserId,
        email: '<EMAIL>',
        role: 'BOAT_OWNER',
        emailVerified: true,
        isApproved: null,
        provider: {
          id: `provider_${testUserId}`,
          isActive: false
        }
      };

      // Mock approved state
      const approvedUser = {
        ...initialUser,
        isApproved: true,
        approvedAt: new Date(),
        approvedBy: 'admin_123',
        provider: {
          ...initialUser.provider,
          isActive: true
        }
      };

      mockPrisma.user.findUnique.mockResolvedValue(initialUser);
      mockPrisma.user.update.mockResolvedValue(approvedUser);
      mockPrisma.provider.update.mockResolvedValue(approvedUser.provider);

      // Approve the user
      await request(app)
        .post(`/api/admin/boat-owners/${testUserId}/approve`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      // Verify the mocks were called with correct data
      expect(mockPrisma.user.update).toHaveBeenCalledWith({
        where: { id: testUserId },
        data: {
          isApproved: true,
          approvedAt: expect.any(Date),
          approvedBy: 'admin_123'
        }
      });

      expect(mockPrisma.provider.update).toHaveBeenCalledWith({
        where: { userId: testUserId },
        data: {
          isActive: true,
          isVerified: true
        }
      });
    });

    test('should handle email service failures gracefully', async () => {
      const testUserId = `emailfail_${testUserCounter}`;
      
      // Mock email service to fail
      emailService.sendBoatOwnerApprovalEmail.mockRejectedValue(new Error('Email service down'));

      // Mock user data
      const mockUser = {
        id: testUserId,
        email: '<EMAIL>',
        role: 'BOAT_OWNER',
        emailVerified: true,
        isApproved: null,
        profile: {
          firstName: 'Email',
          lastName: 'Fail',
          companyName: 'Email Fail Company'
        }
      };

      const approvedUser = {
        ...mockUser,
        isApproved: true,
        approvedAt: new Date(),
        approvedBy: 'admin_123'
      };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.user.update.mockResolvedValue(approvedUser);
      mockPrisma.provider.update.mockResolvedValue({ isActive: true });

      // Approval should still succeed even if email fails
      const approvalResponse = await request(app)
        .post(`/api/admin/boat-owners/${testUserId}/approve`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(approvalResponse.body.success).toBe(true);
      expect(mockPrisma.user.update).toHaveBeenCalled();
    });
  });

  describe('Search and Filtering', () => {
    test('should filter pending boat owners by search criteria', async () => {
      // Mock boat owners with different details
      const allBoatOwners = [
        {
          id: 'marine_user',
          email: '<EMAIL>',
          role: 'BOAT_OWNER',
          emailVerified: true,
          isApproved: null,
          createdAt: new Date(),
          profile: {
            firstName: 'John',
            lastName: 'Ocean',
            companyName: 'Marine Adventures'
          },
          provider: {
            companyName: 'Marine Adventures',
            businessRegistrationNumber: 'BRN001'
          }
        },
        {
          id: 'sailing_user',
          email: '<EMAIL>',
          role: 'BOAT_OWNER',
          emailVerified: true,
          isApproved: null,
          createdAt: new Date(),
          profile: {
            firstName: 'Jane',
            lastName: 'Wind',
            companyName: 'Sailing Solutions'
          },
          provider: {
            companyName: 'Sailing Solutions',
            businessRegistrationNumber: 'BRN002'
          }
        },
        {
          id: 'yacht_user',
          email: '<EMAIL>',
          role: 'BOAT_OWNER',
          emailVerified: true,
          isApproved: null,
          createdAt: new Date(),
          profile: {
            firstName: 'Bob',
            lastName: 'Wave',
            companyName: 'Yacht Services'
          },
          provider: {
            companyName: 'Yacht Services',
            businessRegistrationNumber: 'BRN003'
          }
        }
      ];

      // Mock search by company name "Marine"
      mockPrisma.user.findMany.mockResolvedValue([allBoatOwners[0]]);
      mockPrisma.user.count.mockResolvedValue(1);

      // Search by company name
      const marineSearchResponse = await request(app)
        .get('/api/admin/boat-owners/pending?search=Marine')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(marineSearchResponse.body.success).toBe(true);
      expect(marineSearchResponse.body.data.boatOwners).toHaveLength(1);
      expect(marineSearchResponse.body.data.boatOwners[0].profile.companyName).toBe('Marine Adventures');

      // Mock search by email
      mockPrisma.user.findMany.mockResolvedValue([allBoatOwners[2]]);
      mockPrisma.user.count.mockResolvedValue(1);

      // Search by email
      const yachtSearchResponse = await request(app)
        .get('/api/admin/boat-owners/pending?search=<EMAIL>')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(yachtSearchResponse.body.success).toBe(true);
      expect(yachtSearchResponse.body.data.boatOwners).toHaveLength(1);
      expect(yachtSearchResponse.body.data.boatOwners[0].email).toBe('<EMAIL>');
    });
  });
});