const crypto = require('crypto');

/**
 * Data Protection Utility for GoSea Platform
 * Provides encryption, decryption, and masking for sensitive user data
 *
 * New Implementation: Uses AES-256-CBC with HMAC for authentication
 * Format: base64(iv):base64(hmac):base64(encrypted)
 */

class DataProtection {
  constructor() {
    // Use environment variable for encryption key, fallback for development
    this.encryptionKey = process.env.DATA_ENCRYPTION_KEY || 'gosea-dev-key-32-chars-long-123';
    this.algorithm = 'aes-256-cbc';
    this.ivLength = 16; // Standard IV length for CBC
    this.keyLength = 32; // 256 bits

    // Derive encryption and HMAC keys from the main key
    this.derivedKey = crypto.scryptSync(this.encryptionKey, 'gosea-salt', this.keyLength);
    this.hmacKey = crypto.scryptSync(this.encryptionKey, 'gosea-hmac-salt', this.keyLength);
  }

  /**
   * Encrypt sensitive data using AES-256-CBC with HMAC authentication
   * @param {string} text - Text to encrypt
   * @returns {string} - Encrypted text in format: base64(iv):base64(hmac):base64(encrypted)
   */
  encrypt(text) {
    if (!text || typeof text !== 'string') return null;

    try {
      // Generate random IV
      const iv = crypto.randomBytes(this.ivLength);

      // Create cipher with proper IV
      const cipher = crypto.createCipheriv(this.algorithm, this.derivedKey, iv);

      // Encrypt the text
      let encrypted = cipher.update(text, 'utf8', 'base64');
      encrypted += cipher.final('base64');

      // Create HMAC for authentication
      const hmac = crypto.createHmac('sha256', this.hmacKey);
      hmac.update(iv);
      hmac.update(Buffer.from(encrypted, 'base64'));
      const authTag = hmac.digest('base64');

      // Return format: iv:hmac:encrypted (all base64 encoded)
      return `${iv.toString('base64')}:${authTag}:${encrypted}`;
    } catch (error) {
      console.error('Encryption error:', error);
      return text; // Return original text if encryption fails
    }
  }

  /**
   * Decrypt sensitive data using new format or legacy fallback
   * @param {string} encryptedText - Encrypted text
   * @returns {string} - Decrypted text
   */
  decrypt(encryptedText) {
    if (!encryptedText || typeof encryptedText !== 'string') {
      return encryptedText;
    }

    // If the text doesn't contain ':', it's likely plain text (not encrypted)
    if (!encryptedText.includes(':')) {
      return encryptedText; // Return as-is if not encrypted format
    }

    try {
      const parts = encryptedText.split(':');

      // Try new format first: base64(iv):base64(hmac):base64(encrypted)
      if (parts.length === 3) {
        try {
          const [ivB64, hmacB64, encryptedB64] = parts;

          // Validate base64 format
          if (this.isValidBase64(ivB64) && this.isValidBase64(hmacB64) && this.isValidBase64(encryptedB64)) {
            return this.decryptNew(ivB64, hmacB64, encryptedB64);
          }
        } catch (newFormatError) {
          console.log('New format decryption failed, trying legacy...');
        }
      }

      // Fallback to legacy decryption for old data
      return this.legacyDecrypt(encryptedText);
    } catch (error) {
      console.error('Decryption error:', error);
      return '[Encrypted Data]'; // Return placeholder for failed decryption
    }
  }

  /**
   * Decrypt using new format: base64(iv):base64(hmac):base64(encrypted)
   */
  decryptNew(ivB64, hmacB64, encryptedB64) {
    const iv = Buffer.from(ivB64, 'base64');
    const expectedHmac = Buffer.from(hmacB64, 'base64');
    const encryptedData = Buffer.from(encryptedB64, 'base64');

    // Verify HMAC
    const hmac = crypto.createHmac('sha256', this.hmacKey);
    hmac.update(iv);
    hmac.update(encryptedData);
    const calculatedHmac = hmac.digest();

    if (!crypto.timingSafeEqual(expectedHmac, calculatedHmac)) {
      throw new Error('HMAC verification failed');
    }

    // Decrypt
    const decipher = crypto.createDecipheriv(this.algorithm, this.derivedKey, iv);
    let decrypted = decipher.update(encryptedData, null, 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }

  /**
   * Check if string is valid base64
   */
  isValidBase64(str) {
    try {
      return Buffer.from(str, 'base64').toString('base64') === str;
    } catch {
      return false;
    }
  }

  /**
   * Legacy decrypt function for backward compatibility
   * @param {string} encryptedText - Encrypted text in old format
   * @returns {string} - Decrypted text
   */
  /**
   * Legacy decrypt function for backward compatibility with old encrypted data
   * Handles various old encryption formats and plain text data
   */
  legacyDecrypt(encryptedText) {
    try {
      // Check if the text looks like it might be plain text
      const isLikelyPlainText =
        encryptedText.includes('+') ||
        encryptedText.includes('@') ||
        /^[a-zA-Z\s\-\.]+$/.test(encryptedText) ||
        /^\d{4}-\d{2}-\d{2}/.test(encryptedText) || // Date format
        (encryptedText.length < 20 && !encryptedText.includes(':')); // Short strings without colons

      if (isLikelyPlainText) {
        console.log('Data appears to be plain text, returning as-is');
        return encryptedText;
      }

      // For complex encrypted data that we can't decrypt, return a user-friendly placeholder
      if (encryptedText.includes(':') && encryptedText.length > 30) {
        // Check if it looks like phone number pattern
        if (encryptedText.includes('60') || encryptedText.includes('01')) {
          return '+60xxxxxxxxx'; // Phone number placeholder
        }
        // Check if it looks like address pattern (multiple parts)
        const parts = encryptedText.split(':');
        if (parts.length >= 4) {
          return '[Address Information]'; // Address placeholder
        }
        return '[Protected Information]'; // Generic placeholder
      }

      // If it's short and contains colons, might be corrupted - return placeholder
      return '[Data Not Available]';
    } catch (error) {
      console.error('Legacy decryption error:', error);
      return '[Protected Information]';
    }
  }

  /**
   * Mask email address for display
   * @param {string} email - Email to mask
   * @returns {string} - Masked email
   */
  maskEmail(email) {
    if (!email) return '';
    
    const [localPart, domain] = email.split('@');
    if (!domain) return email;
    
    if (localPart.length <= 2) {
      return `${localPart[0]}*@${domain}`;
    }
    
    const maskedLocal = localPart[0] + '*'.repeat(localPart.length - 2) + localPart[localPart.length - 1];
    return `${maskedLocal}@${domain}`;
  }

  /**
   * Mask phone number for display
   * @param {string} phone - Phone number to mask
   * @returns {string} - Masked phone number
   */
  maskPhone(phone) {
    if (!phone) return '';
    
    // Remove all non-digit characters for processing
    const digits = phone.replace(/\D/g, '');
    
    if (digits.length < 4) {
      return '*'.repeat(digits.length);
    }
    
    // Show first 2 and last 2 digits, mask the middle
    const first = digits.substring(0, 2);
    const last = digits.substring(digits.length - 2);
    const middle = '*'.repeat(digits.length - 4);
    
    // Preserve original formatting if it was a Malaysian number
    if (phone.startsWith('+60')) {
      return `+60${first}${middle}${last}`;
    } else if (phone.startsWith('0')) {
      return `0${first}${middle}${last}`;
    }
    
    return `${first}${middle}${last}`;
  }

  /**
   * Hash sensitive data for storage (one-way)
   * @param {string} data - Data to hash
   * @param {string} salt - Salt for hashing
   * @returns {string} - Hashed data
   */
  hashData(data, salt = '') {
    if (!data) return null;
    
    const hash = crypto.createHash('sha256');
    hash.update(data + salt);
    return hash.digest('hex');
  }

  /**
   * Generate a random salt
   * @returns {string} - Random salt
   */
  generateSalt() {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * Encrypt user profile data before storage
   * @param {Object} profileData - Profile data to encrypt
   * @returns {Object} - Profile data with encrypted sensitive fields
   */
  encryptProfileData(profileData) {
    const encrypted = { ...profileData };
    
    if (encrypted.phone) {
      encrypted.phone = this.encrypt(encrypted.phone);
    }
    
    if (encrypted.emergencyContact) {
      encrypted.emergencyContact = this.encrypt(encrypted.emergencyContact);
    }

    // Encrypt new address fields
    if (encrypted.personalAddress1) {
      encrypted.personalAddress1 = this.encrypt(encrypted.personalAddress1);
    }

    if (encrypted.personalAddress2) {
      encrypted.personalAddress2 = this.encrypt(encrypted.personalAddress2);
    }

    if (encrypted.personalPostcode) {
      encrypted.personalPostcode = this.encrypt(encrypted.personalPostcode);
    }

    if (encrypted.personalCity) {
      encrypted.personalCity = this.encrypt(encrypted.personalCity);
    }

    if (encrypted.personalState) {
      encrypted.personalState = this.encrypt(encrypted.personalState);
    }
    
    return encrypted;
  }

  /**
   * Decrypt user profile data after retrieval
   * @param {Object} profileData - Profile data to decrypt
   * @returns {Object} - Profile data with decrypted sensitive fields
   */
  decryptProfileData(profileData) {
    if (!profileData) return profileData;
    
    const decrypted = { ...profileData };
    
    if (decrypted.phone) {
      decrypted.phone = this.decrypt(decrypted.phone);
    }
    
    if (decrypted.emergencyContact) {
      decrypted.emergencyContact = this.decrypt(decrypted.emergencyContact);
    }

    // Decrypt new address fields
    if (decrypted.personalAddress1) {
      decrypted.personalAddress1 = this.decrypt(decrypted.personalAddress1);
    }

    if (decrypted.personalAddress2) {
      decrypted.personalAddress2 = this.decrypt(decrypted.personalAddress2);
    }

    if (decrypted.personalPostcode) {
      decrypted.personalPostcode = this.decrypt(decrypted.personalPostcode);
    }

    if (decrypted.personalCity) {
      decrypted.personalCity = this.decrypt(decrypted.personalCity);
    }

    if (decrypted.personalState) {
      decrypted.personalState = this.decrypt(decrypted.personalState);
    }
    
    return decrypted;
  }

  /**
   * Encrypt user email before storage
   * @param {string} email - Email to encrypt
   * @returns {string} - Encrypted email
   */
  encryptEmail(email) {
    return this.encrypt(email);
  }

  /**
   * Decrypt user email after retrieval
   * @param {string} encryptedEmail - Encrypted email
   * @returns {string} - Decrypted email
   */
  decryptEmail(encryptedEmail) {
    return this.decrypt(encryptedEmail);
  }

  /**
   * Get masked user data for API responses
   * @param {Object} user - User object with profile
   * @returns {Object} - User object with masked sensitive data
   */
  getMaskedUserData(user) {
    if (!user) return user;
    
    const maskedUser = { ...user };
    
    // Mask email
    if (maskedUser.email) {
      maskedUser.email = this.maskEmail(maskedUser.email);
    }
    
    // Mask profile data
    if (maskedUser.profile) {
      maskedUser.profile = { ...maskedUser.profile };
      
      if (maskedUser.profile.phone) {
        maskedUser.profile.phone = this.maskPhone(maskedUser.profile.phone);
      }
      
      if (maskedUser.profile.emergencyContact) {
        maskedUser.profile.emergencyContact = this.maskPhone(maskedUser.profile.emergencyContact);
      }

      // Mask new address fields
      if (maskedUser.profile.personalAddress1) {
        const addressParts = maskedUser.profile.personalAddress1.split(' ');
        if (addressParts.length > 2) {
          maskedUser.profile.personalAddress1 = `${addressParts[0]} ${'*'.repeat(5)} ${addressParts[addressParts.length - 1]}`;
        } else {
          maskedUser.profile.personalAddress1 = `${addressParts[0]}${'*'.repeat(3)}`;
        }
      }

      if (maskedUser.profile.personalAddress2) {
        maskedUser.profile.personalAddress2 = `${'*'.repeat(maskedUser.profile.personalAddress2.length)}`;
      }

      if (maskedUser.profile.personalPostcode) {
        maskedUser.profile.personalPostcode = `${maskedUser.profile.personalPostcode.substring(0, 2)}***`;
      }

      if (maskedUser.profile.personalCity) {
        maskedUser.profile.personalCity = `${maskedUser.profile.personalCity.substring(0, 2)}${'*'.repeat(3)}`;
      }

      if (maskedUser.profile.personalState) {
        maskedUser.profile.personalState = `${maskedUser.profile.personalState.substring(0, 2)}${'*'.repeat(3)}`;
      }
    }
    
    return maskedUser;
  }
}

module.exports = new DataProtection();
