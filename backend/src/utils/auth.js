const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');

/**
 * Authentication utilities for GoSea platform
 */

// Constants
const SALT_ROUNDS = 12;
const JWT_EXPIRES_IN = '24h';
const REFRESH_TOKEN_EXPIRES_IN = '7d';
const PASSWORD_RESET_EXPIRES_IN = 60 * 60 * 1000; // 1 hour in milliseconds
const EMAIL_VERIFICATION_EXPIRES_IN = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
const MAX_LOGIN_ATTEMPTS = 5;
const LOCK_TIME = 2 * 60 * 60 * 1000; // 2 hours in milliseconds

/**
 * Hash a password using bcrypt
 * @param {string} password - Plain text password
 * @returns {Promise<string>} - Hashed password
 */
async function hashPassword(password) {
  try {
    const salt = await bcrypt.genSalt(SALT_ROUNDS);
    return await bcrypt.hash(password, salt);
  } catch (error) {
    throw new Error('Error hashing password');
  }
}

/**
 * Compare a password with its hash
 * @param {string} password - Plain text password
 * @param {string} hash - Hashed password
 * @returns {Promise<boolean>} - True if password matches
 */
async function comparePassword(password, hash) {
  try {
    return await bcrypt.compare(password, hash);
  } catch (error) {
    throw new Error('Error comparing password');
  }
}

/**
 * Generate JWT access token
 * @param {Object} payload - Token payload
 * @returns {string} - JWT token
 */
function generateAccessToken(payload) {
  return jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
    issuer: 'gosea-platform',
    audience: 'gosea-users'
  });
}

/**
 * Generate JWT refresh token
 * @param {Object} payload - Token payload
 * @returns {string} - JWT refresh token
 */
function generateRefreshToken(payload) {
  return jwt.sign(payload, process.env.JWT_REFRESH_SECRET, {
    expiresIn: REFRESH_TOKEN_EXPIRES_IN,
    issuer: 'gosea-platform',
    audience: 'gosea-users'
  });
}

/**
 * Verify JWT token
 * @param {string} token - JWT token
 * @param {string} secret - JWT secret
 * @returns {Object} - Decoded token payload
 */
function verifyToken(token, secret = process.env.JWT_SECRET) {
  try {
    return jwt.verify(token, secret, {
      issuer: 'gosea-platform',
      audience: 'gosea-users'
    });
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('Token expired');
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('Invalid token');
    } else {
      throw new Error('Token verification failed');
    }
  }
}

/**
 * Generate secure random token
 * @param {number} length - Token length (default: 32)
 * @returns {string} - Random token
 */
function generateSecureToken(length = 32) {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Generate password reset token
 * @returns {Object} - Token and expiry date
 */
function generatePasswordResetToken() {
  const token = generateSecureToken();
  const expiresAt = new Date(Date.now() + PASSWORD_RESET_EXPIRES_IN);
  return { token, expiresAt };
}

/**
 * Generate email verification token
 * @returns {Object} - Token and expiry date
 */
function generateEmailVerificationToken() {
  const token = generateSecureToken();
  const expiresAt = new Date(Date.now() + EMAIL_VERIFICATION_EXPIRES_IN);
  return { token, expiresAt };
}

/**
 * Generate session tokens
 * @param {Object} user - User object
 * @returns {Object} - Session and refresh tokens with metadata
 */
function generateSessionTokens(user) {
  const sessionToken = uuidv4();
  const refreshToken = generateRefreshToken({ 
    userId: user.id, 
    sessionToken,
    role: user.role 
  });
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

  return {
    sessionToken,
    refreshToken,
    expiresAt
  };
}

/**
 * Check if user account is locked
 * @param {Object} user - User object
 * @returns {boolean} - True if account is locked
 */
function isAccountLocked(user) {
  return user.lockedUntil && user.lockedUntil > new Date();
}

/**
 * Check if user should be locked after failed login
 * @param {Object} user - User object
 * @returns {boolean} - True if account should be locked
 */
function shouldLockAccount(user) {
  return user.loginAttempts >= MAX_LOGIN_ATTEMPTS;
}

/**
 * Get account lock time
 * @returns {Date} - Lock expiry time
 */
function getAccountLockTime() {
  return new Date(Date.now() + LOCK_TIME);
}

/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {Object} - Validation result
 */
function validatePasswordStrength(password) {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  const errors = [];
  
  if (password.length < minLength) {
    errors.push(`Password must be at least ${minLength} characters long`);
  }
  if (!hasUpperCase) {
    errors.push('Password must contain at least one uppercase letter');
  }
  if (!hasLowerCase) {
    errors.push('Password must contain at least one lowercase letter');
  }
  if (!hasNumbers) {
    errors.push('Password must contain at least one number');
  }
  if (!hasSpecialChar) {
    errors.push('Password must contain at least one special character');
  }

  return {
    isValid: errors.length === 0,
    errors,
    strength: calculatePasswordStrength(password)
  };
}

/**
 * Calculate password strength score
 * @param {string} password - Password to evaluate
 * @returns {string} - Strength level (weak, medium, strong)
 */
function calculatePasswordStrength(password) {
  let score = 0;
  
  // Length
  if (password.length >= 8) score += 1;
  if (password.length >= 12) score += 1;
  
  // Character types
  if (/[a-z]/.test(password)) score += 1;
  if (/[A-Z]/.test(password)) score += 1;
  if (/\d/.test(password)) score += 1;
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;
  
  // Complexity
  if (password.length >= 16) score += 1;
  if (/[!@#$%^&*(),.?":{}|<>].*[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;

  if (score <= 3) return 'weak';
  if (score <= 5) return 'medium';
  return 'strong';
}

/**
 * Sanitize user data for JWT payload
 * @param {Object} user - User object
 * @returns {Object} - Sanitized user data
 */
function sanitizeUserForToken(user) {
  const sanitized = {
    id: user.id,
    email: user.email,
    role: user.role,
    emailVerified: user.emailVerified,
    isApproved: user.isApproved
  };

  // Include profile data if available
  if (user.profile) {
    sanitized.profile = {
      id: user.profile.id,
      firstName: user.profile.firstName,
      lastName: user.profile.lastName,
      phone: user.profile.phone,
      language: user.profile.language || 'en'
    };
  }

  return sanitized;
}

/**
 * Generate user session data
 * @param {Object} user - User object
 * @param {string} ipAddress - Client IP address
 * @param {string} userAgent - Client user agent
 * @returns {Object} - Session data
 */
function generateSessionData(user, ipAddress, userAgent) {
  const accessToken = generateAccessToken(sanitizeUserForToken(user));
  const sessionTokens = generateSessionTokens(user);
  
  return {
    accessToken,
    ...sessionTokens,
    user: sanitizeUserForToken(user),
    ipAddress,
    userAgent,
    lastActiveAt: new Date()
  };
}

module.exports = {
  // Password utilities
  hashPassword,
  comparePassword,
  validatePasswordStrength,
  
  // Token utilities
  generateAccessToken,
  generateRefreshToken,
  verifyToken,
  generateSecureToken,
  generatePasswordResetToken,
  generateEmailVerificationToken,
  generateSessionTokens,
  
  // Account security
  isAccountLocked,
  shouldLockAccount,
  getAccountLockTime,
  
  // Session management
  sanitizeUserForToken,
  generateSessionData,
  
  // Constants
  MAX_LOGIN_ATTEMPTS,
  LOCK_TIME,
  PASSWORD_RESET_EXPIRES_IN,
  EMAIL_VERIFICATION_EXPIRES_IN
};
