const nodemailer = require('nodemailer');
const crypto = require('crypto');

/**
 * Email Service for GoSea Platform
 * Handles all email communications including verification, password reset, etc.
 */

class EmailService {
  constructor() {
    this.transporter = null;
    this.initializeTransporter();
  }

  /**
   * Initialize email transporter (MailHog for development, Gmail for production)
   */
  initializeTransporter() {
    try {
      // Check if we should use real email (Gmail) or MailHog
      const useRealEmail = process.env.USE_REAL_EMAIL === 'true';
      console.log('USE_REAL_EMAIL environment variable:', process.env.USE_REAL_EMAIL);
      console.log('useRealEmail boolean:', useRealEmail);

      if (useRealEmail) {
        // Gmail SMTP configuration
        this.transporter = nodemailer.createTransport({
          service: 'gmail',
          auth: {
            user: process.env.GMAIL_USER, // Your Gmail address
            pass: process.env.GMAIL_APP_PASSWORD // Gmail App Password (not regular password)
          }
        });
        console.log('Email transporter initialized with Gmail SMTP');
      } else {
        // MailHog configuration for development
        this.transporter = nodemailer.createTransport({
          host: process.env.SMTP_HOST || 'mailhog',
          port: parseInt(process.env.SMTP_PORT) || 1025,
          secure: false, // true for 465, false for other ports
          auth: false, // MailHog doesn't require authentication
          tls: {
            rejectUnauthorized: false
          }
        });
        console.log('Email transporter initialized with MailHog (development mode)');
      }
    } catch (error) {
      console.error('Failed to initialize email transporter:', error);
    }
  }

  /**
   * Generate verification token
   */
  generateVerificationToken() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Send email verification
   */
  async sendVerificationEmail(email, firstName, verificationToken) {
    try {
      const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/verify-email-confirm?token=${verificationToken}`;
      
      const mailOptions = {
        from: {
          name: 'GoSea Platform',
          address: process.env.FROM_EMAIL || '<EMAIL>'
        },
        to: email,
        subject: 'Verify Your GoSea Account',
        html: this.getVerificationEmailTemplate(firstName, verificationUrl, email),
        text: this.getVerificationEmailText(firstName, verificationUrl)
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Verification email sent successfully:', result.messageId);
      
      return {
        success: true,
        messageId: result.messageId,
        verificationToken
      };
    } catch (error) {
      console.error('Failed to send verification email:', error);
      throw new Error('Failed to send verification email');
    }
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(email, firstName, resetToken) {
    try {
      const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password?token=${resetToken}`;
      
      const mailOptions = {
        from: {
          name: 'GoSea Platform',
          address: process.env.FROM_EMAIL || '<EMAIL>'
        },
        to: email,
        subject: 'Reset Your GoSea Password',
        html: this.getPasswordResetEmailTemplate(firstName, resetUrl),
        text: this.getPasswordResetEmailText(firstName, resetUrl)
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Password reset email sent successfully:', result.messageId);
      
      return {
        success: true,
        messageId: result.messageId
      };
    } catch (error) {
      console.error('Failed to send password reset email:', error);
      throw new Error('Failed to send password reset email');
    }
  }

  /**
   * Send welcome email
   */
  async sendWelcomeEmail(email, firstName) {
    try {
      const mailOptions = {
        from: {
          name: 'GoSea Platform',
          address: process.env.FROM_EMAIL || '<EMAIL>'
        },
        to: email,
        subject: 'Welcome to GoSea Platform! 🌊',
        html: this.getWelcomeEmailTemplate(firstName),
        text: this.getWelcomeEmailText(firstName)
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Welcome email sent successfully:', result.messageId);
      
      return {
        success: true,
        messageId: result.messageId
      };
    } catch (error) {
      console.error('Failed to send welcome email:', error);
      throw new Error('Failed to send welcome email');
    }
  }

  /**
   * HTML template for verification email
   */
  getVerificationEmailTemplate(firstName, verificationUrl, email) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Verify Your GoSea Account</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #0ea5e9 0%, #06b6d4 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #0ea5e9; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .logo { font-size: 24px; font-weight: bold; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">🌊 GoSea Platform</div>
        <h1>Verify Your Account</h1>
      </div>
      <div class="content">
        <h2>Hello ${firstName}!</h2>
        <p>Thank you for joining GoSea Platform! To complete your registration and start your maritime journey, please verify your email address.</p>
        
        <p><strong>Email:</strong> ${email}</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationUrl}" class="button">Verify My Account</a>
        </div>
        
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; background: #e2e8f0; padding: 10px; border-radius: 5px;">${verificationUrl}</p>
        
        <p><strong>This verification link will expire in 24 hours.</strong></p>
        
        <p>If you didn't create an account with GoSea, please ignore this email.</p>
        
        <p>Welcome aboard!<br>The GoSea Team</p>
      </div>
      <div class="footer">
        <p>© 2024 GoSea Platform. All rights reserved.</p>
        <p>This email was sent to ${email}</p>
      </div>
    </body>
    </html>
    `;
  }

  /**
   * Plain text version of verification email
   */
  getVerificationEmailText(firstName, verificationUrl) {
    return `
Hello ${firstName}!

Thank you for joining GoSea Platform! To complete your registration and start your maritime journey, please verify your email address.

Click here to verify your account: ${verificationUrl}

This verification link will expire in 24 hours.

If you didn't create an account with GoSea, please ignore this email.

Welcome aboard!
The GoSea Team

© 2024 GoSea Platform. All rights reserved.
    `;
  }

  /**
   * HTML template for password reset email
   */
  getPasswordResetEmailTemplate(firstName, resetUrl) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Reset Your GoSea Password</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #dc2626; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .logo { font-size: 24px; font-weight: bold; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">🌊 GoSea Platform</div>
        <h1>Password Reset Request</h1>
      </div>
      <div class="content">
        <h2>Hello ${firstName}!</h2>
        <p>We received a request to reset your GoSea account password. Click the button below to create a new password:</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetUrl}" class="button">Reset My Password</a>
        </div>
        
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; background: #e2e8f0; padding: 10px; border-radius: 5px;">${resetUrl}</p>
        
        <p><strong>This reset link will expire in 1 hour.</strong></p>
        
        <p>If you didn't request a password reset, please ignore this email. Your password will remain unchanged.</p>
        
        <p>Best regards,<br>The GoSea Team</p>
      </div>
      <div class="footer">
        <p>© 2024 GoSea Platform. All rights reserved.</p>
      </div>
    </body>
    </html>
    `;
  }

  /**
   * Plain text version of password reset email
   */
  getPasswordResetEmailText(firstName, resetUrl) {
    return `
Hello ${firstName}!

We received a request to reset your GoSea account password. Click the link below to create a new password:

${resetUrl}

This reset link will expire in 1 hour.

If you didn't request a password reset, please ignore this email. Your password will remain unchanged.

Best regards,
The GoSea Team

© 2024 GoSea Platform. All rights reserved.
    `;
  }

  /**
   * HTML template for welcome email
   */
  getWelcomeEmailTemplate(firstName) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to GoSea Platform!</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #059669 0%, #10b981 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #059669; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .logo { font-size: 24px; font-weight: bold; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">🌊 GoSea Platform</div>
        <h1>Welcome Aboard!</h1>
      </div>
      <div class="content">
        <h2>Hello ${firstName}!</h2>
        <p>Welcome to GoSea Platform! Your account has been successfully verified and you're ready to start your maritime adventure.</p>
        
        <p>Here's what you can do now:</p>
        <ul>
          <li>🚤 Browse and book amazing boat experiences</li>
          <li>⚓ List your own boats (if you're a boat owner)</li>
          <li>💰 Earn commissions as an affiliate agent</li>
          <li>🌊 Connect with the maritime community</li>
        </ul>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.FRONTEND_URL || 'http://localhost:3000'}/dashboard" class="button">Go to Dashboard</a>
        </div>
        
        <p>If you have any questions, our support team is here to <NAME_EMAIL></p>
        
        <p>Fair winds and following seas!<br>The GoSea Team</p>
      </div>
      <div class="footer">
        <p>© 2024 GoSea Platform. All rights reserved.</p>
      </div>
    </body>
    </html>
    `;
  }

  /**
   * Plain text version of welcome email
   */
  getWelcomeEmailText(firstName) {
    return `
Hello ${firstName}!

Welcome to GoSea Platform! Your account has been successfully verified and you're ready to start your maritime adventure.

Here's what you can do now:
- Browse and book amazing boat experiences
- List your own boats (if you're a boat owner)
- Earn commissions as an affiliate agent
- Connect with the maritime community

Visit your dashboard: ${process.env.FRONTEND_URL || 'http://localhost:3000'}/dashboard

If you have any questions, our support team is here to <NAME_EMAIL>

Fair winds and following seas!
The GoSea Team

© 2024 GoSea Platform. All rights reserved.
    `;
  }

  /**
   * Send boat owner email verification
   */
  async sendBoatOwnerVerificationEmail(email, firstName, verificationToken) {
    try {
      const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/verify-boat-owner-email?token=${verificationToken}`;
      
      const mailOptions = {
        from: {
          name: 'GoSea Platform',
          address: process.env.FROM_EMAIL || '<EMAIL>'
        },
        to: email,
        subject: 'Verify Your Boat Owner Account - GoSea Platform',
        html: this.getBoatOwnerVerificationEmailTemplate(firstName, verificationUrl, email),
        text: this.getBoatOwnerVerificationEmailText(firstName, verificationUrl)
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Boat owner verification email sent successfully:', result.messageId);
      
      return {
        success: true,
        messageId: result.messageId,
        verificationToken
      };
    } catch (error) {
      console.error('Failed to send boat owner verification email:', error);
      throw new Error('Failed to send boat owner verification email');
    }
  }

  /**
   * Send admin notification for boat owner approval
   */
  async sendAdminApprovalNotification(notificationData) {
    try {
      const { adminEmail, boatOwner, approvalLink } = notificationData;
      
      const mailOptions = {
        from: {
          name: 'GoSea Platform Admin',
          address: process.env.FROM_EMAIL || '<EMAIL>'
        },
        to: adminEmail,
        subject: 'New Boat Owner Registration - Approval Required',
        html: this.getAdminApprovalNotificationTemplate(boatOwner, approvalLink),
        text: this.getAdminApprovalNotificationText(boatOwner, approvalLink)
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Admin approval notification sent successfully:', result.messageId);
      
      return {
        success: true,
        messageId: result.messageId
      };
    } catch (error) {
      console.error('Failed to send admin approval notification:', error);
      throw new Error('Failed to send admin approval notification');
    }
  }

  /**
   * Send boat owner approval email
   */
  async sendBoatOwnerApprovalEmail(email, firstName, companyName) {
    try {
      const mailOptions = {
        from: {
          name: 'GoSea Platform',
          address: process.env.FROM_EMAIL || '<EMAIL>'
        },
        to: email,
        subject: 'Your Boat Owner Account Has Been Approved! 🎉',
        html: this.getBoatOwnerApprovalEmailTemplate(firstName, companyName),
        text: this.getBoatOwnerApprovalEmailText(firstName, companyName)
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Boat owner approval email sent successfully:', result.messageId);
      
      return {
        success: true,
        messageId: result.messageId
      };
    } catch (error) {
      console.error('Failed to send boat owner approval email:', error);
      throw new Error('Failed to send boat owner approval email');
    }
  }

  /**
   * Send boat owner rejection email
   */
  async sendBoatOwnerRejectionEmail(email, firstName, rejectionReason) {
    try {
      const mailOptions = {
        from: {
          name: 'GoSea Platform',
          address: process.env.FROM_EMAIL || '<EMAIL>'
        },
        to: email,
        subject: 'Update on Your Boat Owner Application',
        html: this.getBoatOwnerRejectionEmailTemplate(firstName, rejectionReason),
        text: this.getBoatOwnerRejectionEmailText(firstName, rejectionReason)
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Boat owner rejection email sent successfully:', result.messageId);
      
      return {
        success: true,
        messageId: result.messageId
      };
    } catch (error) {
      console.error('Failed to send boat owner rejection email:', error);
      throw new Error('Failed to send boat owner rejection email');
    }
  }

  /**
   * HTML template for boat owner verification email
   */
  getBoatOwnerVerificationEmailTemplate(firstName, verificationUrl, email) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Verify Your Boat Owner Account</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #1e40af; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .logo { font-size: 24px; font-weight: bold; }
        .highlight { background: #dbeafe; padding: 15px; border-radius: 5px; border-left: 4px solid #3b82f6; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">⚓ GoSea Platform</div>
        <h1>Boat Owner Registration</h1>
      </div>
      <div class="content">
        <h2>Hello ${firstName}!</h2>
        <p>Thank you for registering as a boat owner with GoSea Platform! To complete your registration, please verify your email address.</p>
        
        <p><strong>Business Email:</strong> ${email}</p>
        
        <div class="highlight">
          <p><strong>📋 Next Steps:</strong></p>
          <ol>
            <li>Click the verification button below</li>
            <li>Your application will be reviewed by our admin team</li>
            <li>You'll receive approval notification within 1-2 business days</li>
          </ol>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationUrl}" class="button">Verify My Business Account</a>
        </div>
        
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; background: #e2e8f0; padding: 10px; border-radius: 5px;">${verificationUrl}</p>
        
        <p><strong>This verification link will expire in 24 hours.</strong></p>
        
        <p>If you didn't create a boat owner account with GoSea, please ignore this email.</p>
        
        <p>Welcome to the GoSea maritime business community!<br>The GoSea Team</p>
      </div>
      <div class="footer">
        <p>© 2024 GoSea Platform. All rights reserved.</p>
        <p>This email was sent to ${email}</p>
      </div>
    </body>
    </html>
    `;
  }

  /**
   * Plain text version of boat owner verification email
   */
  getBoatOwnerVerificationEmailText(firstName, verificationUrl) {
    return `
Hello ${firstName}!

Thank you for registering as a boat owner with GoSea Platform! To complete your registration, please verify your email address.

Next Steps:
1. Click the verification link below
2. Your application will be reviewed by our admin team
3. You'll receive approval notification within 1-2 business days

Verify your business account: ${verificationUrl}

This verification link will expire in 24 hours.

If you didn't create a boat owner account with GoSea, please ignore this email.

Welcome to the GoSea maritime business community!
The GoSea Team

© 2024 GoSea Platform. All rights reserved.
    `;
  }

  /**
   * HTML template for admin approval notification
   */
  getAdminApprovalNotificationTemplate(boatOwner, approvalLink) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New Boat Owner Registration - Approval Required</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #dc2626; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .logo { font-size: 24px; font-weight: bold; }
        .details { background: #ffffff; padding: 20px; border-radius: 5px; margin: 20px 0; border: 1px solid #e2e8f0; }
        .detail-row { display: flex; margin-bottom: 10px; }
        .detail-label { font-weight: bold; min-width: 120px; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">🚨 GoSea Admin</div>
        <h1>New Boat Owner Registration</h1>
      </div>
      <div class="content">
        <h2>Approval Required</h2>
        <p>A new boat owner has registered and verified their email address. Please review and approve their application.</p>
        
        <div class="details">
          <h3>📋 Application Details</h3>
          <div class="detail-row">
            <span class="detail-label">Name:</span>
            <span>${boatOwner.name}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Email:</span>
            <span>${boatOwner.email}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Company:</span>
            <span>${boatOwner.companyName}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">BRN:</span>
            <span>${boatOwner.brn}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Registration:</span>
            <span>${new Date(boatOwner.registrationDate).toLocaleDateString()}</span>
          </div>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${approvalLink}" class="button">Review Application</a>
        </div>
        
        <p>Please review the application and either approve or reject it through the admin dashboard.</p>
        
        <p>Best regards,<br>GoSea Platform System</p>
      </div>
      <div class="footer">
        <p>© 2024 GoSea Platform. All rights reserved.</p>
      </div>
    </body>
    </html>
    `;
  }

  /**
   * Plain text version of admin approval notification
   */
  getAdminApprovalNotificationText(boatOwner, approvalLink) {
    return `
GoSea Platform - New Boat Owner Registration

Approval Required

A new boat owner has registered and verified their email address. Please review and approve their application.

Application Details:
Name: ${boatOwner.name}
Email: ${boatOwner.email}
Company: ${boatOwner.companyName}
BRN: ${boatOwner.brn}
Registration Date: ${new Date(boatOwner.registrationDate).toLocaleDateString()}

Review Application: ${approvalLink}

Please review the application and either approve or reject it through the admin dashboard.

Best regards,
GoSea Platform System

© 2024 GoSea Platform. All rights reserved.
    `;
  }

  /**
   * HTML template for boat owner approval email
   */
  getBoatOwnerApprovalEmailTemplate(firstName, companyName) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Your Boat Owner Account Has Been Approved!</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #059669 0%, #10b981 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #059669; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .logo { font-size: 24px; font-weight: bold; }
        .highlight { background: #d1fae5; padding: 15px; border-radius: 5px; border-left: 4px solid #10b981; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">🎉 GoSea Platform</div>
        <h1>Account Approved!</h1>
      </div>
      <div class="content">
        <h2>Congratulations ${firstName}!</h2>
        <p>Great news! Your boat owner account for <strong>${companyName}</strong> has been approved and is now active on GoSea Platform.</p>
        
        <div class="highlight">
          <p><strong>✅ You can now:</strong></p>
          <ul>
            <li>Sign in to your boat owner dashboard</li>
            <li>List and manage your boat fleet</li>
            <li>Create and configure marine services</li>
            <li>Set pricing and availability</li>
            <li>Accept bookings from customers</li>
            <li>Track your business performance</li>
          </ul>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.FRONTEND_URL || 'http://localhost:3000'}/boat-owner/dashboard" class="button">Access Your Dashboard</a>
        </div>
        
        <p><strong>🚀 Getting Started:</strong></p>
        <ol>
          <li>Complete your provider profile</li>
          <li>Add your boats to the platform</li>
          <li>Create your first service offerings</li>
          <li>Set your operating areas and availability</li>
        </ol>
        
        <p>If you need any assistance getting started, our support team is here to <NAME_EMAIL></p>
        
        <p>Welcome to the GoSea maritime business community!<br>The GoSea Team</p>
      </div>
      <div class="footer">
        <p>© 2024 GoSea Platform. All rights reserved.</p>
      </div>
    </body>
    </html>
    `;
  }

  /**
   * Plain text version of boat owner approval email
   */
  getBoatOwnerApprovalEmailText(firstName, companyName) {
    return `
Congratulations ${firstName}!

Great news! Your boat owner account for ${companyName} has been approved and is now active on GoSea Platform.

You can now:
- Sign in to your boat owner dashboard
- List and manage your boat fleet
- Create and configure marine services
- Set pricing and availability
- Accept bookings from customers
- Track your business performance

Access your dashboard: ${process.env.FRONTEND_URL || 'http://localhost:3000'}/boat-owner/dashboard

Getting Started:
1. Complete your provider profile
2. Add your boats to the platform
3. Create your first service offerings
4. Set your operating areas and availability

If you need any assistance getting started, our support team is here to <NAME_EMAIL>

Welcome to the GoSea maritime business community!
The GoSea Team

© 2024 GoSea Platform. All rights reserved.
    `;
  }

  /**
   * HTML template for boat owner rejection email
   */
  getBoatOwnerRejectionEmailTemplate(firstName, rejectionReason) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Update on Your Boat Owner Application</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #dc2626; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .logo { font-size: 24px; font-weight: bold; }
        .highlight { background: #fee2e2; padding: 15px; border-radius: 5px; border-left: 4px solid #ef4444; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">📧 GoSea Platform</div>
        <h1>Application Update</h1>
      </div>
      <div class="content">
        <h2>Hello ${firstName},</h2>
        <p>Thank you for your interest in becoming a boat owner on GoSea Platform. After reviewing your application, we are unable to approve your account at this time.</p>
        
        <div class="highlight">
          <p><strong>📋 Reason for Rejection:</strong></p>
          <p>${rejectionReason}</p>
        </div>
        
        <p><strong>🔄 Next Steps:</strong></p>
        <ul>
          <li>Review the rejection reason above</li>
          <li>Address any issues mentioned</li>
          <li>You may re-register with corrected information</li>
          <li>Contact our support team if you have questions</li>
        </ul>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.FRONTEND_URL || 'http://localhost:3000'}/boat-owner/signup" class="button">Re-Register</a>
        </div>
        
        <p>If you believe this rejection was made in error or if you have any questions, please don't hesitate to contact our support <NAME_EMAIL></p>
        
        <p>We appreciate your interest in GoSea Platform.<br>The GoSea Team</p>
      </div>
      <div class="footer">
        <p>© 2024 GoSea Platform. All rights reserved.</p>
      </div>
    </body>
    </html>
    `;
  }

  /**
   * Plain text version of boat owner rejection email
   */
  getBoatOwnerRejectionEmailText(firstName, rejectionReason) {
    return `
Hello ${firstName},

Thank you for your interest in becoming a boat owner on GoSea Platform. After reviewing your application, we are unable to approve your account at this time.

Reason for Rejection:
${rejectionReason}

Next Steps:
- Review the rejection reason above
- Address any issues mentioned
- You may re-register with corrected information
- Contact our support team if you have questions

Re-register: ${process.env.FRONTEND_URL || 'http://localhost:3000'}/boat-owner/signup

If you believe this rejection was made in error or if you have any questions, please don't hesitate to contact our support <NAME_EMAIL>

We appreciate your interest in GoSea Platform.
The GoSea Team

© 2024 GoSea Platform. All rights reserved.
    `;
  }

  /**
   * Send admin notification for boat approval
   */
  async sendBoatApprovalNotification(notificationData) {
    try {
      const { adminEmail, boatOwner, boat, approvalLink } = notificationData;

      const mailOptions = {
        from: {
          name: 'GoSea Platform Admin',
          address: process.env.FROM_EMAIL || '<EMAIL>'
        },
        to: adminEmail,
        subject: 'New Boat Submission - Approval Required',
        html: this.getBoatApprovalNotificationTemplate(boatOwner, boat, approvalLink),
        text: this.getBoatApprovalNotificationText(boatOwner, boat, approvalLink)
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Boat approval notification sent successfully:', result.messageId);

      return {
        success: true,
        messageId: result.messageId
      };
    } catch (error) {
      console.error('Failed to send boat approval notification:', error);
      throw new Error('Failed to send boat approval notification');
    }
  }

  /**
   * HTML template for boat approval notification
   */
  getBoatApprovalNotificationTemplate(boatOwner, boat, approvalLink) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New Boat Submission - GoSea Platform</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">GoSea Platform</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">Admin Notification</p>
      </div>

      <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <h2 style="color: #495057; margin-top: 0;">New Boat Submission Requires Approval</h2>

        <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #007bff;">
          <h3 style="margin-top: 0; color: #007bff;">Boat Details</h3>
          <p><strong>Boat Name:</strong> ${boat.name}</p>
          <p><strong>Registration Number:</strong> ${boat.registrationNumber}</p>
          <p><strong>Capacity:</strong> ${boat.capacity} passengers</p>
          <p><strong>Location:</strong> ${boat.location}</p>
          <p><strong>Description:</strong> ${boat.description}</p>
        </div>

        <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
          <h3 style="margin-top: 0; color: #28a745;">Boat Owner Information</h3>
          <p><strong>Name:</strong> ${boatOwner.profile?.firstName} ${boatOwner.profile?.lastName}</p>
          <p><strong>Email:</strong> ${boatOwner.email}</p>
          <p><strong>Phone:</strong> ${boatOwner.profile?.phone}</p>
          <p><strong>Company:</strong> ${boatOwner.provider?.companyName}</p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="${approvalLink}" style="background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">Review & Approve Boat</a>
        </div>

        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p style="margin: 0; color: #856404;"><strong>Action Required:</strong> Please review the boat details and approve or reject this submission through the admin dashboard.</p>
        </div>
      </div>

      <div style="text-align: center; margin-top: 20px; color: #6c757d; font-size: 12px;">
        <p>This is an automated notification from GoSea Platform Admin System.</p>
        <p>© 2024 GoSea Platform. All rights reserved.</p>
      </div>
    </body>
    </html>
    `;
  }

  /**
   * Plain text version of boat approval notification
   */
  getBoatApprovalNotificationText(boatOwner, boat, approvalLink) {
    return `
GoSea Platform - New Boat Submission

Approval Required

A new boat has been submitted by a boat owner and requires your approval.

Boat Details:
- Name: ${boat.name}
- Registration Number: ${boat.registrationNumber}
- Capacity: ${boat.capacity} passengers
- Location: ${boat.location}
- Description: ${boat.description}

Boat Owner Information:
- Name: ${boatOwner.profile?.firstName} ${boatOwner.profile?.lastName}
- Email: ${boatOwner.email}
- Phone: ${boatOwner.profile?.phone}
- Company: ${boatOwner.provider?.companyName}

Please review and approve this boat submission by visiting:
${approvalLink}

Thank you for maintaining the quality of GoSea Platform.

The GoSea Admin Team

© 2024 GoSea Platform. All rights reserved.
    `;
  }
}

module.exports = new EmailService();
