const { PrismaClient } = require('@prisma/client');
const {
  hashPassword,
  comparePassword,
  generateSessionData,
  generatePasswordResetToken,
  generateEmailVerificationToken,
  isAccountLocked,
  shouldLockAccount,
  getAccountLockTime,
  validatePasswordStrength,
  sanitizeUserForToken
} = require('../utils/auth');
const emailService = require('./emailService');
const dataProtection = require('../utils/dataProtection');

const prisma = new PrismaClient();

/**
 * Authentication service for GoSea platform
 */

class AuthService {
  /**
   * Register a new user
   * @param {Object} userData - User registration data
   * @returns {Promise<Object>} - Registration result
   */
  async register(userData) {
    const { email, password, role, firstName, lastName, phone } = userData;

    try {
      // Normalize email for consistent checking
      const normalizedEmail = email.toLowerCase().trim();
      
      // Enhanced duplicate check - check both by email and by normalized email
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { email: normalizedEmail },
            { email: { equals: normalizedEmail, mode: 'insensitive' } }
          ]
        }
      });

      if (existingUser) {
        console.log(`Registration attempted with existing email: ${normalizedEmail}`);
        console.log(`Existing user details - ID: ${existingUser.id}, Email: ${existingUser.email}, HasPassword: ${!!existingUser.password}, HasGoogleId: ${!!existingUser.googleId}`);
        throw new Error('User already exists with this email');
      }

      // Additional check for Gmail-specific variations (Gmail ignores dots and + aliases)
      if (normalizedEmail.includes('@gmail.com')) {
        // Gmail ignores dots in the local part and everything after +
        const localPart = normalizedEmail.split('@')[0];
        const baseLocalPart = localPart.replace(/\./g, '').split('+')[0];
        
        // Find all Gmail users and check if any match this pattern
        const gmailUsers = await prisma.user.findMany({
          where: {
            email: {
              endsWith: '@gmail.com',
              mode: 'insensitive'
            }
          }
        });
        
        for (const gmailUser of gmailUsers) {
          const existingLocalPart = gmailUser.email.split('@')[0];
          const existingBaseLocalPart = existingLocalPart.replace(/\./g, '').split('+')[0];
          
          if (existingBaseLocalPart === baseLocalPart && gmailUser.email.toLowerCase() !== normalizedEmail) {
            console.log(`Gmail variant registration attempted. Trying: ${normalizedEmail}, Existing: ${gmailUser.email}`);
            throw new Error('An account with this email address (or its Gmail variant) already exists');
          }
        }
      }

      // Validate password strength
      const passwordValidation = validatePasswordStrength(password);
      if (!passwordValidation.isValid) {
        throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
      }

      // Hash password
      const hashedPassword = await hashPassword(password);

      // Generate email verification token
      const { token: emailToken, expiresAt: emailExpiresAt } = generateEmailVerificationToken();

      // Create user with profile in a transaction
      const result = await prisma.$transaction(async (tx) => {
        // Create user
        const user = await tx.user.create({
          data: {
            email: normalizedEmail,
            password: hashedPassword,
            role: role || 'CUSTOMER',
            emailVerified: false
          }
        });

        // Create profile with encrypted sensitive data
        const profileData = {
          userId: user.id,
          firstName,
          lastName,
          phone: phone ? dataProtection.encrypt(phone) : null
        };

        const profile = await tx.profile.create({
          data: profileData
        });

        // Create email verification token
        const verificationToken = await tx.emailVerificationToken.create({
          data: {
            userId: user.id,
            token: emailToken,
            expiresAt: emailExpiresAt
          }
        });

        return { user, profile, verificationToken };
      });

      // Send verification email
      try {
        await emailService.sendVerificationEmail(
          result.user.email,
          result.profile.firstName,
          emailToken
        );
        console.log(`Verification email sent to ${result.user.email}`);
      } catch (emailError) {
        console.error('Failed to send verification email:', emailError);
        // Don't fail registration if email sending fails
      }

      return {
        success: true,
        user: sanitizeUserForToken(result.user),
        profile: result.profile,
        emailVerificationToken: emailToken,
        message: 'User registered successfully. Please check your email for verification instructions.'
      };
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  /**
   * Login user
   * @param {string} email - User email
   * @param {string} password - User password
   * @param {string} ipAddress - Client IP address
   * @param {string} userAgent - Client user agent
   * @returns {Promise<Object>} - Login result
   */
  async login(email, password, ipAddress, userAgent) {
    try {
      // Normalize email for consistent checking
      const normalizedEmail = email.toLowerCase().trim();
      
      // Find user with profile
      const user = await prisma.user.findFirst({
        where: {
          OR: [
            { email: normalizedEmail },
            { email: { equals: normalizedEmail, mode: 'insensitive' } }
          ]
        },
        include: { profile: true }
      });

      if (!user) {
        throw new Error('Invalid email or password');
      }

      // Check if account is locked
      if (isAccountLocked(user)) {
        const lockTimeRemaining = Math.ceil((user.lockedUntil - new Date()) / (1000 * 60));
        throw new Error(`Account is locked. Try again in ${lockTimeRemaining} minutes.`);
      }

      // Check if account is active
      if (!user.isActive) {
        throw new Error('Account is deactivated');
      }

      // Check if email is verified
      if (!user.emailVerified) {
        throw new Error('Please verify your email address before signing in. Check your inbox for the verification email.');
      }

      // Compare password
      const isPasswordValid = await comparePassword(password, user.password);

      if (!isPasswordValid) {
        // Increment login attempts
        const loginAttempts = user.loginAttempts + 1;
        const updateData = { loginAttempts };

        // Lock account if too many attempts
        if (shouldLockAccount({ loginAttempts })) {
          updateData.lockedUntil = getAccountLockTime();
        }

        await prisma.user.update({
          where: { id: user.id },
          data: updateData
        });

        throw new Error('Invalid email or password');
      }

      // Reset login attempts on successful login
      await prisma.user.update({
        where: { id: user.id },
        data: {
          loginAttempts: 0,
          lockedUntil: null,
          lastLoginAt: new Date()
        }
      });

      // Decrypt sensitive profile data before generating session
      const decryptedUser = { ...user };
      if (decryptedUser.profile) {
        decryptedUser.profile = dataProtection.decryptProfileData(decryptedUser.profile);
      }

      // Generate session data
      const sessionData = generateSessionData(decryptedUser, ipAddress, userAgent);

      // Create user session
      const session = await prisma.userSession.create({
        data: {
          userId: user.id,
          sessionToken: sessionData.sessionToken,
          refreshToken: sessionData.refreshToken,
          expiresAt: sessionData.expiresAt,
          ipAddress,
          userAgent,
          lastActiveAt: new Date()
        }
      });

      return {
        success: true,
        accessToken: sessionData.accessToken,
        refreshToken: sessionData.refreshToken,
        sessionToken: sessionData.sessionToken,
        user: sessionData.user,
        expiresAt: sessionData.expiresAt,
        message: 'Login successful'
      };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  /**
   * Logout user
   * @param {string} sessionToken - User session token
   * @returns {Promise<Object>} - Logout result
   */
  async logout(sessionToken) {
    try {
      if (sessionToken) {
        await prisma.userSession.deleteMany({
          where: { sessionToken }
        });
      }

      return {
        success: true,
        message: 'Logout successful'
      };
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }

  /**
   * Refresh access token
   * @param {string} refreshToken - Refresh token
   * @returns {Promise<Object>} - New access token
   */
  async refreshToken(refreshToken) {
    try {
      // Find session with refresh token
      const session = await prisma.userSession.findUnique({
        where: { refreshToken },
        include: { user: { include: { profile: true } } }
      });

      if (!session) {
        throw new Error('Invalid refresh token');
      }

      if (session.expiresAt < new Date()) {
        // Clean up expired session
        await prisma.userSession.delete({
          where: { id: session.id }
        });
        throw new Error('Refresh token expired');
      }

      if (!session.user.isActive) {
        throw new Error('Account is deactivated');
      }

      // Generate new session data
      const sessionData = generateSessionData(
        session.user, 
        session.ipAddress, 
        session.userAgent
      );

      // Update session with new tokens
      await prisma.userSession.update({
        where: { id: session.id },
        data: {
          sessionToken: sessionData.sessionToken,
          refreshToken: sessionData.refreshToken,
          expiresAt: sessionData.expiresAt,
          lastActiveAt: new Date()
        }
      });

      return {
        success: true,
        accessToken: sessionData.accessToken,
        refreshToken: sessionData.refreshToken,
        sessionToken: sessionData.sessionToken,
        user: sessionData.user,
        expiresAt: sessionData.expiresAt,
        message: 'Token refreshed successfully'
      };
    } catch (error) {
      console.error('Token refresh error:', error);
      throw error;
    }
  }

  /**
   * Request password reset
   * @param {string} email - User email
   * @returns {Promise<Object>} - Password reset result
   */
  async requestPasswordReset(email) {
    try {
      const user = await prisma.user.findUnique({
        where: { email: email.toLowerCase() },
        include: { profile: true }
      });

      if (!user) {
        // Don't reveal if email exists for security
        return {
          success: true,
          message: 'If the email exists, a password reset link has been sent.'
        };
      }

      // Generate password reset token
      const { token, expiresAt } = generatePasswordResetToken();
      console.log('Generated password reset token:', token);

      // Delete any existing password reset tokens for this user
      await prisma.passwordResetToken.deleteMany({
        where: { userId: user.id }
      });

      // Create new password reset token
      await prisma.passwordResetToken.create({
        data: {
          userId: user.id,
          token,
          expiresAt
        }
      });

      // Send password reset email
      try {
        await emailService.sendPasswordResetEmail(
          user.email,
          user.profile.firstName,
          token
        );
        console.log(`Password reset email sent to ${user.email}`);
      } catch (emailError) {
        console.error('Failed to send password reset email:', emailError);
        // Don't throw error here - we still want to return success for security
      }

      return {
        success: true,
        message: 'If the email exists, a password reset link has been sent.'
      };
    } catch (error) {
      console.error('Password reset request error:', error);
      throw error;
    }
  }

  /**
   * Reset password using token
   * @param {string} token - Password reset token
   * @param {string} newPassword - New password
   * @returns {Promise<Object>} - Password reset result
   */
  async resetPassword(token, newPassword) {
    try {
      console.log('Attempting password reset with token:', token);
      // Find valid password reset token
      const resetToken = await prisma.passwordResetToken.findUnique({
        where: { token },
        include: { user: true }
      });
      console.log('Found reset token:', resetToken ? 'Yes' : 'No');

      if (!resetToken) {
        throw new Error('Invalid or expired password reset token');
      }

      if (resetToken.expiresAt < new Date()) {
        // Clean up expired token
        await prisma.passwordResetToken.delete({
          where: { id: resetToken.id }
        });
        throw new Error('Password reset token has expired');
      }

      if (resetToken.usedAt) {
        throw new Error('Password reset token has already been used');
      }

      // Validate new password strength
      const passwordValidation = validatePasswordStrength(newPassword);
      if (!passwordValidation.isValid) {
        throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
      }

      // Hash new password
      const hashedPassword = await hashPassword(newPassword);

      // Update user password and mark token as used
      await prisma.$transaction(async (tx) => {
        await tx.user.update({
          where: { id: resetToken.userId },
          data: {
            password: hashedPassword,
            loginAttempts: 0,
            lockedUntil: null
          }
        });

        await tx.passwordResetToken.update({
          where: { id: resetToken.id },
          data: { usedAt: new Date() }
        });

        // Invalidate all existing sessions for security
        await tx.userSession.deleteMany({
          where: { userId: resetToken.userId }
        });
      });

      return {
        success: true,
        message: 'Password reset successfully'
      };
    } catch (error) {
      console.error('Password reset error:', error);
      throw error;
    }
  }

  /**
   * Verify email using token
   * @param {string} token - Email verification token
   * @returns {Promise<Object>} - Email verification result
   */
  async verifyEmail(token) {
    try {
      // Find valid email verification token
      const verificationToken = await prisma.emailVerificationToken.findUnique({
        where: { token },
        include: { 
          user: { 
            include: { 
              profile: true,
              provider: true 
            }
          }
        }
      });

      if (!verificationToken) {
        throw new Error('Invalid or expired email verification token');
      }

      if (verificationToken.expiresAt < new Date()) {
        // Clean up expired token
        await prisma.emailVerificationToken.delete({
          where: { id: verificationToken.id }
        });
        throw new Error('Email verification token has expired');
      }

      if (verificationToken.verifiedAt) {
        throw new Error('Email has already been verified');
      }

      // Update user email verification status and mark token as used
      await prisma.$transaction(async (tx) => {
        await tx.user.update({
          where: { id: verificationToken.userId },
          data: {
            emailVerified: true,
            emailVerifiedAt: new Date()
          }
        });

        await tx.emailVerificationToken.update({
          where: { id: verificationToken.id },
          data: { verifiedAt: new Date() }
        });
      });

      // If this is a boat owner, send admin notification
      if (verificationToken.user.role === 'BOAT_OWNER') {
        await this.notifyAdminOfNewBoatOwner(
          verificationToken.user,
          verificationToken.user.profile,
          verificationToken.user.provider
        );
      }

      return {
        success: true,
        message: verificationToken.user.role === 'BOAT_OWNER' 
          ? 'Email verified successfully. Your account is now pending admin approval.'
          : 'Email verified successfully'
      };
    } catch (error) {
      console.error('Email verification error:', error);
      throw error;
    }
  }

  /**
   * Get user sessions
   * @param {string} userId - User ID
   * @returns {Promise<Array>} - User sessions
   */
  async getUserSessions(userId) {
    try {
      const sessions = await prisma.userSession.findMany({
        where: { 
          userId,
          expiresAt: { gt: new Date() }
        },
        select: {
          id: true,
          sessionToken: true,
          ipAddress: true,
          userAgent: true,
          lastActiveAt: true,
          createdAt: true,
          expiresAt: true
        },
        orderBy: { lastActiveAt: 'desc' }
      });

      return sessions;
    } catch (error) {
      console.error('Get user sessions error:', error);
      throw error;
    }
  }

  /**
   * Revoke user session
   * @param {string} userId - User ID
   * @param {string} sessionToken - Session token to revoke
   * @returns {Promise<Object>} - Revoke result
   */
  async revokeSession(userId, sessionToken) {
    try {
      await prisma.userSession.deleteMany({
        where: { 
          userId,
          sessionToken
        }
      });

      return {
        success: true,
        message: 'Session revoked successfully'
      };
    } catch (error) {
      console.error('Revoke session error:', error);
      throw error;
    }
  }

  /**
   * Revoke all user sessions except current
   * @param {string} userId - User ID
   * @param {string} currentSessionToken - Current session token to keep
   * @returns {Promise<Object>} - Revoke result
   */
  async revokeAllSessions(userId, currentSessionToken) {
    try {
      await prisma.userSession.deleteMany({
        where: { 
          userId,
          sessionToken: { not: currentSessionToken }
        }
      });

      return {
        success: true,
        message: 'All other sessions revoked successfully'
      };
    } catch (error) {
      console.error('Revoke all sessions error:', error);
      throw error;
    }
  }

  /**
   * Register a new boat owner with business details
   * @param {Object} userData - Boat owner registration data
   * @returns {Promise<Object>} - Registration result
   */
  async registerBoatOwner(userData) {
    const { email, password, firstName, lastName, phone, companyName, brn } = userData;

    try {
      // Normalize email for consistent checking
      const normalizedEmail = email.toLowerCase().trim();
      
      // Enhanced duplicate check - check both by email and by normalized email
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { email: normalizedEmail },
            { email: { equals: normalizedEmail, mode: 'insensitive' } }
          ]
        }
      });

      if (existingUser) {
        console.log(`Boat owner registration attempted with existing email: ${normalizedEmail}`);
        throw new Error('User already exists with this email');
      }

      // Additional check for Gmail-specific variations
      if (normalizedEmail.includes('@gmail.com')) {
        const localPart = normalizedEmail.split('@')[0];
        const baseLocalPart = localPart.replace(/\./g, '').split('+')[0];
        
        const gmailUsers = await prisma.user.findMany({
          where: {
            email: {
              endsWith: '@gmail.com',
              mode: 'insensitive'
            }
          }
        });
        
        for (const gmailUser of gmailUsers) {
          const existingLocalPart = gmailUser.email.split('@')[0];
          const existingBaseLocalPart = existingLocalPart.replace(/\./g, '').split('+')[0];
          
          if (existingBaseLocalPart === baseLocalPart && gmailUser.email.toLowerCase() !== normalizedEmail) {
            console.log(`Gmail variant boat owner registration attempted. Trying: ${normalizedEmail}, Existing: ${gmailUser.email}`);
            throw new Error('An account with this email address (or its Gmail variant) already exists');
          }
        }
      }

      // Validate password strength
      const passwordValidation = validatePasswordStrength(password);
      if (!passwordValidation.isValid) {
        throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
      }

      // Hash password
      const hashedPassword = await hashPassword(password);

      // Generate email verification token
      const { token: emailToken, expiresAt: emailExpiresAt } = generateEmailVerificationToken();

      // Create user with profile and provider in a transaction
      const result = await prisma.$transaction(async (tx) => {
        // Create user with BOAT_OWNER role (not approved yet)
        const user = await tx.user.create({
          data: {
            email: normalizedEmail,
            password: hashedPassword,
            role: 'BOAT_OWNER',
            emailVerified: false,
            isApproved: false // Key difference - requires admin approval
          }
        });

        // Create profile with personal information only (no business data)
        const profileData = {
          userId: user.id,
          firstName,
          lastName,
          phone: phone ? dataProtection.encrypt(phone) : null
        };

        const profile = await tx.profile.create({
          data: profileData
        });

        // Create Provider record with all business information
        const provider = await tx.provider.create({
          data: {
            userId: user.id,
            companyName: companyName || null, // Nullable for individual operators
            displayName: companyName || `${firstName} ${lastName}`, // Use name if no company
            businessEmail: normalizedEmail,
            businessPhone: phone ? dataProtection.encrypt(phone) : null, // Encrypted like profile
            brn: brn || null, // Nullable for individual operators
            isActive: false, // Inactive until approved
            isVerified: false
          }
        });

        // Create email verification token
        const verificationToken = await tx.emailVerificationToken.create({
          data: {
            userId: user.id,
            token: emailToken,
            expiresAt: emailExpiresAt
          }
        });

        return { user, profile, provider, verificationToken };
      });

      // Send verification email to boat owner
      try {
        await emailService.sendBoatOwnerVerificationEmail(
          result.user.email,
          result.profile.firstName,
          emailToken
        );
        console.log(`Boat owner verification email sent to ${result.user.email}`);
      } catch (emailError) {
        console.error('Failed to send boat owner verification email:', emailError);
        // Don't fail registration if email sending fails
      }

      return {
        success: true,
        user: sanitizeUserForToken(result.user),
        profile: result.profile,
        emailVerificationToken: emailToken,
        message: 'Registration successful. Please check your email to verify your account. Once verified, an admin will review your application.'
      };
    } catch (error) {
      console.error('Boat owner registration error:', error);
      throw error;
    }
  }

  /**
   * Login boat owner with approval checking
   * @param {string} email - Boat owner email
   * @param {string} password - Boat owner password
   * @param {string} ipAddress - Client IP address
   * @param {string} userAgent - Client user agent
   * @returns {Promise<Object>} - Login result
   */
  async loginBoatOwner(email, password, ipAddress, userAgent) {
    try {
      // Normalize email for consistent checking
      const normalizedEmail = email.toLowerCase().trim();
      
      // Find boat owner user
      const user = await prisma.user.findFirst({
        where: {
          AND: [
            {
              OR: [
                { email: normalizedEmail },
                { email: { equals: normalizedEmail, mode: 'insensitive' } }
              ]
            },
            { role: 'BOAT_OWNER' } // Ensure only boat owners can use this endpoint
          ]
        },
        include: { 
          profile: true,
          provider: true
        }
      });

      if (!user) {
        throw new Error('Invalid email or password');
      }

      // Check if account is locked
      if (isAccountLocked(user)) {
        const lockTimeRemaining = Math.ceil((user.lockedUntil - new Date()) / (1000 * 60));
        throw new Error(`Account is locked. Try again in ${lockTimeRemaining} minutes.`);
      }

      // Check if account is active
      if (!user.isActive) {
        throw new Error('Account is deactivated');
      }

      // Check if email is verified
      if (!user.emailVerified) {
        throw new Error('Please verify your email address first. Check your inbox for the verification email.');
      }

      // NEW: Admin approval check
      if (!user.isApproved) {
        throw new Error('Your account is pending admin approval. You will receive an email once approved.');
      }

      // Compare password
      const isPasswordValid = await comparePassword(password, user.password);

      if (!isPasswordValid) {
        // Increment login attempts
        const loginAttempts = user.loginAttempts + 1;
        const updateData = { loginAttempts };

        // Lock account if too many attempts
        if (shouldLockAccount({ loginAttempts })) {
          updateData.lockedUntil = getAccountLockTime();
        }

        await prisma.user.update({
          where: { id: user.id },
          data: updateData
        });

        throw new Error('Invalid email or password');
      }

      // Reset login attempts on successful login
      await prisma.user.update({
        where: { id: user.id },
        data: {
          loginAttempts: 0,
          lockedUntil: null,
          lastLoginAt: new Date()
        }
      });

      // Decrypt sensitive profile data before generating session
      const decryptedUser = { ...user };
      if (decryptedUser.profile) {
        decryptedUser.profile = dataProtection.decryptProfileData(decryptedUser.profile);
      }

      // Generate session data
      const sessionData = generateSessionData(decryptedUser, ipAddress, userAgent);

      // Create user session
      const session = await prisma.userSession.create({
        data: {
          userId: user.id,
          sessionToken: sessionData.sessionToken,
          refreshToken: sessionData.refreshToken,
          expiresAt: sessionData.expiresAt,
          ipAddress,
          userAgent,
          lastActiveAt: new Date()
        }
      });

      return {
        success: true,
        accessToken: sessionData.accessToken,
        refreshToken: sessionData.refreshToken,
        sessionToken: sessionData.sessionToken,
        user: sessionData.user,
        expiresAt: sessionData.expiresAt,
        message: 'Login successful'
      };
    } catch (error) {
      console.error('Boat owner login error:', error);
      throw error;
    }
  }

  /**
   * Send admin notification when boat owner email is verified
   * @param {Object} user - User object
   * @param {Object} profile - Profile object
   * @param {Object} provider - Provider object
   * @returns {Promise<void>}
   */
  async notifyAdminOfNewBoatOwner(user, profile, provider) {
    try {
      // Find all admin users
      const adminUsers = await prisma.user.findMany({
        where: { 
          role: 'ADMIN',
          isActive: true
        },
        select: {
          email: true
        }
      });

      // If no admin users found, log and return
      if (!adminUsers || adminUsers.length === 0) {
        console.log('No admin users found to notify');
        return;
      }

      // Send notification to each admin user
      for (const admin of adminUsers) {
        try {
          await emailService.sendAdminApprovalNotification({
            adminEmail: admin.email,
            boatOwner: {
              id: user.id,
              name: `${profile.firstName} ${profile.lastName}`,
              email: user.email,
              companyName: profile.companyName,
              brn: profile.brn,
              registrationDate: user.createdAt
            },
            approvalLink: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/admin/dashboard?tab=boat-owners&filter=pending&highlight=${user.id}`
          });
          console.log(`Admin notification sent to: ${admin.email}`);
        } catch (emailError) {
          console.error(`Failed to send admin notification to ${admin.email}:`, emailError);
        }
      }
    } catch (error) {
      console.error('Failed to fetch admin users or send notifications:', error);
      // Don't throw error here - this is a notification failure, not a process failure
    }
  }

  /**
   * Override email verification for boat owners to trigger admin notification
   * @param {string} token - Email verification token
   * @returns {Promise<Object>} - Email verification result
   */
  async verifyBoatOwnerEmail(token) {
    try {
      // Find valid email verification token
      const verificationToken = await prisma.emailVerificationToken.findUnique({
        where: { token },
        include: { 
          user: { 
            include: { 
              profile: true,
              provider: true
            }
          }
        }
      });

      if (!verificationToken) {
        throw new Error('Invalid or expired email verification token');
      }

      if (verificationToken.expiresAt < new Date()) {
        // Clean up expired token
        await prisma.emailVerificationToken.delete({
          where: { id: verificationToken.id }
        });
        throw new Error('Email verification token has expired');
      }

      if (verificationToken.verifiedAt) {
        throw new Error('Email has already been verified');
      }

      // Only process boat owner verification through this method
      if (verificationToken.user.role !== 'BOAT_OWNER') {
        throw new Error('Invalid verification method for this user type');
      }

      // Update user email verification status and mark token as used
      await prisma.$transaction(async (tx) => {
        await tx.user.update({
          where: { id: verificationToken.userId },
          data: {
            emailVerified: true,
            emailVerifiedAt: new Date()
          }
        });

        await tx.emailVerificationToken.update({
          where: { id: verificationToken.id },
          data: { verifiedAt: new Date() }
        });
      });

      // Send admin notification for boat owner approval
      await this.notifyAdminOfNewBoatOwner(
        verificationToken.user,
        verificationToken.user.profile,
        verificationToken.user.provider
      );

      return {
        success: true,
        message: 'Email verified successfully. Your account is now pending admin approval.'
      };
    } catch (error) {
      console.error('Boat owner email verification error:', error);
      throw error;
    }
  }
}

module.exports = new AuthService();
