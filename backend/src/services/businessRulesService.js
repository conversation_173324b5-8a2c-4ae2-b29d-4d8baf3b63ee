const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Business Rules Service
 * Handles validation and enforcement of business rules for the GoSea platform
 */

class BusinessRulesService {
  /**
   * Validates that a boat can be assigned to a service
   * @param {string} boatId - The boat ID
   * @param {string} serviceId - The service ID
   * @param {boolean} isPrimary - Whether this is a primary assignment
   * @param {number|null} maxCapacityOverride - Optional capacity override
   * @returns {Promise<{valid: boolean, error?: string}>}
   */
  static async validateServiceAssignment(boatId, serviceId, isPrimary = false, maxCapacityOverride = null) {
    try {
      // Get boat and service information
      const boat = await prisma.boat.findUnique({
        where: { id: boatId },
        include: { provider: true },
      });

      const service = await prisma.providerService.findUnique({
        where: { id: serviceId },
        include: { provider: true },
      });

      if (!boat) {
        return { valid: false, error: 'Boat not found' };
      }

      if (!service) {
        return { valid: false, error: 'Service not found' };
      }

      // Check if boat is approved
      if (boat.status !== 'APPROVED') {
        return {
          valid: false,
          error: 'Only approved boats can be assigned to services',
        };
      }

      // Check if boat belongs to the same provider as the service
      if (boat.providerId !== service.providerId) {
        return {
          valid: false,
          error: 'Boat can only be assigned to services from the same provider',
        };
      }

      // Validate capacity override rule: cannot exceed boat physical capacity
      if (maxCapacityOverride !== null && maxCapacityOverride > boat.capacity) {
        return {
          valid: false,
          error: `Capacity override (${maxCapacityOverride}) cannot exceed boat physical capacity (${boat.capacity})`,
        };
      }

      // Validate capacity override is positive
      if (maxCapacityOverride !== null && maxCapacityOverride <= 0) {
        return {
          valid: false,
          error: 'Capacity override must be a positive number',
        };
      }

      // Check if boat is already assigned as primary to another service
      if (isPrimary) {
        const existingPrimaryAssignment = await prisma.serviceAssignment.findFirst({
          where: {
            boatId: boatId,
            isPrimary: true,
            isActive: true,
          },
          include: {
            service: true,
          },
        });

        if (existingPrimaryAssignment && existingPrimaryAssignment.serviceId !== serviceId) {
          return {
            valid: false,
            error: `Boat is already assigned as primary to service: ${existingPrimaryAssignment.service.name}`,
          };
        }
      }

      return { valid: true };
    } catch (error) {
      console.error('Error validating service assignment:', error);
      return { valid: false, error: 'Validation failed' };
    }
  }

  /**
   * Validates that a passenger transport service has required routes
   * @param {string} serviceId - The service ID
   * @returns {Promise<{valid: boolean, error?: string}>}
   */
  static async validatePassengerTransportRoutes(serviceId) {
    try {
      const service = await prisma.providerService.findUnique({
        where: { id: serviceId },
        include: {
          serviceType: {
            include: {
              category: true,
            },
          },
          serviceRoutes: {
            include: {
              route: {
                include: {
                  departureJetty: true,
                  destination: true,
                },
              },
            },
          },
        },
      });

      if (!service) {
        return { valid: false, error: 'Service not found' };
      }

      // Check if this is a passenger transport service
      if (service.serviceType.category.code === 'PASSENGER_TRANSPORT') {
        if (service.serviceRoutes.length === 0) {
          return {
            valid: false,
            error: 'Passenger transport services must have at least one route assigned',
          };
        }

        // Validate that all routes are active
        const inactiveRoutes = service.serviceRoutes.filter(
          sr => !sr.route.departureJetty.isActive || !sr.route.destination.isActive
        );

        if (inactiveRoutes.length > 0) {
          return {
            valid: false,
            error: 'All assigned routes must have active jetties and destinations',
          };
        }
      }

      return { valid: true };
    } catch (error) {
      console.error('Error validating passenger transport routes:', error);
      return { valid: false, error: 'Validation failed' };
    }
  }

  /**
   * Validates provider operating area assignment
   * @param {string} providerId - The provider ID
   * @param {string} jettyId - The jetty ID
   * @returns {Promise<{valid: boolean, error?: string}>}
   */
  static async validateProviderOperatingArea(providerId, jettyId) {
    try {
      const provider = await prisma.provider.findUnique({
        where: { id: providerId },
      });

      const jetty = await prisma.jetty.findUnique({
        where: { id: jettyId },
      });

      if (!provider) {
        return { valid: false, error: 'Provider not found' };
      }

      if (!jetty) {
        return { valid: false, error: 'Jetty not found' };
      }

      if (!provider.isActive) {
        return { valid: false, error: 'Provider is not active' };
      }

      if (!jetty.isActive) {
        return { valid: false, error: 'Jetty is not active' };
      }

      // Check if already assigned
      const existingAssignment = await prisma.providerOperatingArea.findFirst({
        where: {
          providerId: providerId,
          jettyId: jettyId,
          isActive: true,
        },
      });

      if (existingAssignment) {
        return { valid: false, error: 'Provider is already assigned to this jetty' };
      }

      return { valid: true };
    } catch (error) {
      console.error('Error validating provider operating area:', error);
      return { valid: false, error: 'Validation failed' };
    }
  }

  /**
   * Calculates total available capacity for a service
   * @param {string} serviceId - The service ID
   * @returns {Promise<{capacity: number, assignments: Array}>}
   */
  static async calculateServiceCapacity(serviceId) {
    try {
      const assignments = await prisma.serviceAssignment.findMany({
        where: {
          serviceId: serviceId,
          isActive: true,
        },
        include: {
          boat: true,
        },
      });

      let totalCapacity = 0;
      const capacityBreakdown = assignments.map(assignment => {
        const capacity = assignment.maxCapacityOverride || assignment.boat.capacity;
        totalCapacity += capacity;
        
        return {
          boatId: assignment.boatId,
          boatName: assignment.boat.name,
          isPrimary: assignment.isPrimary,
          capacity: capacity,
          override: assignment.maxCapacityOverride !== null,
        };
      });

      return {
        capacity: totalCapacity,
        assignments: capacityBreakdown,
      };
    } catch (error) {
      console.error('Error calculating service capacity:', error);
      return { capacity: 0, assignments: [] };
    }
  }

  /**
   * Validates capacity override for service assignment update
   * @param {string} serviceAssignmentId - The service assignment ID
   * @param {number|null} maxCapacityOverride - New capacity override value
   * @returns {Promise<{valid: boolean, error?: string}>}
   */
  static async validateCapacityOverride(serviceAssignmentId, maxCapacityOverride) {
    try {
      const assignment = await prisma.serviceAssignment.findUnique({
        where: { id: serviceAssignmentId },
        include: { boat: true },
      });

      if (!assignment) {
        return { valid: false, error: 'Service assignment not found' };
      }

      // Validate capacity override rule: cannot exceed boat physical capacity
      if (maxCapacityOverride !== null && maxCapacityOverride > assignment.boat.capacity) {
        return {
          valid: false,
          error: `Capacity override (${maxCapacityOverride}) cannot exceed boat physical capacity (${assignment.boat.capacity})`,
        };
      }

      // Validate capacity override is positive
      if (maxCapacityOverride !== null && maxCapacityOverride <= 0) {
        return {
          valid: false,
          error: 'Capacity override must be a positive number',
        };
      }

      return { valid: true };
    } catch (error) {
      console.error('Error validating capacity override:', error);
      return { valid: false, error: 'Validation failed' };
    }
  }

  /**
   * Ensures a provider exists for a boat owner
   * @param {string} userId - The user ID (boat owner)
   * @returns {Promise<{providerId: string}>}
   */
  static async ensureProviderForUser(userId) {
    try {
      // Check if provider already exists
      let provider = await prisma.provider.findUnique({
        where: { userId: userId },
      });

      if (!provider) {
        // Get user and profile information
        const user = await prisma.user.findUnique({
          where: { id: userId },
          include: { profile: true },
        });

        if (!user) {
          throw new Error('User not found');
        }

        // Create auto-generated provider
        provider = await prisma.provider.create({
          data: {
            userId: userId,
            companyName: user.profile 
              ? `${user.profile.firstName} ${user.profile.lastName} Marine Services`
              : 'Marine Services',
            displayName: user.profile
              ? `${user.profile.firstName} ${user.profile.lastName}`
              : 'Boat Owner',
            businessPhone: user.profile?.phone || user.email,
            businessEmail: user.email,
            isAutoGenerated: true,
          },
        });
      }

      return { providerId: provider.id };
    } catch (error) {
      console.error('Error ensuring provider for user:', error);
      throw error;
    }
  }

  /**
   * Validates route data
   * @param {Object} routeData - Route data to validate
   * @returns {Promise<{valid: boolean, error?: string}>}
   */
  static async validateRoute(routeData) {
    try {
      const { departureJettyId, destinationId, distance, estimatedDuration } = routeData;

      // Check if jetty exists and is active
      const jetty = await prisma.jetty.findUnique({
        where: { id: departureJettyId },
      });

      if (!jetty || !jetty.isActive) {
        return { valid: false, error: 'Departure jetty does not exist or is not active' };
      }

      // Check if destination exists and is active
      const destination = await prisma.destination.findUnique({
        where: { id: destinationId },
      });

      if (!destination || !destination.isActive) {
        return { valid: false, error: 'Destination does not exist or is not active' };
      }

      // Validate distance and duration
      if (distance <= 0) {
        return { valid: false, error: 'Route distance must be positive' };
      }

      if (estimatedDuration <= 0) {
        return { valid: false, error: 'Route estimated duration must be positive' };
      }

      // Check for duplicate routes
      const existingRoute = await prisma.route.findFirst({
        where: {
          departureJettyId: departureJettyId,
          destinationId: destinationId,
          isActive: true,
        },
      });

      if (existingRoute) {
        return { valid: false, error: 'Route already exists between this jetty and destination' };
      }

      return { valid: true };
    } catch (error) {
      console.error('Error validating route:', error);
      return { valid: false, error: 'Validation failed' };
    }
  }
}

module.exports = BusinessRulesService;
