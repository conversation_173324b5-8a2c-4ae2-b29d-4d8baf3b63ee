const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');

const prisma = new PrismaClient();

/**
 * Dummy Payment Service
 * Simulates payment gateway operations for development
 * Will be replaced with actual Malaysian payment gateway (ToyyibPay/iPay88/etc.)
 */
class PaymentService {
  /**
   * Create a payment intent for booking (DUMMY IMPLEMENTATION)
   */
  static async createPaymentIntent(bookingData) {
    try {
      const { amount, currency = 'myr', bookingId, customerId } = bookingData;

      // Generate dummy payment intent ID
      const paymentIntentId = `pi_dummy_${crypto.randomBytes(16).toString('hex')}`;
      const clientSecret = `${paymentIntentId}_secret_${crypto.randomBytes(8).toString('hex')}`;

      // Simulate payment gateway response
      const dummyPaymentIntent = {
        id: paymentIntentId,
        amount: Math.round(amount * 100), // Convert to cents
        currency: currency.toLowerCase(),
        status: 'requires_payment_method',
        metadata: {
          bookingId,
          customerId,
          type: 'boat_booking'
        },
        client_secret: clientSecret
      };

      // Log for development
      console.log('🔄 DUMMY Payment Intent Created:', {
        id: paymentIntentId,
        amount: `${currency.toUpperCase()} ${amount}`,
        bookingId,
        customerId
      });

      return {
        success: true,
        clientSecret: dummyPaymentIntent.client_secret,
        paymentIntentId: dummyPaymentIntent.id,
        amount: amount,
        currency: currency
      };
    } catch (error) {
      console.error('Create payment intent error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create a deposit payment intent (30% of total) (DUMMY IMPLEMENTATION)
   */
  static async createDepositPaymentIntent(bookingData) {
    try {
      const { totalAmount, currency = 'myr', bookingId, customerId } = bookingData;
      const depositAmount = totalAmount * 0.3; // 30% deposit
      const remainingAmount = totalAmount - depositAmount;

      // Generate dummy payment intent ID
      const paymentIntentId = `pi_deposit_dummy_${crypto.randomBytes(16).toString('hex')}`;
      const clientSecret = `${paymentIntentId}_secret_${crypto.randomBytes(8).toString('hex')}`;

      // Simulate payment gateway response
      const dummyPaymentIntent = {
        id: paymentIntentId,
        amount: Math.round(depositAmount * 100), // Convert to cents
        currency: currency.toLowerCase(),
        status: 'requires_payment_method',
        metadata: {
          bookingId,
          customerId,
          type: 'boat_booking_deposit',
          totalAmount: totalAmount.toString(),
          remainingAmount: remainingAmount.toString()
        },
        client_secret: clientSecret
      };

      // Log for development
      console.log('🔄 DUMMY Deposit Payment Intent Created:', {
        id: paymentIntentId,
        depositAmount: `${currency.toUpperCase()} ${depositAmount}`,
        remainingAmount: `${currency.toUpperCase()} ${remainingAmount}`,
        totalAmount: `${currency.toUpperCase()} ${totalAmount}`,
        bookingId,
        customerId
      });

      return {
        success: true,
        clientSecret: dummyPaymentIntent.client_secret,
        paymentIntentId: dummyPaymentIntent.id,
        depositAmount,
        remainingAmount
      };
    } catch (error) {
      console.error('Create deposit payment intent error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Validate discount code
   */
  static async validateDiscountCode(code, totalAmount) {
    try {
      const discountCode = await prisma.discountCode.findUnique({
        where: { code: code.toUpperCase() }
      });

      if (!discountCode) {
        return {
          success: false,
          error: 'Invalid discount code'
        };
      }

      // Check if code is active
      if (!discountCode.isActive) {
        return {
          success: false,
          error: 'Discount code is no longer active'
        };
      }

      // Check validity dates
      const now = new Date();
      if (discountCode.validFrom && now < discountCode.validFrom) {
        return {
          success: false,
          error: 'Discount code is not yet valid'
        };
      }

      if (discountCode.validUntil && now > discountCode.validUntil) {
        return {
          success: false,
          error: 'Discount code has expired'
        };
      }

      // Check usage limit
      if (discountCode.usageLimit && discountCode.usedCount >= discountCode.usageLimit) {
        return {
          success: false,
          error: 'Discount code usage limit reached'
        };
      }

      // Check minimum amount
      if (discountCode.minAmount && totalAmount < discountCode.minAmount) {
        return {
          success: false,
          error: `Minimum amount of RM ${discountCode.minAmount} required`
        };
      }

      // Calculate discount amount
      let discountAmount = 0;
      if (discountCode.discountType === 'percentage') {
        discountAmount = (totalAmount * discountCode.discountValue) / 100;
      } else if (discountCode.discountType === 'fixed') {
        discountAmount = parseFloat(discountCode.discountValue);
      }

      // Apply maximum discount limit
      if (discountCode.maxDiscount && discountAmount > discountCode.maxDiscount) {
        discountAmount = parseFloat(discountCode.maxDiscount);
      }

      // Ensure discount doesn't exceed total amount
      discountAmount = Math.min(discountAmount, totalAmount);

      return {
        success: true,
        discountCode,
        discountAmount,
        finalAmount: totalAmount - discountAmount
      };
    } catch (error) {
      console.error('Validate discount code error:', error);
      return {
        success: false,
        error: 'Failed to validate discount code'
      };
    }
  }

  /**
   * Process successful payment (DUMMY IMPLEMENTATION)
   */
  static async processSuccessfulPayment(paymentIntentId, paymentData = {}) {
    try {
      // Simulate payment verification
      console.log('🔄 DUMMY Processing Payment:', paymentIntentId);

      // For dummy implementation, we'll accept the payment data directly
      const { bookingId, customerId, type, amount, currency = 'MYR' } = paymentData;

      if (!bookingId || !amount) {
        return {
          success: false,
          error: 'Missing required payment data'
        };
      }

      // Create payment record in database
      const payment = await prisma.payment.create({
        data: {
          bookingId,
          amount: parseFloat(amount),
          currency: currency.toUpperCase(),
          paymentMethod: 'DUMMY_GATEWAY', // Will be replaced with actual gateway
          stripePaymentIntentId: paymentIntentId, // Keeping field name for compatibility
          status: 'COMPLETED',
          paidAt: new Date()
        }
      });

      // Update booking status
      await prisma.booking.update({
        where: { id: bookingId },
        data: {
          status: type === 'boat_booking_deposit' ? 'DEPOSIT_PAID' : 'CONFIRMED'
        }
      });

      // Update discount code usage if applicable
      const booking = await prisma.booking.findUnique({
        where: { id: bookingId },
        select: { discountCode: true }
      });

      if (booking.discountCode) {
        await prisma.discountCode.update({
          where: { code: booking.discountCode },
          data: { usedCount: { increment: 1 } }
        });
      }

      // Log successful payment
      console.log('✅ DUMMY Payment Processed Successfully:', {
        paymentId: payment.id,
        bookingId,
        amount: `${currency} ${amount}`,
        status: payment.status
      });

      return {
        success: true,
        payment,
        paymentIntent: {
          id: paymentIntentId,
          status: 'succeeded',
          amount: amount * 100, // Convert to cents for compatibility
          currency: currency.toLowerCase(),
          metadata: { bookingId, customerId, type }
        }
      };
    } catch (error) {
      console.error('Process successful payment error:', error);
      return {
        success: false,
        error: 'Failed to process payment'
      };
    }
  }

  /**
   * Simulate payment confirmation (DUMMY IMPLEMENTATION)
   * This method simulates what would happen when a user completes payment
   */
  static async confirmPayment(paymentIntentId, paymentData) {
    try {
      // Simulate payment gateway callback/webhook
      console.log('🔄 DUMMY Payment Confirmation Received:', paymentIntentId);

      // In a real implementation, this would verify the payment with the gateway
      // For now, we'll simulate a successful payment
      const result = await this.processSuccessfulPayment(paymentIntentId, paymentData);

      if (result.success) {
        console.log('✅ DUMMY Payment Confirmed and Processed');
        return {
          success: true,
          message: 'Payment confirmed successfully',
          payment: result.payment,
          booking: result.paymentIntent.metadata
        };
      } else {
        return result;
      }
    } catch (error) {
      console.error('Confirm payment error:', error);
      return {
        success: false,
        error: 'Failed to confirm payment'
      };
    }
  }

  /**
   * Generate PDF receipt
   */
  static async generateReceipt(paymentId) {
    try {
      const payment = await prisma.payment.findUnique({
        where: { id: paymentId },
        include: {
          booking: {
            include: {
              boat: true,
              customer: {
                include: { profile: true }
              }
            }
          }
        }
      });

      if (!payment) {
        return {
          success: false,
          error: 'Payment not found'
        };
      }

      // TODO: Implement PDF generation using a library like puppeteer or jsPDF
      // For now, return receipt data
      const receiptData = {
        receiptNumber: `RCP-${payment.id.slice(-8).toUpperCase()}`,
        paymentDate: payment.paidAt,
        customerName: payment.booking.customer.profile ? 
          `${payment.booking.customer.profile.firstName} ${payment.booking.customer.profile.lastName}` : 
          'Customer',
        boatName: payment.booking.boat.name,
        serviceDate: payment.booking.serviceDate,
        amount: payment.amount,
        currency: payment.currency,
        paymentMethod: payment.paymentMethod
      };

      return {
        success: true,
        receiptData
      };
    } catch (error) {
      console.error('Generate receipt error:', error);
      return {
        success: false,
        error: 'Failed to generate receipt'
      };
    }
  }
}

module.exports = PaymentService;
