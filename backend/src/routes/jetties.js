const express = require('express');
const { PrismaClient } = require('@prisma/client');

const router = express.Router();
const prisma = new PrismaClient();

/**
 * @swagger
 * tags:
 *   name: Jetties
 *   description: Jetty management and information endpoints
 */

/**
 * @swagger
 * /api/jetties:
 *   get:
 *     summary: Get all active jetties
 *     description: Retrieve all active jetties with their available destinations and operating providers
 *     tags: [Jetties]
 *     responses:
 *       200:
 *         description: Jetties retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/Jetty'
 *                       - type: object
 *                         properties:
 *                           destinationCount:
 *                             type: integer
 *                             description: Number of available destinations
 *                           providerCount:
 *                             type: integer
 *                             description: Number of operating providers
 *                           availableDestinations:
 *                             type: array
 *                             items:
 *                               $ref: '#/components/schemas/Destination'
 *                 count:
 *                   type: integer
 *                   description: Total number of jetties
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/', async (req, res) => {
  try {
    const jetties = await prisma.jetty.findMany({
      where: {
        isActive: true,
      },
      include: {
        routes: {
          where: {
            isActive: true,
          },
          include: {
            destination: true,
          },
        },
        operatingAreas: {
          where: {
            isActive: true,
            provider: {
              isActive: true,
            },
          },
          include: {
            provider: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    // Add metrics to each jetty
    const jettiesWithMetrics = jetties.map(jetty => ({
      ...jetty,
      destinationCount: jetty.routes.length,
      providerCount: jetty.operatingAreas.length,
      availableDestinations: jetty.routes.map(route => route.destination),
    }));

    res.json({
      success: true,
      data: jettiesWithMetrics,
      count: jettiesWithMetrics.length,
    });
  } catch (error) {
    console.error('Error fetching jetties:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch jetties',
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/jetties/{id}:
 *   get:
 *     summary: Get specific jetty details
 *     description: Retrieve detailed information about a specific jetty including routes, destinations, and operating providers
 *     tags: [Jetties]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Jetty identifier
 *     responses:
 *       200:
 *         description: Jetty details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Jetty'
 *       404:
 *         description: Jetty not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const jetty = await prisma.jetty.findUnique({
      where: {
        id: id,
        isActive: true,
      },
      include: {
        routes: {
          where: {
            isActive: true,
          },
          include: {
            destination: true,
          },
          orderBy: {
            estimatedDuration: 'asc',
          },
        },
        operatingAreas: {
          where: {
            isActive: true,
            provider: {
              isActive: true,
            },
          },
          include: {
            provider: {
              include: {
                services: {
                  where: {
                    isActive: true,
                  },
                  include: {
                    serviceType: {
                      include: {
                        category: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!jetty) {
      return res.status(404).json({
        success: false,
        message: 'Jetty not found',
      });
    }

    res.json({
      success: true,
      data: jetty,
    });
  } catch (error) {
    console.error('Error fetching jetty:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch jetty',
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/jetties/{id}/destinations:
 *   get:
 *     summary: Get destinations accessible from a jetty
 *     description: Retrieve all destinations that can be reached from a specific jetty
 *     tags: [Jetties]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Jetty identifier
 *     responses:
 *       200:
 *         description: Destinations retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/Destination'
 *                       - type: object
 *                         properties:
 *                           route:
 *                             $ref: '#/components/schemas/Route'
 *                 count:
 *                   type: integer
 *                   description: Number of destinations
 *                 jetty:
 *                   $ref: '#/components/schemas/Jetty'
 *       404:
 *         description: No destinations found for this jetty
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/:id/destinations', async (req, res) => {
  try {
    const { id } = req.params;

    const routes = await prisma.route.findMany({
      where: {
        departureJettyId: id,
        isActive: true,
      },
      include: {
        destination: true,
        departureJetty: true,
      },
      orderBy: {
        estimatedDuration: 'asc',
      },
    });

    if (routes.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No destinations found for this jetty',
      });
    }

    const destinations = routes.map(route => ({
      ...route.destination,
      route: {
        id: route.id,
        distance: route.distance,
        estimatedDuration: route.estimatedDuration,
        difficulty: route.difficulty,
      },
    }));

    res.json({
      success: true,
      data: destinations,
      count: destinations.length,
      jetty: routes[0].departureJetty,
    });
  } catch (error) {
    console.error('Error fetching destinations for jetty:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch destinations',
      error: error.message,
    });
  }
});

module.exports = router;
