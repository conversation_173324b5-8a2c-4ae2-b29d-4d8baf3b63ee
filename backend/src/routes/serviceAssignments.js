const express = require('express');
const { PrismaClient } = require('@prisma/client');
const BusinessRulesService = require('../services/businessRulesService');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

/**
 * @route POST /api/service-assignments
 * @desc Assign a boat to a service
 * @access Private (Boat Owner/Provider)
 */
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { boatId, serviceId, isPrimary = false, maxCapacityOverride } = req.body;

    if (!boatId || !serviceId) {
      return res.status(400).json({
        success: false,
        message: 'Boat ID and Service ID are required',
      });
    }

    // Validate the assignment including capacity override
    const validation = await BusinessRulesService.validateServiceAssignment(
      boatId,
      serviceId,
      isPrimary,
      maxCapacityOverride
    );

    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        message: validation.error,
      });
    }

    // Check if user owns the boat or is the provider
    const boat = await prisma.boat.findUnique({
      where: { id: boatId },
      include: { provider: true },
    });

    if (boat.ownerId !== req.user.id && boat.provider.userId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only assign your own boats to services',
      });
    }

    // If this is a primary assignment, remove primary status from other assignments
    if (isPrimary) {
      await prisma.serviceAssignment.updateMany({
        where: {
          boatId: boatId,
          isPrimary: true,
          isActive: true,
        },
        data: {
          isPrimary: false,
        },
      });
    }

    // Create the assignment
    const assignment = await prisma.serviceAssignment.create({
      data: {
        boatId,
        serviceId,
        isPrimary,
        maxCapacityOverride,
        assignedAt: new Date(),
      },
      include: {
        boat: {
          include: {
            photos: true,
          },
        },
        service: {
          include: {
            serviceType: {
              include: {
                category: true,
              },
            },
          },
        },
      },
    });

    // Calculate updated service capacity
    const capacityInfo = await BusinessRulesService.calculateServiceCapacity(serviceId);

    res.status(201).json({
      success: true,
      data: assignment,
      serviceCapacity: capacityInfo,
      message: 'Boat successfully assigned to service',
    });
  } catch (error) {
    console.error('Error creating service assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assign boat to service',
      error: error.message,
    });
  }
});

/**
 * @route GET /api/service-assignments/service/:serviceId
 * @desc Get all boat assignments for a service
 * @access Public
 */
router.get('/service/:serviceId', async (req, res) => {
  try {
    const { serviceId } = req.params;

    const assignments = await prisma.serviceAssignment.findMany({
      where: {
        serviceId: serviceId,
        isActive: true,
      },
      include: {
        boat: {
          include: {
            photos: true,
            owner: {
              include: {
                profile: true,
              },
            },
          },
        },
        service: {
          include: {
            serviceType: {
              include: {
                category: true,
              },
            },
            provider: true,
          },
        },
      },
      orderBy: [
        { isPrimary: 'desc' },
        { assignedAt: 'asc' },
      ],
    });

    // Calculate capacity information
    const capacityInfo = await BusinessRulesService.calculateServiceCapacity(serviceId);

    res.json({
      success: true,
      data: assignments,
      count: assignments.length,
      serviceCapacity: capacityInfo,
    });
  } catch (error) {
    console.error('Error fetching service assignments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch service assignments',
      error: error.message,
    });
  }
});

/**
 * @route GET /api/service-assignments/boat/:boatId
 * @desc Get all service assignments for a boat
 * @access Public
 */
router.get('/boat/:boatId', async (req, res) => {
  try {
    const { boatId } = req.params;

    const assignments = await prisma.serviceAssignment.findMany({
      where: {
        boatId: boatId,
        isActive: true,
      },
      include: {
        service: {
          include: {
            serviceType: {
              include: {
                category: true,
              },
            },
            provider: true,
            serviceRoutes: {
              include: {
                route: {
                  include: {
                    departureJetty: true,
                    destination: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: [
        { isPrimary: 'desc' },
        { assignedAt: 'asc' },
      ],
    });

    res.json({
      success: true,
      data: assignments,
      count: assignments.length,
    });
  } catch (error) {
    console.error('Error fetching boat assignments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch boat assignments',
      error: error.message,
    });
  }
});

/**
 * @route PUT /api/service-assignments/:id
 * @desc Update a service assignment
 * @access Private (Boat Owner/Provider)
 */
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { isPrimary, maxCapacityOverride, isActive } = req.body;

    // Get the current assignment
    const currentAssignment = await prisma.serviceAssignment.findUnique({
      where: { id },
      include: {
        boat: {
          include: {
            provider: true,
          },
        },
      },
    });

    if (!currentAssignment) {
      return res.status(404).json({
        success: false,
        message: 'Service assignment not found',
      });
    }

    // Check permissions
    if (
      currentAssignment.boat.ownerId !== req.user.id &&
      currentAssignment.boat.provider.userId !== req.user.id
    ) {
      return res.status(403).json({
        success: false,
        message: 'You can only modify assignments for your own boats',
      });
    }

    // Validate capacity override if it's being updated
    if (maxCapacityOverride !== undefined) {
      const capacityValidation = await BusinessRulesService.validateCapacityOverride(
        id,
        maxCapacityOverride
      );

      if (!capacityValidation.valid) {
        return res.status(400).json({
          success: false,
          message: capacityValidation.error,
        });
      }
    }

    // If setting as primary, validate and remove primary from others
    if (isPrimary === true && !currentAssignment.isPrimary) {
      const validation = await BusinessRulesService.validateServiceAssignment(
        currentAssignment.boatId,
        currentAssignment.serviceId,
        true,
        maxCapacityOverride !== undefined ? maxCapacityOverride : currentAssignment.maxCapacityOverride
      );

      if (!validation.valid) {
        return res.status(400).json({
          success: false,
          message: validation.error,
        });
      }

      // Remove primary status from other assignments for this boat
      await prisma.serviceAssignment.updateMany({
        where: {
          boatId: currentAssignment.boatId,
          isPrimary: true,
          isActive: true,
          id: { not: id },
        },
        data: {
          isPrimary: false,
        },
      });
    }

    // Update the assignment
    const updatedAssignment = await prisma.serviceAssignment.update({
      where: { id },
      data: {
        ...(isPrimary !== undefined && { isPrimary }),
        ...(maxCapacityOverride !== undefined && { maxCapacityOverride }),
        ...(isActive !== undefined && { isActive }),
        updatedAt: new Date(),
      },
      include: {
        boat: {
          include: {
            photos: true,
          },
        },
        service: {
          include: {
            serviceType: {
              include: {
                category: true,
              },
            },
          },
        },
      },
    });

    // Calculate updated service capacity
    const capacityInfo = await BusinessRulesService.calculateServiceCapacity(
      currentAssignment.serviceId
    );

    res.json({
      success: true,
      data: updatedAssignment,
      serviceCapacity: capacityInfo,
      message: 'Service assignment updated successfully',
    });
  } catch (error) {
    console.error('Error updating service assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update service assignment',
      error: error.message,
    });
  }
});

/**
 * @route DELETE /api/service-assignments/:id
 * @desc Remove a boat from a service (soft delete)
 * @access Private (Boat Owner/Provider)
 */
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Get the current assignment
    const assignment = await prisma.serviceAssignment.findUnique({
      where: { id },
      include: {
        boat: {
          include: {
            provider: true,
          },
        },
      },
    });

    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Service assignment not found',
      });
    }

    // Check permissions
    if (
      assignment.boat.ownerId !== req.user.id &&
      assignment.boat.provider.userId !== req.user.id
    ) {
      return res.status(403).json({
        success: false,
        message: 'You can only remove assignments for your own boats',
      });
    }

    // Soft delete the assignment
    await prisma.serviceAssignment.update({
      where: { id },
      data: {
        isActive: false,
        updatedAt: new Date(),
      },
    });

    // Calculate updated service capacity
    const capacityInfo = await BusinessRulesService.calculateServiceCapacity(assignment.serviceId);

    res.json({
      success: true,
      serviceCapacity: capacityInfo,
      message: 'Boat removed from service successfully',
    });
  } catch (error) {
    console.error('Error removing service assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove boat from service',
      error: error.message,
    });
  }
});

module.exports = router;
