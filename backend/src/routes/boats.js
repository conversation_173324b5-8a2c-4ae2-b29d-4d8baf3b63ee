const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

/**
 * @swagger
 * /api/boats/search:
 *   get:
 *     summary: Search for boats
 *     description: Search for boats with various filters like service type, location, date, and capacity
 *     tags: [Boats]
 *     parameters:
 *       - in: query
 *         name: serviceType
 *         schema:
 *           type: string
 *           enum: [SNORKELING, PASSENGER_BOAT]
 *         description: Type of service offered by the boat
 *       - in: query
 *         name: location
 *         schema:
 *           type: string
 *           enum: [REDANG_ISLAND, PERHENTIAN_ISLAND]
 *         description: Operating location of the boat
 *       - in: query
 *         name: date
 *         schema:
 *           type: string
 *           format: date
 *         description: Date for availability check (YYYY-MM-DD)
 *       - in: query
 *         name: time
 *         schema:
 *           type: string
 *         description: Time slot for availability check
 *       - in: query
 *         name: capacity
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Minimum capacity required
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 12
 *         description: Number of results per page
 *     responses:
 *       200:
 *         description: Search results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 boats:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Boat'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/search', async (req, res) => {
  try {
    const {
      serviceType,
      location,
      date,
      time,
      capacity,
      page = 1,
      limit = 12
    } = req.query;

    // Build where clause for filtering
    const where = {
      status: 'APPROVED', // Only show approved boats
      isActive: true,
      AND: []
    };

    // Service type filter
    if (serviceType && ['SNORKELING', 'PASSENGER_BOAT'].includes(serviceType)) {
      where.AND.push({ serviceType });
    }

    // Location filter
    if (location && ['REDANG_ISLAND', 'PERHENTIAN_ISLAND'].includes(location)) {
      where.AND.push({ location });
    }

    // Capacity filter
    if (capacity) {
      const capacityNum = parseInt(capacity);
      if (!isNaN(capacityNum) && capacityNum > 0) {
        where.AND.push({ capacity: { gte: capacityNum } });
      }
    }

    // Date and time availability filter
    if (date) {
      const searchDate = new Date(date);
      if (!isNaN(searchDate.getTime())) {
        // Check availability for the specific date
        where.AND.push({
          OR: [
            // No specific availability records (assume available)
            { availability: { none: {} } },
            // Has availability record for this date and is available
            {
              availability: {
                some: {
                  date: searchDate,
                  isAvailable: true
                }
              }
            }
          ]
        });
      }
    }

    // Remove empty AND array if no filters
    if (where.AND.length === 0) {
      delete where.AND;
    }

    // Calculate pagination
    const pageNum = Math.max(1, parseInt(page));
    const limitNum = Math.min(50, Math.max(1, parseInt(limit))); // Max 50 per page
    const skip = (pageNum - 1) * limitNum;

    // Execute search query
    const [boats, totalCount] = await Promise.all([
      prisma.boat.findMany({
        where,
        include: {
          photos: {
            orderBy: { order: 'asc' },
            take: 1 // Only get the first photo for listing
          },
          packages: {
            orderBy: { createdAt: 'asc' },
            take: 1 // Only get the first package for basic info
          },
          owner: {
            select: {
              id: true,
              profile: {
                select: {
                  firstName: true,
                  lastName: true
                }
              }
            }
          }
        },
        orderBy: [
          { createdAt: 'desc' }
        ],
        skip,
        take: limitNum
      }),
      prisma.boat.count({ where })
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limitNum);
    const hasNextPage = pageNum < totalPages;
    const hasPrevPage = pageNum > 1;

    // Format response
    const formattedBoats = boats.map(boat => ({
      id: boat.id,
      name: boat.name,
      description: boat.description,
      serviceType: boat.serviceType,
      location: boat.location,
      capacity: boat.capacity,
      basePrice: parseFloat(boat.basePrice),
      photos: boat.photos.map(photo => ({
        id: photo.id,
        url: photo.url,
        alt: photo.alt || boat.name
      })),
      package: boat.packages[0] ? {
        id: boat.packages[0].id,
        name: boat.packages[0].name,
        description: boat.packages[0].description,
        name: boat.packages[0].name,
        description: boat.packages[0].description
      } : null,
      owner: {
        id: boat.owner.id,
        name: boat.owner.profile ? 
          `${boat.owner.profile.firstName} ${boat.owner.profile.lastName}`.trim() : 
          'Boat Owner'
      }
    }));

    res.json({
      success: true,
      boats: formattedBoats,
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalCount,
        hasNextPage,
        hasPrevPage,
        limit: limitNum
      },
      filters: {
        serviceType: serviceType || null,
        location: location || null,
        date: date || null,
        time: time || null,
        capacity: capacity || null
      }
    });

  } catch (error) {
    console.error('Boat search error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search boats',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/boats/{id}:
 *   get:
 *     summary: Get boat details
 *     description: Get detailed information about a specific boat including photos, packages, amenities, and availability
 *     tags: [Boats]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Unique boat identifier
 *     responses:
 *       200:
 *         description: Boat details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 boat:
 *                   $ref: '#/components/schemas/Boat'
 *       404:
 *         description: Boat not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const boat = await prisma.boat.findUnique({
      where: {
        id,
        status: 'APPROVED',
        isActive: true
      },
      include: {
        photos: {
          orderBy: { order: 'asc' }
        },
        packages: {
          orderBy: { createdAt: 'asc' }
        },
        amenities: true,
        availability: {
          where: {
            date: {
              gte: new Date() // Only future availability
            }
          },
          orderBy: { date: 'asc' },
          take: 30 // Next 30 days
        },
        owner: {
          select: {
            id: true,
            profile: {
              select: {
                firstName: true,
                lastName: true,
                phone: true
              }
            }
          }
        }
      }
    });

    if (!boat) {
      return res.status(404).json({
        success: false,
        message: 'Boat not found or not available'
      });
    }

    // Format response
    const formattedBoat = {
      id: boat.id,
      name: boat.name,
      description: boat.description,
      serviceType: boat.serviceType,
      location: boat.location,
      capacity: boat.capacity,
      basePrice: parseFloat(boat.basePrice),
      // New gallery images field
      galleryImages: boat.galleryImages || boat.photos.map(photo => ({
        url: photo.url,
        alt: photo.alt || boat.name,
        isPrimary: photo.order === 0
      })),
      // Package details - use default structure since column removed
      packageDetails: {
        duration: boat.packages[0]?.duration || null,
        departureTime: "09:00",
        returnTime: "15:00",
        meetingPoint: `${boat.location} Jetty`
      },
      // Itinerary - use default structure since column removed
      itinerary: [
        { time: "09:00", activity: "Departure", duration: "30 min" },
        { time: "09:30", activity: "Main Activity", duration: "4 hours" },
        { time: "13:30", activity: "Return Journey", duration: "30 min" }
      ],
      // Included items field
      includedItems: boat.includedItems || [
        "Life jackets",
        "Safety equipment",
        "Professional guide"
      ],
      // Age pricing - use default structure since column removed
      agePricing: {
        adult: parseFloat(boat.basePrice),
        child: parseFloat(boat.basePrice) * 0.7,
        infant: 0
      },
      photos: boat.photos.map(photo => ({
        id: photo.id,
        url: photo.url,
        alt: photo.alt || boat.name,
        order: photo.order
      })),
      packages: boat.packages.map(pkg => ({
        id: pkg.id,
        name: pkg.name,
        description: pkg.description,
        price: parseFloat(boat.basePrice), // Use boat's base price since package doesn't have price
        duration: '4 hours', // Default duration since package doesn't have duration field
        includedItems: pkg.includedItems || [],
        itinerary: pkg.itinerary
      })),
      amenities: boat.amenities.map(amenity => ({
        id: amenity.id,
        name: amenity.name,
        description: amenity.description
      })),
      availability: boat.availability.map(avail => ({
        date: avail.date.toISOString().split('T')[0],
        isAvailable: avail.isAvailable,
        availableSlots: avail.availableSlots || [
          { time: "09:00", availableCapacity: boat.capacity, totalCapacity: boat.capacity }
        ]
      })),
      owner: {
        id: boat.owner.id,
        name: boat.owner.profile ?
          `${boat.owner.profile.firstName} ${boat.owner.profile.lastName}`.trim() :
          'Boat Owner',
        companyName: boat.owner.profile?.firstName ?
          `${boat.owner.profile.firstName} Marine Services` :
          'Marine Adventures',
        rating: 4.5, // Mock rating for now
        reviewCount: 25 // Mock review count for now
      }
    };

    res.json({
      success: true,
      boat: formattedBoat
    });

  } catch (error) {
    console.error('Boat details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get boat details',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

/**
 * GET /api/boats
 * Get featured/popular boats for homepage
 */
router.get('/', async (req, res) => {
  try {
    const { limit = 6 } = req.query;
    const limitNum = Math.min(12, Math.max(1, parseInt(limit)));

    const boats = await prisma.boat.findMany({
      where: {
        status: 'APPROVED',
        isActive: true
      },
      include: {
        photos: {
          orderBy: { order: 'asc' },
          take: 1
        },
        packages: {
          orderBy: { createdAt: 'asc' },
          take: 1
        }
      },
      orderBy: [
        { createdAt: 'desc' }
      ],
      take: limitNum
    });

    const formattedBoats = boats.map(boat => ({
      id: boat.id,
      name: boat.name,
      serviceType: boat.serviceType,
      location: boat.location,
      capacity: boat.capacity,
      basePrice: parseFloat(boat.basePrice),
      photo: boat.photos[0] ? {
        url: boat.photos[0].url,
        alt: boat.photos[0].alt || boat.name
      } : null,
      startingPrice: parseFloat(boat.basePrice)
    }));

    res.json({
      success: true,
      boats: formattedBoats
    });

  } catch (error) {
    console.error('Featured boats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get featured boats',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

module.exports = router;
