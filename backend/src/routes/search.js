const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

/**
 * @swagger
 * /api/search/service-categories:
 *   get:
 *     summary: Get service categories
 *     description: Retrieve all active service categories with their service types for search filters
 *     tags: [Search]
 *     responses:
 *       200:
 *         description: Service categories retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *                       isActive:
 *                         type: boolean
 *                       serviceTypes:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                             name:
 *                               type: string
 *                             description:
 *                               type: string
 *                 count:
 *                   type: integer
 *                   description: Number of categories returned
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/service-categories', async (req, res) => {
  try {
    const categories = await prisma.serviceCategory.findMany({
      where: {
        isActive: true,
      },
      include: {
        serviceTypes: {
          where: {
            isActive: true,
          },
          include: {
            _count: {
              select: {
                providerServices: {
                  where: {
                    isActive: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        sortOrder: 'asc',
      },
    });

    res.json({
      success: true,
      data: categories,
      count: categories.length,
    });
  } catch (error) {
    console.error('Error fetching service categories:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch service categories',
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/search/jetties:
 *   get:
 *     summary: Get jetties for departure selection
 *     description: Retrieve all active jetties with their available destinations and operating providers for search filters
 *     tags: [Search]
 *     responses:
 *       200:
 *         description: Jetties retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/Jetty'
 *                       - type: object
 *                         properties:
 *                           availableDestinations:
 *                             type: array
 *                             items:
 *                               $ref: '#/components/schemas/Destination'
 *                           providerCount:
 *                             type: integer
 *                             description: Number of operating providers
 *                           serviceCount:
 *                             type: integer
 *                             description: Total number of available services
 *                 count:
 *                   type: integer
 *                   description: Number of jetties returned
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/jetties', async (req, res) => {
  try {
    const jetties = await prisma.jetty.findMany({
      where: {
        isActive: true,
      },
      include: {
        routes: {
          where: {
            isActive: true,
          },
          include: {
            destination: true,
          },
        },
        operatingAreas: {
          where: {
            isActive: true,
            provider: {
              isActive: true,
            },
          },
          include: {
            provider: {
              include: {
                _count: {
                  select: {
                    services: {
                      where: {
                        isActive: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    // Calculate available destinations and provider count for each jetty
    const jettiesWithMetrics = jetties.map(jetty => ({
      ...jetty,
      availableDestinations: jetty.routes.map(route => route.destination),
      providerCount: jetty.operatingAreas.length,
      serviceCount: jetty.operatingAreas.reduce(
        (total, area) => total + area.provider._count.services,
        0
      ),
    }));

    res.json({
      success: true,
      data: jettiesWithMetrics,
      count: jettiesWithMetrics.length,
    });
  } catch (error) {
    console.error('Error fetching jetties:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch jetties',
      error: error.message,
    });
  }
});

/**
 * @route GET /api/search/destinations
 * @desc Get all destinations for search filters
 * @access Public
 */
router.get('/destinations', async (req, res) => {
  try {
    const { jettyId } = req.query;

    let whereClause = {
      isActive: true,
    };

    // Filter by jetty if provided
    if (jettyId) {
      whereClause.routes = {
        some: {
          departureJettyId: jettyId,
          isActive: true,
        },
      };
    }

    const destinations = await prisma.destination.findMany({
      where: whereClause,
      include: {
        routes: {
          where: {
            isActive: true,
            ...(jettyId && { departureJettyId: jettyId }),
          },
          include: {
            departureJetty: true,
          },
        },
        _count: {
          select: {
            routes: {
              where: {
                isActive: true,
                ...(jettyId && { departureJettyId: jettyId }),
              },
            },
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    res.json({
      success: true,
      data: destinations,
      count: destinations.length,
      filters: {
        jettyId: jettyId || null,
      },
    });
  } catch (error) {
    console.error('Error fetching destinations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch destinations',
      error: error.message,
    });
  }
});

/**
 * @route GET /api/search/routes
 * @desc Get available routes between jetty and destination
 * @access Public
 */
router.get('/routes', async (req, res) => {
  try {
    const { jettyId, destinationId } = req.query;

    if (!jettyId || !destinationId) {
      return res.status(400).json({
        success: false,
        message: 'Both jetty ID and destination ID are required',
      });
    }

    const routes = await prisma.route.findMany({
      where: {
        departureJettyId: jettyId,
        destinationId: destinationId,
        isActive: true,
      },
      include: {
        departureJetty: true,
        destination: true,
        serviceRoutes: {
          where: {
            service: {
              isActive: true,
            },
          },
          include: {
            service: {
              include: {
                provider: true,
                serviceType: {
                  include: {
                    category: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    res.json({
      success: true,
      data: routes,
      count: routes.length,
      filters: {
        jettyId,
        destinationId,
      },
    });
  } catch (error) {
    console.error('Error fetching routes:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch routes',
      error: error.message,
    });
  }
});

/**
 * @route GET /api/search/providers
 * @desc Search providers by service category, route, and date
 * @access Public
 */
router.get('/providers', async (req, res) => {
  try {
    const {
      serviceCategory,
      jettyId,
      destinationId,
      routeId,
      date,
      minCapacity,
      maxPrice,
      page = 1,
      limit = 12,
    } = req.query;

    // Build where clause with cascading filters
    let whereClause = {
      isActive: true,
    };

    // Cascading filter logic:
    // 1. Destination only: Filter providers with services that have routes ending at the selected destination
    // 2. Destination + Service Type: Filter by destination routes AND matching service category
    // 3. Destination + Service Type + Jetty: Filter by destination routes, service category, AND routes starting from selected jetty
    // 4. All 4 fields: Apply all above filters PLUS availability check for the selected date/time

    let serviceFilters = {
      isActive: true,
    };

    // Always filter by destination if provided (primary filter)
    if (destinationId) {
      serviceFilters.serviceRoutes = {
        some: {
          route: {
            destinationId: destinationId,
            ...(jettyId && {
              departureJettyId: jettyId,
            }),
          },
        },
      };
    } else if (routeId) {
      // Handle specific route selection
      serviceFilters.serviceRoutes = {
        some: {
          routeId: routeId,
        },
      };
    }

    // Add service category filter if provided
    if (serviceCategory) {
      serviceFilters.serviceType = {
        categoryId: serviceCategory,
      };
    }

    // Apply service filters to where clause
    whereClause.services = {
      some: serviceFilters,
    };

    // Filter by operating area (jetty) at provider level if jetty is specified
    if (jettyId) {
      whereClause.operatingAreas = {
        some: {
          jettyId: jettyId,
          isActive: true,
        },
      };
    }

    // Calculate pagination
    const pageNum = Math.max(1, parseInt(page));
    const limitNum = Math.min(50, Math.max(1, parseInt(limit)));
    const skip = (pageNum - 1) * limitNum;

    const [providers, totalCount] = await Promise.all([
      prisma.provider.findMany({
        where: whereClause,
        include: {
          user: {
            include: {
              profile: true,
            },
          },
          boats: {
            where: {
              isActive: true,
            },
            select: {
              id: true,
              name: true,
              description: true,
              capacity: true,
              basePrice: true,
              status: true,
              isActive: true,
              registrationNumber: true,
              yearBuilt: true,
              engineType: true,
              enginePower: true,
              material: true,
              length: true,
              safetyRating: true,
              serviceType: true,
              location: true,
              amenities: true,
              galleryImages: true,
              ownerId: true,
              providerId: true,
              createdAt: true,
              updatedAt: true,
              photos: {
                take: 1,
              },
            },
          },
          services: {
            where: serviceFilters,
            include: {
              serviceType: {
                include: {
                  category: true,
                },
              },
              serviceRoutes: {
                include: {
                  route: {
                    include: {
                      departureJetty: true,
                      destination: true,
                    },
                  },
                },
              },
              serviceAssignments: {
                where: {
                  isActive: true,
                },
                include: {
                  boat: {
                    select: {
                      id: true,
                      name: true,
                      description: true,
                      capacity: true,
                      basePrice: true,
                      status: true,
                      isActive: true,
                      registrationNumber: true,
                      yearBuilt: true,
                      engineType: true,
                      enginePower: true,
                      material: true,
                      length: true,
                      safetyRating: true,
                      serviceType: true,
                      location: true,
                      amenities: true,
                      galleryImages: true,
                      ownerId: true,
                      providerId: true,
                      createdAt: true,
                      updatedAt: true
                    }
                  },
                },
              },
            },
          },
        },
        orderBy: [
          { rating: 'desc' },
          { displayName: 'asc' },
        ],
        skip,
        take: limitNum,
      }),
      prisma.provider.count({ where: whereClause }),
    ]);

    // Calculate metrics and filter by capacity/price if needed
    let filteredProviders = providers.map(provider => {
      const relevantServices = provider.services.filter(service => {
        // Apply capacity filter
        if (minCapacity) {
          const totalCapacity = service.serviceAssignments.reduce(
            (total, assignment) => total + (assignment.maxCapacityOverride || assignment.boat.capacity),
            0
          );
          if (totalCapacity < parseInt(minCapacity)) return false;
        }

        // Apply price filter
        if (maxPrice) {
          if (parseFloat(service.basePrice) > parseFloat(maxPrice)) return false;
        }

        return true;
      });

      if (relevantServices.length === 0 && (minCapacity || maxPrice)) {
        return null; // Filter out providers with no matching services
      }

      return {
        ...provider,
        services: relevantServices,
        boatCount: provider.boats.length,
        serviceCount: relevantServices.length,
        startingPrice: relevantServices.length > 0
          ? Math.min(...relevantServices.map(s => parseFloat(s.basePrice)))
          : null,
        totalCapacity: relevantServices.reduce((total, service) => {
          return total + service.serviceAssignments.reduce(
            (serviceTotal, assignment) => serviceTotal + (assignment.maxCapacityOverride || assignment.boat.capacity),
            0
          );
        }, 0),
      };
    }).filter(Boolean);

    const totalPages = Math.ceil(totalCount / limitNum);

    res.json({
      success: true,
      data: filteredProviders,
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalCount: filteredProviders.length,
        hasNextPage: pageNum < totalPages,
        hasPrevPage: pageNum > 1,
        limit: limitNum,
      },
      filters: {
        serviceCategory: serviceCategory || null,
        jettyId: jettyId || null,
        destinationId: destinationId || null,
        routeId: routeId || null,
        date: date || null,
        minCapacity: minCapacity || null,
        maxPrice: maxPrice || null,
      },
    });
  } catch (error) {
    console.error('Error searching providers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search providers',
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/search/postcode/{postcode}:
 *   get:
 *     summary: Get city and state by postcode
 *     description: Retrieve city and state information for a given Malaysian postcode
 *     tags: [Search]
 *     parameters:
 *       - in: path
 *         name: postcode
 *         required: true
 *         schema:
 *           type: string
 *         description: Malaysian postcode (e.g., "5000")
 *     responses:
 *       200:
 *         description: Postcode information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PostcodeResponse'
 *       400:
 *         description: Bad request - postcode is required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Postcode not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/postcode/:postcode', async (req, res) => {
  try {
    const { postcode } = req.params;
    
    // Validate postcode
    if (!postcode) {
      return res.status(400).json({
        success: false,
        message: 'Postcode is required'
      });
    }
    
    // Find postcode in database
    const postcodeData = await prisma.postcode.findFirst({
      where: {
        postcode: postcode.toString()
      },
      include: {
        state: true
      }
    });
    
    if (!postcodeData) {
      return res.status(404).json({
        success: false,
        message: 'Postcode not found'
      });
    }
    
    res.json({
      success: true,
      data: {
        city: postcodeData.city,
        state: postcodeData.state.name
      }
    });
  } catch (error) {
    console.error('Postcode lookup error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to lookup postcode'
    });
  }
});

module.exports = router;
