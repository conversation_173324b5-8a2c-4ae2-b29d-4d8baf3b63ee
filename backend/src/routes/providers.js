const express = require('express');
const { PrismaClient } = require('@prisma/client');
const BusinessRulesService = require('../services/businessRulesService');

const router = express.Router();
const prisma = new PrismaClient();

/**
 * @swagger
 * tags:
 *   name: Providers
 *   description: Provider management and search endpoints
 */

/**
 * @swagger
 * /api/providers:
 *   get:
 *     summary: Get all providers
 *     description: Retrieve all active providers with their services, boats, and metrics
 *     tags: [Providers]
 *     responses:
 *       200:
 *         description: Providers retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/Provider'
 *                       - type: object
 *                         properties:
 *                           boatCount:
 *                             type: integer
 *                             description: Number of boats owned
 *                           serviceCount:
 *                             type: integer
 *                             description: Number of services offered
 *                           startingPrice:
 *                             type: number
 *                             description: Lowest service price
 *                 count:
 *                   type: integer
 *                   description: Total number of providers
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/', async (req, res) => {
  try {
    const providers = await prisma.provider.findMany({
      where: {
        isActive: true,
      },
      include: {
        user: {
          include: {
            profile: true,
          },
        },
        boats: {
          where: {
            isActive: true,
          },
          include: {
            photos: true,
          },
        },
        services: {
          where: {
            isActive: true,
          },
          include: {
            serviceType: {
              include: {
                category: true,
              },
            },
            serviceRoutes: {
              include: {
                route: {
                  include: {
                    departureJetty: true,
                    destination: true,
                  },
                },
              },
            },
            serviceAssignments: {
              where: {
                isActive: true,
              },
              include: {
                boat: true,
              },
            },
          },
        },
      },
      orderBy: {
        displayName: 'asc',
      },
    });

    // Calculate additional metrics for each provider
    const providersWithMetrics = await Promise.all(providers.map(async (provider) => {
      // Calculate capacity for each service
      const servicesWithCapacity = await Promise.all(
        provider.services.map(async (service) => {
          const capacityInfo = await BusinessRulesService.calculateServiceCapacity(service.id);
          return {
            ...service,
            calculatedCapacity: capacityInfo.capacity
          };
        })
      );

      return {
        ...provider,
        services: servicesWithCapacity,
        boatCount: provider.boats.length,
        serviceCount: provider.services.length,
        startingPrice: provider.services.length > 0 
          ? Math.min(...provider.services.map(s => parseFloat(s.basePrice)))
          : null,
        totalCapacity: servicesWithCapacity.reduce((total, service) => {
          return total + service.calculatedCapacity;
        }, 0),
      };
    }));

    res.json({
      success: true,
      data: providersWithMetrics,
      count: providersWithMetrics.length,
    });
  } catch (error) {
    console.error('Error fetching providers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch providers',
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/providers/{id}:
 *   get:
 *     summary: Get specific provider details
 *     description: Retrieve detailed information about a specific provider including boats, services, and operating areas
 *     tags: [Providers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Provider identifier
 *     responses:
 *       200:
 *         description: Provider details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Provider'
 *       404:
 *         description: Provider not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const provider = await prisma.provider.findUnique({
      where: {
        id: id,
        isActive: true,
      },
      include: {
        user: {
          include: {
            profile: true,
          },
        },
        boats: {
          where: {
            isActive: true,
          },
          include: {
            photos: true,
            serviceAssignments: {
              include: {
                service: {
                  include: {
                    serviceType: {
                      include: {
                        category: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
        services: {
          where: {
            isActive: true,
          },
          include: {
            serviceType: {
              include: {
                category: true,
              },
            },
            serviceRoutes: {
              include: {
                route: {
                  include: {
                    departureJetty: true,
                    destination: true,
                  },
                },
              },
            },
            serviceAssignments: {
              include: {
                boat: {
                  include: {
                    photos: true,
                  },
                },
              },
            },
          },
        },
        operatingAreas: {
          include: {
            jetty: true,
          },
        },
      },
    });

    if (!provider) {
      return res.status(404).json({
        success: false,
        message: 'Provider not found',
      });
    }

    // Calculate capacity for each service
    const servicesWithCapacity = await Promise.all(
      provider.services.map(async (service) => {
        const capacityInfo = await BusinessRulesService.calculateServiceCapacity(service.id);
        return {
          ...service,
          calculatedCapacity: capacityInfo.capacity
        };
      })
    );

    // Update provider with calculated capacity services
    const providerWithCapacity = {
      ...provider,
      services: servicesWithCapacity
    };

    res.json({
      success: true,
      data: providerWithCapacity,
    });
  } catch (error) {
    console.error('Error fetching provider:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch provider',
      error: error.message,
    });
  }
});

/**
 * @route GET /api/providers/search
 * @desc Search providers by jetty and service category
 * @access Public
 */
router.get('/search', async (req, res) => {
  try {
    const { jettyId, categoryId, destinationId } = req.query;

    let whereClause = {
      isActive: true,
    };

    // Filter by operating area (jetty)
    if (jettyId) {
      whereClause.operatingAreas = {
        some: {
          jettyId: jettyId,
          isActive: true,
        },
      };
    }

    // Filter by service category
    if (categoryId) {
      whereClause.services = {
        some: {
          isActive: true,
          serviceType: {
            categoryId: categoryId,
          },
        },
      };
    }

    // Filter by destination (for passenger transport services)
    if (destinationId) {
      whereClause.services = {
        some: {
          isActive: true,
          serviceRoutes: {
            some: {
              route: {
                destinationId: destinationId,
              },
            },
          },
        },
      };
    }

    const providers = await prisma.provider.findMany({
      where: whereClause,
      include: {
        user: {
          include: {
            profile: true,
          },
        },
        boats: {
          where: {
            isActive: true,
          },
          include: {
            photos: {
              take: 1,
            },
          },
        },
        services: {
          where: {
            isActive: true,
            ...(categoryId && {
              serviceType: {
                categoryId: categoryId,
              },
            }),
            ...(destinationId && {
              serviceRoutes: {
                some: {
                  route: {
                    destinationId: destinationId,
                  },
                },
              },
            }),
          },
          include: {
            serviceType: {
              include: {
                category: true,
              },
            },
            serviceRoutes: {
              where: destinationId ? {
                route: {
                  destinationId: destinationId,
                },
              } : {},
              include: {
                route: {
                  include: {
                    departureJetty: true,
                    destination: true,
                  },
                },
              },
            },
            serviceAssignments: {
              where: {
                isActive: true,
              },
              include: {
                boat: true,
              },
            },
          },
        },
      },
      orderBy: {
        rating: 'desc',
      },
    });

    // Calculate metrics for search results
    const searchResults = await Promise.all(providers.map(async (provider) => {
      // Calculate capacity for each service
      const servicesWithCapacity = await Promise.all(
        provider.services.map(async (service) => {
          const capacityInfo = await BusinessRulesService.calculateServiceCapacity(service.id);
          return {
            ...service,
            calculatedCapacity: capacityInfo.capacity
          };
        })
      );

      return {
        ...provider,
        services: servicesWithCapacity,
        boatCount: provider.boats.length,
        serviceCount: provider.services.length,
        startingPrice: provider.services.length > 0 
          ? Math.min(...provider.services.map(s => parseFloat(s.basePrice)))
          : null,
        totalCapacity: servicesWithCapacity.reduce((total, service) => {
          return total + service.calculatedCapacity;
        }, 0),
      };
    }));

    res.json({
      success: true,
      data: searchResults,
      count: searchResults.length,
      filters: {
        jettyId,
        categoryId,
        destinationId,
      },
    });
  } catch (error) {
    console.error('Error searching providers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search providers',
      error: error.message,
    });
  }
});

module.exports = router;
