const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');
const { body, query, param } = require('express-validator');

const router = express.Router();
const prisma = new PrismaClient();

// Admin authentication middleware
const requireAdmin = [authenticateToken, requireRole('ADMIN')];

/**
 * @swagger
 * tags:
 *   name: Admin
 *   description: Admin management endpoints
 */

// =====================
// USERS MANAGEMENT
// =====================

/**
 * @swagger
 * /api/admin/users:
 *   get:
 *     summary: Get all users with pagination and filtering
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [CUSTOMER, BOAT_OWNER, AFFILIATE_AGENT, ADMIN]
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: List of users
 */
router.get('/users', requireAdmin, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('role').optional().isIn(['CUSTOMER', 'BOAT_OWNER', 'AFFILIATE_AGENT', 'ADMIN']),
  query('isActive').optional().isBoolean(),
  query('search').optional().isString().trim()
], validateRequest, async (req, res) => {
  try {
    const { page = 1, limit = 20, role, isActive, search } = req.query;
    const skip = (page - 1) * limit;

    // Build filter conditions
    const where = {};
    if (role) where.role = role;
    if (isActive !== undefined) where.isActive = isActive === 'true';
    if (search) {
      where.OR = [
        { email: { contains: search, mode: 'insensitive' } },
        { profile: { firstName: { contains: search, mode: 'insensitive' } } },
        { profile: { lastName: { contains: search, mode: 'insensitive' } } }
      ];
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          profile: true,
          provider: {
            select: {
              id: true,
              companyName: true,
              isVerified: true,
              isActive: true
            }
          },
          _count: {
            select: {
              boats: true,
              bookings: true,
              affiliateLinks: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ]);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Admin get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/admin/users/{id}:
 *   get:
 *     summary: Get user details by ID
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: User details
 */
router.get('/users/:id', requireAdmin, [
  param('id').isString().notEmpty()
], validateRequest, async (req, res) => {
  try {
    const { id } = req.params;

    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        profile: true,
        provider: {
          include: {
            services: {
              select: {
                id: true,
                name: true,
                isActive: true
              }
            },
            _count: {
              select: {
                boats: true,
                bookings: true
              }
            }
          }
        },
        boats: {
          select: {
            id: true,
            name: true,
            status: true,
            isActive: true
          }
        },
        bookings: {
          select: {
            id: true,
            status: true,
            totalAmount: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        affiliateLinks: {
          select: {
            id: true,
            code: true,
            clickCount: true,
            isActive: true
          }
        }
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Admin get user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user details',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/admin/users/{id}/status:
 *   patch:
 *     summary: Update user status (activate/deactivate)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               isActive:
 *                 type: boolean
 *             required:
 *               - isActive
 *     responses:
 *       200:
 *         description: User status updated
 */
router.patch('/users/:id/status', requireAdmin, [
  param('id').isString().notEmpty(),
  body('isActive').isBoolean()
], validateRequest, async (req, res) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;

    const user = await prisma.user.update({
      where: { id },
      data: { isActive },
      include: {
        profile: true,
        provider: true
      }
    });

    res.json({
      success: true,
      message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
      data: user
    });
  } catch (error) {
    console.error('Admin update user status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user status',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/admin/users/{id}/role:
 *   patch:
 *     summary: Update user role
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               role:
 *                 type: string
 *                 enum: [CUSTOMER, BOAT_OWNER, AFFILIATE_AGENT, ADMIN]
 *             required:
 *               - role
 *     responses:
 *       200:
 *         description: User role updated
 */
router.patch('/users/:id/role', requireAdmin, [
  param('id').isString().notEmpty(),
  body('role').isIn(['CUSTOMER', 'BOAT_OWNER', 'AFFILIATE_AGENT', 'ADMIN'])
], validateRequest, async (req, res) => {
  try {
    const { id } = req.params;
    const { role } = req.body;

    const user = await prisma.user.update({
      where: { id },
      data: { role },
      include: {
        profile: true,
        provider: true
      }
    });

    res.json({
      success: true,
      message: `User role updated to ${role} successfully`,
      data: user
    });
  } catch (error) {
    console.error('Admin update user role error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user role',
      error: error.message
    });
  }
});

// =====================
// PROVIDERS MANAGEMENT
// =====================

/**
 * @swagger
 * /api/admin/providers:
 *   get:
 *     summary: Get all providers with pagination and filtering
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: isVerified
 *         schema:
 *           type: boolean
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: List of providers
 */
router.get('/providers', requireAdmin, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('isVerified').optional().isBoolean(),
  query('isActive').optional().isBoolean(),
  query('search').optional().isString().trim()
], validateRequest, async (req, res) => {
  try {
    const { page = 1, limit = 20, isVerified, isActive, search } = req.query;
    const skip = (page - 1) * limit;

    // Build filter conditions
    const where = {};
    if (isVerified !== undefined) where.isVerified = isVerified === 'true';
    if (isActive !== undefined) where.isActive = isActive === 'true';
    if (search) {
      where.OR = [
        { companyName: { contains: search, mode: 'insensitive' } },
        { displayName: { contains: search, mode: 'insensitive' } },
        { businessEmail: { contains: search, mode: 'insensitive' } }
      ];
    }

    const [providers, total] = await Promise.all([
      prisma.provider.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          user: {
            select: {
              id: true,
              email: true,
              isActive: true,
              createdAt: true,
              profile: {
                select: {
                  firstName: true,
                  lastName: true,
                  phone: true
                }
              }
            }
          },
          _count: {
            select: {
              boats: true,
              services: true,
              bookings: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.provider.count({ where })
    ]);

    res.json({
      success: true,
      data: {
        providers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Admin get providers error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch providers',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/admin/providers/{id}/verify:
 *   patch:
 *     summary: Update provider verification status
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               isVerified:
 *                 type: boolean
 *             required:
 *               - isVerified
 *     responses:
 *       200:
 *         description: Provider verification status updated
 */
router.patch('/providers/:id/verify', requireAdmin, [
  param('id').isString().notEmpty(),
  body('isVerified').isBoolean()
], validateRequest, async (req, res) => {
  try {
    const { id } = req.params;
    const { isVerified } = req.body;

    const provider = await prisma.provider.update({
      where: { id },
      data: { isVerified },
      include: {
        user: {
          select: {
            email: true,
            profile: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        }
      }
    });

    res.json({
      success: true,
      message: `Provider ${isVerified ? 'verified' : 'unverified'} successfully`,
      data: provider
    });
  } catch (error) {
    console.error('Admin update provider verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update provider verification status',
      error: error.message
    });
  }
});

// =====================
// BOATS MANAGEMENT
// =====================

/**
 * @swagger
 * /api/admin/boats:
 *   get:
 *     summary: Get all boats with pagination and filtering
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [DRAFT, PENDING_APPROVAL, APPROVED, REJECTED, INACTIVE, MAINTENANCE]
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: List of boats
 */
router.get('/boats', requireAdmin, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['DRAFT', 'PENDING_APPROVAL', 'APPROVED', 'REJECTED', 'INACTIVE', 'MAINTENANCE']),
  query('isActive').optional().isBoolean(),
  query('search').optional().isString().trim()
], validateRequest, async (req, res) => {
  try {
    const { page = 1, limit = 20, status, isActive, search } = req.query;
    const skip = (page - 1) * limit;

    // Build filter conditions
    const where = {};
    if (status) where.status = status;
    if (isActive !== undefined) where.isActive = isActive === 'true';
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { registrationNumber: { contains: search, mode: 'insensitive' } }
      ];
    }

    const [boats, total] = await Promise.all([
      prisma.boat.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          owner: {
            select: {
              id: true,
              email: true,
              profile: {
                select: {
                  firstName: true,
                  lastName: true,
                  phone: true
                }
              }
            }
          },
          provider: {
            select: {
              id: true,
              companyName: true,
              isVerified: true
            }
          },
          _count: {
            select: {
              serviceAssignments: true,
              bookings: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.boat.count({ where })
    ]);

    res.json({
      success: true,
      data: {
        boats,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Admin get boats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch boats',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/admin/boats/{id}/status:
 *   patch:
 *     summary: Update boat status
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [DRAFT, PENDING_APPROVAL, APPROVED, REJECTED, INACTIVE, MAINTENANCE]
 *             required:
 *               - status
 *     responses:
 *       200:
 *         description: Boat status updated
 */
router.patch('/boats/:id/status', requireAdmin, [
  param('id').isString().notEmpty(),
  body('status').isIn(['DRAFT', 'PENDING_APPROVAL', 'APPROVED', 'REJECTED', 'INACTIVE', 'MAINTENANCE']),
  body('rejectionReason').optional().isString().trim()
], validateRequest, async (req, res) => {
  try {
    const { id } = req.params;
    const { status, rejectionReason } = req.body;
    const adminUserId = req.user.id;

    // Validate rejection reason for REJECTED status
    if (status === 'REJECTED' && !rejectionReason) {
      return res.status(400).json({
        success: false,
        message: 'Rejection reason is required when rejecting a boat'
      });
    }

    // Prepare update data
    const updateData = { status };

    if (status === 'APPROVED') {
      updateData.approvedBy = adminUserId;
      updateData.approvedAt = new Date();
      updateData.rejectionReason = null; // Clear any previous rejection reason
    } else if (status === 'REJECTED') {
      updateData.rejectionReason = rejectionReason;
      updateData.approvedBy = null;
      updateData.approvedAt = null;
    }

    const boat = await prisma.boat.update({
      where: { id },
      data: updateData,
      include: {
        owner: {
          select: {
            email: true,
            profile: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        },
        provider: {
          select: {
            companyName: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: `Boat status updated to ${status} successfully`,
      data: boat
    });
  } catch (error) {
    console.error('Admin update boat status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update boat status',
      error: error.message
    });
  }
});

// =====================
// DASHBOARD STATS
// =====================

/**
 * @swagger
 * /api/admin/dashboard/stats:
 *   get:
 *     summary: Get admin dashboard statistics
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard statistics
 */
router.get('/dashboard/stats', requireAdmin, async (req, res) => {
  try {
    const [
      totalUsers,
      activeUsers,
      totalProviders,
      verifiedProviders,
      totalBoats,
      approvedBoats,
      pendingBoats,
      totalBookings,
      recentBookings
    ] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({ where: { isActive: true } }),
      prisma.provider.count(),
      prisma.provider.count({ where: { isVerified: true } }),
      prisma.boat.count(),
      prisma.boat.count({ where: { status: 'APPROVED' } }),
      prisma.boat.count({ where: { status: 'PENDING_APPROVAL' } }),
      prisma.booking.count(),
      prisma.booking.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
          }
        }
      })
    ]);

    const stats = {
      users: {
        total: totalUsers,
        active: activeUsers,
        inactive: totalUsers - activeUsers
      },
      providers: {
        total: totalProviders,
        verified: verifiedProviders,
        unverified: totalProviders - verifiedProviders
      },
      boats: {
        total: totalBoats,
        approved: approvedBoats,
        pending: pendingBoats,
        other: totalBoats - approvedBoats - pendingBoats
      },
      bookings: {
        total: totalBookings,
        recent: recentBookings
      }
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Admin dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard statistics',
      error: error.message
    });
  }
});

// =====================
// BOAT OWNER APPROVAL MANAGEMENT
// =====================

/**
 * @swagger
 * /api/admin/boat-owners/pending:
 *   get:
 *     summary: Get pending boat owner approvals
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *     responses:
 *       200:
 *         description: List of pending boat owner approvals
 */
router.get('/boat-owners/pending', requireAdmin, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 })
], validateRequest, async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const skip = (page - 1) * limit;

    const where = {
      role: 'BOAT_OWNER',
      emailVerified: true,
      isApproved: false
    };

    const [pendingBoatOwners, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          profile: true,
          provider: {
            select: {
              id: true,
              companyName: true,
              businessPhone: true,
              businessEmail: true,
              brn: true,
              isVerified: true
            }
          }
        },
        orderBy: { createdAt: 'asc' } // Oldest first for approval queue
      }),
      prisma.user.count({ where })
    ]);

    // Decrypt sensitive profile data before sending to frontend
    const dataProtection = require('../utils/dataProtection');
    const decryptedPendingBoatOwners = pendingBoatOwners.map(user => {
      const decryptedUser = { ...user };
      if (decryptedUser.profile) {
        decryptedUser.profile = dataProtection.decryptProfileData(decryptedUser.profile);
      }
      // Decrypt provider businessPhone if it exists
      if (decryptedUser.provider && decryptedUser.provider.businessPhone) {
        decryptedUser.provider.businessPhone = dataProtection.decrypt(decryptedUser.provider.businessPhone);
      }
      return decryptedUser;
    });

    res.json({
      success: true,
      data: {
        pendingBoatOwners: decryptedPendingBoatOwners,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Admin get pending boat owners error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch pending boat owners',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/admin/boat-owners/{id}/approve:
 *   post:
 *     summary: Approve boat owner account
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               approvalNotes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Boat owner approved successfully
 */
router.post('/boat-owners/:id/approve', requireAdmin, [
  param('id').isString().notEmpty(),
  body('approvalNotes').optional().isString()
], validateRequest, async (req, res) => {
  try {
    const { id } = req.params;
    const { approvalNotes } = req.body;
    const adminUserId = req.user.id;

    // Find the boat owner
    const boatOwner = await prisma.user.findFirst({
      where: {
        id,
        role: 'BOAT_OWNER',
        emailVerified: true,
        isApproved: false
      },
      include: {
        profile: true,
        provider: true
      }
    });

    if (!boatOwner) {
      return res.status(404).json({
        success: false,
        message: 'Boat owner not found or already processed',
        code: 'BOAT_OWNER_NOT_FOUND'
      });
    }

    // Update user and provider in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Approve user account
      const approvedUser = await tx.user.update({
        where: { id },
        data: {
          isApproved: true,
          approvedAt: new Date(),
          approvedBy: adminUserId
        },
        include: {
          profile: true
        }
      });

      // Activate provider account
      await tx.provider.update({
        where: { userId: id },
        data: {
          isActive: true,
          isVerified: true
        }
      });

      return approvedUser;
    });

    // Send approval email to boat owner
    try {
      const emailService = require('../services/emailService');
      await emailService.sendBoatOwnerApprovalEmail(
        result.email,
        result.profile.firstName,
        result.profile.companyName
      );
      console.log(`Approval email sent to ${result.email}`);
    } catch (emailError) {
      console.error('Failed to send approval email:', emailError);
      // Don't fail the approval if email sending fails
    }

    console.log(`Boat owner approved by admin ${adminUserId}: ${result.email}`);

    res.json({
      success: true,
      message: 'Boat owner approved successfully',
      data: {
        user: result,
        approvalNotes
      }
    });
  } catch (error) {
    console.error('Admin approve boat owner error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to approve boat owner',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/admin/boat-owners/{id}/reject:
 *   post:
 *     summary: Reject boat owner account
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - rejectionReason
 *             properties:
 *               rejectionReason:
 *                 type: string
 *     responses:
 *       200:
 *         description: Boat owner rejected successfully
 */
router.post('/boat-owners/:id/reject', requireAdmin, [
  param('id').isString().notEmpty(),
  body('rejectionReason').notEmpty().withMessage('Rejection reason is required')
], validateRequest, async (req, res) => {
  try {
    const { id } = req.params;
    const { rejectionReason } = req.body;
    const adminUserId = req.user.id;

    // Find the boat owner
    const boatOwner = await prisma.user.findFirst({
      where: {
        id,
        role: 'BOAT_OWNER',
        emailVerified: true,
        isApproved: false
      },
      include: {
        profile: true,
        provider: true
      }
    });

    if (!boatOwner) {
      return res.status(404).json({
        success: false,
        message: 'Boat owner not found or already processed',
        code: 'BOAT_OWNER_NOT_FOUND'
      });
    }

    // Update user with rejection details
    const result = await prisma.user.update({
      where: { id },
      data: {
        isApproved: false, // Explicitly set to false
        rejectionReason,
        approvedBy: adminUserId // Track who made the decision
      },
      include: {
        profile: true
      }
    });

    // Send rejection email to boat owner
    try {
      const emailService = require('../services/emailService');
      await emailService.sendBoatOwnerRejectionEmail(
        result.email,
        result.profile.firstName,
        rejectionReason
      );
      console.log(`Rejection email sent to ${result.email}`);
    } catch (emailError) {
      console.error('Failed to send rejection email:', emailError);
      // Don't fail the rejection if email sending fails
    }

    console.log(`Boat owner rejected by admin ${adminUserId}: ${result.email}`);

    res.json({
      success: true,
      message: 'Boat owner rejected successfully',
      data: {
        user: result,
        rejectionReason
      }
    });
  } catch (error) {
    console.error('Admin reject boat owner error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reject boat owner',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/admin/boat-owners/approved:
 *   get:
 *     summary: Get approved boat owners
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *     responses:
 *       200:
 *         description: List of approved boat owners
 */
router.get('/boat-owners/approved', requireAdmin, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 })
], validateRequest, async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const skip = (page - 1) * limit;

    const where = {
      role: 'BOAT_OWNER',
      emailVerified: true,
      isApproved: true
    };

    const [approvedBoatOwners, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          profile: true,
          provider: {
            select: {
              id: true,
              companyName: true,
              businessPhone: true,
              businessEmail: true,
              brn: true,
              isVerified: true,
              rating: true,
              reviewCount: true,
              _count: {
                select: {
                  boats: true,
                  services: true,
                  bookings: true
                }
              }
            }
          }
        },
        orderBy: { approvedAt: 'desc' }
      }),
      prisma.user.count({ where })
    ]);

    // Decrypt sensitive profile data before sending to frontend
    const dataProtection = require('../utils/dataProtection');
    const decryptedApprovedBoatOwners = approvedBoatOwners.map(user => {
      const decryptedUser = { ...user };
      if (decryptedUser.profile) {
        decryptedUser.profile = dataProtection.decryptProfileData(decryptedUser.profile);
      }
      // Decrypt provider businessPhone if it exists
      if (decryptedUser.provider && decryptedUser.provider.businessPhone) {
        decryptedUser.provider.businessPhone = dataProtection.decrypt(decryptedUser.provider.businessPhone);
      }
      return decryptedUser;
    });

    res.json({
      success: true,
      data: {
        approvedBoatOwners: decryptedApprovedBoatOwners,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Admin get approved boat owners error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch approved boat owners',
      error: error.message
    });
  }
});

module.exports = router;