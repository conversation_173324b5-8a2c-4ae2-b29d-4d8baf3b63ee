const express = require('express');
const { PrismaClient } = require('@prisma/client');
const BusinessRulesService = require('../services/businessRulesService');

const router = express.Router();
const prisma = new PrismaClient();

/**
 * @swagger
 * /api/services/categories:
 *   get:
 *     summary: Get all service categories
 *     description: Retrieve all active service categories with their associated service types
 *     tags: [Services]
 *     responses:
 *       200:
 *         description: Service categories retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ServiceCategory'
 *                 count:
 *                   type: integer
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/categories', async (req, res) => {
  try {
    const categories = await prisma.serviceCategory.findMany({
      where: {
        isActive: true,
      },
      include: {
        serviceTypes: {
          where: {
            isActive: true,
          },
          orderBy: {
            name: 'asc',
          },
        },
      },
      orderBy: {
        sortOrder: 'asc',
      },
    });

    res.json({
      success: true,
      data: categories,
      count: categories.length,
    });
  } catch (error) {
    console.error('Error fetching service categories:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch service categories',
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/services/{id}:
 *   get:
 *     summary: Get detailed information about a specific service
 *     description: Retrieve comprehensive details about a service including pricing packages, itinerary, provider info, and assigned boats
 *     tags: [Services]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Service ID
 *     responses:
 *       200:
 *         description: Service details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 service:
 *                   $ref: '#/components/schemas/Service'
 *       404:
 *         description: Service not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const service = await prisma.providerService.findUnique({
      where: {
        id: id,
        isActive: true
      },
      include: {
        provider: {
          include: {
            user: {
              include: {
                profile: true
              }
            }
          }
        },
        serviceType: {
          include: {
            category: true
          }
        },
        serviceRoutes: {
          where: { isActive: true },
          include: {
            route: {
              include: {
                departureJetty: true,
                destination: true
              }
            }
          }
        },
        serviceAssignments: {
          where: { isActive: true },
          include: {
            boat: {
              include: {
                photos: true
              }
            }
          },
          orderBy: [
            { isPrimary: 'desc' },
            { assignedAt: 'asc' }
          ]
        },
        servicePackages: {
          where: { isActive: true },
          include: {
            packageType: true
          },
          orderBy: {
            packageType: {
              sortOrder: 'asc'
            }
          }
        },
        serviceAgePricing: {
          where: { isActive: true },
          include: {
            ageCategory: true
          },
          orderBy: {
            ageCategory: {
              sortOrder: 'asc'
            }
          }
        }
      }
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found or not available'
      });
    }

    // Format age pricing from ServiceAgePricing table
    const agePricing = {};
    if (service.serviceAgePricing && service.serviceAgePricing.length > 0) {
      service.serviceAgePricing.forEach(pricing => {
        const ageCode = pricing.ageCategory.code.toLowerCase();
        agePricing[ageCode] = parseFloat(pricing.price);
      });
    }

    // Calculate actual capacity from service assignments
    const capacityInfo = await BusinessRulesService.calculateServiceCapacity(service.id);

    // Format the service data for frontend consumption
    const formattedService = {
      id: service.id,
      name: service.name,
      description: service.description,
      basePrice: parseFloat(service.basePrice),
      agePricing: Object.keys(agePricing).length > 0 ? agePricing : service.agePricing, // Fallback to JSON field if no ServiceAgePricing
      duration: service.duration,
      maxCapacity: service.maxCapacity,
      calculatedCapacity: capacityInfo.capacity, // Add calculated capacity from assignments
      includedItems: service.includedItems || [],
      excludedItems: service.excludedItems || [],
      specialInstruction: service.specialInstruction,
      images: service.images || [],
      itinerary: service.itinerary,
      requirements: service.requirements,
      isActive: service.isActive,
      createdAt: service.createdAt,
      updatedAt: service.updatedAt,

      // Provider information
      provider: {
        id: service.provider.id,
        displayName: service.provider.displayName,
        companyName: service.provider.companyName,
        description: service.provider.description,
        rating: parseFloat(service.provider.rating),
        reviewCount: service.provider.reviewCount,
        isVerified: service.provider.isVerified,
        logoUrl: service.provider.logoUrl,
        coverImageUrl: service.provider.coverImageUrl,
        businessPhone: service.provider.businessPhone,
        businessEmail: service.provider.businessEmail
      },

      // Service type information
      serviceType: {
        id: service.serviceType.id,
        name: service.serviceType.name,
        code: service.serviceType.code,
        description: service.serviceType.description,
        category: {
          id: service.serviceType.category.id,
          name: service.serviceType.category.name,
          code: service.serviceType.category.code,
          requiresDestination: service.serviceType.category.requiresDestination
        }
      },

      // Available routes
      routes: service.serviceRoutes.map(sr => ({
        id: sr.route.id,
        priceModifier: parseFloat(sr.priceModifier),
        departureJetty: {
          id: sr.route.departureJetty.id,
          name: sr.route.departureJetty.name,
          location: sr.route.departureJetty.location
        },
        destination: {
          id: sr.route.destination.id,
          name: sr.route.destination.name,
          description: sr.route.destination.description
        },
        distance: parseFloat(sr.route.distance),
        estimatedDuration: sr.route.estimatedDuration
      })),

      // Assigned boats
      assignedBoats: service.serviceAssignments.map(sa => ({
        id: sa.boat.id,
        name: sa.boat.name,
        capacity: sa.boat.capacity,
        boatType: sa.boat.boatType,
        isPrimary: sa.isPrimary,
        photos: sa.boat.photos.map(photo => ({
          id: photo.id,
          url: photo.url,
          caption: photo.caption,
          isPrimary: photo.isPrimary
        }))
      })),

      // Gallery images (from assigned boats)
      galleryImages: service.serviceAssignments
        .flatMap(sa => sa.boat.photos)
        .filter(photo => photo.url)
        .map(photo => photo.url)
        .slice(0, 10), // Limit to 10 images

      // Dynamic pricing packages
      packages: service.servicePackages.map(sp => ({
        id: sp.id,
        packageType: {
          id: sp.packageType.id,
          name: sp.packageType.name,
          code: sp.packageType.code,
          description: sp.packageType.description,
          isDefault: sp.packageType.isDefault
        },
        description: sp.description,
        basePrice: parseFloat(sp.basePrice),
        agePricing: sp.agePricing,
        priceModifier: parseFloat(sp.priceModifier),
        includedItems: sp.includedItems || [],
        excludedItems: sp.excludedItems || []
      }))
    };

    res.json({
      success: true,
      service: formattedService
    });

  } catch (error) {
    console.error('Error fetching service details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch service details',
      error: error.message
    });
  }
});

/**
 * @route GET /api/services/types
 * @desc Get all service types with their categories
 * @access Public
 */
router.get('/types', async (req, res) => {
  try {
    const { categoryId } = req.query;

    let whereClause = {
      isActive: true,
    };

    if (categoryId) {
      whereClause.categoryId = categoryId;
    }

    const serviceTypes = await prisma.serviceType.findMany({
      where: whereClause,
      include: {
        category: true,
        providerServices: {
          where: {
            isActive: true,
          },
          include: {
            provider: {
              where: {
                isActive: true,
              },
            },
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    // Add metrics to each service type
    const serviceTypesWithMetrics = serviceTypes.map(serviceType => ({
      ...serviceType,
      providerCount: serviceType.providerServices.length,
      availableProviders: serviceType.providerServices.map(ps => ps.provider),
    }));

    res.json({
      success: true,
      data: serviceTypesWithMetrics,
      count: serviceTypesWithMetrics.length,
    });
  } catch (error) {
    console.error('Error fetching service types:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch service types',
      error: error.message,
    });
  }
});

// Duplicate route removed - using the first implementation above

/**
 * @route GET /api/services/:id/timeslots
 * @desc Get available timeslots for a service and date
 * @access Public
 */
router.get('/:id/timeslots', async (req, res) => {
  try {
    const { id } = req.params;
    const { date } = req.query;

    if (!date) {
      return res.status(400).json({
        success: false,
        message: 'Date parameter is required',
      });
    }

    const requestedDate = new Date(date);
    const dayOfWeek = requestedDate.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Calculate total capacity from service assignments (always from here)
    const service = await prisma.providerService.findUnique({
      where: { id },
      include: {
        serviceAssignments: {
          where: { isActive: true },
          include: { boat: true }
        }
      }
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found',
      });
    }

    const totalCapacity = service.serviceAssignments.reduce((total, assignment) => {
      return total + (assignment.maxCapacityOverride || assignment.boat.capacity);
    }, 0);

    // STEP 1: Get recurring schedule (always takes priority)
    const schedules = await prisma.serviceSchedule.findMany({
      where: {
        serviceId: id,
        OR: [
          { dayOfWeek: dayOfWeek, isActive: true },  // Specific day schedules
          { dayOfWeek: null, isActive: true }        // Every day schedules
        ],
      },
      orderBy: { departureTime: 'asc' },
    });

    if (schedules.length === 0) {
      return res.json({
        success: true,
        data: {
          date: date,
          timeSlots: [],
          totalCapacity: totalCapacity,
          bookedCapacity: 0,
          availableCapacity: 0,
          source: 'no_schedule',
          message: 'No recurring schedule found for this day'
        },
      });
    }

    // STEP 2: Filter out blocked timeslots from ServiceAvailability
    const blockedSlots = await prisma.serviceAvailability.findMany({
      where: {
        serviceId: id,
        date: requestedDate,
        isActive: false  // FALSE = BLOCKED
      }
    });

    const blockedTimeSlots = new Set(blockedSlots.map(block => block.timeSlot));

    // STEP 3: Calculate booked capacity from actual bookings
    const existingBookings = await prisma.booking.findMany({
      where: {
        serviceId: id,
        serviceDate: requestedDate,
        status: { in: ['PENDING', 'CONFIRMED'] }
      }
    });

    // Create timeslot data with capacity info
    const timeSlotData = await Promise.all(
      schedules
        .filter(schedule => !blockedTimeSlots.has(schedule.departureTime))
        .map(async (schedule) => {
          // Calculate booked capacity for this specific timeslot
          const timeslotBookings = existingBookings.filter(
            booking => booking.serviceTime === schedule.departureTime
          );

          const bookedCapacity = timeslotBookings.reduce((total, booking) => {
            return total + booking.passengerCount;
          }, 0);

          const availableCapacity = Math.max(0, totalCapacity - bookedCapacity);

          return {
            timeSlot: schedule.departureTime,
            totalCapacity: totalCapacity,
            bookedCapacity: bookedCapacity,
            availableCapacity: availableCapacity,
            isAvailable: availableCapacity > 0
          };
        })
    );

    // Simple array of available timeslots (for backward compatibility)
    const availableTimeSlots = timeSlotData
      .filter(slot => slot.isAvailable)
      .map(slot => slot.timeSlot);

    res.json({
      success: true,
      data: {
        date: date,
        timeSlots: availableTimeSlots,
        timeslotDetails: timeSlotData, // Detailed capacity per timeslot
        totalCapacity: totalCapacity,
        source: 'recurring_schedule_with_booking_check',
        blockedCount: blockedSlots.length
      },
    });

  } catch (error) {
    console.error('Error fetching service timeslots:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch service timeslots',
      error: error.message,
    });
  }
});

/**
 * @route GET /api/services/:id/availability
 * @desc Get availability for a specific service
 * @access Public
 */
router.get('/:id/availability', async (req, res) => {
  try {
    const { id } = req.params;
    const { date, timeSlot } = req.query;

    let whereClause = {
      serviceId: id,
      isActive: true,
    };

    if (date) {
      whereClause.date = new Date(date);
    } else {
      whereClause.date = {
        gte: new Date(),
      };
    }

    if (timeSlot) {
      whereClause.timeSlot = timeSlot;
    }

    const availability = await prisma.serviceAvailability.findMany({
      where: whereClause,
      include: {
        service: {
          include: {
            serviceAssignments: {
              where: {
                isActive: true,
              },
              include: {
                boat: true,
              },
            },
          },
        },
      },
      orderBy: [
        { date: 'asc' },
        { timeSlot: 'asc' },
      ],
    });

    res.json({
      success: true,
      data: availability,
      count: availability.length,
    });
  } catch (error) {
    console.error('Error fetching service availability:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch service availability',
      error: error.message,
    });
  }
});

/**
 * @route GET /api/services/search
 * @desc Search services by various criteria
 * @access Public
 */
router.get('/search', async (req, res) => {
  try {
    const { categoryId, jettyId, destinationId, date, capacity } = req.query;

    let whereClause = {
      isActive: true,
    };

    // Filter by service category
    if (categoryId) {
      whereClause.serviceType = {
        categoryId: categoryId,
      };
    }

    // Filter by jetty (through provider operating areas)
    if (jettyId) {
      whereClause.provider = {
        operatingAreas: {
          some: {
            jettyId: jettyId,
            isActive: true,
          },
        },
      };
    }

    // Filter by destination (through service routes)
    if (destinationId) {
      whereClause.serviceRoutes = {
        some: {
          route: {
            destinationId: destinationId,
          },
        },
      };
    }

    // Filter by minimum capacity
    if (capacity) {
      whereClause.maxCapacity = {
        gte: parseInt(capacity),
      };
    }

    const services = await prisma.providerService.findMany({
      where: whereClause,
      include: {
        provider: {
          include: {
            user: {
              include: {
                profile: true,
              },
            },
          },
        },
        serviceType: {
          include: {
            category: true,
          },
        },
        serviceRoutes: {
          where: destinationId ? {
            route: {
              destinationId: destinationId,
            },
          } : {},
          include: {
            route: {
              include: {
                departureJetty: true,
                destination: true,
              },
            },
          },
        },
        serviceAssignments: {
          where: {
            isActive: true,
          },
          include: {
            boat: {
              include: {
                photos: {
                  take: 1,
                },
              },
            },
          },
        },
      },
      orderBy: {
        basePrice: 'asc',
      },
    });

    res.json({
      success: true,
      data: services,
      count: services.length,
      filters: {
        categoryId,
        jettyId,
        destinationId,
        date,
        capacity,
      },
    });
  } catch (error) {
    console.error('Error searching services:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search services',
      error: error.message,
    });
  }
});

/**
 * @route GET /api/services/config/age-categories
 * @desc Get all active age categories
 * @access Public
 */
router.get('/config/age-categories', async (req, res) => {
  try {
    const ageCategories = await prisma.ageCategory.findMany({
      where: {
        isActive: true,
      },
      orderBy: {
        sortOrder: 'asc',
      },
    });

    res.json({
      success: true,
      data: ageCategories,
    });
  } catch (error) {
    console.error('Error fetching age categories:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch age categories',
      error: error.message,
    });
  }
});

/**
 * @route GET /api/services/:id/age-ranges
 * @desc Get age ranges for a specific service (with service-specific overrides)
 * @access Public
 */
router.get('/:id/age-ranges', async (req, res) => {
  try {
    const { id } = req.params;

    // First, verify the service exists
    const service = await prisma.providerService.findUnique({
      where: { id }
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found'
      });
    }

    // Get service-specific age range configurations
    const serviceAgeRanges = await prisma.serviceAgeRange.findMany({
      where: {
        serviceId: id,
        isActive: true
      },
      include: {
        ageCategory: true
      },
      orderBy: {
        ageCategory: {
          sortOrder: 'asc'
        }
      }
    });

    // If no service-specific age ranges are configured, return all default age categories
    // This maintains backward compatibility for services without specific age restrictions
    if (serviceAgeRanges.length === 0) {
      const ageCategories = await prisma.ageCategory.findMany({
        where: { isActive: true },
        orderBy: { sortOrder: 'asc' }
      });

      const finalAgeRanges = ageCategories.map(category => ({
        id: category.id,
        code: category.code,
        name: category.name,
        description: category.description,
        minAge: category.defaultMinAge,
        maxAge: category.defaultMaxAge,
        isConfigurable: category.isConfigurable,
        hasOverride: false,
        sortOrder: category.sortOrder
      }));

      return res.json({
        success: true,
        data: {
          serviceId: id,
          ageRanges: finalAgeRanges
        }
      });
    }

    // Return only the age categories that have been specifically configured for this service
    const finalAgeRanges = serviceAgeRanges.map(sar => ({
      id: sar.ageCategory.id,
      code: sar.ageCategory.code,
      name: sar.ageCategory.name,
      description: sar.ageCategory.description,
      minAge: sar.minAge,
      maxAge: sar.maxAge,
      isConfigurable: sar.ageCategory.isConfigurable,
      hasOverride: true,
      sortOrder: sar.ageCategory.sortOrder
    }));

    res.json({
      success: true,
      data: {
        serviceId: id,
        ageRanges: finalAgeRanges
      }
    });

  } catch (error) {
    console.error('Error fetching service age ranges:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/services/{id}/age-ranges:
 *   post:
 *     summary: Update age ranges for a specific service
 *     description: Create or update age range configurations for a service
 *     tags: [Services]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Service ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ageRanges
 *             properties:
 *               ageRanges:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - ageCategoryId
 *                     - minAge
 *                   properties:
 *                     ageCategoryId:
 *                       type: string
 *                       description: Age category identifier
 *                     minAge:
 *                       type: integer
 *                       description: Minimum age for this category
 *                     maxAge:
 *                       type: integer
 *                       description: Maximum age for this category (optional)
 *     responses:
 *       200:
 *         description: Age ranges updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Updated 3 age range configurations
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/AgeRange'
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Service not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/:id/age-ranges', async (req, res) => {
  try {
    const { id } = req.params;
    const { ageRanges } = req.body;

    // Verify the service exists
    const service = await prisma.providerService.findUnique({
      where: { id }
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found'
      });
    }

    // Validate age ranges data
    if (!Array.isArray(ageRanges)) {
      return res.status(400).json({
        success: false,
        message: 'Age ranges must be an array'
      });
    }

    // Process each age range update
    const results = [];

    for (const range of ageRanges) {
      const { ageCategoryId, minAge, maxAge } = range;

      // Validate required fields
      if (!ageCategoryId || minAge === undefined) {
        continue; // Skip invalid entries
      }

      // Verify age category exists and is configurable
      const ageCategory = await prisma.ageCategory.findUnique({
        where: { id: ageCategoryId }
      });

      if (!ageCategory || !ageCategory.isConfigurable) {
        continue; // Skip non-configurable categories
      }

      // Upsert service age range
      const result = await prisma.serviceAgeRange.upsert({
        where: {
          serviceId_ageCategoryId: {
            serviceId: id,
            ageCategoryId: ageCategoryId
          }
        },
        update: {
          minAge: minAge,
          maxAge: maxAge,
          updatedAt: new Date()
        },
        create: {
          id: `sar_${id}_${ageCategory.code.toLowerCase()}`,
          serviceId: id,
          ageCategoryId: ageCategoryId,
          minAge: minAge,
          maxAge: maxAge
        }
      });

      results.push(result);
    }

    res.json({
      success: true,
      message: `Updated ${results.length} age range configurations`,
      data: results
    });

  } catch (error) {
    console.error('Error updating service age ranges:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/services/{id}/block-date:
 *   post:
 *     summary: Block specific date/timeslots for a service
 *     description: Block specific date and timeslots to make them unavailable for booking
 *     tags: [Services]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Service ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - date
 *               - timeSlots
 *             properties:
 *               date:
 *                 type: string
 *                 format: date
 *                 description: Date to block (YYYY-MM-DD)
 *                 example: "2025-08-25"
 *               timeSlots:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of time slots to block
 *                 example: ["08:00", "14:00"]
 *               reason:
 *                 type: string
 *                 description: Optional reason for blocking
 *                 example: "Boat maintenance"
 *     responses:
 *       200:
 *         description: Date/timeslots blocked successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   description: Created blocking records
 *                 message:
 *                   type: string
 *                   example: "Successfully blocked 2 timeslots on 2025-08-25"
 *       404:
 *         description: Service not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/:id/block-date', async (req, res) => {
  try {
    const { id: serviceId } = req.params;
    const { date, timeSlots, reason } = req.body; // timeSlots is array

    // For now, skip authentication check since middleware is not included
    // TODO: Add authenticateToken middleware when admin panel is ready

    // Validate service exists
    const service = await prisma.providerService.findUnique({
      where: { id: serviceId }
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found'
      });
    }

    const requestedDate = new Date(date);
    const blockingPromises = timeSlots.map(timeSlot =>
      prisma.serviceAvailability.upsert({
        where: {
          serviceId_date_timeSlot: {
            serviceId,
            date: requestedDate,
            timeSlot
          }
        },
        update: {
          isActive: false, // Block the slot
          updatedAt: new Date()
        },
        create: {
          serviceId,
          date: requestedDate,
          timeSlot,
          availableCapacity: 0,
          totalCapacity: 0,
          isActive: false // Block the slot
        }
      })
    );

    const results = await Promise.all(blockingPromises);

    res.json({
      success: true,
      data: results,
      message: `Successfully blocked ${timeSlots.length} timeslots on ${date}`
    });

  } catch (error) {
    console.error('Error blocking date:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to block date',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/services/{id}/unblock-date:
 *   post:
 *     summary: Unblock specific date/timeslots for a service
 *     description: Remove blocking from specific date and timeslots to make them available again
 *     tags: [Services]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Service ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - date
 *               - timeSlots
 *             properties:
 *               date:
 *                 type: string
 *                 format: date
 *                 description: Date to unblock (YYYY-MM-DD)
 *                 example: "2025-08-25"
 *               timeSlots:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of time slots to unblock
 *                 example: ["08:00", "14:00"]
 *     responses:
 *       200:
 *         description: Date/timeslots unblocked successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     deletedCount:
 *                       type: integer
 *                       description: Number of blocking records deleted
 *                 message:
 *                   type: string
 *                   example: "Successfully unblocked 2 timeslots on 2025-08-25"
 *       404:
 *         description: Service not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/:id/unblock-date', async (req, res) => {
  try {
    const { id: serviceId } = req.params;
    const { date, timeSlots } = req.body;

    // Validate service exists
    const service = await prisma.providerService.findUnique({
      where: { id: serviceId }
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found'
      });
    }

    const requestedDate = new Date(date);

    // Remove blocking records (delete them entirely)
    const deleteResult = await prisma.serviceAvailability.deleteMany({
      where: {
        serviceId,
        date: requestedDate,
        timeSlot: { in: timeSlots }
      }
    });

    res.json({
      success: true,
      data: { deletedCount: deleteResult.count },
      message: `Successfully unblocked ${deleteResult.count} timeslots on ${date}`
    });

  } catch (error) {
    console.error('Error unblocking date:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to unblock date',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/services/{id}/bulk-block:
 *   post:
 *     summary: Block multiple dates for a service
 *     description: Block multiple dates for a service (holidays, maintenance periods, etc.)
 *     tags: [Services]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Service ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - dates
 *             properties:
 *               dates:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: date
 *                 description: Array of dates to block (YYYY-MM-DD)
 *                 example: ["2025-12-25", "2025-01-01"]
 *               reason:
 *                 type: string
 *                 description: Optional reason for blocking
 *                 example: "Holiday closure"
 *     responses:
 *       200:
 *         description: Multiple dates blocked successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     blockedDates:
 *                       type: integer
 *                       description: Number of dates blocked
 *                     blockedSlots:
 *                       type: integer
 *                       description: Total number of timeslots blocked
 *                 message:
 *                   type: string
 *                   example: "Successfully blocked 2 dates with 6 total timeslots"
 *       400:
 *         description: Bad request (no active schedules found)
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Service not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/:id/bulk-block', async (req, res) => {
  try {
    const { id: serviceId } = req.params;
    const { dates, reason } = req.body; // dates is array of date strings

    // Validate service exists and get schedules
    const service = await prisma.providerService.findUnique({
      where: { id: serviceId },
      include: {
        serviceSchedules: { where: { isActive: true } }
      }
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found'
      });
    }

    // Get all timeslots from schedules
    const timeSlots = service.serviceSchedules.map(schedule => schedule.departureTime);

    if (timeSlots.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No active schedules found for this service'
      });
    }

    const blockingPromises = [];
    
    for (const date of dates) {
      const requestedDate = new Date(date);
      for (const timeSlot of timeSlots) {
        blockingPromises.push(
          prisma.serviceAvailability.upsert({
            where: {
              serviceId_date_timeSlot: { serviceId, date: requestedDate, timeSlot }
            },
            update: { 
              isActive: false, 
              updatedAt: new Date()
            },
            create: {
              serviceId,
              date: requestedDate,
              timeSlot,
              availableCapacity: 0,
              totalCapacity: 0,
              isActive: false
            }
          })
        );
      }
    }

    const results = await Promise.all(blockingPromises);

    res.json({
      success: true,
      data: { 
        blockedDates: dates.length,
        blockedSlots: results.length
      },
      message: `Successfully blocked ${dates.length} dates with ${results.length} total timeslots`
    });

  } catch (error) {
    console.error('Error bulk blocking dates:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to bulk block dates',
      error: error.message
    });
  }
});

module.exports = router;
