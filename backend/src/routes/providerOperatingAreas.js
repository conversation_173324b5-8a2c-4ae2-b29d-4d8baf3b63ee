const express = require('express');
const { PrismaClient } = require('@prisma/client');
const BusinessRulesService = require('../services/businessRulesService');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

/**
 * @route POST /api/provider-operating-areas
 * @desc Add a jetty to a provider's operating area
 * @access Private (Provider)
 */
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { providerId, jettyId } = req.body;

    if (!providerId || !jettyId) {
      return res.status(400).json({
        success: false,
        message: 'Provider ID and Jetty ID are required',
      });
    }

    // Check if user owns the provider
    const provider = await prisma.provider.findUnique({
      where: { id: providerId },
    });

    if (!provider || provider.userId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only manage operating areas for your own provider account',
      });
    }

    // Validate the operating area assignment
    const validation = await BusinessRulesService.validateProviderOperatingArea(
      providerId,
      jettyId
    );

    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        message: validation.error,
      });
    }

    // Create the operating area assignment
    const operatingArea = await prisma.providerOperatingArea.create({
      data: {
        providerId,
        jettyId,
      },
      include: {
        jetty: {
          include: {
            routes: {
              where: {
                isActive: true,
              },
              include: {
                destination: true,
              },
            },
          },
        },
        provider: true,
      },
    });

    res.status(201).json({
      success: true,
      data: operatingArea,
      message: 'Operating area added successfully',
    });
  } catch (error) {
    console.error('Error creating provider operating area:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add operating area',
      error: error.message,
    });
  }
});

/**
 * @route GET /api/provider-operating-areas/provider/:providerId
 * @desc Get all operating areas for a provider
 * @access Public
 */
router.get('/provider/:providerId', async (req, res) => {
  try {
    const { providerId } = req.params;

    const operatingAreas = await prisma.providerOperatingArea.findMany({
      where: {
        providerId: providerId,
        isActive: true,
      },
      include: {
        jetty: {
          include: {
            routes: {
              where: {
                isActive: true,
              },
              include: {
                destination: true,
              },
            },
          },
        },
      },
      orderBy: {
        jetty: {
          name: 'asc',
        },
      },
    });

    // Calculate available destinations from all operating jetties
    const availableDestinations = new Map();
    operatingAreas.forEach(area => {
      area.jetty.routes.forEach(route => {
        if (!availableDestinations.has(route.destination.id)) {
          availableDestinations.set(route.destination.id, {
            ...route.destination,
            accessibleFrom: [],
          });
        }
        availableDestinations.get(route.destination.id).accessibleFrom.push({
          jetty: {
            id: area.jetty.id,
            name: area.jetty.name,
            code: area.jetty.code,
          },
          route: {
            id: route.id,
            distance: route.distance,
            estimatedDuration: route.estimatedDuration,
          },
        });
      });
    });

    res.json({
      success: true,
      data: operatingAreas,
      count: operatingAreas.length,
      availableDestinations: Array.from(availableDestinations.values()),
    });
  } catch (error) {
    console.error('Error fetching provider operating areas:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch operating areas',
      error: error.message,
    });
  }
});

/**
 * @route GET /api/provider-operating-areas/jetty/:jettyId
 * @desc Get all providers operating from a specific jetty
 * @access Public
 */
router.get('/jetty/:jettyId', async (req, res) => {
  try {
    const { jettyId } = req.params;

    const operatingAreas = await prisma.providerOperatingArea.findMany({
      where: {
        jettyId: jettyId,
        isActive: true,
        provider: {
          isActive: true,
        },
      },
      include: {
        provider: {
          include: {
            user: {
              include: {
                profile: true,
              },
            },
            services: {
              where: {
                isActive: true,
              },
              include: {
                serviceType: {
                  include: {
                    category: true,
                  },
                },
              },
            },
            boats: {
              where: {
                isActive: true,
              },
              include: {
                photos: {
                  take: 1,
                },
              },
            },
          },
        },
        jetty: true,
      },
      orderBy: {
        provider: {
          rating: 'desc',
        },
      },
    });

    res.json({
      success: true,
      data: operatingAreas,
      count: operatingAreas.length,
      jetty: operatingAreas[0]?.jetty || null,
    });
  } catch (error) {
    console.error('Error fetching jetty operating areas:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch providers for jetty',
      error: error.message,
    });
  }
});

/**
 * @route PUT /api/provider-operating-areas/:id
 * @desc Update a provider operating area
 * @access Private (Provider)
 */
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;

    // Get the current operating area
    const currentOperatingArea = await prisma.providerOperatingArea.findUnique({
      where: { id },
      include: {
        provider: true,
      },
    });

    if (!currentOperatingArea) {
      return res.status(404).json({
        success: false,
        message: 'Operating area not found',
      });
    }

    // Check permissions
    if (currentOperatingArea.provider.userId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only modify your own provider operating areas',
      });
    }

    // Update the operating area
    const updatedOperatingArea = await prisma.providerOperatingArea.update({
      where: { id },
      data: {
        ...(isActive !== undefined && { isActive }),
        updatedAt: new Date(),
      },
      include: {
        jetty: true,
        provider: true,
      },
    });

    res.json({
      success: true,
      data: updatedOperatingArea,
      message: 'Operating area updated successfully',
    });
  } catch (error) {
    console.error('Error updating provider operating area:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update operating area',
      error: error.message,
    });
  }
});

/**
 * @route DELETE /api/provider-operating-areas/:id
 * @desc Remove a jetty from provider's operating area (soft delete)
 * @access Private (Provider)
 */
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Get the current operating area
    const operatingArea = await prisma.providerOperatingArea.findUnique({
      where: { id },
      include: {
        provider: true,
      },
    });

    if (!operatingArea) {
      return res.status(404).json({
        success: false,
        message: 'Operating area not found',
      });
    }

    // Check permissions
    if (operatingArea.provider.userId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only remove your own provider operating areas',
      });
    }

    // Soft delete the operating area
    await prisma.providerOperatingArea.update({
      where: { id },
      data: {
        isActive: false,
        updatedAt: new Date(),
      },
    });

    res.json({
      success: true,
      message: 'Operating area removed successfully',
    });
  } catch (error) {
    console.error('Error removing provider operating area:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove operating area',
      error: error.message,
    });
  }
});

/**
 * @route GET /api/provider-operating-areas/validate
 * @desc Validate if a provider can operate from a specific jetty
 * @access Public
 */
router.get('/validate', async (req, res) => {
  try {
    const { providerId, jettyId } = req.query;

    if (!providerId || !jettyId) {
      return res.status(400).json({
        success: false,
        message: 'Provider ID and Jetty ID are required',
      });
    }

    const validation = await BusinessRulesService.validateProviderOperatingArea(
      providerId,
      jettyId
    );

    res.json({
      success: true,
      valid: validation.valid,
      message: validation.error || 'Operating area assignment is valid',
    });
  } catch (error) {
    console.error('Error validating provider operating area:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to validate operating area',
      error: error.message,
    });
  }
});

module.exports = router;
