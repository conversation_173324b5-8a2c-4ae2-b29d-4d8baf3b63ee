const express = require('express');
const { PrismaClient } = require('@prisma/client');
const BusinessRulesService = require('../services/businessRulesService');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

/**
 * @swagger
 * /api/bookings:
 *   post:
 *     summary: Create a new booking
 *     description: Create a new booking for a service with passenger details and payment information
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - serviceId
 *               - bookingDate
 *               - timeSlot
 *               - passengers
 *             properties:
 *               serviceId:
 *                 type: string
 *                 description: ID of the service to book
 *               routeId:
 *                 type: string
 *                 description: ID of the route (for passenger transport)
 *               bookingDate:
 *                 type: string
 *                 format: date
 *                 description: Date of the booking
 *               timeSlot:
 *                 type: string
 *                 description: Time slot for the booking
 *               passengers:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     age:
 *                       type: integer
 *                     nationality:
 *                       type: string
 *               specialRequests:
 *                 type: string
 *                 description: Any special requests
 *               contactInfo:
 *                 type: object
 *                 properties:
 *                   phone:
 *                     type: string
 *                   email:
 *                     type: string
 *     responses:
 *       201:
 *         description: Booking created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 booking:
 *                   $ref: '#/components/schemas/Booking'
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/', authenticateToken, async (req, res) => {
  try {
    const {
      serviceId,
      routeId,
      bookingDate,
      bookingTime,
      passengers,
      contactInfo,
      discountCode,
      paymentOption = 'deposit', // 'deposit' or 'full'
      specialRequests,
      selectedPackage = 'standard', // Add package selection support
    } = req.body;

    // Validate required fields
    if (!serviceId || !bookingDate || !bookingTime || !passengers) {
      return res.status(400).json({
        success: false,
        message: 'Service ID, booking date, time, and passenger information are required',
      });
    }

    // Get service details with validation
    const service = await prisma.providerService.findUnique({
      where: { id: serviceId },
      include: {
        provider: {
          include: {
            user: {
              include: {
                profile: true,
              },
            },
          },
        },
        serviceType: {
          include: {
            category: true,
          },
        },
        serviceRoutes: {
          include: {
            route: {
              include: {
                departureJetty: true,
                destination: true,
              },
            },
          },
        },
        serviceAssignments: {
          where: {
            isActive: true,
          },
          include: {
            boat: {
              include: {
                photos: {
                  take: 1,
                },
              },
            },
          },
          orderBy: [
            { isPrimary: 'desc' },
            { assignedAt: 'asc' },
          ],
        },
      },
    });

    if (!service || !service.isActive) {
      return res.status(404).json({
        success: false,
        message: 'Service not found or not available',
      });
    }

    // Validate passenger transport route requirement
    if (service.serviceType.category.code === 'PASSENGER_TRANSPORT') {
      if (!routeId) {
        return res.status(400).json({
          success: false,
          message: 'Route selection is required for passenger transport services',
        });
      }

      // Validate route is available for this service
      const serviceRoute = service.serviceRoutes.find(sr => sr.routeId === routeId);
      if (!serviceRoute) {
        return res.status(400).json({
          success: false,
          message: 'Selected route is not available for this service',
        });
      }
    }

    // Calculate total passenger count
    const totalPassengers = Object.values(passengers).reduce((sum, count) => sum + (count || 0), 0);

    if (totalPassengers <= 0) {
      return res.status(400).json({
        success: false,
        message: 'At least one passenger is required',
      });
    }

    // Check available capacity
    const capacityInfo = await BusinessRulesService.calculateServiceCapacity(serviceId);
    
    if (totalPassengers > capacityInfo.capacity) {
      return res.status(400).json({
        success: false,
        message: `Insufficient capacity. Available: ${capacityInfo.capacity}, Requested: ${totalPassengers}`,
      });
    }

    // Assign boat using priority logic (primary boat first)
    const assignedBoat = service.serviceAssignments.find(assignment => assignment.isPrimary) 
      || service.serviceAssignments[0];

    if (!assignedBoat) {
      return res.status(400).json({
        success: false,
        message: 'No boats available for this service',
      });
    }

    // Calculate pricing with age-based rates
    const basePrice = parseFloat(service.basePrice);
    let totalAmount = 0;

    // Package pricing multiplier
    const packageMultiplier = selectedPackage === 'premium' ? 1.25 : 1; // 25% increase for premium

    // Apply age-based pricing if available
    if (service.agePricing) {
      const adultPrice = parseFloat(service.agePricing.adult || basePrice) * packageMultiplier;
      const childPrice = parseFloat(service.agePricing.child || (basePrice * 0.7)) * packageMultiplier;
      const toddlerPrice = parseFloat(service.agePricing.toddler || 0); // Toddlers always free

      totalAmount = (passengers.adults * adultPrice) +
                   (passengers.children * childPrice) +
                   (passengers.toddlers * toddlerPrice);
    } else {
      // Fallback to standard age-based pricing with package adjustment
      const adultPrice = basePrice * packageMultiplier;
      const childPrice = (basePrice * 0.7) * packageMultiplier; // 30% discount for children
      const toddlerPrice = 0; // Free for toddlers

      totalAmount = (passengers.adults * adultPrice) +
                   (passengers.children * childPrice) +
                   (passengers.toddlers * toddlerPrice);
    }

    // Apply discount if provided
    let discountAmount = 0;
    if (discountCode) {
      const discount = await prisma.discountCode.findFirst({
        where: {
          code: discountCode,
          isActive: true,
          validFrom: { lte: new Date() },
          validUntil: { gte: new Date() },
        },
      });

      if (discount) {
        if (discount.discountType === 'PERCENTAGE') {
          discountAmount = (totalAmount * discount.discountValue) / 100;
        } else {
          discountAmount = discount.discountValue;
        }
        discountAmount = Math.min(discountAmount, totalAmount);
      }
    }

    const finalAmount = totalAmount - discountAmount;
    const depositAmount = paymentOption === 'deposit' ? finalAmount * 0.3 : finalAmount;

    // Create the booking
    const booking = await prisma.booking.create({
      data: {
        customerId: req.user.id,
        serviceId: serviceId,
        routeId: routeId,
        assignedBoatId: assignedBoat.boatId,
        bookingDate: new Date(bookingDate),
        serviceTime: bookingTime,
        serviceType: service.serviceType.code,
        passengerCount: totalPassengers,
        passengerBreakdown: {
          ...passengers,
          selectedPackage: selectedPackage
        },
        totalAmount: finalAmount,
        discountCode: discountCode,
        discountAmount: discountAmount,
        paymentOption: paymentOption,
        depositAmount: depositAmount,
        contactInfo: contactInfo,
        specialRequests: specialRequests,
        status: 'PENDING_PAYMENT',
      },
      include: {
        customer: {
          include: {
            profile: true,
          },
        },
        service: {
          include: {
            provider: true,
            serviceType: {
              include: {
                category: true,
              },
            },
          },
        },
        route: {
          include: {
            departureJetty: true,
            destination: true,
          },
        },
        assignedBoat: {
          include: {
            photos: {
              take: 1,
            },
          },
        },
      },
    });

    res.status(201).json({
      success: true,
      data: booking,
      message: 'Booking created successfully',
      nextStep: 'payment',
      paymentAmount: depositAmount,
    });
  } catch (error) {
    console.error('Error creating booking:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create booking',
      error: error.message,
    });
  }
});

/**
 * @route GET /api/bookings/:id
 * @desc Get booking details
 * @access Private (Customer/Provider)
 */
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const booking = await prisma.booking.findUnique({
      where: { id },
      include: {
        customer: {
          include: {
            profile: true,
          },
        },
        service: {
          include: {
            provider: {
              include: {
                user: {
                  include: {
                    profile: true,
                  },
                },
              },
            },
            serviceType: {
              include: {
                category: true,
              },
            },
          },
        },
        route: {
          include: {
            departureJetty: true,
            destination: true,
          },
        },
        assignedBoat: {
          include: {
            photos: true,
            owner: {
              include: {
                profile: true,
              },
            },
          },
        },
      },
    });

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found',
      });
    }

    // Check permissions
    const isCustomer = booking.customerId === req.user.id;
    const isProvider = booking.service.provider.userId === req.user.id;
    const isBoatOwner = booking.assignedBoat.ownerId === req.user.id;

    if (!isCustomer && !isProvider && !isBoatOwner) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to view this booking',
      });
    }

    res.json({
      success: true,
      data: booking,
    });
  } catch (error) {
    console.error('Error fetching booking:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch booking',
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /api/bookings:
 *   get:
 *     summary: Get user bookings
 *     description: Retrieve bookings for the authenticated user (customer bookings or provider bookings)
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, CONFIRMED, CANCELLED, COMPLETED]
 *         description: Filter bookings by status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: Number of bookings per page
 *     responses:
 *       200:
 *         description: Bookings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 bookings:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Booking'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;
    const userId = req.user.id;

    // Calculate pagination
    const pageNum = Math.max(1, parseInt(page));
    const limitNum = Math.min(50, Math.max(1, parseInt(limit)));
    const skip = (pageNum - 1) * limitNum;

    // Build where clause
    let whereClause = {
      OR: [
        { customerId: userId }, // User's own bookings
        { service: { provider: { userId: userId } } }, // Provider's service bookings
        { assignedBoat: { ownerId: userId } }, // Boat owner's bookings
      ],
    };

    if (status) {
      whereClause.status = status;
    }

    const [bookings, totalCount] = await Promise.all([
      prisma.booking.findMany({
        where: whereClause,
        include: {
          customer: {
            include: {
              profile: true,
            },
          },
          service: {
            include: {
              provider: true,
              serviceType: {
                include: {
                  category: true,
                },
              },
            },
          },
          route: {
            include: {
              departureJetty: true,
              destination: true,
            },
          },
          assignedBoat: {
            include: {
              photos: {
                take: 1,
              },
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limitNum,
      }),
      prisma.booking.count({ where: whereClause }),
    ]);

    const totalPages = Math.ceil(totalCount / limitNum);

    res.json({
      success: true,
      data: bookings,
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalCount,
        hasNextPage: pageNum < totalPages,
        hasPrevPage: pageNum > 1,
        limit: limitNum,
      },
    });
  } catch (error) {
    console.error('Error fetching bookings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch bookings',
      error: error.message,
    });
  }
});

/**
 * @route PUT /api/bookings/:id
 * @desc Update booking status or details
 * @access Private (Customer/Provider)
 */
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { status, specialRequests, contactInfo } = req.body;

    // Get current booking
    const currentBooking = await prisma.booking.findUnique({
      where: { id },
      include: {
        service: {
          include: {
            provider: true,
          },
        },
        assignedBoat: true,
      },
    });

    if (!currentBooking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found',
      });
    }

    // Check permissions
    const isCustomer = currentBooking.customerId === req.user.id;
    const isProvider = currentBooking.service.provider.userId === req.user.id;
    const isBoatOwner = currentBooking.assignedBoat.ownerId === req.user.id;

    if (!isCustomer && !isProvider && !isBoatOwner) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to update this booking',
      });
    }

    // Validate status transitions
    const allowedTransitions = {
      PENDING_PAYMENT: ['CONFIRMED', 'CANCELLED'],
      CONFIRMED: ['COMPLETED', 'CANCELLED'],
      COMPLETED: [],
      CANCELLED: [],
    };

    if (status && !allowedTransitions[currentBooking.status]?.includes(status)) {
      return res.status(400).json({
        success: false,
        message: `Cannot change status from ${currentBooking.status} to ${status}`,
      });
    }

    // Update booking
    const updatedBooking = await prisma.booking.update({
      where: { id },
      data: {
        ...(status && { status }),
        ...(specialRequests && { specialRequests }),
        ...(contactInfo && { contactInfo }),
        updatedAt: new Date(),
      },
      include: {
        customer: {
          include: {
            profile: true,
          },
        },
        service: {
          include: {
            provider: true,
            serviceType: {
              include: {
                category: true,
              },
            },
          },
        },
        route: {
          include: {
            departureJetty: true,
            destination: true,
          },
        },
        assignedBoat: {
          include: {
            photos: {
              take: 1,
            },
          },
        },
      },
    });

    res.json({
      success: true,
      data: updatedBooking,
      message: 'Booking updated successfully',
    });
  } catch (error) {
    console.error('Error updating booking:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update booking',
      error: error.message,
    });
  }
});

/**
 * @route DELETE /api/bookings/:id
 * @desc Cancel a booking
 * @access Private (Customer)
 */
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    // Get current booking
    const booking = await prisma.booking.findUnique({
      where: { id },
      include: {
        service: {
          include: {
            provider: true,
          },
        },
      },
    });

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found',
      });
    }

    // Check permissions (only customer can cancel)
    if (booking.customerId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only cancel your own bookings',
      });
    }

    // Check if booking can be cancelled
    if (!['PENDING_PAYMENT', 'CONFIRMED'].includes(booking.status)) {
      return res.status(400).json({
        success: false,
        message: 'This booking cannot be cancelled',
      });
    }

    // Update booking status to cancelled
    const cancelledBooking = await prisma.booking.update({
      where: { id },
      data: {
        status: 'CANCELLED',
        cancellationReason: reason,
        cancelledAt: new Date(),
      },
    });

    res.json({
      success: true,
      data: cancelledBooking,
      message: 'Booking cancelled successfully',
    });
  } catch (error) {
    console.error('Error cancelling booking:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel booking',
      error: error.message,
    });
  }
});

/**
 * @route GET /api/bookings/available-capacity
 * @desc Check available capacity for a service on a specific date/time
 * @access Public
 */
router.get('/available-capacity', async (req, res) => {
  try {
    const { serviceId, date, time } = req.query;

    if (!serviceId || !date || !time) {
      return res.status(400).json({
        success: false,
        message: 'Service ID, date, and time are required',
      });
    }

    const requestedDate = new Date(date);

    // STEP 1: Check if timeslot is blocked
    const blockedSlot = await prisma.serviceAvailability.findFirst({
      where: {
        serviceId: serviceId,
        date: requestedDate,
        timeSlot: time,
        isActive: false // FALSE = BLOCKED
      }
    });

    if (blockedSlot) {
      return res.json({
        success: true,
        data: {
          totalCapacity: 0,
          bookedCapacity: 0,
          availableCapacity: 0,
          assignedBoats: [],
          existingBookings: 0,
          status: 'blocked',
          blockReason: 'Date/time is blocked'
        }
      });
    }

    // STEP 2: Get service capacity from assignments
    const capacityInfo = await BusinessRulesService.calculateServiceCapacity(serviceId);

    // STEP 3: Get existing bookings for this date/time
    const existingBookings = await prisma.booking.findMany({
      where: {
        serviceId: serviceId,
        serviceDate: requestedDate,
        serviceTime: time,
        status: { in: ['PENDING', 'CONFIRMED'] },
      },
    });

    // STEP 4: Calculate booked capacity from actual bookings
    const bookedCapacity = existingBookings.reduce((total, booking) => {
      return total + booking.passengerCount;
    }, 0);

    const availableCapacity = Math.max(0, capacityInfo.capacity - bookedCapacity);

    res.json({
      success: true,
      data: {
        totalCapacity: capacityInfo.capacity,
        bookedCapacity,
        availableCapacity,
        assignedBoats: capacityInfo.assignments,
        existingBookings: existingBookings.length,
        status: availableCapacity > 0 ? 'available' : 'full'
      },
    });

  } catch (error) {
    console.error('Error checking available capacity:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check available capacity',
      error: error.message,
    });
  }
});

/**
 * @route POST /api/bookings/validate-passenger-transport
 * @desc Validate passenger transport booking requirements
 * @access Public
 */
router.post('/validate-passenger-transport', async (req, res) => {
  try {
    const { serviceId, routeId, passengers, date, time } = req.body;

    if (!serviceId) {
      return res.status(400).json({
        success: false,
        message: 'Service ID is required',
      });
    }

    // Validate passenger transport routes
    const routeValidation = await BusinessRulesService.validatePassengerTransportRoutes(serviceId);

    if (!routeValidation.valid) {
      return res.status(400).json({
        success: false,
        message: routeValidation.error,
      });
    }

    // Get service details
    const service = await prisma.providerService.findUnique({
      where: { id: serviceId },
      include: {
        serviceType: {
          include: {
            category: true,
          },
        },
        serviceRoutes: {
          include: {
            route: {
              include: {
                departureJetty: true,
                destination: true,
              },
            },
          },
        },
      },
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found',
      });
    }

    // Validate route selection for passenger transport
    if (service.serviceType.category.code === 'PASSENGER_TRANSPORT') {
      if (!routeId) {
        return res.status(400).json({
          success: false,
          message: 'Route selection is required for passenger transport services',
          availableRoutes: service.serviceRoutes.map(sr => ({
            id: sr.route.id,
            departureJetty: sr.route.departureJetty.name,
            destination: sr.route.destination.name,
            distance: sr.route.distance,
            estimatedDuration: sr.route.estimatedDuration,
          })),
        });
      }

      // Validate selected route
      const selectedRoute = service.serviceRoutes.find(sr => sr.routeId === routeId);
      if (!selectedRoute) {
        return res.status(400).json({
          success: false,
          message: 'Selected route is not available for this service',
        });
      }
    }

    // Check capacity if date/time provided
    let capacityCheck = null;
    if (date && time && passengers) {
      const totalPassengers = Object.values(passengers).reduce((sum, count) => sum + (count || 0), 0);

      const capacityResponse = await fetch(`${req.protocol}://${req.get('host')}/api/bookings/available-capacity?serviceId=${serviceId}&date=${date}&time=${time}`);
      const capacityData = await capacityResponse.json();

      if (capacityData.success) {
        capacityCheck = {
          requested: totalPassengers,
          available: capacityData.data.availableCapacity,
          sufficient: totalPassengers <= capacityData.data.availableCapacity,
        };
      }
    }

    res.json({
      success: true,
      valid: true,
      message: 'Passenger transport booking validation passed',
      service: {
        id: service.id,
        name: service.name,
        category: service.serviceType.category.name,
        requiresRoute: service.serviceType.category.code === 'PASSENGER_TRANSPORT',
      },
      capacityCheck,
    });
  } catch (error) {
    console.error('Error validating passenger transport booking:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to validate booking',
      error: error.message,
    });
  }
});

module.exports = router;
