const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const PaymentService = require('../services/paymentService');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Payments
 *   description: Payment processing and management endpoints
 */

/**
 * @swagger
 * /api/payments/create-intent:
 *   post:
 *     summary: Create payment intent for full payment
 *     description: Creates a Stripe payment intent for full payment of a booking
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - bookingId
 *               - amount
 *             properties:
 *               bookingId:
 *                 type: string
 *                 description: Booking identifier
 *               amount:
 *                 type: number
 *                 format: decimal
 *                 description: Payment amount
 *               currency:
 *                 type: string
 *                 enum: [myr, usd]
 *                 default: myr
 *                 description: Payment currency
 *     responses:
 *       200:
 *         description: Payment intent created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 clientSecret:
 *                   type: string
 *                   description: Client secret for payment confirmation
 *                 paymentIntentId:
 *                   type: string
 *                   description: Payment intent identifier
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/create-intent', authenticateToken, async (req, res) => {
  try {
    const { bookingId, amount, currency = 'myr' } = req.body;
    const customerId = req.user.id;

    if (!bookingId || !amount) {
      return res.status(400).json({
        success: false,
        message: 'Booking ID and amount are required'
      });
    }

    const result = await PaymentService.createPaymentIntent({
      bookingId,
      amount: parseFloat(amount),
      currency,
      customerId
    });

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: result.error
      });
    }

    res.json({
      success: true,
      clientSecret: result.clientSecret,
      paymentIntentId: result.paymentIntentId
    });
  } catch (error) {
    console.error('Create payment intent error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create payment intent'
    });
  }
});

/**
 * @swagger
 * /api/payments/create-deposit-intent:
 *   post:
 *     summary: Create payment intent for deposit payment
 *     description: Creates a Stripe payment intent for 30% deposit payment of a booking
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - bookingId
 *               - totalAmount
 *             properties:
 *               bookingId:
 *                 type: string
 *                 description: Booking identifier
 *               totalAmount:
 *                 type: number
 *                 format: decimal
 *                 description: Total booking amount
 *               currency:
 *                 type: string
 *                 enum: [myr, usd]
 *                 default: myr
 *                 description: Payment currency
 *     responses:
 *       200:
 *         description: Deposit payment intent created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 clientSecret:
 *                   type: string
 *                   description: Client secret for payment confirmation
 *                 paymentIntentId:
 *                   type: string
 *                   description: Payment intent identifier
 *                 depositAmount:
 *                   type: number
 *                   description: Deposit amount (30% of total)
 *                 remainingAmount:
 *                   type: number
 *                   description: Remaining amount after deposit
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/create-deposit-intent', authenticateToken, async (req, res) => {
  try {
    const { bookingId, totalAmount, currency = 'myr' } = req.body;
    const customerId = req.user.id;

    if (!bookingId || !totalAmount) {
      return res.status(400).json({
        success: false,
        message: 'Booking ID and total amount are required'
      });
    }

    const result = await PaymentService.createDepositPaymentIntent({
      bookingId,
      totalAmount: parseFloat(totalAmount),
      currency,
      customerId
    });

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: result.error
      });
    }

    res.json({
      success: true,
      clientSecret: result.clientSecret,
      paymentIntentId: result.paymentIntentId,
      depositAmount: result.depositAmount,
      remainingAmount: result.remainingAmount
    });
  } catch (error) {
    console.error('Create deposit payment intent error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create deposit payment intent'
    });
  }
});

/**
 * @swagger
 * /api/payments/validate-promo:
 *   post:
 *     summary: Validate promo/discount code
 *     description: Validates a discount code and calculates the discount amount
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - code
 *               - totalAmount
 *             properties:
 *               code:
 *                 type: string
 *                 description: Discount/promo code
 *               totalAmount:
 *                 type: number
 *                 format: decimal
 *                 description: Total amount before discount
 *     responses:
 *       200:
 *         description: Promo code validated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 discountAmount:
 *                   type: number
 *                   description: Discount amount
 *                 finalAmount:
 *                   type: number
 *                   description: Final amount after discount
 *                 discountCode:
 *                   $ref: '#/components/schemas/PromoCode'
 *       400:
 *         description: Invalid promo code or request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/validate-promo', authenticateToken, async (req, res) => {
  try {
    const { code, totalAmount } = req.body;

    if (!code || !totalAmount) {
      return res.status(400).json({
        success: false,
        message: 'Discount code and total amount are required'
      });
    }

    const result = await PaymentService.validateDiscountCode(
      code,
      parseFloat(totalAmount)
    );

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: result.error
      });
    }

    res.json({
      success: true,
      discountAmount: result.discountAmount,
      finalAmount: result.finalAmount,
      discountCode: {
        code: result.discountCode.code,
        discountType: result.discountCode.discountType,
        discountValue: result.discountCode.discountValue
      }
    });
  } catch (error) {
    console.error('Validate promo code error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to validate promo code'
    });
  }
});

/**
 * @swagger
 * /api/payments/confirm:
 *   post:
 *     summary: Confirm payment
 *     description: Confirms a successful payment and updates booking status
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - paymentIntentId
 *               - bookingId
 *               - amount
 *             properties:
 *               paymentIntentId:
 *                 type: string
 *                 description: Payment intent identifier from Stripe
 *               bookingId:
 *                 type: string
 *                 description: Booking identifier
 *               amount:
 *                 type: number
 *                 format: decimal
 *                 description: Payment amount
 *               currency:
 *                 type: string
 *                 enum: [MYR, USD]
 *                 default: MYR
 *                 description: Payment currency
 *               type:
 *                 type: string
 *                 default: boat_booking
 *                 description: Payment type
 *     responses:
 *       200:
 *         description: Payment confirmed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 payment:
 *                   $ref: '#/components/schemas/Payment'
 *                 booking:
 *                   $ref: '#/components/schemas/Booking'
 *                 message:
 *                   type: string
 *                   example: Payment confirmed and processed successfully
 *       400:
 *         description: Invalid request data or payment failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/confirm', authenticateToken, async (req, res) => {
  try {
    const { paymentIntentId, bookingId, amount, currency, type } = req.body;
    const customerId = req.user.id;

    if (!paymentIntentId || !bookingId || !amount) {
      return res.status(400).json({
        success: false,
        message: 'Payment intent ID, booking ID, and amount are required'
      });
    }

    // For dummy implementation, we pass the payment data directly
    const paymentData = {
      bookingId,
      customerId,
      amount: parseFloat(amount),
      currency: currency || 'MYR',
      type: type || 'boat_booking'
    };

    const result = await PaymentService.confirmPayment(paymentIntentId, paymentData);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: result.error
      });
    }

    res.json({
      success: true,
      payment: result.payment,
      booking: result.booking,
      message: 'Payment confirmed and processed successfully'
    });
  } catch (error) {
    console.error('Confirm payment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to confirm payment'
    });
  }
});

/**
 * @swagger
 * /api/payments/{id}/receipt:
 *   get:
 *     summary: Generate and download payment receipt
 *     description: Generates a PDF receipt for a payment
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Payment identifier
 *     responses:
 *       200:
 *         description: Receipt generated successfully
 *         content:
 *           application/pdf:
 *             schema:
 *               type: string
 *               format: binary
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 receiptUrl:
 *                   type: string
 *                   format: uri
 *                   description: URL to download the receipt
 *       404:
 *         description: Payment not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/:id/receipt', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await PaymentService.generateReceipt(id);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        message: result.error
      });
    }

    // For now, return receipt data as JSON
    // TODO: Generate actual PDF and return as download
    res.json({
      success: true,
      receipt: result.receiptData
    });
  } catch (error) {
    console.error('Generate receipt error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate receipt'
    });
  }
});

/**
 * POST /api/payments/webhook
 * Stripe webhook endpoint for payment events
 */
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  const sig = req.headers['stripe-signature'];
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Handle the event
  switch (event.type) {
    case 'payment_intent.succeeded':
      const paymentIntent = event.data.object;
      console.log('Payment succeeded:', paymentIntent.id);
      
      // Process the successful payment
      await PaymentService.processSuccessfulPayment(paymentIntent.id);
      break;

    case 'payment_intent.payment_failed':
      const failedPayment = event.data.object;
      console.log('Payment failed:', failedPayment.id);
      
      // Handle failed payment
      // TODO: Update booking status, send notification
      break;

    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  res.json({ received: true });
});

module.exports = router;
