const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createOverflowTestService() {
  try {
    console.log('🎯 Creating service with many included items for overflow testing...');

    // Get a provider to assign service to
    const provider = await prisma.provider.findFirst({
      where: { isActive: true }
    });

    if (!provider) {
      throw new Error('No active provider found. Please seed providers first.');
    }

    // Get a service type
    const serviceType = await prisma.serviceType.findFirst({
      where: { isActive: true }
    });

    if (!serviceType) {
      throw new Error('No active service type found. Please seed service types first.');
    }

    // Create service with many included items
    const service = await prisma.providerService.create({
      data: {
        id: 'ps_overflow_test',
        providerId: provider.id,
        serviceTypeId: serviceType.id,
        name: 'Luxury All-Inclusive Experience',
        description: 'Premium service with extensive inclusions to test overflow UI',
        basePrice: 200.00,
        maxCapacity: 10,
        includedItems: [
          'Professional snorkeling equipment',
          'Wetsuit',
          'Marine guide',
          'Underwater photos',
          'Premium refreshments',
          'Gourmet lunch',
          'Towels and changing facilities',
          'Complimentary transport',
          'Professional videography',
          'Souvenir package',
          'Insurance coverage',
          'Equipment sanitization'
        ],
        isActive: true
      }
    });

    // Create packages with many included items
    const packages = [
      {
        packageTypeId: 'pkg_standard',
        basePrice: 200.00,
        priceModifier: 1.0,
        includedItems: [
          'Professional snorkeling equipment',
          'Wetsuit',
          'Marine guide',
          'Underwater photos',
          'Premium refreshments',
          'Gourmet lunch',
          'Towels and changing facilities'
        ]
      },
      {
        packageTypeId: 'pkg_premium',
        basePrice: 250.00,
        priceModifier: 1.25,
        includedItems: [
          'Professional snorkeling equipment',
          'Wetsuit',
          'Marine guide',
          'Underwater photos',
          'Premium refreshments',
          'Gourmet lunch',
          'Towels and changing facilities',
          'Complimentary transport',
          'Professional videography',
          'Souvenir package'
        ]
      },
      {
        packageTypeId: 'pkg_luxury',
        basePrice: 300.00,
        priceModifier: 1.5,
        includedItems: [
          'Professional snorkeling equipment',
          'Wetsuit',
          'Marine guide',
          'Underwater photos',
          'Premium refreshments',
          'Gourmet lunch',
          'Towels and changing facilities',
          'Complimentary transport',
          'Professional videography',
          'Souvenir package',
          'Insurance coverage',
          'Equipment sanitization',
          'Private boat access',
          'Exclusive reef locations',
          'Personal photographer'
        ]
      }
    ];

    for (const pkg of packages) {
      await prisma.servicePackage.create({
        data: {
          id: `sp_${service.id}_${pkg.packageTypeId.split('_')[1]}`,
          serviceId: service.id,
          packageTypeId: pkg.packageTypeId,
          basePrice: pkg.basePrice,
          agePricing: null,
          priceModifier: pkg.priceModifier,
          includedItems: pkg.includedItems,
          excludedItems: [],
          isActive: true
        }
      });
    }

    console.log(`✅ Created service: ${service.name} (ID: ${service.id})`);
    console.log(`📦 Standard package: ${packages[0].includedItems.length} items`);
    console.log(`📦 Premium package: ${packages[1].includedItems.length} items`);
    console.log(`📦 Luxury package: ${packages[2].includedItems.length} items (should show overflow)`);

  } catch (error) {
    console.error('❌ Error creating overflow test service:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

createOverflowTestService();
