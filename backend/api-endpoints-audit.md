# GoSea API Endpoints Audit

## Complete Endpoint Catalog

### Core System Routes
1. `GET /api/health` - Health check endpoint
2. `GET /api/` - API information and available endpoints
3. `GET /` - Root route with API overview

### Authentication Routes (`/api/auth`)
1. `GET /api/auth/` - API info and available endpoints
2. `POST /api/auth/register` - User registration
3. `POST /api/auth/login` - User login
4. `POST /api/auth/logout` - User logout
5. `POST /api/auth/refresh` - Refresh access token
6. `POST /api/auth/password-reset/request` - Request password reset
7. `POST /api/auth/password-reset/confirm` - Confirm password reset
8. `GET /api/auth/me` - Get current user info
9. `PUT /api/auth/profile` - Update user profile
10. `GET /api/auth/sessions` - Get user sessions
11. `DELETE /api/auth/sessions/:sessionToken` - Revoke specific session
12. `DELETE /api/auth/sessions` - Revoke all sessions except current
13. `POST /api/auth/resend-verification` - Resend email verification
14. `POST /api/auth/verify-email` - Verify email with token
15. `GET /api/auth/google` - Initiate Google OAuth
16. `GET /api/auth/google/callback` - Google OAuth callback
17. `POST /api/auth/google/link` - Link Google account

### Boats Routes (`/api/boats`)
1. `GET /api/boats/search` - Search boats with filters
2. `GET /api/boats/:id` - Get specific boat details
3. `GET /api/boats/` - Get featured/popular boats

### Services Routes (`/api/services`)
1. `GET /api/services/:id` - Get specific service details
2. `GET /api/services/:id/age-ranges` - Get age ranges for service pricing
3. `GET /api/services/:id/timeslots` - Get available timeslots for service on specific date
4. `GET /api/services/categories` - Get service categories
5. `GET /api/services/types` - Get service types
6. `GET /api/services/:id/availability` - Get service availability
7. `GET /api/services/search` - Search services
8. `GET /api/services/config/age-categories` - Get age categories configuration

### Bookings Routes (`/api/bookings`)
1. `POST /api/bookings` - Create new booking
2. `GET /api/bookings/:id` - Get booking details
3. `GET /api/bookings` - Get user bookings
4. `PUT /api/bookings/:id` - Update booking
5. `DELETE /api/bookings/:id` - Cancel booking
6. `GET /api/bookings/available-capacity` - Check available capacity
7. `POST /api/bookings/validate-passenger-transport` - Validate transport booking

### Payments Routes (`/api/payments`)
1. `POST /api/payments/create-intent` - Create payment intent
2. `POST /api/payments/create-deposit-intent` - Create deposit payment intent
3. `POST /api/payments/validate-promo` - Validate promo code
4. `POST /api/payments/confirm` - Confirm payment
5. `GET /api/payments/:id/receipt` - Generate PDF receipt
6. `POST /api/payments/webhook` - Stripe webhook

### Jetties Routes (`/api/jetties`)
1. `GET /api/jetties` - Get all jetties
2. `GET /api/jetties/:id` - Get specific jetty
3. `GET /api/jetties/:id/destinations` - Get jetty destinations

### Providers Routes (`/api/providers`)
1. `GET /api/providers` - Get all providers
2. `GET /api/providers/:id` - Get specific provider
3. `GET /api/providers/search` - Search providers

### Search Routes (`/api/search`)
1. `GET /api/search/service-categories` - Get service categories for search
2. `GET /api/search/jetties` - Get jetties for search
3. `GET /api/search/destinations` - Get destinations for search
4. `GET /api/search/routes` - Get routes between jetty and destination
5. `GET /api/search/providers` - Search providers with filters

### Service Assignments Routes (`/api/service-assignments`)
1. `POST /api/service-assignments` - Assign boat to service
2. `GET /api/service-assignments/service/:serviceId` - Get boat assignments for service
3. `GET /api/service-assignments/boat/:boatId` - Get service assignments for boat
4. `PUT /api/service-assignments/:id` - Update service assignment
5. `DELETE /api/service-assignments/:id` - Remove boat from service

### Provider Operating Areas Routes (`/api/provider-operating-areas`)
1. `POST /api/provider-operating-areas` - Add jetty to provider operating area
2. `GET /api/provider-operating-areas/provider/:providerId` - Get provider operating areas
3. `GET /api/provider-operating-areas/jetty/:jettyId` - Get providers for jetty
4. `PUT /api/provider-operating-areas/:id` - Update operating area
5. `DELETE /api/provider-operating-areas/:id` - Remove operating area
6. `GET /api/provider-operating-areas/validate` - Validate provider-jetty operation

## Frontend API Usage Analysis

### Homepage (`/frontend/src/pages/index.js`)
- `GET /api/providers` - Fetch top providers for homepage display

### Boats/Search Page (`/frontend/src/pages/boats.js`)
- `GET /api/search/providers` - Search providers with filters

### Booking Page (`/frontend/src/pages/booking.js`)
- `GET /api/providers/:id` - Get provider details
- `GET /api/services/:id` - Get service details
- `GET /api/services/:id/age-ranges` - Get age ranges for pricing
- `GET /api/services/:id/timeslots` - Get available timeslots
- `POST /api/bookings` - Create new booking

### Service Details Page (`/frontend/src/pages/services/[id].js`)
- `GET /api/services/:id` - Get service details

### Provider Details Page (`/frontend/src/pages/providers/[id].js`)
- `GET /api/providers/:id` - Get provider details

### Search Components
- `GET /api/search/jetties` - JettySelector component
- `GET /api/search/destinations` - DestinationSelector component
- `GET /api/search/service-categories` - ServiceCategorySelector component
- `GET /api/search/routes` - RouteSelector component

### Authentication
- `POST /api/auth/register` - User registration (SignUpModal)

## Issues Found

### 1. Missing Documentation
Most endpoints lack proper Swagger documentation. Only a few have complete OpenAPI specs.

### 2. Inconsistent Response Schemas
Different endpoints return different response formats without standardized schemas.

### 3. Missing Schemas in Swagger
Current Swagger config only has basic schemas (User, Boat, Booking, etc.) but missing:
- Service schemas
- Provider schemas
- Jetty schemas
- Age range schemas
- Timeslot schemas

### 4. Undocumented Endpoints
Several endpoints used by frontend are not documented:
- `GET /api/services/:id/age-ranges`
- `GET /api/services/:id/timeslots`
- `GET /api/services/config/age-categories`

## Recommended Improvements

### 1. Complete Swagger Documentation
Add comprehensive OpenAPI documentation for all endpoints including:
- Request/response schemas
- Parameter descriptions
- Error response codes
- Example requests and responses

### 2. Standardize Response Format
Implement consistent response format across all endpoints:
```json
{
  "success": boolean,
  "data": object|array,
  "message": string,
  "pagination": object (for paginated responses),
  "filters": object (for search endpoints)
}
```

### 3. Add Missing Schemas
Create Swagger schemas for:
- Service (with packages, routes, age ranges)
- Provider (with services, boats, operating areas)
- Jetty (with routes, destinations)
- Destination
- Route
- AgeRange
- Timeslot
- SearchFilters

### 4. API Versioning
Consider implementing API versioning for future compatibility:
- `/api/v1/providers`
- `/api/v1/services`

### 5. Rate Limiting Documentation
Document rate limiting policies for public endpoints.

### 6. Authentication Documentation
Provide clear documentation for:
- JWT token usage
- OAuth flow
- Required headers
- Token refresh process
- Payment schemas
- Search result schemas
- Package schemas (newly added)

## Total Endpoints: 51 (after removing duplicate)

## Actions Taken

### 1. Fixed Duplicate Route Issue
- Removed duplicate `GET /api/services/:id` route in services.js
- Kept the first implementation which includes the new pricing packages functionality

### 2. Enhanced Swagger Documentation
- Added comprehensive schemas for:
  - Service (with packages and itinerary)
  - ServicePackage
  - PackageType
  - Provider
  - ServiceType
  - ServiceCategory
  - Jetty
  - Payment
  - ItineraryItem
- Added detailed OpenAPI documentation for key endpoints:
  - `GET /api/services/{id}` - Complete with request/response schemas
  - `GET /api/services/categories` - Service categories endpoint
  - `POST /api/bookings` - Booking creation with full request schema

### 3. Swagger Configuration Improvements
- Updated swagger.js with comprehensive schemas
- All new schemas include proper descriptions and data types
- Added proper enum values where applicable
- Included nested object relationships

### 4. Documentation Access
- Swagger documentation available at: http://localhost:5001/api/docs
- All endpoints now have proper categorization with tags
- Request/response examples included

## Recommendations for Future Improvements

1. **Complete Documentation Coverage**: Add Swagger documentation for remaining 48 endpoints
2. **Response Standardization**: Implement consistent response format across all endpoints
3. **Error Code Standardization**: Define standard error codes and messages
4. **API Versioning**: Consider implementing API versioning strategy
5. **Rate Limiting Documentation**: Document rate limiting policies in Swagger
6. **Authentication Documentation**: Add detailed authentication flow documentation

## Verification
- ✅ Duplicate route removed
- ✅ Swagger documentation accessible
- ✅ New schemas properly defined
- ✅ Key endpoints documented with examples
- ✅ No syntax errors in route files
