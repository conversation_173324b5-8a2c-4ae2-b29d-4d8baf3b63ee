const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function migrateSchema() {
  console.log('🚀 Starting schema optimization migration...');
  
  try {
    // Phase 1: Add new columns to Provider table
    console.log('📝 Phase 1: Adding new columns to Provider table...');
    
    await prisma.$executeRaw`
      ALTER TABLE providers 
      ADD COLUMN IF NOT EXISTS "agencyName" TEXT,
      ADD COLUMN IF NOT EXISTS "licenseExpiryDate" TIMESTAMP(3),
      ADD COLUMN IF NOT EXISTS "certifications" JSONB,
      ADD COLUMN IF NOT EXISTS "businessPhone" TEXT,
      ADD COLUMN IF NOT EXISTS "businessEmail" TEXT,
      ADD COLUMN IF NOT EXISTS "websiteUrl" TEXT,
      ADD COLUMN IF NOT EXISTS "businessAddress1" TEXT,
      ADD COLUMN IF NOT EXISTS "businessAddress2" TEXT,
      ADD COLUMN IF NOT EXISTS "businessCity" TEXT,
      ADD COLUMN IF NOT EXISTS "businessPostcode" TEXT,
      ADD COLUMN IF NOT EXISTS "businessState" TEXT,
      ADD COLUMN IF NOT EXISTS "brandColors" JSONB,
      ADD COLUMN IF NOT EXISTS "totalBookings" INTEGER DEFAULT 0,
      ADD COLUMN IF NOT EXISTS "verifiedAt" TIMESTAMP(3),
      ADD COLUMN IF NOT EXISTS "verifiedBy" TEXT
    `;

    // Make existing required fields nullable
    await prisma.$executeRaw`ALTER TABLE providers ALTER COLUMN "companyName" DROP NOT NULL`;
    await prisma.$executeRaw`ALTER TABLE providers ALTER COLUMN "contactPhone" DROP NOT NULL`;
    await prisma.$executeRaw`ALTER TABLE providers ALTER COLUMN "contactEmail" DROP NOT NULL`;

    console.log('✅ Phase 1 completed: New columns added to Provider table');

    // Phase 2: Migrate business data from Profile to Provider
    console.log('📝 Phase 2: Migrating business data from Profile to Provider...');
    
    const migrationResult = await prisma.$executeRaw`
      UPDATE providers 
      SET 
        "agencyName" = profiles."agencyName",
        "businessPhone" = profiles.phone,
        "businessAddress1" = profiles.address1,
        "businessAddress2" = profiles.address2,
        "businessCity" = profiles.city,
        "businessPostcode" = profiles.postcode,
        "businessState" = profiles.state
      FROM profiles 
      WHERE providers."userId" = profiles."userId"
    `;

    console.log(`✅ Phase 2 completed: Migrated business data for ${migrationResult} records`);

    // Phase 3: Add new personal address columns to Profile
    console.log('📝 Phase 3: Adding personal address columns to Profile...');
    
    await prisma.$executeRaw`
      ALTER TABLE profiles 
      ADD COLUMN IF NOT EXISTS "personalAddress1" TEXT,
      ADD COLUMN IF NOT EXISTS "personalAddress2" TEXT,
      ADD COLUMN IF NOT EXISTS "personalCity" TEXT,
      ADD COLUMN IF NOT EXISTS "personalPostcode" TEXT,
      ADD COLUMN IF NOT EXISTS "personalState" TEXT,
      ADD COLUMN IF NOT EXISTS "timezone" TEXT DEFAULT 'Asia/Kuala_Lumpur'
    `;

    console.log('✅ Phase 3 completed: Personal address columns added to Profile');

    // Phase 4: Remove business columns from Profile
    console.log('📝 Phase 4: Removing business columns from Profile...');
    
    await prisma.$executeRaw`
      ALTER TABLE profiles 
      DROP COLUMN IF EXISTS "companyName",
      DROP COLUMN IF EXISTS "brn",
      DROP COLUMN IF EXISTS "agencyName"
    `;

    await prisma.$executeRaw`
      ALTER TABLE profiles 
      DROP COLUMN IF EXISTS "address1",
      DROP COLUMN IF EXISTS "address2",
      DROP COLUMN IF EXISTS "city",
      DROP COLUMN IF EXISTS "postcode",
      DROP COLUMN IF EXISTS "state"
    `;

    console.log('✅ Phase 4 completed: Business columns removed from Profile');

    // Phase 5: Clean up Provider table
    console.log('📝 Phase 5: Cleaning up Provider table...');
    
    await prisma.$executeRaw`
      ALTER TABLE providers 
      DROP COLUMN IF EXISTS "contactPhone",
      DROP COLUMN IF EXISTS "contactEmail"
    `;

    console.log('✅ Phase 5 completed: Old contact columns removed from Provider');

    // Phase 6: Add performance indexes
    console.log('📝 Phase 6: Adding performance indexes...');
    
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "idx_providers_company_name" ON providers("companyName") WHERE "companyName" IS NOT NULL`;
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "idx_providers_brn" ON providers("brn") WHERE "brn" IS NOT NULL`;
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "idx_providers_verified_active" ON providers("isVerified", "isActive")`;
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "idx_users_role_active" ON users("role", "isActive", "isApproved")`;
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "idx_profiles_completion" ON profiles("profileCompletedAt") WHERE "profileCompletedAt" IS NOT NULL`;

    console.log('✅ Phase 6 completed: Performance indexes added');

    // Phase 7: Data validation
    console.log('📝 Phase 7: Validating migration...');
    
    // Check boat owners have Provider records
    const boatOwnersWithoutProvider = await prisma.$queryRaw`
      SELECT u.email, u.role 
      FROM users u 
      LEFT JOIN providers pr ON u.id = pr."userId" 
      WHERE u.role = 'BOAT_OWNER' AND pr.id IS NULL
    `;

    if (boatOwnersWithoutProvider.length > 0) {
      console.warn('⚠️  Found boat owners without Provider records:', boatOwnersWithoutProvider);
    } else {
      console.log('✅ All boat owners have Provider records');
    }

    // Check Provider business data
    const providersWithBusinessData = await prisma.$queryRaw`
      SELECT p."companyName", p."brn", p."agencyName", p."businessPhone", p."businessAddress1"
      FROM providers p
      JOIN users u ON p."userId" = u.id
      WHERE u.role = 'BOAT_OWNER'
      ORDER BY p."createdAt"
    `;

    console.log('📊 Provider business data after migration:');
    console.table(providersWithBusinessData);

    console.log('🎉 Schema optimization migration completed successfully!');
    
    return {
      success: true,
      migratedRecords: migrationResult,
      boatOwnersWithoutProvider: boatOwnersWithoutProvider.length,
      providersWithBusinessData: providersWithBusinessData.length
    };

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateSchema()
    .then((result) => {
      console.log('Migration result:', result);
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration error:', error);
      process.exit(1);
    });
}

module.exports = { migrateSchema };
