const { PrismaClient } = require('@prisma/client');
const BusinessRulesService = require('./src/services/businessRulesService');

const prisma = new PrismaClient();

async function testCapacityManagement() {
    try {
        console.log('🧪 Testing New Capacity Management System\n');

        // Test 1: Validate capacity override rules
        console.log('1️⃣ Testing Capacity Override Validation Rules');
        
        // Find a test boat and service
        const testBoat = await prisma.boat.findFirst({
            where: { isActive: true }
        });
        
        const testService = await prisma.providerService.findFirst({
            where: { isActive: true }
        });
        
        if (!testBoat || !testService) {
            console.log('❌ No test boats or services found. Please run seed data first.');
            return;
        }
        
        console.log(`   Using test boat: ${testBoat.name} (capacity: ${testBoat.capacity})`);
        console.log(`   Using test service: ${testService.name}`);
        
        // Test valid override (within boat capacity)
        const validOverride = await BusinessRulesService.validateServiceAssignment(
            testBoat.id, 
            testService.id, 
            false, 
            testBoat.capacity - 2 // 2 less than max capacity
        );
        
        if (validOverride.valid) {
            console.log('   ✅ Valid override (within capacity): PASSED');
        } else {
            console.log(`   ❌ Valid override test failed: ${validOverride.error}`);
        }
        
        // Test invalid override (exceeds boat capacity)
        const invalidOverride = await BusinessRulesService.validateServiceAssignment(
            testBoat.id, 
            testService.id, 
            false, 
            testBoat.capacity + 5 // 5 more than max capacity
        );
        
        if (!invalidOverride.valid && invalidOverride.error.includes('cannot exceed boat physical capacity')) {
            console.log('   ✅ Invalid override (exceeds capacity): PASSED');
        } else {
            console.log(`   ❌ Invalid override test failed: Expected error, got: ${invalidOverride.error}`);
        }
        
        // Test invalid override (negative/zero)
        const negativeOverride = await BusinessRulesService.validateServiceAssignment(
            testBoat.id, 
            testService.id, 
            false, 
            0 // Zero capacity
        );
        
        if (!negativeOverride.valid && negativeOverride.error.includes('must be a positive number')) {
            console.log('   ✅ Zero/negative override validation: PASSED');
        } else {
            console.log(`   ❌ Zero/negative override test failed: ${negativeOverride.error}`);
        }

        // Test 2: Calculate service capacity with existing assignments
        console.log('\n2️⃣ Testing Service Capacity Calculation');
        
        // Find services with boat assignments
        const servicesWithBoats = await prisma.providerService.findMany({
            where: {
                serviceAssignments: {
                    some: { isActive: true }
                }
            },
            include: {
                serviceAssignments: {
                    where: { isActive: true },
                    include: { boat: true }
                }
            },
            take: 3
        });
        
        for (const service of servicesWithBoats) {
            console.log(`   Testing service: ${service.name}`);
            
            const capacityInfo = await BusinessRulesService.calculateServiceCapacity(service.id);
            
            console.log(`   Total calculated capacity: ${capacityInfo.capacity}`);
            console.log('   Boat breakdown:');
            
            let expectedTotal = 0;
            capacityInfo.assignments.forEach(assignment => {
                const effectiveCapacity = assignment.capacity;
                expectedTotal += effectiveCapacity;
                
                console.log(`     - ${assignment.boatName}: ${effectiveCapacity} seats ${assignment.override ? '(overridden)' : '(physical)'} from boat capacity ${assignment.boat?.capacity || 'unknown'}`);
            });
            
            if (capacityInfo.capacity === expectedTotal) {
                console.log('   ✅ Capacity calculation: PASSED');
            } else {
                console.log(`   ❌ Capacity calculation mismatch: Expected ${expectedTotal}, got ${capacityInfo.capacity}`);
            }
            console.log('');
        }

        // Test 3: Test validateCapacityOverride method with existing assignment
        console.log('3️⃣ Testing Capacity Override Validation for Updates');
        
        const existingAssignment = await prisma.serviceAssignment.findFirst({
            where: { isActive: true },
            include: { boat: true }
        });
        
        if (existingAssignment) {
            console.log(`   Testing assignment: ${existingAssignment.boat.name}`);
            
            // Test valid override update
            const validUpdate = await BusinessRulesService.validateCapacityOverride(
                existingAssignment.id,
                existingAssignment.boat.capacity - 1
            );
            
            if (validUpdate.valid) {
                console.log('   ✅ Valid capacity override update: PASSED');
            } else {
                console.log(`   ❌ Valid update test failed: ${validUpdate.error}`);
            }
            
            // Test invalid override update
            const invalidUpdate = await BusinessRulesService.validateCapacityOverride(
                existingAssignment.id,
                existingAssignment.boat.capacity + 10
            );
            
            if (!invalidUpdate.valid && invalidUpdate.error.includes('cannot exceed boat physical capacity')) {
                console.log('   ✅ Invalid capacity override update: PASSED');
            } else {
                console.log(`   ❌ Invalid update test failed: ${invalidUpdate.error}`);
            }
        }

        console.log('\n✅ Capacity Management Tests Completed!');
        console.log('\n📊 Key Features Implemented:');
        console.log('   • Capacity override validation (cannot exceed boat physical capacity)');
        console.log('   • Positive number validation for overrides');
        console.log('   • Multiple boats per service with individual overrides');
        console.log('   • Automatic capacity calculation from assignments');
        console.log('   • Updated validation methods for create and update operations');

    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        await prisma.$disconnect();
    }
}

testCapacityManagement();