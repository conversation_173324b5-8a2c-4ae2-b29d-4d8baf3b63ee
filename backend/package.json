{"name": "gosea-backend", "version": "0.1.0", "description": "GoSea Platform Backend - Express.js API", "main": "src/index.js", "scripts": {"dev": "nodemon src/index.js", "start": "node src/index.js", "build": "echo 'No build step required for development'", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=unit", "lint": "eslint src --ext .js,.ts", "lint:fix": "eslint src --ext .js,.ts --fix", "format": "prettier --write src", "format:check": "prettier --check src", "db:migrate": "prisma migrate dev", "db:migrate:reset": "prisma migrate reset", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:seed": "node prisma/seed_production.js", "db:deploy": "prisma migrate deploy", "clean": "rimraf dist coverage .nyc_output"}, "dependencies": {"@prisma/client": "^5.5.0", "bcryptjs": "^2.4.3", "bull": "^4.12.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.0", "express-session": "^1.18.1", "express-validator": "^7.0.1", "express-winston": "^4.2.0", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "redis": "^4.6.10", "sharp": "^0.32.6", "stripe": "^18.3.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.0", "@types/cors": "^2.8.0", "@types/express": "^4.17.0", "@types/jsonwebtoken": "^9.0.0", "@types/lodash": "^4.14.0", "@types/multer": "^1.4.0", "@types/node": "^20.8.0", "@types/nodemailer": "^6.4.0", "@types/passport": "^1.0.0", "@types/passport-google-oauth20": "^2.0.0", "@types/passport-jwt": "^3.0.0", "@types/uuid": "^9.0.0", "eslint": "^8.51.0", "eslint-config-node": "^4.1.0", "eslint-plugin-node": "^11.1.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "prettier": "^3.0.0", "prisma": "^5.5.0", "rimraf": "^5.0.0", "supertest": "^6.3.3"}, "prisma": {"seed": "node prisma/seed_production.js"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.10.0"}