const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function addTestPackages() {
  try {
    console.log('Adding test packages to existing service...');

    // Create package types first
    const standardPackageType = await prisma.packageType.upsert({
      where: { code: 'STANDARD' },
      update: {},
      create: {
        code: 'STANDARD',
        name: 'Standard Package',
        description: 'Basic service package',
        isDefault: true
      }
    });

    const premiumPackageType = await prisma.packageType.upsert({
      where: { code: 'PREMIUM' },
      update: {},
      create: {
        code: 'PREMIUM',
        name: 'Premium Package',
        description: 'Enhanced service package',
        isDefault: false
      }
    });

    // Add packages to existing service
    const serviceId = 'ps_coral_snorkeling_half';
    
    // Check if service exists
    const service = await prisma.providerService.findUnique({
      where: { id: serviceId }
    });

    if (!service) {
      console.error('Service not found:', serviceId);
      return;
    }

    // Add packages to the service
    const standardPackage = await prisma.servicePackage.create({
      data: {
        serviceId: serviceId,
        packageTypeId: standardPackageType.id,
        basePrice: '85',
        priceModifier: '0',
        includedItems: ['Transportation', 'Basic refreshments', 'Safety equipment'],
        excludedItems: [],
        isActive: true
      }
    });

    const premiumPackage = await prisma.servicePackage.create({
      data: {
        serviceId: serviceId,
        packageTypeId: premiumPackageType.id,
        basePrice: '120',
        priceModifier: '35',
        includedItems: ['Transportation', 'Premium refreshments', 'Safety equipment', 'Lunch', 'Photos'],
        excludedItems: [],
        isActive: true
      }
    });

    console.log('✅ Successfully added packages:');
    console.log(`- Standard Package: ${standardPackage.id}`);
    console.log(`- Premium Package: ${premiumPackage.id}`);
    console.log(`- Service: ${serviceId} now has packages for Scenario 2 testing`);

  } catch (error) {
    console.error('Error adding packages:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addTestPackages();
