const { PrismaClient } = require('@prisma/client');
const BusinessRulesService = require('./src/services/businessRulesService');

const prisma = new PrismaClient();

async function demonstrateCapacityManagement() {
    try {
        console.log('🎯 Capacity Management System Demonstration\n');
        
        // Clean up any existing demo data first
        await prisma.serviceAssignment.deleteMany({
            where: { id: { startsWith: 'demo_' } }
        });
        await prisma.providerService.deleteMany({
            where: { id: { startsWith: 'demo_' } }
        });
        await prisma.boat.deleteMany({
            where: { id: { startsWith: 'demo_' } }
        });
        await prisma.provider.deleteMany({
            where: { id: { startsWith: 'demo_' } }
        });
        await prisma.user.deleteMany({
            where: { id: { startsWith: 'demo_' } }
        });

        // Create demo user
        const demoUser = await prisma.user.create({
            data: {
                id: 'demo_user',
                email: '<EMAIL>',
                role: 'BOAT_OWNER',
                emailVerified: true
            }
        });

        // Create demo provider
        const demoProvider = await prisma.provider.create({
            data: {
                id: 'demo_provider',
                userId: demoUser.id,
                companyName: 'Demo Marine Services',
                displayName: 'Demo Provider',
                contactEmail: '<EMAIL>',
                contactPhone: '+60123456789'
            }
        });

        // Create demo boats with different capacities
        const boats = await Promise.all([
            prisma.boat.create({
                data: {
                    id: 'demo_boat_1',
                    ownerId: demoUser.id,
                    providerId: demoProvider.id,
                    name: 'Speed Demon',
                    description: 'Fast speedboat',
                    capacity: 12,
                    basePrice: 150.00,
                    status: 'APPROVED',
                    isActive: true
                }
            }),
            prisma.boat.create({
                data: {
                    id: 'demo_boat_2',
                    ownerId: demoUser.id,
                    providerId: demoProvider.id,
                    name: 'Ocean Cruiser',
                    description: 'Comfortable cruiser',
                    capacity: 20,
                    basePrice: 200.00,
                    status: 'APPROVED',
                    isActive: true
                }
            }),
            prisma.boat.create({
                data: {
                    id: 'demo_boat_3',
                    ownerId: demoUser.id,
                    providerId: demoProvider.id,
                    name: 'Island Hopper',
                    description: 'Multi-destination boat',
                    capacity: 15,
                    basePrice: 180.00,
                    status: 'APPROVED',
                    isActive: true
                }
            })
        ]);

        // Get existing service types or create demo ones
        let serviceType = await prisma.serviceType.findFirst();
        if (!serviceType) {
            const serviceCategory = await prisma.serviceCategory.create({
                data: {
                    id: 'demo_category',
                    name: 'Demo Services',
                    code: 'DEMO',
                    description: 'Demo service category'
                }
            });
            
            serviceType = await prisma.serviceType.create({
                data: {
                    id: 'demo_service_type',
                    categoryId: serviceCategory.id,
                    name: 'Demo Tours',
                    code: 'DEMO_TOURS',
                    description: 'Demo tour services'
                }
            });
        }

        // Create demo services
        const services = await Promise.all([
            prisma.providerService.create({
                data: {
                    id: 'demo_service_1',
                    providerId: demoProvider.id,
                    serviceTypeId: serviceType.id,
                    name: 'Basic Tour',
                    description: 'Single boat, full capacity',
                    basePrice: 100.00,
                    maxCapacity: 12
                }
            }),
            prisma.providerService.create({
                data: {
                    id: 'demo_service_2',
                    providerId: demoProvider.id,
                    serviceTypeId: serviceType.id,
                    name: 'Premium Tour',
                    description: 'Single boat, reduced capacity for comfort',
                    basePrice: 150.00,
                    maxCapacity: 8
                }
            }),
            prisma.providerService.create({
                data: {
                    id: 'demo_service_3',
                    providerId: demoProvider.id,
                    serviceTypeId: serviceType.id,
                    name: 'Multi-Boat Adventure',
                    description: 'Multiple boats with different capacity settings',
                    basePrice: 200.00,
                    maxCapacity: 35
                }
            })
        ]);

        console.log('✅ Demo data created successfully!\n');

        // Scenario 1: Single boat, no capacity override
        console.log('🎬 Scenario 1: Single boat, no capacity override');
        
        await prisma.serviceAssignment.create({
            data: {
                id: 'demo_assignment_1',
                serviceId: services[0].id,
                boatId: boats[0].id,
                isPrimary: true,
                maxCapacityOverride: null,
                isActive: true
            }
        });
        
        let capacityInfo = await BusinessRulesService.calculateServiceCapacity(services[0].id);
        console.log(`Service: ${services[0].name}`);
        console.log(`Boat: ${boats[0].name} (${boats[0].capacity} seats)`);
        console.log(`Effective capacity: ${capacityInfo.capacity} seats (no override)\n`);

        // Scenario 2: Single boat, capacity override for premium service
        console.log('🎬 Scenario 2: Single boat, capacity override for premium service');
        
        const validationResult = await BusinessRulesService.validateServiceAssignment(
            boats[1].id,
            services[1].id,
            true,
            8
        );
        
        if (validationResult.valid) {
            console.log('✅ Validation passed: Override is within boat capacity');
            
            await prisma.serviceAssignment.create({
                data: {
                    id: 'demo_assignment_2',
                    serviceId: services[1].id,
                    boatId: boats[1].id,
                    isPrimary: true,
                    maxCapacityOverride: 8,
                    isActive: true
                }
            });
            
            capacityInfo = await BusinessRulesService.calculateServiceCapacity(services[1].id);
            console.log(`Service: ${services[1].name}`);
            console.log(`Boat: ${boats[1].name} (${boats[1].capacity} seats)`);
            console.log(`Effective capacity: ${capacityInfo.capacity} seats (override from ${boats[1].capacity} to 8)\n`);
        } else {
            console.log(`❌ Validation failed: ${validationResult.error}\n`);
        }

        // Scenario 3: Multiple boats with mixed overrides
        console.log('🎬 Scenario 3: Multiple boats with mixed overrides');
        
        await Promise.all([
            prisma.serviceAssignment.create({
                data: {
                    id: 'demo_assignment_3a',
                    serviceId: services[2].id,
                    boatId: boats[0].id,
                    isPrimary: true,
                    maxCapacityOverride: null,
                    isActive: true
                }
            }),
            prisma.serviceAssignment.create({
                data: {
                    id: 'demo_assignment_3b',
                    serviceId: services[2].id,
                    boatId: boats[1].id,
                    isPrimary: false,
                    maxCapacityOverride: 15,
                    isActive: true
                }
            }),
            prisma.serviceAssignment.create({
                data: {
                    id: 'demo_assignment_3c',
                    serviceId: services[2].id,
                    boatId: boats[2].id,
                    isPrimary: false,
                    maxCapacityOverride: 10,
                    isActive: true
                }
            })
        ]);
        
        capacityInfo = await BusinessRulesService.calculateServiceCapacity(services[2].id);
        console.log(`Service: ${services[2].name}`);
        console.log('Boat assignments:');
        capacityInfo.assignments.forEach(assignment => {
            const boat = boats.find(b => b.id === assignment.boatId);
            const overrideText = assignment.override ? 
                ` (override from ${boat?.capacity} to ${assignment.capacity})` :
                ' (full capacity)';
            console.log(`  - ${assignment.boatName}: ${assignment.capacity} seats${overrideText}`);
        });
        console.log(`Total service capacity: ${capacityInfo.capacity} seats\n`);

        // Scenario 4: Testing validation rules
        console.log('🎬 Scenario 4: Testing validation rules');
        
        const invalidValidation = await BusinessRulesService.validateServiceAssignment(
            boats[0].id,
            services[0].id,
            false,
            20 // Exceeds boat capacity
        );
        
        if (!invalidValidation.valid) {
            console.log(`✅ Validation correctly rejected: ${invalidValidation.error}`);
        } else {
            console.log('❌ Validation should have failed but passed');
        }
        
        const negativeValidation = await BusinessRulesService.validateServiceAssignment(
            boats[0].id,
            services[0].id,
            false,
            -5 // Negative capacity
        );
        
        if (!negativeValidation.valid) {
            console.log(`✅ Validation correctly rejected negative capacity: ${negativeValidation.error}`);
        } else {
            console.log('❌ Validation should have failed for negative capacity');
        }

        console.log('\n🎉 Capacity Management Demonstration Complete!');
        console.log('\n📋 Summary of implemented features:');
        console.log('  ✅ Individual capacity overrides per boat assignment');
        console.log('  ✅ Validation: Override cannot exceed boat physical capacity');
        console.log('  ✅ Validation: Override must be positive number');
        console.log('  ✅ Multiple boats per service with accumulated capacity');
        console.log('  ✅ Automatic service capacity calculation');
        console.log('  ✅ Enhanced BusinessRulesService with new validation methods');
        console.log('  ✅ Updated API endpoints with capacity validation');

    } catch (error) {
        console.error('❌ Demonstration failed:', error);
    } finally {
        // Clean up demo data
        try {
            await prisma.serviceAssignment.deleteMany({
                where: { id: { startsWith: 'demo_' } }
            });
            await prisma.providerService.deleteMany({
                where: { id: { startsWith: 'demo_' } }
            });
            await prisma.boat.deleteMany({
                where: { id: { startsWith: 'demo_' } }
            });
            await prisma.provider.deleteMany({
                where: { id: { startsWith: 'demo_' } }
            });
            await prisma.user.deleteMany({
                where: { id: { startsWith: 'demo_' } }
            });
            await prisma.serviceType.deleteMany({
                where: { id: { startsWith: 'demo_' } }
            });
            await prisma.serviceCategory.deleteMany({
                where: { id: { startsWith: 'demo_' } }
            });
            console.log('\n🧹 Demo data cleaned up');
        } catch (cleanupError) {
            console.log('Note: Some demo data may remain in database');
        }
        
        await prisma.$disconnect();
    }
}

demonstrateCapacityManagement();