const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedComprehensivePricing() {
  try {
    console.log('🎯 Seeding comprehensive pricing scenarios...');

    // Get existing services to work with
    const existingServices = await prisma.providerService.findMany({
      where: { isActive: true },
      take: 4
    });

    if (existingServices.length < 4) {
      console.log('❌ Need at least 4 services in database. Creating additional services...');
      
      // Get a provider to assign services to
      const provider = await prisma.provider.findFirst({
        where: { isActive: true }
      });

      if (!provider) {
        throw new Error('No active provider found. Please seed providers first.');
      }

      // Get a service type
      const serviceType = await prisma.serviceType.findFirst({
        where: { isActive: true }
      });

      if (!serviceType) {
        throw new Error('No active service type found. Please seed service types first.');
      }

      // Create additional services if needed
      const servicesToCreate = 4 - existingServices.length;
      for (let i = 0; i < servicesToCreate; i++) {
        const newService = await prisma.providerService.create({
          data: {
            id: `ps_pricing_scenario_${i + 1}`,
            providerId: provider.id,
            serviceTypeId: serviceType.id,
            name: `Pricing Test Service ${i + 1}`,
            description: `Test service for pricing scenario ${i + 1}`,
            basePrice: 100.00,
            maxCapacity: 12,
            includedItems: ['Basic equipment', 'Safety briefing'],
            isActive: true
          }
        });
        existingServices.push(newService);
      }
    }

    console.log(`📋 Working with ${existingServices.length} services for pricing scenarios`);

    // Clear existing packages for these services
    await prisma.servicePackage.deleteMany({
      where: {
        serviceId: {
          in: existingServices.map(s => s.id)
        }
      }
    });

    // Clear existing age pricing for these services
    await prisma.serviceAgePricing.deleteMany({
      where: {
        serviceId: {
          in: existingServices.map(s => s.id)
        }
      }
    });

    // Scenario 1: Basic pricing (no packages, no age-based pricing)
    console.log('💰 Scenario 1: Basic pricing (single base price)');
    const service1 = existingServices[0];
    await prisma.providerService.update({
      where: { id: service1.id },
      data: {
        name: 'Basic Island Hopping',
        description: 'Simple island hopping tour with basic pricing',
        basePrice: 85.00,
        agePricing: null, // No age-based pricing
        includedItems: ['Transportation', 'Basic refreshments']
      }
    });
    console.log(`✅ Service "${service1.name}" configured for basic pricing`);

    // Scenario 2: Package variations only (no age-based pricing)
    console.log('📦 Scenario 2: Package variations only');
    const service2 = existingServices[1];
    await prisma.providerService.update({
      where: { id: service2.id },
      data: {
        name: 'Snorkeling Adventure Packages',
        description: 'Snorkeling tour with multiple package options',
        basePrice: 95.00,
        agePricing: null, // No age-based pricing
        includedItems: ['Snorkeling equipment', 'Guide']
      }
    });

    // Create packages for service 2
    const packages2 = [
      {
        packageTypeId: 'pkg_standard',
        basePrice: 95.00,
        priceModifier: 1.0,
        includedItems: ['Snorkeling equipment', 'Guide', 'Basic refreshments'],
        isDefault: true
      },
      {
        packageTypeId: 'pkg_premium',
        basePrice: 125.00,
        priceModifier: 1.32,
        includedItems: ['Snorkeling equipment', 'Guide', 'Premium refreshments', 'Underwater photos', 'Towels'],
        isDefault: false
      },
      {
        packageTypeId: 'pkg_luxury',
        basePrice: 155.00,
        priceModifier: 1.63,
        includedItems: ['Premium snorkeling equipment', 'Private guide', 'Gourmet meal', 'Professional photography', 'Luxury transport', 'Complimentary gear'],
        isDefault: false
      }
    ];

    for (const pkg of packages2) {
      await prisma.servicePackage.create({
        data: {
          id: `sp_${service2.id}_${pkg.packageTypeId.split('_')[1]}`,
          serviceId: service2.id,
          packageTypeId: pkg.packageTypeId,
          basePrice: pkg.basePrice,
          agePricing: null, // No age pricing for this scenario
          priceModifier: pkg.priceModifier,
          includedItems: pkg.includedItems,
          excludedItems: [],
          isActive: true
        }
      });
    }
    console.log(`✅ Service "${service2.name}" configured with 3 packages (no age pricing)`);

    // Scenario 3: Age-based pricing only (no package variations)
    console.log('👥 Scenario 3: Age-based pricing only');
    const service3 = existingServices[2];
    await prisma.providerService.update({
      where: { id: service3.id },
      data: {
        name: 'Family Coral Garden Tour',
        description: 'Family-friendly tour with age-based pricing',
        basePrice: 110.00,
        agePricing: {
          adult: 110.00,
          child: 77.00,    // 30% discount
          toddler: 0,      // Free
          senior: 99.00    // 10% discount
        },
        includedItems: ['Marine guide', 'Life jackets', 'Refreshments']
      }
    });

    // Create single standard package with age pricing
    await prisma.servicePackage.create({
      data: {
        id: `sp_${service3.id}_standard`,
        serviceId: service3.id,
        packageTypeId: 'pkg_standard',
        basePrice: 110.00,
        agePricing: {
          adult: 110.00,
          child: 77.00,
          toddler: 0,
          senior: 99.00
        },
        priceModifier: 1.0,
        includedItems: ['Marine guide', 'Life jackets', 'Refreshments', 'Safety equipment'],
        excludedItems: [],
        isActive: true
      }
    });
    console.log(`✅ Service "${service3.name}" configured with age-based pricing only`);

    // Scenario 4: Full variation (both packages AND age-based pricing)
    console.log('🎯 Scenario 4: Full variation (packages + age pricing)');
    const service4 = existingServices[3];
    await prisma.providerService.update({
      where: { id: service4.id },
      data: {
        name: 'Premium Marine Experience',
        description: 'Complete marine experience with full pricing variations',
        basePrice: 120.00,
        agePricing: {
          adult: 120.00,
          child: 84.00,    // 30% discount
          toddler: 0,      // Free
          senior: 108.00   // 10% discount
        },
        includedItems: ['Professional guide', 'Premium equipment']
      }
    });

    // Create packages with age pricing for service 4
    const packages4 = [
      {
        packageTypeId: 'pkg_standard',
        basePrice: 120.00,
        priceModifier: 1.0,
        agePricing: { adult: 120.00, child: 84.00, toddler: 0, senior: 108.00 },
        includedItems: ['Professional guide', 'Premium equipment', 'Refreshments'],
        isDefault: true
      },
      {
        packageTypeId: 'pkg_premium',
        basePrice: 150.00,
        priceModifier: 1.25,
        agePricing: { adult: 150.00, child: 105.00, toddler: 0, senior: 135.00 },
        includedItems: ['Professional guide', 'Premium equipment', 'Gourmet refreshments', 'Photography', 'Towels', 'Gear bag'],
        isDefault: false,
        isMostPopular: true // Mark as most popular
      },
      {
        packageTypeId: 'pkg_luxury',
        basePrice: 180.00,
        priceModifier: 1.5,
        agePricing: { adult: 180.00, child: 126.00, toddler: 0, senior: 162.00 },
        includedItems: ['Private guide', 'Luxury equipment', 'Premium dining', 'Professional videography', 'Luxury transport', 'Exclusive access', 'Souvenir package'],
        isDefault: false
      }
    ];

    for (const pkg of packages4) {
      await prisma.servicePackage.create({
        data: {
          id: `sp_${service4.id}_${pkg.packageTypeId.split('_')[1]}`,
          serviceId: service4.id,
          packageTypeId: pkg.packageTypeId,
          basePrice: pkg.basePrice,
          agePricing: pkg.agePricing,
          priceModifier: pkg.priceModifier,
          includedItems: pkg.includedItems,
          excludedItems: [],
          isActive: true
        }
      });
    }
    console.log(`✅ Service "${service4.name}" configured with full variations`);

    console.log('\n🎉 Comprehensive pricing scenarios seeded successfully!');
    console.log('\n📊 Summary:');
    console.log(`1. ${service1.name}: Basic pricing (RM ${service1.basePrice})`);
    console.log(`2. ${service2.name}: 3 packages (RM 95-155)`);
    console.log(`3. ${service3.name}: Age-based pricing (Adult: RM 110, Child: RM 77, Senior: RM 99, Toddler: Free)`);
    console.log(`4. ${service4.name}: Full variation (3 packages × 4 age groups = 12 pricing combinations)`);

  } catch (error) {
    console.error('❌ Error seeding comprehensive pricing:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

seedComprehensivePricing();
