const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkServices() {
  try {
    console.log('Checking provider services...');
    
    const services = await prisma.providerService.findMany({
      select: {
        id: true,
        name: true,
        itinerary: true,
        isActive: true
      },
      take: 5
    });
    
    console.log(`Found ${services.length} services:`);
    services.forEach(service => {
      console.log(`- ${service.id}: ${service.name} (Active: ${service.isActive})`);
      console.log(`  Itinerary: ${service.itinerary ? 'YES' : 'NO'}`);
      if (service.itinerary) {
        console.log(`  Itinerary data:`, JSON.stringify(service.itinerary, null, 2));
      }
      console.log('');
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkServices();
