# GoSea API Documentation Usage Guide

This guide explains how to use and maintain the GoSea API documentation system built with Fumadocs.

## 🚀 Quick Start

### Accessing the Documentation

The API documentation is available at:
- **Development**: http://localhost:3002
- **Production**: https://docs-api.gosea.my (when deployed)

### Starting the Documentation Server

#### Option 1: Direct from docs directory
```bash
cd backend/docs
npm run dev
```

#### Option 2: From backend directory
```bash
cd backend
npm run docs:dev
```

#### Option 3: Using Docker
```bash
# From project root
docker-compose --profile docs up api-docs
```

## 📖 Documentation Structure

### Main Sections

1. **Introduction** (`/docs`) - Overview of the GoSea API
2. **Getting Started** (`/docs/getting-started`) - Setup and first requests
3. **Authentication** (`/docs/authentication`) - JWT and OAuth integration
4. **API Endpoints** (`/docs/endpoints`) - Complete endpoint reference
5. **Integration Examples** (`/docs/examples`) - Practical code examples
6. **Error Handling** (`/docs/errors`) - Error codes and handling

### Navigation Features

- **Sidebar Navigation** - Organized by sections with expandable subsections
- **Search Functionality** - Global search across all documentation
- **Table of Contents** - Page-level navigation for long documents
- **Breadcrumbs** - Current location tracking
- **Dark/Light Mode** - Theme switching support

## 🔍 Using the Documentation

### Finding Information

1. **Browse by Section** - Use the sidebar to navigate to specific topics
2. **Search** - Use the search bar (Ctrl/Cmd + K) to find specific content
3. **Table of Contents** - Use the right sidebar for page navigation
4. **Cross-References** - Click on internal links to jump between sections

### Code Examples

All code examples include:
- **Syntax Highlighting** - Language-specific formatting
- **Copy Button** - One-click copying to clipboard
- **Multiple Languages** - Examples in JavaScript, cURL, and other formats
- **Live Examples** - Interactive examples where applicable

### API Reference

Each endpoint includes:
- **HTTP Method and URL** - Complete endpoint specification
- **Parameters** - Required and optional parameters with types
- **Request Examples** - Sample request bodies
- **Response Examples** - Sample responses with status codes
- **Error Responses** - Common error scenarios

## ✏️ Updating Documentation

### Content Management

Documentation content is stored in MDX files under `backend/docs/content/docs/`:

```
content/docs/
├── index.mdx              # Introduction page
├── getting-started.mdx    # Setup guide
├── authentication.mdx     # Auth documentation
├── endpoints.mdx          # API reference
├── examples.mdx           # Code examples
├── errors.mdx             # Error handling
└── meta.json              # Navigation structure
```

### Adding New Pages

1. **Create MDX File**
   ```bash
   cd backend/docs/content/docs
   touch new-section.mdx
   ```

2. **Add Frontmatter**
   ```mdx
   ---
   title: New Section Title
   description: Brief description for SEO
   ---

   # New Section Content
   ```

3. **Update Navigation**
   ```json
   // content/docs/meta.json
   {
     "title": "GoSea API Documentation",
     "pages": [
       "index",
       "getting-started",
       "authentication",
       "endpoints",
       "examples",
       "errors",
       "new-section"
     ]
   }
   ```

4. **Regenerate Map**
   ```bash
   cd backend/docs
   npx fumadocs-mdx
   ```

### Editing Existing Content

1. **Edit MDX Files** - Modify content in `content/docs/*.mdx`
2. **Preview Changes** - Development server auto-reloads
3. **Test Links** - Verify internal and external links work
4. **Check Formatting** - Ensure proper MDX syntax

### MDX Syntax Guide

#### Headers
```mdx
# H1 Header
## H2 Header
### H3 Header
```

#### Code Blocks
```mdx
```javascript
const example = async () => {
  const response = await fetch('/api/endpoint');
  return await response.json();
};
```
```

#### API Endpoints
```mdx
### POST /api/auth/login
Authenticate user and receive JWT tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {...},
    "tokens": {...}
  }
}
```
```

#### Callouts and Alerts
```mdx
> **Note**: This is an important note

> **Warning**: This is a warning message
```

## 🔧 Maintenance Tasks

### Regular Updates

1. **API Changes** - Update endpoint documentation when APIs change
2. **Code Examples** - Keep examples current with latest API versions
3. **Error Codes** - Update error documentation for new error types
4. **Dependencies** - Keep Fumadocs and dependencies updated

### Content Review

Monthly review checklist:
- [ ] Verify all code examples work
- [ ] Check for broken internal links
- [ ] Update API endpoint documentation
- [ ] Review error handling examples
- [ ] Test authentication flows
- [ ] Validate integration examples

### Performance Optimization

1. **Image Optimization** - Compress images and use appropriate formats
2. **Bundle Analysis** - Monitor bundle size and optimize imports
3. **Search Index** - Ensure search functionality is fast and accurate
4. **Loading Performance** - Monitor page load times

## 🚀 Deployment

### Development Deployment

The documentation automatically rebuilds when:
- MDX files are modified
- Configuration files are updated
- Dependencies are changed

### Production Deployment

1. **Build Documentation**
   ```bash
   cd backend/docs
   npm run build
   ```

2. **Start Production Server**
   ```bash
   npm run start
   ```

3. **Docker Production**
   ```bash
   docker build -f Dockerfile.prod -t gosea-api-docs .
   docker run -p 3002:3002 gosea-api-docs
   ```

### CI/CD Integration

Example GitHub Actions workflow:

```yaml
name: Deploy API Documentation

on:
  push:
    branches: [main]
    paths: ['backend/docs/**']

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - run: cd backend/docs && npm ci
      - run: cd backend/docs && npx fumadocs-mdx
      - run: cd backend/docs && npm run build
      - run: cd backend/docs && npm run start
```

## 🎯 Best Practices

### Content Writing

1. **Clear Headings** - Use descriptive, hierarchical headings
2. **Code Examples** - Provide working, copy-pasteable examples
3. **Error Scenarios** - Document common error cases
4. **Prerequisites** - List requirements clearly
5. **Step-by-Step** - Break complex processes into steps

### Code Examples

1. **Complete Examples** - Show full, working code
2. **Error Handling** - Include proper error handling
3. **Comments** - Add explanatory comments
4. **Multiple Languages** - Provide examples in different languages
5. **Real Data** - Use realistic example data

### API Documentation

1. **Consistent Format** - Use consistent formatting for all endpoints
2. **Parameter Details** - Document all parameters with types and constraints
3. **Response Examples** - Show both success and error responses
4. **Status Codes** - Document all possible HTTP status codes
5. **Rate Limits** - Document any rate limiting

## 🔍 Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Clear cache and rebuild
   rm -rf .next node_modules
   npm install
   npx fumadocs-mdx
   npm run build
   ```

2. **Search Not Working**
   ```bash
   # Regenerate search index
   npx fumadocs-mdx
   ```

3. **Broken Links**
   - Check file paths in MDX files
   - Verify meta.json configuration
   - Ensure proper frontmatter

4. **Styling Issues**
   - Check Tailwind CSS configuration
   - Verify component imports
   - Review custom CSS

### Getting Help

- **Documentation**: https://fumadocs.vercel.app/
- **GitHub Issues**: Report bugs and feature requests
- **Community**: Join discussions and get help
- **Support**: Contact development team for urgent issues

## 📊 Analytics and Monitoring

### Usage Analytics

Track documentation usage:
- Page views and popular sections
- Search queries and results
- User flow through documentation
- Time spent on pages

### Performance Monitoring

Monitor documentation performance:
- Page load times
- Search response times
- Build and deployment times
- Error rates and types

### Feedback Collection

Collect user feedback:
- Page rating system
- Feedback forms
- User surveys
- GitHub issues and discussions

This comprehensive guide should help you effectively use and maintain the GoSea API documentation system!
