# Backend Development Dockerfile for GoSea Platform
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm

# Install system dependencies for Prisma
RUN apk add --no-cache openssl

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S expressjs -u 1001

# Copy package files
COPY package*.json pnpm-lock.yaml* ./

# Install dependencies
RUN pnpm install

# Copy source code
COPY . .

# Create necessary directories
RUN mkdir -p uploads logs

# Generate Prisma client
RUN pnpm prisma generate

# Change ownership to expressjs user
RUN chown -R expressjs:nodejs /app
USER expressjs

# Expose port
EXPOSE 5000

# Set environment variables for development
ENV NODE_ENV=development
ENV PORT=5000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5000/api/health || exit 1

# Start development server with node<PERSON> ["pnpm", "dev"]
