# GoSea Database Setup Guide

## Overview
This document contains all the essential commands for setting up, managing, and maintaining the GoSea platform database using Prisma and PostgreSQL.

## Prerequisites
- Docker and Docker Compose installed
- GoSea project containers running
- Node.js and npm/pnpm installed

## Quick Start

### 1. Start Development Environment
```bash
# Start all services
docker compose up -d

# Check service status
docker compose ps
```

### 2. Reset and Seed Database (Recommended for fresh setup)
```bash
# Complete database reset and seeding
docker compose exec backend npx prisma migrate reset --force
```

## Database Management Commands

### Migration Commands

#### Check Migration Status
```bash
# Check current migration status
docker compose exec backend npx prisma migrate status
```

#### Create New Migration
```bash
# Create migration for schema changes
docker compose exec backend npx prisma migrate dev --name your_migration_name
```

#### Apply Migrations
```bash
# Apply pending migrations
docker compose exec backend npx prisma migrate deploy
```

#### Reset Database
```bash
# Reset database and reapply all migrations (DESTRUCTIVE)
docker compose exec backend npx prisma migrate reset --force
```

### Schema Synchronization

#### Force Push Schema (Development Only)
```bash
# Force sync schema with database (DESTRUCTIVE - use carefully)
docker compose exec backend npx prisma db push --force-reset
```

#### Generate Prisma Client
```bash
# Regenerate Prisma client after schema changes
docker compose exec backend npx prisma generate
```

### Seeding Commands

#### Run Production Seed
```bash
# Seed database with production data
docker compose exec backend node prisma/seed_production.js
```

#### Run Development Seed
```bash
# Seed database with development data
docker compose exec backend node prisma/seed.js
```

#### Run Specific Seed Files
```bash
# Seed boats data
docker compose exec backend node prisma/seed-boats.js

# Seed users data
docker compose exec backend node prisma/seed.js

# Seed discount codes
docker compose exec backend node prisma/seed-discount-codes.js
```

## Database Access and Tools

### Database GUI Tools

#### Prisma Studio (Recommended)
```bash
# Open database browser in web interface
docker compose exec backend npx prisma studio
```
- Access at: http://localhost:5555

#### pgAdmin
```bash
# Start pgAdmin (if not running)
docker compose --profile tools up pgadmin
```
- Access at: http://localhost:8080
- Email: <EMAIL>
- Password: admin123

### Direct Database Access

#### PostgreSQL CLI
```bash
# Connect to database
docker compose exec postgres psql -U gosea -d gosea_db

# List all tables
docker compose exec postgres psql -U gosea -d gosea_db -c "\dt"

# List all databases
docker compose exec postgres psql -U gosea -d gosea_db -c "\l"
```

#### Database Backup
```bash
# Create backup
docker compose exec postgres pg_dump -U gosea gosea_db > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore backup
docker compose exec -T postgres psql -U gosea -d gosea_db < backup_file.sql
```

## Development Workflow

### Schema Changes
```bash
# 1. Modify schema.prisma file
# 2. Create migration
docker compose exec backend npx prisma migrate dev --name add_new_feature

# 3. Generate client
docker compose exec backend npx prisma generate

# 4. Test changes
docker compose exec backend node prisma/seed_production.js
```

### Troubleshooting

#### Common Issues

**Migration Errors:**
```bash
# Reset and clean start
docker compose exec backend npx prisma migrate reset --force
```

**Schema Sync Issues:**
```bash
# Force schema sync (development only)
docker compose exec backend npx prisma db push --force-reset
docker compose exec backend npx prisma generate
```

**Seed Failures:**
```bash
# Clear and reseed
docker compose exec backend npx prisma migrate reset --force
```

#### Database Health Check
```bash
# Check database connectivity
docker compose exec backend npx prisma db execute --file <(echo "SELECT 1;")

# Check migration state
docker compose exec backend npx prisma migrate status
```

## Production Deployment

### Production Migration
```bash
# Apply migrations in production
docker compose exec backend npx prisma migrate deploy

# Generate optimized client
docker compose exec backend npx prisma generate --schema=./prisma/schema.prisma
```

### Production Backup
```bash
# Create production backup
docker compose exec postgres pg_dump -U gosea -h localhost gosea_db > prod_backup_$(date +%Y%m%d).sql
```

## Available Scripts

The project includes several helper scripts in the `scripts/` directory:

### Database Migration Script
```bash
./scripts/db-migrate.sh
```

### Development Setup
```bash
./scripts/dev-setup.sh
```

### Install Dependencies
```bash
./scripts/install-deps.sh
```

## Database Schema Information

### Key Tables
- `users` - User accounts and authentication
- `profiles` - User profile information
- `providers` - Boat service providers
- `boats` - Boat listings and details
- `provider_services` - Service offerings
- `bookings` - Customer bookings
- `jetties` - Departure locations
- `destinations` - Destination islands
- `routes` - Travel routes

### Relationships
- Users → Providers (1:1)
- Providers → Boats (1:many)
- Providers → Services (1:many)
- Boats → Services (many:many via assignments)
- Services → Routes (many:many)
- Users → Bookings (1:many)

## Environment Variables

Key environment variables for database configuration:

```env
DATABASE_URL=*****************************************/gosea_db
JWT_SECRET=your-jwt-secret
JWT_REFRESH_SECRET=your-refresh-secret
NODE_ENV=development
```

## Monitoring and Logs

### View Container Logs
```bash
# Backend logs
docker compose logs -f backend

# Database logs
docker compose logs -f postgres

# All services
docker compose logs -f
```

### Database Performance
```bash
# Check active connections
docker compose exec postgres psql -U gosea -d gosea_db -c "SELECT * FROM pg_stat_activity;"

# Check table sizes
docker compose exec postgres psql -U gosea -d gosea_db -c "SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size FROM pg_tables ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;"
```

## Quick Reference

### Most Common Commands
```bash
# Fresh database setup
docker compose exec backend npx prisma migrate reset --force

# Check status
docker compose exec backend npx prisma migrate status

# View database
docker compose exec backend npx prisma studio

# Generate client
docker compose exec backend npx prisma generate
```

### Emergency Recovery
```bash
# Complete reset (use with caution)
docker compose down -v
docker compose up -d
docker compose exec backend npx prisma migrate reset --force
```

---

**Last Updated:** September 9, 2025
**GoSea Platform Version:** v1.0.0
