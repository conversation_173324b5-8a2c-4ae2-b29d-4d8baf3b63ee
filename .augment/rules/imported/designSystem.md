---
type: "manual"
---

# Design System - GoSea Platform

## Color Palette

### Primary Colors
```css
--primary: #0ea5e9;      /* Sky 500 - Main brand color */
--primary-focus: #0284c7; /* Sky 600 - Hover/Focus states */
--primary-light: #38bdf8; /* Sky 400 - Light variant */
--primary-dark: #0369a1;  /* Sky 700 - Dark variant */
--primary-content: #ffffff; /* White - Text on primary */
```

### Secondary Colors
```css
--secondary: #f59e0b;     /* Amber 500 - Secondary actions */
--secondary-focus: #d97706; /* Amber 600 - Hover/Focus states */
--secondary-light: #fbbf24; /* Amber 400 - Light variant */
--secondary-dark: #b45309;  /* Amber 700 - Dark variant */
--secondary-content: #ffffff; /* White - Text on secondary */
```

### Accent Colors
```css
--accent: #06b6d4;        /* <PERSON>an 500 - Highlights/Accents */
--accent-focus: #0891b2;  /* <PERSON><PERSON> 600 - Hover/Focus states */
--accent-light: #22d3ee;  /* <PERSON>an 400 - Light variant */
--accent-dark: #0e7490;   /* <PERSON>an 700 - Dark variant */
--accent-content: #ffffff; /* White - Text on accent */
```

### Neutral Colors
```css
--neutral: #64748b;       /* Slate 500 - Text */
--neutral-focus: #475569; /* Slate 600 - Headings */
--neutral-light: #94a3b8; /* Slate 400 - Light text */
--neutral-dark: #334155;  /* Slate 700 - Dark text */
--neutral-content: #ffffff; /* White - Text on neutral */
```

### State Colors
```css
--success: #10b981;       /* Emerald 500 - Success states */
--success-content: #ffffff; /* White - Text on success */
--warning: #f59e0b;       /* Amber 500 - Warning states */
--warning-content: #ffffff; /* White - Text on warning */
--error: #ef4444;         /* Red 500 - Error states */
--error-content: #ffffff;  /* White - Text on error */
--info: #0ea5e9;          /* Sky 500 - Information states */
--info-content: #ffffff;   /* White - Text on info */
```

### Background Colors
```css
--base-100: #ffffff;     /* White - Main background */
--base-200: #f0f9ff;    /* Sky 50 - Secondary background */
--base-300: #e0f2fe;    /* Sky 100 - Tertiary background */
--base-400: #bae6fd;    /* Sky 200 - Quaternary background */
--base-content: #0f172a; /* Slate 900 - Main text color */
--base-content-light: #475569; /* Slate 600 - Secondary text */
```

## Typography

### Font Families
```css
--font-primary: 'Inter', sans-serif;   /* Main text */
--font-display: 'Poppins', sans-serif; /* Headings */
```

### Font Sizes
```css
/* Body Text */
--text-xs: 0.75rem;   /* 12px */
--text-sm: 0.875rem;  /* 14px */
--text-base: 1rem;    /* 16px */
--text-lg: 1.125rem;  /* 18px */
--text-xl: 1.25rem;   /* 20px */

/* Headings */
--text-2xl: 1.5rem;   /* 24px */
--text-3xl: 1.875rem; /* 30px */
--text-4xl: 2.25rem;  /* 36px */
--text-5xl: 3rem;     /* 48px */
```

### Font Weights
```css
--font-light: 300;
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
```

### Line Heights
```css
--leading-none: 1;      /* Headings */
--leading-tight: 1.25;  /* Compact text */
--leading-normal: 1.5;  /* Body text */
--leading-relaxed: 1.75; /* Readable text */
```

## Spacing

### Base Spacing Units
```css
--spacing-0: 0;
--spacing-1: 0.25rem;  /* 4px */
--spacing-2: 0.5rem;   /* 8px */
--spacing-3: 0.75rem;  /* 12px */
--spacing-4: 1rem;     /* 16px */
--spacing-6: 1.5rem;   /* 24px */
--spacing-8: 2rem;     /* 32px */
--spacing-12: 3rem;    /* 48px */
--spacing-16: 4rem;    /* 64px */
```

### Layout Spacing
```css
--container-padding: 1rem;  /* 16px */
--section-spacing: 4rem;   /* 64px */
--grid-gap: 1.5rem;       /* 24px */
```

## Border Radius
```css
--radius-sm: 0.125rem;   /* 2px */
--radius-md: 0.375rem;   /* 6px */
--radius-lg: 0.5rem;     /* 8px */
--radius-xl: 0.75rem;    /* 12px */
--radius-full: 9999px;   /* Full round */
```

## Shadows
```css
--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
--shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
```

## Transitions
```css
--transition-all: all 0.3s ease;
--transition-colors: background-color, border-color, color, fill, stroke 0.3s ease;
--transition-opacity: opacity 0.3s ease;
--transition-transform: transform 0.3s ease;
```

## Z-Index Layers
```css
--z-0: 0;
--z-10: 10;      /* Dropdowns */
--z-20: 20;      /* Sticky elements */
--z-30: 30;      /* Modals backdrop */
--z-40: 40;      /* Modals */
--z-50: 50;      /* Tooltips */
```

## Component-Specific Styles

### Buttons
```css
/* Base Button */
--btn-padding-x: 1rem;
--btn-padding-y: 0.5rem;
--btn-font-size: var(--text-sm);
--btn-font-weight: var(--font-medium);
--btn-radius: var(--radius-md);

/* Size Variants */
--btn-sm-padding-x: 0.75rem;
--btn-sm-padding-y: 0.375rem;
--btn-lg-padding-x: 1.5rem;
--btn-lg-padding-y: 0.75rem;
```

### Cards
```css
--card-padding: 1.5rem;
--card-radius: var(--radius-lg);
--card-shadow: var(--shadow-md);
--card-bg: var(--base-100);
```

### Forms
```css
--input-height: 2.5rem;
--input-padding-x: 0.75rem;
--input-radius: var(--radius-md);
--input-border: 1px solid;
--input-border-color: rgb(209 213 219);
```

## Responsive Breakpoints
```css
--screen-sm: 640px;   /* Small devices */
--screen-md: 768px;   /* Medium devices */
--screen-lg: 1024px;  /* Large devices */
--screen-xl: 1280px;  /* Extra large devices */
--screen-2xl: 1536px; /* 2X large devices */
```

## Usage with Tailwind CSS & DaisyUI

These design tokens will be implemented through:
1. Tailwind CSS configuration (tailwind.config.js)
2. DaisyUI theme configuration
3. Custom CSS variables for specific components

Example Tailwind configuration:
```javascript
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: 'var(--primary)',
        secondary: 'var(--secondary)',
        accent: 'var(--accent)',
        // ... other colors
      },
      fontFamily: {
        sans: ['var(--font-primary)'],
        display: ['var(--font-display)'],
      },
      fontSize: {
        // ... font sizes
      },
      spacing: {
        // ... spacing values
      },
      borderRadius: {
        // ... border radius values
      },
      boxShadow: {
        // ... shadow values
      }
    }
  }
}
