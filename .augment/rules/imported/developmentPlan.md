---
type: "manual"
---

# GoSea Platform Development Plan

## Executive Summary
This development plan breaks down the GoSea platform into 4 phases and 12 modules, designed for parallel development within a containerized Docker environment. The plan prioritizes core functionality (authentication, booking) before advanced features, enabling early user testing and iterative development.

## Phase Overview

### Phase 1: Foundation & Core Infrastructure (Weeks 1-4)
**Goal**: Establish containerized development environment and core authentication system
- **Modules**: M1 (Docker Setup), M2 (Authentication Core), M3 (Database Foundation)
- **Deliverable**: Working authentication system for all user types
- **Milestone**: Users can register, login, and access role-specific dashboards

### Phase 2: Core Business Logic (Weeks 5-10)
**Goal**: Implement essential boat management and booking functionality
- **Modules**: M4 (Boat Management), M5 (Search & Discovery), M6 (Booking System)
- **Deliverable**: End-to-end booking flow for customers and boat owners
- **Milestone**: Customers can search, book, and pay for boat services

### Phase 3: Advanced Features & Integrations (Weeks 11-16)
**Goal**: Add affiliate system, payment processing, and notifications
- **Modules**: M7 (Payment Integration), M8 (Affiliate System), M9 (Notification System)
- **Deliverable**: Complete platform with all user roles functional
- **Milestone**: Affiliate agents can generate bookings and earn commissions

### Phase 4: Optimization & Production (Weeks 17-20)
**Goal**: Performance optimization, analytics, and production deployment
- **Modules**: M10 (Analytics & Reporting), M11 (Admin Panel), M12 (Production Deployment)
- **Deliverable**: Production-ready platform with monitoring and analytics
- **Milestone**: Platform deployed and ready for public use

## Module Breakdown

### M1: Docker Infrastructure Setup
**Phase**: 1 | **Complexity**: Medium | **Team**: DevOps/Full-stack | **Duration**: 1 week

#### Scope & Boundaries
- Complete containerized development environment
- CI/CD pipeline foundation
- Development tooling and scripts

#### Dependencies
- None (Foundation module)

#### Deliverables
- Docker Compose configuration for all services
- Frontend container (Next.js with hot reload)
- Backend container (Express.js with nodemon)
- PostgreSQL container with persistent volumes
- MailHog container for email testing
- Redis container for caching
- Development scripts and documentation
- Environment variable management
- Container health checks and monitoring

#### Docker Integration
- **Primary Focus**: This IS the Docker integration
- **Services**: All core containers defined and orchestrated
- **Networking**: Internal container communication setup
- **Volumes**: Persistent data and hot reload configuration

#### Testing Strategy
- Container startup and health verification
- Service connectivity testing
- Hot reload functionality validation
- Volume mounting verification

---

### M2: Authentication & User Management Core
**Phase**: 1 | **Complexity**: High | **Team**: Full-stack | **Duration**: 2 weeks

#### Scope & Boundaries
- Multi-method authentication (Google OAuth, Email/OTP)
- Role-based access control (Customer, Boat Owner, Affiliate Agent)
- User profile management
- Admin approval workflow

#### Dependencies
- M1 (Docker Infrastructure)

#### Deliverables
- JWT-based authentication system
- Google OAuth integration
- Email OTP verification system
- User registration flows for all roles
- Profile management interfaces
- Admin approval workflow
- Password recovery system
- Role-based route protection
- Session management

#### Docker Integration
- **Backend Services**: Authentication APIs in Express container
- **Frontend Services**: Auth components in Next.js container
- **Database**: User tables and relationships in PostgreSQL
- **Email**: OTP delivery via MailHog container
- **Environment**: OAuth keys and JWT secrets management

#### Testing Strategy
- Unit tests for authentication logic
- Integration tests for OAuth flow
- E2E tests for registration/login flows
- Security testing for JWT handling

---

### M3: Database Foundation & Core Models
**Phase**: 1 | **Complexity**: Medium | **Team**: Backend | **Duration**: 1 week

#### Scope & Boundaries
- Prisma schema design and implementation
- Core database models and relationships
- Migration system setup
- Data seeding and fixtures

#### Dependencies
- M1 (Docker Infrastructure)
- M2 (Authentication Core) - for user models

#### Deliverables
- Complete Prisma schema
- Database migrations
- Model relationships and constraints
- Seed data for development
- Database backup/restore procedures
- Performance indexes
- Data validation rules

#### Docker Integration
- **Database**: PostgreSQL container with schema
- **Backend**: Prisma client in Express container
- **Migrations**: Automated migration runner
- **Volumes**: Persistent database storage

#### Testing Strategy
- Schema validation tests
- Migration rollback testing
- Data integrity constraints testing
- Performance benchmarking

---

### M4: Boat Management System
**Phase**: 2 | **Complexity**: High | **Team**: Full-stack | **Duration**: 2 weeks

#### Scope & Boundaries
- Boat listing creation and management
- Photo upload and gallery system
- Availability calendar management
- Pricing and service configuration

#### Dependencies
- M2 (Authentication) - for boat owner access
- M3 (Database Foundation) - for boat models

#### Deliverables
- Boat listing CRUD operations
- Multi-photo upload system
- Interactive availability calendar
- Pricing configuration interface
- Service type and amenity management
- Draft/publish workflow
- Admin approval system
- Boat owner dashboard

#### Docker Integration
- **Frontend**: Boat management UI in Next.js container
- **Backend**: Boat APIs in Express container
- **Database**: Boat models and relationships in PostgreSQL
- **File Storage**: Image uploads in containerized storage
- **Cache**: Boat data caching in Redis container

#### Testing Strategy
- CRUD operation testing
- File upload validation
- Calendar functionality testing
- Permission and access control testing

---

### M5: Search & Discovery Engine
**Phase**: 2 | **Complexity**: Medium | **Team**: Full-stack | **Duration**: 1.5 weeks

#### Scope & Boundaries
- Advanced search filters and functionality
- Real-time availability checking
- Geolocation-based searches
- Search result optimization

#### Dependencies
- M3 (Database Foundation) - for search queries
- M4 (Boat Management) - for searchable content

#### Deliverables
- Advanced search interface
- Filter system (service type, location, date, price)
- Real-time availability integration
- Search result pagination
- Geolocation search functionality
- Search performance optimization
- Mobile-responsive search UI

#### Docker Integration
- **Frontend**: Search UI components in Next.js container
- **Backend**: Search APIs and algorithms in Express container
- **Database**: Optimized search queries in PostgreSQL
- **Cache**: Search result caching in Redis container

#### Testing Strategy
- Search accuracy validation
- Performance testing with large datasets
- Filter combination testing
- Mobile responsiveness testing

---

### M6: Booking System Core
**Phase**: 2 | **Complexity**: High | **Team**: Full-stack | **Duration**: 2.5 weeks

#### Scope & Boundaries
- End-to-end booking flow
- Capacity validation and management
- Booking modifications and cancellations
- Basic payment integration (without full processing)

#### Dependencies
- M2 (Authentication) - for user booking access
- M4 (Boat Management) - for bookable boats
- M5 (Search & Discovery) - for booking initiation

#### Deliverables
- Booking creation workflow
- Real-time availability checking
- Capacity validation system
- Passenger information collection
- Booking confirmation system
- Modification and cancellation flow
- Booking history and management
- Email confirmations
- Basic payment placeholder

#### Docker Integration
- **Frontend**: Booking UI flow in Next.js container
- **Backend**: Booking logic and APIs in Express container
- **Database**: Booking models and transactions in PostgreSQL
- **Email**: Booking confirmations via MailHog container
- **Cache**: Availability caching in Redis container

#### Testing Strategy
- End-to-end booking flow testing
- Concurrent booking conflict testing
- Capacity validation testing
- Email delivery verification

---

### M7: Payment Processing Integration
**Phase**: 3 | **Complexity**: High | **Team**: Backend-focused | **Duration**: 2 weeks

#### Scope & Boundaries
- Multiple payment method integration
- Full and deposit payment models
- Refund processing system
- Payment security and compliance

#### Dependencies
- M6 (Booking System) - for payment integration points

#### Deliverables
- Payment gateway integration (FPX, cards)
- Full payment and deposit workflows
- Automated refund processing
- Payment receipt generation
- Payment status tracking
- Security compliance implementation
- Payment failure handling
- Promotional discount system

#### Docker Integration
- **Backend**: Payment APIs in Express container
- **Database**: Payment records in PostgreSQL
- **Security**: Environment-based payment credentials
- **Monitoring**: Payment transaction logging

#### Testing Strategy
- Payment flow integration testing
- Refund processing validation
- Security and compliance testing
- Payment failure scenario testing

---

### M8: Affiliate System
**Phase**: 3 | **Complexity**: High | **Team**: Full-stack | **Duration**: 2 weeks

#### Scope & Boundaries
- Affiliate link generation and tracking
- Commission calculation and management
- Booking on behalf functionality
- Affiliate dashboard and reporting

#### Dependencies
- M2 (Authentication) - for affiliate user access
- M6 (Booking System) - for affiliate bookings
- M7 (Payment Processing) - for commission calculations

#### Deliverables
- Unique affiliate link generation
- Booking attribution tracking
- Commission calculation engine
- Affiliate booking workflow
- Commission dashboard
- Payout management system
- Marketing material distribution
- Performance analytics

#### Docker Integration
- **Frontend**: Affiliate dashboard in Next.js container
- **Backend**: Affiliate logic and APIs in Express container
- **Database**: Affiliate models and tracking in PostgreSQL
- **Cache**: Commission calculations in Redis container

#### Testing Strategy
- Link tracking accuracy testing
- Commission calculation validation
- Affiliate booking flow testing
- Payout system verification

---

### M9: Notification System
**Phase**: 3 | **Complexity**: Medium | **Team**: Backend-focused | **Duration**: 1 week

#### Scope & Boundaries
- Email and in-app notification system
- Automated notification triggers
- Notification preferences and management
- Real-time notification delivery

#### Dependencies
- M6 (Booking System) - for booking notifications
- M7 (Payment Processing) - for payment notifications
- M8 (Affiliate System) - for affiliate notifications

#### Deliverables
- Email notification system
- In-app notification system
- Notification templates
- Automated trigger system
- User notification preferences
- Real-time notification delivery
- Notification history and tracking

#### Docker Integration
- **Backend**: Notification service in Express container
- **Email**: SMTP integration with MailHog container
- **Database**: Notification logs in PostgreSQL
- **Queue**: Notification queue in Redis container

#### Testing Strategy
- Email delivery testing
- Notification trigger validation
- Real-time delivery testing
- Template rendering verification

---

### M10: Analytics & Reporting
**Phase**: 4 | **Complexity**: Medium | **Team**: Full-stack | **Duration**: 1.5 weeks

#### Scope & Boundaries
- Business analytics and reporting
- User behavior tracking
- Performance monitoring
- Revenue and commission reporting

#### Dependencies
- All previous modules for data collection

#### Deliverables
- Analytics dashboard
- Revenue reporting system
- User behavior tracking
- Performance metrics
- Commission reporting
- Booking analytics
- Export functionality
- Real-time monitoring

#### Docker Integration
- **Frontend**: Analytics dashboard in Next.js container
- **Backend**: Analytics APIs in Express container
- **Database**: Analytics queries in PostgreSQL
- **Cache**: Report caching in Redis container

#### Testing Strategy
- Data accuracy validation
- Report generation testing
- Performance monitoring verification
- Export functionality testing

---

### M11: Admin Panel
**Phase**: 4 | **Complexity**: Medium | **Team**: Full-stack | **Duration**: 1 week

#### Scope & Boundaries
- Administrative interface and controls
- User and content moderation
- System configuration management
- Support ticket system

#### Dependencies
- All previous modules for admin oversight

#### Deliverables
- Admin dashboard
- User management interface
- Content approval system
- System configuration panel
- Support ticket management
- Audit logging system
- Admin reporting tools

#### Docker Integration
- **Frontend**: Admin interface in Next.js container
- **Backend**: Admin APIs in Express container
- **Database**: Admin operations in PostgreSQL
- **Security**: Admin access controls

#### Testing Strategy
- Admin functionality testing
- Access control validation
- Audit logging verification
- Support system testing

---

### M12: Production Deployment
**Phase**: 4 | **Complexity**: High | **Team**: DevOps/Full-stack | **Duration**: 1.5 weeks

#### Scope & Boundaries
- Production environment setup
- CI/CD pipeline implementation
- Monitoring and logging systems
- Performance optimization

#### Dependencies
- All previous modules for complete system

#### Deliverables
- Production Docker configuration
- CI/CD pipeline
- Monitoring and alerting
- Backup and recovery systems
- Performance optimization
- Security hardening
- Documentation and runbooks

#### Docker Integration
- **Production**: Optimized production containers
- **Orchestration**: Container orchestration setup
- **Monitoring**: Logging and monitoring containers
- **Security**: Production security configuration

#### Testing Strategy
- Production deployment testing
- Performance benchmarking
- Security penetration testing
- Disaster recovery testing

## Development Timeline & Dependencies

### Parallel Development Opportunities

#### Week 1-2: Foundation Setup
```
M1: Docker Infrastructure (Week 1)
├── M2: Authentication Core (Week 2-3) [Depends on M1]
└── M3: Database Foundation (Week 2) [Depends on M1]
```

#### Week 3-6: Core Development
```
M2: Authentication Core (Week 2-3) [Continues]
M3: Database Foundation (Week 2) [Completes]
├── M4: Boat Management (Week 4-5) [Depends on M2, M3]
└── M5: Search & Discovery (Week 5-6) [Depends on M3, M4]
```

#### Week 6-10: Business Logic
```
M4: Boat Management (Week 4-5) [Continues]
M5: Search & Discovery (Week 5-6) [Continues]
└── M6: Booking System (Week 7-10) [Depends on M2, M4, M5]
```

#### Week 11-16: Advanced Features
```
M6: Booking System (Week 7-10) [Completes]
├── M7: Payment Integration (Week 11-12) [Depends on M6]
├── M8: Affiliate System (Week 13-14) [Depends on M2, M6, M7]
└── M9: Notification System (Week 15) [Depends on M6, M7, M8]
```

#### Week 17-20: Production Ready
```
All Previous Modules [Complete]
├── M10: Analytics & Reporting (Week 17-18) [Depends on all]
├── M11: Admin Panel (Week 19) [Depends on all]
└── M12: Production Deployment (Week 19-20) [Depends on all]
```

### Critical Path Analysis
**Primary Path**: M1 → M2 → M4 → M6 → M7 → M12
**Secondary Path**: M1 → M3 → M5 → M6 → M8 → M10
**Support Path**: M9 → M11 (Can be developed in parallel)

### Team Allocation Strategy

#### Frontend Team Focus
- **Weeks 1-4**: Authentication UI, User dashboards
- **Weeks 5-8**: Boat management interfaces, Search UI
- **Weeks 9-12**: Booking flow, Payment interfaces
- **Weeks 13-16**: Affiliate dashboard, Notifications UI
- **Weeks 17-20**: Analytics dashboard, Admin panel

#### Backend Team Focus
- **Weeks 1-4**: Docker setup, Auth APIs, Database models
- **Weeks 5-8**: Boat APIs, Search algorithms
- **Weeks 9-12**: Booking logic, Payment integration
- **Weeks 13-16**: Affiliate system, Notification service
- **Weeks 17-20**: Analytics APIs, Production deployment

#### Full-Stack Coordination Points
- **Week 3**: Authentication integration
- **Week 6**: Boat management integration
- **Week 10**: Booking system integration
- **Week 14**: Affiliate system integration
- **Week 18**: Analytics integration

## Integration Strategy

### Container Communication Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Database      │
│   (Next.js)     │◄──►│  (Express.js)   │◄──►│ (PostgreSQL)    │
│   Port: 3000    │    │   Port: 5000    │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         │              │     Redis       │              │
         │              │   (Caching)     │              │
         │              │   Port: 6379    │              │
         │              └─────────────────┘              │
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                        ┌─────────────────┐
                        │     SMTP        │
                        │   (MailHog)     │
                        │ Port: 1025/8025 │
                        └─────────────────┘
```

### API Integration Points

#### Module Integration Contracts

**M2 (Authentication) → All Modules**
```javascript
// JWT Token Structure
{
  userId: string,
  role: 'customer' | 'boat_owner' | 'affiliate_agent' | 'admin',
  permissions: string[],
  exp: number
}

// Auth Middleware Integration
app.use('/api/protected', authenticateToken, authorizeRole(['customer']));
```

**M4 (Boat Management) → M5 (Search), M6 (Booking)**
```javascript
// Boat Data Contract
{
  id: string,
  name: string,
  serviceType: 'snorkeling' | 'passenger',
  location: 'redang' | 'perhentian',
  capacity: number,
  pricing: PricingModel,
  availability: AvailabilityCalendar,
  amenities: string[],
  photos: string[]
}
```

**M6 (Booking) → M7 (Payment), M8 (Affiliate)**
```javascript
// Booking Data Contract
{
  id: string,
  customerId: string,
  boatId: string,
  serviceDate: Date,
  passengerCount: number,
  totalAmount: number,
  paymentStatus: 'pending' | 'partial' | 'completed',
  affiliateId?: string,
  commission?: number
}
```

### Database Integration Strategy

#### Shared Models Across Modules
```prisma
// Core models used by multiple modules
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  role        UserRole
  profile     Profile?
  bookings    Booking[]
  boats       Boat[]   // For boat owners
  affiliateLinks AffiliateLink[] // For affiliates
}

model Boat {
  id           String    @id @default(cuid())
  ownerId      String
  name         String
  serviceType  ServiceType
  location     Location
  bookings     Booking[]
  availability Availability[]
}

model Booking {
  id           String        @id @default(cuid())
  customerId   String
  boatId       String
  status       BookingStatus
  payments     Payment[]
  affiliateId  String?
}
```

### Environment Configuration Integration

#### Container Environment Variables
```yaml
# Shared across all containers
environment:
  - NODE_ENV=development
  - DATABASE_URL=*****************************************/gosea_db
  - REDIS_URL=redis://redis:6379
  - SMTP_HOST=mailhog
  - SMTP_PORT=1025

# Module-specific configurations
  - JWT_SECRET=${JWT_SECRET}
  - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
  - PAYMENT_GATEWAY_KEY=${PAYMENT_GATEWAY_KEY}
  - AFFILIATE_COMMISSION_RATE=0.10
```

## Testing Strategy

### Module-Level Testing

#### Unit Testing (Per Module)
```bash
# Frontend Testing (Jest + React Testing Library)
docker-compose exec frontend pnpm test:unit

# Backend Testing (Jest + Supertest)
docker-compose exec backend pnpm test:unit

# Database Testing (Prisma + Jest)
docker-compose exec backend pnpm test:db
```

#### Integration Testing (Cross-Module)
```bash
# API Integration Tests
docker-compose exec backend pnpm test:integration

# Frontend-Backend Integration
docker-compose exec frontend pnpm test:e2e

# Database Integration
docker-compose exec backend pnpm test:db:integration
```

### End-to-End Testing Strategy

#### E2E Test Scenarios by Phase

**Phase 1 E2E Tests**
- User registration and authentication flow
- Role-based access control validation
- Profile management functionality

**Phase 2 E2E Tests**
- Complete booking flow (search → select → book)
- Boat owner listing and management
- Real-time availability updates

**Phase 3 E2E Tests**
- Payment processing end-to-end
- Affiliate link generation and tracking
- Notification delivery verification

**Phase 4 E2E Tests**
- Complete user journeys for all roles
- Performance and load testing
- Security and penetration testing

### Continuous Integration Testing

#### Docker-Based CI Pipeline
```yaml
# .github/workflows/ci.yml
name: CI Pipeline
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build containers
        run: docker-compose build
      - name: Run unit tests
        run: |
          docker-compose run --rm frontend pnpm test:unit
          docker-compose run --rm backend pnpm test:unit
      - name: Run integration tests
        run: docker-compose run --rm backend pnpm test:integration
      - name: Run E2E tests
        run: docker-compose run --rm cypress
```

### Quality Gates by Module

#### Module Completion Criteria
1. **Unit Test Coverage**: Minimum 80% code coverage
2. **Integration Tests**: All API endpoints tested
3. **E2E Tests**: Critical user flows validated
4. **Performance Tests**: Response time < 3 seconds
5. **Security Tests**: Authentication and authorization verified
6. **Docker Tests**: Container health and connectivity verified

#### Cross-Module Integration Validation
1. **API Contract Testing**: Ensure module interfaces remain stable
2. **Database Migration Testing**: Verify schema changes don't break existing modules
3. **Environment Testing**: Validate configuration across all containers
4. **Performance Testing**: Ensure module additions don't degrade performance

This comprehensive development plan provides a structured approach to building the GoSea platform with clear module boundaries, parallel development opportunities, and robust integration within the containerized environment.
