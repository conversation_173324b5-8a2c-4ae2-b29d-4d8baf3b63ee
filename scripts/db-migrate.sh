#!/bin/bash

# GoSea Platform Database Migration Script
# This script runs database migrations

set -e

echo "🗃️  GoSea Platform Database Migration"
echo "===================================="

# Check if backend container is running
if ! docker-compose ps backend | grep -q "Up"; then
    echo "❌ Backend container is not running. Please run ./scripts/dev-setup.sh first."
    exit 1
fi

echo "🔧 Running database migrations..."
docker-compose exec backend pnpm prisma migrate dev --name init

echo "🔧 Generating Prisma client..."
docker-compose exec backend pnpm prisma generate

echo "✅ Database migrations completed successfully!"
echo ""
echo "🛠️  Database commands:"
echo "   View database: docker-compose exec backend pnpm prisma studio"
echo "   Reset database: docker-compose exec backend pnpm prisma migrate reset"
echo "   Create migration: docker-compose exec backend pnpm prisma migrate dev --name <name>"
