#!/bin/bash

# GoSea Platform Dependency Installation Script
# This script installs dependencies for both frontend and backend

set -e

echo "📦 Installing GoSea Platform Dependencies"
echo "========================================"

# Check if containers are running
if ! docker-compose ps | grep -q "Up"; then
    echo "❌ Containers are not running. Please run ./scripts/dev-setup.sh first."
    exit 1
fi

echo "🔧 Installing frontend dependencies..."
docker-compose exec frontend pnpm install

echo "🔧 Installing backend dependencies..."
docker-compose exec backend pnpm install

echo "🔧 Generating Prisma client..."
docker-compose exec backend pnpm prisma generate

echo "✅ All dependencies installed successfully!"
echo ""
echo "🛠️  Next steps:"
echo "   1. Run database migrations: ./scripts/db-migrate.sh"
echo "   2. View logs: docker-compose logs -f"
