#!/bin/bash

# GoSea Development Server Fix Script
# Resolves common Next.js Docker development issues

echo "🔧 GoSea Development Server Fix Script"
echo "======================================"

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo "❌ Docker is not running. Please start Docker first."
        exit 1
    fi
    echo "✅ Docker is running"
}

# Function to restart frontend container
restart_frontend() {
    echo "🔄 Restarting frontend container..."
    docker restart gosea-frontend
    
    # Wait for container to be healthy
    echo "⏳ Waiting for container to be ready..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker exec gosea-frontend curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
            echo "✅ Frontend container is healthy"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        echo "⚠️  Container health check timeout, but continuing..."
    fi
}

# Function to clear Next.js cache
clear_nextjs_cache() {
    echo "🧹 Clearing Next.js cache..."
    
    # Stop the container first
    docker stop gosea-frontend > /dev/null 2>&1
    
    # Remove .next directory from host if it exists
    if [ -d "./frontend/.next" ]; then
        echo "🗑️  Removing .next directory..."
        rm -rf ./frontend/.next
    fi
    
    # Remove node_modules/.cache if it exists
    if [ -d "./frontend/node_modules/.cache" ]; then
        echo "🗑️  Removing node_modules cache..."
        rm -rf ./frontend/node_modules/.cache
    fi
    
    # Start the container again
    docker start gosea-frontend > /dev/null 2>&1
}

# Function to rebuild frontend container
rebuild_frontend() {
    echo "🏗️  Rebuilding frontend container..."
    docker-compose build --no-cache frontend
    docker-compose up -d frontend
}

# Function to check container logs
check_logs() {
    echo "📋 Recent frontend container logs:"
    echo "=================================="
    docker logs gosea-frontend --tail 20
}

# Function to test the fix
test_fix() {
    echo "🧪 Testing the fix..."
    
    # Wait a moment for the server to start
    sleep 5
    
    # Test if the main page loads
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        echo "✅ Main page is accessible"
    else
        echo "❌ Main page is not accessible"
        return 1
    fi
    
    # Check for 404 errors in recent logs
    recent_404s=$(docker logs gosea-frontend --tail 50 | grep -c "404" || true)
    if [ "$recent_404s" -eq 0 ]; then
        echo "✅ No recent 404 errors found"
    else
        echo "⚠️  Found $recent_404s recent 404 errors"
    fi
}

# Main execution
main() {
    check_docker
    
    echo ""
    echo "Select fix option:"
    echo "1. Quick restart (recommended)"
    echo "2. Clear cache and restart"
    echo "3. Full rebuild"
    echo "4. Check logs only"
    echo "5. Run all fixes"
    
    read -p "Enter option (1-5): " option
    
    case $option in
        1)
            restart_frontend
            test_fix
            ;;
        2)
            clear_nextjs_cache
            restart_frontend
            test_fix
            ;;
        3)
            rebuild_frontend
            test_fix
            ;;
        4)
            check_logs
            ;;
        5)
            clear_nextjs_cache
            restart_frontend
            sleep 10
            test_fix
            if [ $? -ne 0 ]; then
                echo "🔄 Quick fixes didn't work, trying full rebuild..."
                rebuild_frontend
                test_fix
            fi
            ;;
        *)
            echo "❌ Invalid option"
            exit 1
            ;;
    esac
    
    echo ""
    echo "🎉 Fix script completed!"
    echo "💡 If issues persist, try:"
    echo "   - Clear browser cache"
    echo "   - Check Docker resources (memory/CPU)"
    echo "   - Restart Docker Desktop"
}

# Run main function
main "$@"
