#!/bin/bash

# GoSea Platform Testing Script
# This script runs tests for both frontend and backend

set -e

echo "🧪 GoSea Platform Testing"
echo "========================"

# Check if containers are running
if ! docker-compose ps | grep -q "Up"; then
    echo "❌ Containers are not running. Please run ./scripts/dev-setup.sh first."
    exit 1
fi

echo "🔧 Running frontend tests..."
docker-compose exec frontend pnpm test

echo "🔧 Running backend tests..."
docker-compose exec backend pnpm test

echo "✅ All tests completed!"
echo ""
echo "🛠️  Test commands:"
echo "   Frontend unit tests: docker-compose exec frontend pnpm test:unit"
echo "   Frontend E2E tests: docker-compose exec frontend pnpm test:e2e"
echo "   Backend unit tests: docker-compose exec backend pnpm test:unit"
echo "   Backend integration tests: docker-compose exec backend pnpm test:integration"
echo "   Test coverage: docker-compose exec [frontend|backend] pnpm test:coverage"
