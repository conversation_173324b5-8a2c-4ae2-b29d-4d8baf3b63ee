#!/bin/bash

# GoSea Platform Setup Verification Script
# This script verifies that the containerized development environment is working correctly

set -e

echo "🚢 GoSea Platform Setup Verification"
echo "===================================="

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check service health
check_service() {
    local service_name=$1
    local url=$2
    local expected_status=$3
    
    echo -n "🔍 Checking $service_name... "
    
    if curl -s -f "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ HEALTHY${NC}"
        return 0
    else
        echo -e "${RED}❌ UNHEALTHY${NC}"
        return 1
    fi
}

# Function to check container status
check_container() {
    local container_name=$1
    echo -n "🐳 Checking container $container_name... "
    
    if docker compose ps | grep -q "$container_name.*Up"; then
        echo -e "${GREEN}✅ RUNNING${NC}"
        return 0
    else
        echo -e "${RED}❌ NOT RUNNING${NC}"
        return 1
    fi
}

echo ""
echo "📋 Container Status Check"
echo "========================"

# Check all containers
check_container "gosea-frontend"
check_container "gosea-backend"
check_container "gosea-postgres"
check_container "gosea-redis"
check_container "gosea-mailhog"

echo ""
echo "🌐 Service Health Check"
echo "======================"

# Check service endpoints
check_service "Frontend" "http://localhost:3000/api/health" "200"
check_service "Backend" "http://localhost:5001/api/health" "200"
check_service "MailHog UI" "http://localhost:8025" "200"

echo ""
echo "🔌 Port Availability Check"
echo "=========================="

# Check if ports are accessible
ports=("3000:Frontend" "5001:Backend" "5432:PostgreSQL" "6379:Redis" "8025:MailHog")

for port_info in "${ports[@]}"; do
    IFS=':' read -r port service <<< "$port_info"
    echo -n "🔌 Port $port ($service)... "
    
    if nc -z localhost "$port" 2>/dev/null; then
        echo -e "${GREEN}✅ OPEN${NC}"
    else
        echo -e "${RED}❌ CLOSED${NC}"
    fi
done

echo ""
echo "📊 Database Connection Check"
echo "============================"

echo -n "🗃️  PostgreSQL connection... "
if docker compose exec -T postgres pg_isready -U gosea -d gosea_db > /dev/null 2>&1; then
    echo -e "${GREEN}✅ CONNECTED${NC}"
else
    echo -e "${RED}❌ CONNECTION FAILED${NC}"
fi

echo ""
echo "🔄 Hot Reload Test"
echo "=================="

echo -n "🔥 Testing hot reload capability... "
if [ -d "frontend/src" ] && [ -d "backend/src" ]; then
    echo -e "${GREEN}✅ CONFIGURED${NC}"
    echo "   📁 Frontend source mounted: frontend/src"
    echo "   📁 Backend source mounted: backend/src"
else
    echo -e "${RED}❌ NOT CONFIGURED${NC}"
fi

echo ""
echo "📚 Documentation Check"
echo "======================"

docs=("README.md" "memory-bank/developmentPlan.md" "memory-bank/dockerRequirements.md")

for doc in "${docs[@]}"; do
    echo -n "📄 $doc... "
    if [ -f "$doc" ]; then
        echo -e "${GREEN}✅ EXISTS${NC}"
    else
        echo -e "${RED}❌ MISSING${NC}"
    fi
done

echo ""
echo "🛠️  Development Scripts Check"
echo "============================="

scripts=("scripts/dev-setup.sh" "scripts/install-deps.sh" "scripts/db-migrate.sh" "scripts/test.sh")

for script in "${scripts[@]}"; do
    echo -n "📜 $script... "
    if [ -f "$script" ] && [ -x "$script" ]; then
        echo -e "${GREEN}✅ EXECUTABLE${NC}"
    else
        echo -e "${RED}❌ MISSING/NOT EXECUTABLE${NC}"
    fi
done

echo ""
echo "🎯 Summary"
echo "=========="

echo "📍 Service URLs:"
echo "   🌐 Frontend:  http://localhost:3000"
echo "   🔧 Backend:   http://localhost:5001"
echo "   📧 MailHog:   http://localhost:8025"
echo "   🗃️  Database:  localhost:5432"
echo "   💾 Redis:     localhost:6379"

echo ""
echo "🚀 Next Steps:"
echo "   1. Install dependencies: ./scripts/install-deps.sh"
echo "   2. Run database migrations: ./scripts/db-migrate.sh"
echo "   3. Start development: docker compose logs -f"

echo ""
echo "✅ GoSea Platform containerized development environment verification complete!"
echo "🎉 Ready for Phase 1 development: Authentication & User Management"
