#!/bin/bash

# GoSea Platform Development Setup Script
# This script sets up the development environment

set -e

echo "🚢 GoSea Platform Development Setup"
echo "=================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

echo "✅ Docker is running"

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed. Please install docker-compose and try again."
    exit 1
fi

echo "✅ docker-compose is available"

# Create .env.local if it doesn't exist
if [ ! -f .env.local ]; then
    echo "📝 Creating .env.local from template..."
    cp .env.example .env.local
    echo "✅ .env.local created. Please review and update the configuration."
else
    echo "✅ .env.local already exists"
fi

# Build and start containers
echo "🐳 Building and starting Docker containers..."
docker-compose build
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check if services are running
echo "🔍 Checking service health..."

# Check frontend
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "✅ Frontend is running on http://localhost:3000"
else
    echo "⚠️  Frontend health check failed"
fi

# Check backend
if curl -f http://localhost:5000/api/health > /dev/null 2>&1; then
    echo "✅ Backend is running on http://localhost:5000"
else
    echo "⚠️  Backend health check failed"
fi

# Check database
if docker-compose exec -T postgres pg_isready -U gosea -d gosea_db > /dev/null 2>&1; then
    echo "✅ Database is ready"
else
    echo "⚠️  Database health check failed"
fi

echo ""
echo "🎉 Development environment setup complete!"
echo ""
echo "📋 Available services:"
echo "   Frontend:  http://localhost:3000"
echo "   Backend:   http://localhost:5000"
echo "   Database:  localhost:5432"
echo "   Email UI:  http://localhost:8025"
echo "   pgAdmin:   http://localhost:8080 (optional)"
echo ""
echo "🛠️  Next steps:"
echo "   1. Install dependencies: ./scripts/install-deps.sh"
echo "   2. Run database migrations: ./scripts/db-migrate.sh"
echo "   3. Start development: docker-compose logs -f"
echo ""
echo "📚 For more information, see README.md"
