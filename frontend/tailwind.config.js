/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#0ea5e9',  // Sky 500 - Main brand color
          50: '#f0f9ff',       // Sky 50
          100: '#e0f2fe',      // Sky 100
          200: '#bae6fd',      // Sky 200
          300: '#7dd3fc',      // Sky 300
          400: '#38bdf8',      // Sky 400 - Light variant
          500: '#0ea5e9',      // Sky 500 - Main brand color
          600: '#0284c7',      // Sky 600 - Hover/Focus states
          700: '#0369a1',      // Sky 700 - Dark variant
          800: '#075985',      // Sky 800
          900: '#0c4a6e',      // Sky 900
        },
        secondary: {
          DEFAULT: '#f59e0b',  // Amber 500 - Secondary actions
          50: '#fffbeb',       // Amber 50
          100: '#fef3c7',      // Amber 100
          200: '#fde68a',      // Amber 200
          300: '#fcd34d',      // Amber 300
          400: '#fbbf24',      // Amber 400 - Light variant
          500: '#f59e0b',      // Amber 500 - Secondary actions
          600: '#d97706',      // Amber 600 - Hover/Focus states
          700: '#b45309',      // Amber 700 - Dark variant
          800: '#92400e',      // Amber 800
          900: '#78350f',      // Amber 900
        },
        accent: {
          DEFAULT: '#06b6d4',  // Cyan 500 - Highlights/Accents
          50: '#ecfeff',       // Cyan 50
          100: '#cffafe',      // Cyan 100
          200: '#a5f3fc',      // Cyan 200
          300: '#67e8f9',      // Cyan 300
          400: '#22d3ee',      // Cyan 400 - Light variant
          500: '#06b6d4',      // Cyan 500 - Highlights/Accents
          600: '#0891b2',      // Cyan 600 - Hover/Focus states
          700: '#0e7490',      // Cyan 700 - Dark variant
          800: '#155e75',      // Cyan 800
          900: '#164e63',      // Cyan 900
        },
        neutral: {
          DEFAULT: '#64748b',  // Slate 500 - Text
          50: '#f8fafc',       // Slate 50
          100: '#f1f5f9',      // Slate 100
          200: '#e2e8f0',      // Slate 200
          300: '#cbd5e1',      // Slate 300
          400: '#94a3b8',      // Slate 400 - Light text
          500: '#64748b',      // Slate 500 - Text
          600: '#475569',      // Slate 600 - Headings
          700: '#334155',      // Slate 700 - Dark text
          800: '#1e293b',      // Slate 800
          900: '#0f172a',      // Slate 900
        },
        success: '#10b981',    // Emerald 500
        warning: '#f59e0b',    // Amber 500
        error: '#ef4444',      // Red 500
        info: '#0ea5e9',       // Sky 500
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        heading: ['Poppins', 'system-ui', 'sans-serif'],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'bounce-slow': 'bounce 2s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [
    require('daisyui'),
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
  daisyui: {
    themes: [
      {
        gosea: {
          primary: '#0ea5e9',        // Sky 500 - Main brand color
          'primary-focus': '#0284c7', // Sky 600 - Hover/Focus states
          'primary-content': '#ffffff', // White - Text on primary

          secondary: '#f59e0b',      // Amber 500 - Secondary actions
          'secondary-focus': '#d97706', // Amber 600 - Hover/Focus states
          'secondary-content': '#ffffff', // White - Text on secondary

          accent: '#06b6d4',         // Cyan 500 - Highlights/Accents
          'accent-focus': '#0891b2', // Cyan 600 - Hover/Focus states
          'accent-content': '#ffffff', // White - Text on accent

          neutral: '#64748b',        // Slate 500 - Text
          'neutral-focus': '#475569', // Slate 600 - Headings
          'neutral-content': '#ffffff', // White - Text on neutral

          'base-100': '#ffffff',     // White - Main background
          'base-200': '#f0f9ff',     // Sky 50 - Secondary background
          'base-300': '#e0f2fe',     // Sky 100 - Tertiary background
          'base-content': '#0f172a', // Slate 900 - Main text color

          info: '#0ea5e9',           // Sky 500 - Information states
          'info-content': '#ffffff', // White - Text on info
          success: '#10b981',        // Emerald 500 - Success states
          'success-content': '#ffffff', // White - Text on success
          warning: '#f59e0b',        // Amber 500 - Warning states
          'warning-content': '#ffffff', // White - Text on warning
          error: '#ef4444',          // Red 500 - Error states
          'error-content': '#ffffff', // White - Text on error
        },
      },
      'light',
      'dark',
    ],
    base: true,
    styled: true,
    utils: true,
    rtl: false,
    prefix: '',
    logs: false,
  },
};
