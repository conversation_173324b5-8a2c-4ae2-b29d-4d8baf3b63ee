version: '3.8'

services:
  gosea-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: gosea-frontend-prod
    ports:
      - "3003:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
    networks:
      - gosea-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  gosea-network:
    driver: bridge
