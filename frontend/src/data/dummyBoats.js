/**
 * Dummy boat data for development and testing
 * This will be replaced with real API data in production
 */

export const dummyBoats = [
  {
    id: 'boat-001',
    name: 'Ocean Explorer',
    description: 'A spacious and comfortable boat perfect for snorkeling adventures around Redang Island. Features modern safety equipment and experienced crew.',
    serviceType: 'SNORKELING',
    location: 'REDANG',
    capacity: 12,
    basePrice: 280,
    photos: [
      {
        id: 'photo-001',
        url: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        alt: 'Ocean Explorer - Main View'
      },
      {
        id: 'photo-002', 
        url: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        alt: 'Ocean Explorer - Interior'
      }
    ],
    packages: [
      {
        id: 'pkg-001',
        name: 'Half Day Snorkeling',
        description: 'Perfect introduction to Redang\'s marine life',
        price: 280,
        duration: '4 hours',
        includedItems: ['Snorkeling gear', 'Life jackets', 'Refreshments', 'Professional guide']
      }
    ],
    amenities: [
      { id: 'am-001', name: 'Air Conditioning', description: 'Climate controlled cabin' },
      { id: 'am-002', name: 'Safety Equipment', description: 'Life jackets and emergency gear' },
      { id: 'am-003', name: 'Snorkeling Gear', description: 'Masks, fins, and snorkels provided' }
    ],
    owner: {
      id: 'owner-001',
      name: 'Captain Ahmad'
    }
  },
  {
    id: 'boat-002',
    name: 'Island Hopper',
    description: 'Fast and reliable passenger boat for island transfers and sightseeing tours around Perhentian Islands.',
    serviceType: 'PASSENGER',
    location: 'PERHENTIAN',
    capacity: 20,
    basePrice: 150,
    photos: [
      {
        id: 'photo-003',
        url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        alt: 'Island Hopper - Main View'
      }
    ],
    packages: [
      {
        id: 'pkg-002',
        name: 'Island Transfer',
        description: 'Quick and comfortable island transportation',
        price: 150,
        duration: '1 hour',
        includedItems: ['Life jackets', 'Comfortable seating', 'Weather protection']
      }
    ],
    amenities: [
      { id: 'am-004', name: 'Comfortable Seating', description: 'Cushioned seats for all passengers' },
      { id: 'am-005', name: 'Weather Protection', description: 'Covered seating area' }
    ],
    owner: {
      id: 'owner-002',
      name: 'Captain Lim'
    }
  },
  {
    id: 'boat-003',
    name: 'Coral Discovery',
    description: 'Specialized snorkeling vessel with underwater viewing windows and premium snorkeling equipment.',
    serviceType: 'SNORKELING',
    location: 'REDANG',
    capacity: 8,
    basePrice: 450,
    photos: [
      {
        id: 'photo-004',
        url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        alt: 'Coral Discovery - Main View'
      }
    ],
    packages: [
      {
        id: 'pkg-003',
        name: 'Premium Snorkeling Experience',
        description: 'Luxury snorkeling with professional underwater photography',
        price: 450,
        duration: '6 hours',
        includedItems: ['Premium snorkeling gear', 'Underwater camera', 'Lunch', 'Professional guide', 'Underwater photos']
      }
    ],
    amenities: [
      { id: 'am-006', name: 'Underwater Windows', description: 'View marine life from inside the boat' },
      { id: 'am-007', name: 'Premium Equipment', description: 'High-quality snorkeling gear' },
      { id: 'am-008', name: 'Photography Service', description: 'Professional underwater photography' }
    ],
    owner: {
      id: 'owner-003',
      name: 'Captain Sarah'
    }
  },
  {
    id: 'boat-004',
    name: 'Sea Breeze',
    description: 'Family-friendly passenger boat perfect for day trips and island exploration around Perhentian.',
    serviceType: 'PASSENGER',
    location: 'PERHENTIAN',
    capacity: 15,
    basePrice: 200,
    photos: [
      {
        id: 'photo-005',
        url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        alt: 'Sea Breeze - Main View'
      }
    ],
    packages: [
      {
        id: 'pkg-004',
        name: 'Family Day Trip',
        description: 'Perfect for families with children',
        price: 200,
        duration: '3 hours',
        includedItems: ['Life jackets', 'Snacks', 'Drinks', 'Family-friendly guide']
      }
    ],
    amenities: [
      { id: 'am-009', name: 'Family Seating', description: 'Spacious seating for families' },
      { id: 'am-010', name: 'Safety Rails', description: 'Extra safety features for children' }
    ],
    owner: {
      id: 'owner-004',
      name: 'Captain Wong'
    }
  },
  {
    id: 'boat-005',
    name: 'Reef Explorer',
    description: 'Advanced snorkeling boat with glass bottom viewing and professional marine biologist guide.',
    serviceType: 'SNORKELING',
    location: 'PERHENTIAN_ISLAND',
    capacity: 10,
    basePrice: 380,
    photos: [
      {
        id: 'photo-006',
        url: 'https://images.unsplash.com/photo-1566024287286-457247b70310?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        alt: 'Reef Explorer - Main View'
      }
    ],
    packages: [
      {
        id: 'pkg-005',
        name: 'Educational Snorkeling Tour',
        description: 'Learn about marine life with expert guide',
        price: 380,
        duration: '5 hours',
        includedItems: ['Professional snorkeling gear', 'Marine biologist guide', 'Educational materials', 'Light lunch']
      }
    ],
    amenities: [
      { id: 'am-011', name: 'Glass Bottom', description: 'View coral reefs from the boat' },
      { id: 'am-012', name: 'Expert Guide', description: 'Marine biologist on board' }
    ],
    owner: {
      id: 'owner-005',
      name: 'Dr. Marina'
    }
  },
  {
    id: 'boat-006',
    name: 'Swift Current',
    description: 'High-speed passenger boat for quick transfers between islands and mainland.',
    serviceType: 'PASSENGER_BOAT',
    location: 'REDANG_ISLAND',
    capacity: 18,
    basePrice: 180,
    photos: [
      {
        id: 'photo-007',
        url: 'https://images.unsplash.com/photo-1520637836862-4d197d17c90a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        alt: 'Swift Current - Main View'
      }
    ],
    packages: [
      {
        id: 'pkg-006',
        name: 'Express Transfer',
        description: 'Fast and efficient island transportation',
        price: 180,
        duration: '45 minutes',
        includedItems: ['High-speed transfer', 'Life jackets', 'Comfortable seating']
      }
    ],
    amenities: [
      { id: 'am-013', name: 'High Speed', description: 'Fast transportation' },
      { id: 'am-014', name: 'Smooth Ride', description: 'Stable hull design' }
    ],
    owner: {
      id: 'owner-006',
      name: 'Captain Raj'
    }
  },
  {
    id: 'boat-007',
    name: 'Turtle Bay',
    description: 'Eco-friendly snorkeling boat specializing in turtle watching and coral conservation tours.',
    serviceType: 'SNORKELING',
    location: 'REDANG_ISLAND',
    capacity: 6,
    basePrice: 500,
    photos: [
      {
        id: 'photo-008',
        url: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        alt: 'Turtle Bay - Main View'
      }
    ],
    packages: [
      {
        id: 'pkg-007',
        name: 'Turtle Conservation Tour',
        description: 'Exclusive turtle watching experience',
        price: 500,
        duration: '7 hours',
        includedItems: ['Eco-friendly gear', 'Turtle watching guide', 'Conservation education', 'Organic lunch', 'Certificate']
      }
    ],
    amenities: [
      { id: 'am-015', name: 'Eco-Friendly', description: 'Environmentally conscious operations' },
      { id: 'am-016', name: 'Turtle Specialist', description: 'Expert turtle watching guide' }
    ],
    owner: {
      id: 'owner-007',
      name: 'Captain Green'
    }
  },
  {
    id: 'boat-008',
    name: 'Paradise Cruiser',
    description: 'Luxury passenger boat with premium amenities for comfortable island cruising.',
    serviceType: 'PASSENGER_BOAT',
    location: 'PERHENTIAN_ISLAND',
    capacity: 12,
    basePrice: 350,
    photos: [
      {
        id: 'photo-009',
        url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        alt: 'Paradise Cruiser - Main View'
      }
    ],
    packages: [
      {
        id: 'pkg-008',
        name: 'Luxury Island Cruise',
        description: 'Premium cruising experience with gourmet dining',
        price: 350,
        duration: '4 hours',
        includedItems: ['Luxury seating', 'Gourmet lunch', 'Premium drinks', 'Entertainment system']
      }
    ],
    amenities: [
      { id: 'am-017', name: 'Luxury Interior', description: 'Premium cabin with AC' },
      { id: 'am-018', name: 'Gourmet Catering', description: 'High-quality food and beverages' }
    ],
    owner: {
      id: 'owner-008',
      name: 'Captain Luxury'
    }
  },
  {
    id: 'boat-009',
    name: 'Adventure Seeker',
    description: 'Multi-activity boat perfect for snorkeling, diving, and water sports adventures.',
    serviceType: 'SNORKELING',
    location: 'PERHENTIAN_ISLAND',
    capacity: 14,
    basePrice: 320,
    photos: [
      {
        id: 'photo-010',
        url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        alt: 'Adventure Seeker - Main View'
      }
    ],
    packages: [
      {
        id: 'pkg-009',
        name: 'Multi-Activity Adventure',
        description: 'Snorkeling, kayaking, and water sports',
        price: 320,
        duration: '6 hours',
        includedItems: ['Snorkeling gear', 'Kayaks', 'Water sports equipment', 'Adventure guide', 'Lunch']
      }
    ],
    amenities: [
      { id: 'am-019', name: 'Water Sports Equipment', description: 'Kayaks and water toys' },
      { id: 'am-020', name: 'Adventure Guide', description: 'Experienced activity coordinator' }
    ],
    owner: {
      id: 'owner-009',
      name: 'Captain Adventure'
    }
  },
  {
    id: 'boat-010',
    name: 'Sunset Express',
    description: 'Elegant passenger boat specializing in sunset cruises and romantic evening trips.',
    serviceType: 'PASSENGER_BOAT',
    location: 'REDANG_ISLAND',
    capacity: 16,
    basePrice: 250,
    photos: [
      {
        id: 'photo-011',
        url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        alt: 'Sunset Express - Main View'
      }
    ],
    packages: [
      {
        id: 'pkg-010',
        name: 'Romantic Sunset Cruise',
        description: 'Perfect for couples and special occasions',
        price: 250,
        duration: '2.5 hours',
        includedItems: ['Sunset viewing', 'Romantic ambiance', 'Light refreshments', 'Photography service']
      }
    ],
    amenities: [
      { id: 'am-021', name: 'Romantic Setting', description: 'Perfect for couples' },
      { id: 'am-022', name: 'Sunset Views', description: 'Best sunset viewing spots' }
    ],
    owner: {
      id: 'owner-010',
      name: 'Captain Romance'
    }
  }
];

// Helper function to filter boats based on search criteria
export const filterBoats = (boats, filters) => {
  return boats.filter(boat => {
    // Service type filter
    if (filters.serviceType && boat.serviceType !== filters.serviceType) {
      return false;
    }

    // Location filter
    if (filters.location && boat.location !== filters.location) {
      return false;
    }

    // Legacy capacity filter (removed from UI but kept for backward compatibility)
    if (filters.capacity) {
      const requiredCapacity = parseInt(filters.capacity);
      if (boat.capacity < requiredCapacity) {
        return false;
      }
    }

    // DateTime filter would require availability data
    // For now, we'll assume all boats are available for any selected datetime
    // In a real application, this would check against boat availability schedules

    return true;
  });
};

export default dummyBoats;
