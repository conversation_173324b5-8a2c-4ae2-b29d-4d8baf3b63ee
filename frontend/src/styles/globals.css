@tailwind base;
@tailwind components;
@tailwind utilities;

/* GoSea Design System CSS Variables */
:root {
  /* Primary Colors */
  --primary: #0ea5e9;      /* Sky 500 - Main brand color */
  --primary-focus: #0284c7; /* Sky 600 - Hover/Focus states */
  --primary-light: #38bdf8; /* Sky 400 - Light variant */
  --primary-dark: #0369a1;  /* Sky 700 - Dark variant */
  --primary-content: #ffffff; /* White - Text on primary */

  /* Secondary Colors */
  --secondary: #f59e0b;     /* Amber 500 - Secondary actions */
  --secondary-focus: #d97706; /* Amber 600 - Hover/Focus states */
  --secondary-light: #fbbf24; /* Amber 400 - Light variant */
  --secondary-dark: #b45309;  /* Amber 700 - Dark variant */
  --secondary-content: #ffffff; /* White - Text on secondary */

  /* Accent Colors */
  --accent: #06b6d4;        /* Cyan 500 - Highlights/Accents */
  --accent-focus: #0891b2;  /* <PERSON>an 600 - Hover/Focus states */
  --accent-light: #22d3ee;  /* Cyan 400 - Light variant */
  --accent-dark: #0e7490;   /* Cyan 700 - Dark variant */
  --accent-content: #ffffff; /* White - Text on accent */

  /* Neutral Colors */
  --neutral: #64748b;       /* Slate 500 - Text */
  --neutral-focus: #475569; /* Slate 600 - Headings */
  --neutral-light: #94a3b8; /* Slate 400 - Light text */
  --neutral-dark: #334155;  /* Slate 700 - Dark text */
  --neutral-content: #ffffff; /* White - Text on neutral */

  /* State Colors */
  --success: #10b981;       /* Emerald 500 - Success states */
  --success-content: #ffffff; /* White - Text on success */
  --warning: #f59e0b;       /* Amber 500 - Warning states */
  --warning-content: #ffffff; /* White - Text on warning */
  --error: #ef4444;         /* Red 500 - Error states */
  --error-content: #ffffff;  /* White - Text on error */
  --info: #0ea5e9;          /* Sky 500 - Information states */
  --info-content: #ffffff;   /* White - Text on info */

  /* Background Colors */
  --base-100: #ffffff;     /* White - Main background */
  --base-200: #f0f9ff;    /* Sky 50 - Secondary background */
  --base-300: #e0f2fe;    /* Sky 100 - Tertiary background */
  --base-400: #bae6fd;    /* Sky 200 - Quaternary background */
  --base-content: #0f172a; /* Slate 900 - Main text color */
  --base-content-light: #475569; /* Slate 600 - Secondary text */

  /* Typography */
  --font-primary: 'Inter', sans-serif;   /* Main text */
  --font-display: 'Poppins', sans-serif; /* Headings */

  /* Transitions */
  --transition-all: all 0.3s ease;
  --transition-colors: background-color, border-color, color, fill, stroke 0.3s ease;
  --transition-opacity: opacity 0.3s ease;
  --transition-transform: transform 0.3s ease;
}

/* Custom CSS for GoSea Platform */

/* Base styles */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
}

/* Custom component styles */
@layer components {
  .btn-primary {
    @apply bg-amber-600 hover:bg-amber-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-accent {
    @apply bg-amber-600 hover:bg-amber-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-outline {
    @apply border-2 border-amber-600 text-amber-600 hover:bg-amber-600 hover:text-white font-medium py-2 px-4 rounded-lg transition-all duration-200;
  }
  
  .btn-success {
    @apply border border-green-600 text-green-600 hover:bg-green-600 hover:text-white font-medium py-1 px-2 rounded transition-all duration-200 text-xs;
  }
  
  .btn-error {
    @apply border border-red-600 text-red-600 hover:bg-red-600 hover:text-white font-medium py-1 px-2 rounded transition-all duration-200 text-xs;
  }
  
  .btn-warning {
    @apply border border-amber-600 text-amber-600 hover:bg-amber-600 hover:text-white font-medium py-1 px-2 rounded transition-all duration-200 text-xs;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden;
  }
  
  .card-body {
    @apply p-6;
  }
  
  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  
  /* Admin dashboard tab styles */
  .admin-tab {
    @apply flex items-center py-4 px-2 sm:px-1 border-b-2 font-medium text-sm whitespace-nowrap;
    border-color: transparent;
    color: var(--neutral);
  }
  
  .admin-tab:hover {
    color: #f59e0b;
    border-color: #f59e0b;
  }
  
  .admin-tab:hover svg {
    color: #f59e0b;
  }
  
  .admin-tab.active {
    color: #f59e0b;
    border-color: #f59e0b;
  }
  
  .admin-tab.active svg {
    color: #f59e0b;
  }
  
  /* Admin dashboard button styles */
  .admin-btn {
    @apply inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded;
  }
  
  .admin-btn-success {
    @apply admin-btn bg-white border-green-600 text-green-600 hover:bg-green-600 hover:text-white hover:border-green-600;
  }
  
  .admin-btn-error {
    @apply admin-btn bg-white border-red-600 text-red-600 hover:bg-red-600 hover:text-white hover:border-red-600;
  }
  
  .admin-btn-warning {
    @apply admin-btn bg-white border-amber-600 text-amber-600 hover:bg-amber-600 hover:text-white hover:border-amber-600;
  }
  
  .admin-btn-info {
    @apply admin-btn bg-white border-sky-600 text-sky-600 hover:bg-sky-600 hover:text-white hover:border-sky-600;
  }
}

/* Custom utilities */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .animation-delay-400 {
    animation-delay: 400ms;
  }
}

/* Loading animations */
.loading-spinner {
  @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
}

.loading-spinner-white {
  @apply loading-spinner border-white;
}

.loading-spinner-current {
  @apply loading-spinner border-current;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}

/* Focus styles for accessibility */
.focus-visible {
  @apply outline-none ring-2 ring-amber-500 ring-offset-2;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
