/**
 * Test file for validation utilities
 * Run this to verify that validation functions work correctly
 */

import { validateMalaysianPhoneNumber } from './validation.js';

// Test cases for Malaysian phone number validation
const testCases = [
  // Valid formats
  { phone: '+60123456789', expected: true, description: 'International format with +60' },
  { phone: '60123456789', expected: true, description: 'International format without +' },
  { phone: '0123456789', expected: true, description: 'Local format with 0' },
  { phone: '123456789', expected: true, description: 'Mobile number only' },
  { phone: '+601234567890', expected: true, description: 'International format with 10 digits' },
  { phone: '01234567890', expected: true, description: 'Local format with 10 digits' },
  { phone: '+60 123 456 789', expected: true, description: 'With spaces' },
  { phone: '+60-123-456-789', expected: true, description: 'With dashes' },
  { phone: '+60(123)456789', expected: true, description: 'With parentheses' },
  
  // Invalid formats
  { phone: '', expected: false, description: 'Empty string' },
  { phone: null, expected: false, description: 'Null value' },
  { phone: undefined, expected: false, description: 'Undefined value' },
  { phone: '+60012345678', expected: false, description: 'Starting with 0 after +60' },
  { phone: '60012345678', expected: false, description: 'Starting with 0 after 60' },
  { phone: '+6012345678', expected: false, description: 'Too short (8 digits)' },
  { phone: '+601234567890123', expected: false, description: 'Too long (13 digits)' },
  { phone: '+65123456789', expected: false, description: 'Singapore number (+65)' },
  { phone: '123456', expected: false, description: 'Too short' },
  { phone: 'abc123456789', expected: false, description: 'Contains letters' },
  { phone: '+60abc456789', expected: false, description: 'Contains letters in number' }
];

// Function to run tests
function runValidationTests() {
  console.log('🧪 Running Malaysian Phone Number Validation Tests...\n');
  
  let passed = 0;
  let failed = 0;
  
  testCases.forEach((testCase, index) => {
    const result = validateMalaysianPhoneNumber(testCase.phone);
    const success = result === testCase.expected;
    
    if (success) {
      console.log(`✅ Test ${index + 1}: ${testCase.description}`);
      console.log(`   Input: "${testCase.phone}" → Expected: ${testCase.expected}, Got: ${result}\n`);
      passed++;
    } else {
      console.log(`❌ Test ${index + 1}: ${testCase.description}`);
      console.log(`   Input: "${testCase.phone}" → Expected: ${testCase.expected}, Got: ${result}\n`);
      failed++;
    }
  });
  
  console.log(`📊 Test Results: ${passed} passed, ${failed} failed out of ${testCases.length} tests`);
  
  if (failed === 0) {
    console.log('🎉 All tests passed! Malaysian phone number validation is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please check the validation logic.');
  }
  
  return failed === 0;
}

// Export for use in other files
export { runValidationTests };

// Run tests if this file is executed directly (for Node.js environment)
if (typeof window === 'undefined' && typeof process !== 'undefined') {
  runValidationTests();
}
