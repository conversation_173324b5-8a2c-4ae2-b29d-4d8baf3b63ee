/**
 * Validation Utilities for GoSea Platform
 * Centralized validation functions for form inputs and data validation
 */

/**
 * Validate Malaysian phone number format
 * Supports multiple formats: +60123456789, 60123456789, 0123456789, 123456789
 * @param {string} phone - Phone number to validate
 * @returns {boolean} - True if valid Malaysian phone number
 */
export const validateMalaysianPhoneNumber = (phone) => {
  if (!phone || !phone.trim()) {
    return false;
  }

  // Remove all spaces, dashes, and parentheses
  const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
  
  // Malaysian phone number patterns
  const patterns = [
    /^\+60[1-9][0-9]{7,9}$/,  // +60123456789 (international format)
    /^60[1-9][0-9]{7,9}$/,    // 60123456789 (without + prefix)
    /^0[1-9][0-9]{7,9}$/,     // 0123456789 (local format)
    /^[1-9][0-9]{7,9}$/       // 123456789 (mobile number only)
  ];
  
  return patterns.some(pattern => pattern.test(cleanPhone));
};

/**
 * Validate email format
 * @param {string} email - Email address to validate
 * @returns {boolean} - True if valid email format
 */
export const validateEmail = (email) => {
  if (!email || !email.trim()) {
    return false;
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
};

/**
 * Validate password strength
 * Requirements: At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character
 * @param {string} password - Password to validate
 * @returns {Object} - Validation result with details
 */
export const validatePasswordStrength = (password) => {
  if (!password) {
    return {
      isValid: false,
      errors: ['Password is required'],
      strength: 'weak'
    };
  }

  const errors = [];
  const minLength = 8;
  
  // Check length
  if (password.length < minLength) {
    errors.push(`Password must be at least ${minLength} characters long`);
  }
  
  // Check for uppercase letter
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  // Check for lowercase letter
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  // Check for number
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  // Check for special character
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  // Calculate strength
  let strength = 'weak';
  if (errors.length === 0) {
    if (password.length >= 12) {
      strength = 'strong';
    } else if (password.length >= 10) {
      strength = 'medium';
    } else {
      strength = 'fair';
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    strength
  };
};

/**
 * Validate required field
 * @param {string} value - Value to validate
 * @param {string} fieldName - Name of the field for error message
 * @returns {string|null} - Error message or null if valid
 */
export const validateRequired = (value, fieldName) => {
  if (!value || !value.toString().trim()) {
    return `${fieldName} is required`;
  }
  return null;
};

/**
 * Validate name field (first name, last name)
 * @param {string} name - Name to validate
 * @param {string} fieldName - Name of the field for error message
 * @returns {string|null} - Error message or null if valid
 */
export const validateName = (name, fieldName) => {
  if (!name || !name.trim()) {
    return `${fieldName} is required`;
  }
  
  if (name.trim().length < 2) {
    return `${fieldName} must be at least 2 characters long`;
  }
  
  if (name.trim().length > 50) {
    return `${fieldName} must be less than 50 characters long`;
  }
  
  // Check for valid name characters (letters, spaces, hyphens, apostrophes)
  if (!/^[a-zA-Z\s\-']+$/.test(name.trim())) {
    return `${fieldName} can only contain letters, spaces, hyphens, and apostrophes`;
  }
  
  return null;
};

/**
 * Validate date of birth
 * @param {string|Date} dateOfBirth - Date of birth to validate
 * @returns {string|null} - Error message or null if valid
 */
export const validateDateOfBirth = (dateOfBirth) => {
  if (!dateOfBirth) {
    return null; // Optional field
  }
  
  const birthDate = new Date(dateOfBirth);
  const today = new Date();
  
  // Check if valid date
  if (isNaN(birthDate.getTime())) {
    return 'Please enter a valid date of birth';
  }
  
  // Check if not in the future
  if (birthDate > today) {
    return 'Date of birth cannot be in the future';
  }
  
  // Check reasonable age range (13-120 years)
  const age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  const dayDiff = today.getDate() - birthDate.getDate();
  
  let actualAge = age;
  if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
    actualAge--;
  }
  
  if (actualAge < 13) {
    return 'You must be at least 13 years old to register';
  }
  
  if (actualAge > 120) {
    return 'Please enter a valid date of birth';
  }
  
  return null;
};

/**
 * Validate postcode (Malaysian format)
 * @param {string} postcode - Postcode to validate
 * @returns {string|null} - Error message or null if valid
 */
export const validatePostcode = (postcode) => {
  if (!postcode || !postcode.trim()) {
    return null; // Optional field
  }
  
  // Malaysian postcode format: 5 digits
  const postcodeRegex = /^\d{5}$/;
  if (!postcodeRegex.test(postcode.trim())) {
    return 'Please enter a valid 5-digit postcode';
  }
  
  return null;
};

/**
 * Validate text field with length constraints
 * @param {string} value - Value to validate
 * @param {string} fieldName - Name of the field for error message
 * @param {number} minLength - Minimum length (default: 0)
 * @param {number} maxLength - Maximum length (default: 255)
 * @returns {string|null} - Error message or null if valid
 */
export const validateTextLength = (value, fieldName, minLength = 0, maxLength = 255) => {
  if (!value || !value.trim()) {
    if (minLength > 0) {
      return `${fieldName} is required`;
    }
    return null; // Optional field
  }
  
  const trimmedValue = value.trim();
  
  if (trimmedValue.length < minLength) {
    return `${fieldName} must be at least ${minLength} characters long`;
  }
  
  if (trimmedValue.length > maxLength) {
    return `${fieldName} must be less than ${maxLength} characters long`;
  }
  
  return null;
};

/**
 * Validate capacity (number of people)
 * @param {string|number} capacity - Capacity to validate
 * @returns {string|null} - Error message or null if valid
 */
export const validateCapacity = (capacity) => {
  if (!capacity) {
    return null; // Optional field
  }
  
  const numCapacity = parseInt(capacity);
  
  if (isNaN(numCapacity) || numCapacity < 1) {
    return 'Capacity must be at least 1 person';
  }
  
  if (numCapacity > 100) {
    return 'Capacity cannot exceed 100 people';
  }
  
  return null;
};

/**
 * Validate price (monetary value)
 * @param {string|number} price - Price to validate
 * @returns {string|null} - Error message or null if valid
 */
export const validatePrice = (price) => {
  if (!price) {
    return 'Price is required';
  }
  
  const numPrice = parseFloat(price);
  
  if (isNaN(numPrice) || numPrice < 0) {
    return 'Price must be a valid positive number';
  }
  
  if (numPrice > 999999.99) {
    return 'Price cannot exceed RM 999,999.99';
  }
  
  return null;
};

// Export all validation functions as default object for convenience
export default {
  validateMalaysianPhoneNumber,
  validateEmail,
  validatePasswordStrength,
  validateRequired,
  validateName,
  validateDateOfBirth,
  validatePostcode,
  validateTextLength,
  validateCapacity,
  validatePrice
};
