/**
 * Test for package inclusions overflow UI enhancement
 * This test verifies that packages with 6+ included items display
 * a "+X more" indicator with hover tooltip functionality
 */

describe('Package Inclusions Overflow UI Enhancement', () => {
  describe('Inclusion Display Logic', () => {
    it('should display all items when 6 or fewer items', () => {
      const packageWith6Items = {
        includedItems: [
          'Item 1', 'Item 2', 'Item 3', 'Item 4', 'Item 5', 'Item 6'
        ]
      };

      const packageWith4Items = {
        includedItems: [
          'Item 1', 'Item 2', 'Item 3', 'Item 4'
        ]
      };

      // Should show all items when 6 or fewer
      expect(packageWith6Items.includedItems.length).toBe(6);
      expect(packageWith6Items.includedItems.length <= 6).toBe(true);
      
      expect(packageWith4Items.includedItems.length).toBe(4);
      expect(packageWith4Items.includedItems.length <= 6).toBe(true);
    });

    it('should show overflow indicator when more than 6 items', () => {
      const packageWith10Items = {
        includedItems: [
          'Item 1', 'Item 2', 'Item 3', 'Item 4', 'Item 5', 'Item 6',
          'Item 7', 'Item 8', 'Item 9', 'Item 10'
        ]
      };

      const packageWith15Items = {
        includedItems: [
          'Professional snorkeling equipment',
          'Wetsuit',
          'Marine guide',
          'Underwater photos',
          'Premium refreshments',
          'Gourmet lunch',
          'Towels and changing facilities',
          'Complimentary transport',
          'Professional videography',
          'Souvenir package',
          'Insurance coverage',
          'Equipment sanitization',
          'Private boat access',
          'Exclusive reef locations',
          'Personal photographer'
        ]
      };

      // Should show overflow indicator
      expect(packageWith10Items.includedItems.length > 6).toBe(true);
      expect(packageWith15Items.includedItems.length > 6).toBe(true);

      // Calculate overflow count
      const overflow10 = packageWith10Items.includedItems.length - 6;
      const overflow15 = packageWith15Items.includedItems.length - 6;

      expect(overflow10).toBe(4);
      expect(overflow15).toBe(9);
    });

    it('should correctly slice items for display', () => {
      const packageItems = [
        'Item 1', 'Item 2', 'Item 3', 'Item 4', 'Item 5', 'Item 6',
        'Item 7', 'Item 8', 'Item 9', 'Item 10'
      ];

      // First 6 items should be displayed
      const displayedItems = packageItems.slice(0, 6);
      const hiddenItems = packageItems.slice(6);

      expect(displayedItems).toHaveLength(6);
      expect(displayedItems).toEqual([
        'Item 1', 'Item 2', 'Item 3', 'Item 4', 'Item 5', 'Item 6'
      ]);

      expect(hiddenItems).toHaveLength(4);
      expect(hiddenItems).toEqual([
        'Item 7', 'Item 8', 'Item 9', 'Item 10'
      ]);
    });
  });

  describe('Tooltip Content Generation', () => {
    it('should generate correct tooltip content for overflow items', () => {
      const luxuryPackageItems = [
        'Professional snorkeling equipment',
        'Wetsuit',
        'Marine guide',
        'Underwater photos',
        'Premium refreshments',
        'Gourmet lunch',
        'Towels and changing facilities',
        'Complimentary transport',
        'Professional videography',
        'Souvenir package',
        'Insurance coverage',
        'Equipment sanitization',
        'Private boat access',
        'Exclusive reef locations',
        'Personal photographer'
      ];

      const displayedItems = luxuryPackageItems.slice(0, 6);
      const tooltipItems = luxuryPackageItems.slice(6);

      expect(displayedItems).toHaveLength(6);
      expect(tooltipItems).toHaveLength(9);

      // Verify tooltip items
      expect(tooltipItems).toEqual([
        'Towels and changing facilities',
        'Complimentary transport',
        'Professional videography',
        'Souvenir package',
        'Insurance coverage',
        'Equipment sanitization',
        'Private boat access',
        'Exclusive reef locations',
        'Personal photographer'
      ]);
    });

    it('should handle edge cases correctly', () => {
      // Exactly 7 items (1 overflow)
      const package7Items = {
        includedItems: ['Item 1', 'Item 2', 'Item 3', 'Item 4', 'Item 5', 'Item 6', 'Item 7']
      };

      const shouldShowOverflow = package7Items.includedItems.length > 6;
      const overflowCount = package7Items.includedItems.length - 6;

      expect(shouldShowOverflow).toBe(true);
      expect(overflowCount).toBe(1);

      // Empty array
      const emptyPackage = { includedItems: [] };
      expect(emptyPackage.includedItems.length > 6).toBe(false);

      // Null items
      const nullPackage = { includedItems: null };
      expect((nullPackage.includedItems || []).length > 6).toBe(false);
    });
  });

  describe('Responsive Design Considerations', () => {
    it('should handle long item names gracefully', () => {
      const packageWithLongNames = {
        includedItems: [
          'Professional underwater photography equipment with waterproof housing',
          'Premium wetsuit with thermal protection and comfort padding',
          'Certified marine biology guide with extensive reef knowledge',
          'High-resolution underwater photos delivered digitally within 24 hours',
          'Gourmet refreshments including tropical fruits and premium beverages',
          'Multi-course lunch featuring local seafood and vegetarian options',
          'Luxury towels and private changing facilities with lockers',
          'Door-to-door transportation in air-conditioned vehicles',
          'Professional videography with drone footage and editing services'
        ]
      };

      // Should still work with long names
      expect(packageWithLongNames.includedItems.length > 6).toBe(true);
      const displayedItems = packageWithLongNames.includedItems.slice(0, 6);
      const tooltipItems = packageWithLongNames.includedItems.slice(6);

      expect(displayedItems).toHaveLength(6);
      expect(tooltipItems).toHaveLength(3);
    });

    it('should validate CSS classes for responsive design', () => {
      // Test the CSS classes used for responsive design
      const responsiveClasses = {
        container: 'flex flex-wrap gap-1',
        item: 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800',
        overflow: 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 cursor-help',
        tooltip: 'absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 whitespace-nowrap max-w-xs'
      };

      // Verify responsive classes are present
      expect(responsiveClasses.container).toContain('flex-wrap');
      expect(responsiveClasses.item).toContain('text-xs');
      expect(responsiveClasses.overflow).toContain('cursor-help');
      expect(responsiveClasses.tooltip).toContain('max-w-xs');
      expect(responsiveClasses.tooltip).toContain('group-hover:opacity-100');
    });
  });

  describe('Accessibility Considerations', () => {
    it('should include proper accessibility attributes', () => {
      // The overflow indicator should have cursor-help for accessibility
      const overflowClasses = 'cursor-help';
      expect(overflowClasses).toBe('cursor-help');

      // Tooltip should be properly positioned and readable
      const tooltipClasses = 'bg-gray-900 text-white text-xs';
      expect(tooltipClasses).toContain('text-white'); // Good contrast
      expect(tooltipClasses).toContain('bg-gray-900'); // Dark background
    });

    it('should handle keyboard navigation considerations', () => {
      // The tooltip should be accessible via hover
      const hoverClasses = 'group-hover:opacity-100';
      expect(hoverClasses).toBe('group-hover:opacity-100');

      // Tooltip should have proper z-index for layering
      const zIndexClass = 'z-10';
      expect(zIndexClass).toBe('z-10');
    });
  });

  describe('Performance Considerations', () => {
    it('should efficiently handle large numbers of items', () => {
      // Create a package with many items
      const manyItems = Array.from({ length: 50 }, (_, i) => `Item ${i + 1}`);
      const packageWithManyItems = { includedItems: manyItems };

      // Should still only display first 6
      const displayedItems = packageWithManyItems.includedItems.slice(0, 6);
      const overflowCount = packageWithManyItems.includedItems.length - 6;

      expect(displayedItems).toHaveLength(6);
      expect(overflowCount).toBe(44);

      // Tooltip should contain remaining items
      const tooltipItems = packageWithManyItems.includedItems.slice(6);
      expect(tooltipItems).toHaveLength(44);
    });
  });
});
