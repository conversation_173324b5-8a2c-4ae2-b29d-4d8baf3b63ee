import React from 'react';
import { render, waitFor } from '@testing-library/react';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';
import AuthCallback from '../auth/callback';
import GoogleCallback from '../auth/google/callback';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Mock AuthContext
jest.mock('../../contexts/AuthContext', () => ({
  useAuth: jest.fn(),
}));

// Mock sessionStorage
const mockSessionStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage,
});

describe('Auth Callback Pages', () => {
  const mockRouter = {
    push: jest.fn(),
    query: {},
    isReady: true,
  };
  
  const mockAuth = {
    login: jest.fn(),
    isLoading: false,
    error: null,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockReturnValue(mockRouter);
    useAuth.mockReturnValue(mockAuth);
    mockSessionStorage.getItem.mockClear();
    mockSessionStorage.setItem.mockClear();
    mockSessionStorage.removeItem.mockClear();
  });

  describe('AuthCallback (/auth/callback)', () => {
    it('should redirect to stored return URL after successful authentication', async () => {
      // Mock successful authentication
      mockAuth.login.mockResolvedValue({ success: true });
      mockSessionStorage.getItem.mockReturnValue('/boats/123');
      
      mockRouter.query = {
        token: 'mock-token',
        refreshToken: 'mock-refresh-token',
        user: JSON.stringify({ id: 1, email: '<EMAIL>' }),
      };

      render(<AuthCallback />);

      await waitFor(() => {
        expect(mockAuth.login).toHaveBeenCalled();
      });

      // Should redirect to stored URL after 2 seconds
      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/boats/123');
      }, { timeout: 3000 });

      // Should clear sessionStorage
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('gosea_return_url');
    });

    it('should redirect to home page when no return URL is stored', async () => {
      // Mock successful authentication
      mockAuth.login.mockResolvedValue({ success: true });
      mockSessionStorage.getItem.mockReturnValue(null);
      
      mockRouter.query = {
        token: 'mock-token',
        refreshToken: 'mock-refresh-token',
        user: JSON.stringify({ id: 1, email: '<EMAIL>' }),
      };

      render(<AuthCallback />);

      await waitFor(() => {
        expect(mockAuth.login).toHaveBeenCalled();
      });

      // Should redirect to home page after 2 seconds
      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/');
      }, { timeout: 3000 });
    });
  });

  describe('GoogleCallback (/auth/google/callback)', () => {
    it('should redirect to stored return URL for returning users', async () => {
      // Mock successful authentication for returning user
      mockAuth.login.mockResolvedValue({ success: true });
      mockSessionStorage.getItem.mockReturnValue('/search?location=penang');
      
      mockRouter.query = {
        token: 'mock-token',
        refreshToken: 'mock-refresh-token',
        sessionToken: 'mock-session-token',
        user: JSON.stringify({ 
          id: 1, 
          email: '<EMAIL>',
          isFirstTimeGoogleUser: false 
        }),
      };

      render(<GoogleCallback />);

      await waitFor(() => {
        expect(mockAuth.login).toHaveBeenCalled();
      });

      // Should redirect to stored URL after 2 seconds
      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/search?location=penang');
      }, { timeout: 3000 });

      // Should clear sessionStorage
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('gosea_return_url');
    });

    it('should redirect to home page when no return URL is stored for returning users', async () => {
      // Mock successful authentication for returning user
      mockAuth.login.mockResolvedValue({ success: true });
      mockSessionStorage.getItem.mockReturnValue(null);
      
      mockRouter.query = {
        token: 'mock-token',
        refreshToken: 'mock-refresh-token',
        sessionToken: 'mock-session-token',
        user: JSON.stringify({ 
          id: 1, 
          email: '<EMAIL>',
          isFirstTimeGoogleUser: false 
        }),
      };

      render(<GoogleCallback />);

      await waitFor(() => {
        expect(mockAuth.login).toHaveBeenCalled();
      });

      // Should redirect to home page after 2 seconds
      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/');
      }, { timeout: 3000 });
    });

    it('should redirect to dashboard for first-time Google users', async () => {
      // Mock successful authentication for first-time user
      mockAuth.login.mockResolvedValue({ success: true });
      mockSessionStorage.getItem.mockReturnValue('/boats/456');
      
      mockRouter.query = {
        token: 'mock-token',
        refreshToken: 'mock-refresh-token',
        sessionToken: 'mock-session-token',
        user: JSON.stringify({ 
          id: 1, 
          email: '<EMAIL>',
          isFirstTimeGoogleUser: true 
        }),
        first_time: 'true',
      };

      render(<GoogleCallback />);

      await waitFor(() => {
        expect(mockAuth.login).toHaveBeenCalled();
      });

      // Should redirect to dashboard with profile completion for first-time users
      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/dashboard?show_profile_completion=true');
      }, { timeout: 3000 });

      // Should NOT clear sessionStorage for first-time users (preserve for after profile completion)
      expect(mockSessionStorage.removeItem).not.toHaveBeenCalled();
    });
  });
});
