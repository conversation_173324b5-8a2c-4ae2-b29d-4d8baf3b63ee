/**
 * Test for service itinerary data transformation
 * This test verifies that the frontend correctly processes itinerary data
 * from the database format to display format
 */

describe('Service Itinerary Data Processing', () => {
  describe('Itinerary Data Transformation', () => {
    it('should transform database itinerary format to display format', () => {
      // Mock database format (what comes from API)
      const databaseItinerary = [
        {
          time: '09:00',
          activity: 'Meet at jetty and safety briefing',
          location: 'Departure Jetty'
        },
        {
          time: '10:00',
          activity: 'First snorkeling session',
          location: 'Coral Garden'
        },
        {
          time: '12:00',
          activity: 'Second snorkeling session',
          location: 'Marine Park'
        }
      ];

      // Transform to display format (what frontend expects)
      const displayItinerary = databaseItinerary.map(item => ({
        time: item.time,
        activity: item.activity,
        description: item.location ? `Location: ${item.location}` : item.description || '',
        location: item.location
      }));

      // Verify transformation
      expect(displayItinerary).toHaveLength(3);
      expect(displayItinerary[0]).toEqual({
        time: '09:00',
        activity: 'Meet at jetty and safety briefing',
        description: 'Location: Departure Jetty',
        location: 'Departure Jetty'
      });
      expect(displayItinerary[1]).toEqual({
        time: '10:00',
        activity: 'First snorkeling session',
        description: 'Location: Coral Garden',
        location: 'Coral Garden'
      });
      expect(displayItinerary[2]).toEqual({
        time: '12:00',
        activity: 'Second snorkeling session',
        description: 'Location: Marine Park',
        location: 'Marine Park'
      });
    });

    it('should handle null itinerary data gracefully', () => {
      // Test null itinerary (service without itinerary data)
      const service = {
        itinerary: null,
        description: 'Test service description'
      };

      // Simulate frontend logic for handling null itinerary
      const displayItinerary = (service.itinerary && Array.isArray(service.itinerary) && service.itinerary.length > 0)
        ? service.itinerary.map(item => ({
            time: item.time,
            activity: item.activity,
            description: item.location ? `Location: ${item.location}` : item.description || '',
            location: item.location
          }))
        : [
            {
              time: "08:00 AM",
              activity: "Departure from Jetty",
              description: "Meet at the designated jetty for check-in and safety briefing",
            },
            {
              time: "09:15 AM",
              activity: "Main Activity",
              description: service.description || "Enjoy the main service activity with professional guidance",
            }
          ];

      expect(displayItinerary).toHaveLength(2);
      expect(displayItinerary[0].activity).toBe('Departure from Jetty');
      expect(displayItinerary[1].description).toBe('Test service description');
    });

    it('should handle empty itinerary array', () => {
      // Test empty itinerary array
      const service = {
        itinerary: [],
        description: 'Test service description'
      };

      // Simulate frontend logic for handling empty itinerary
      const displayItinerary = (service.itinerary && Array.isArray(service.itinerary) && service.itinerary.length > 0)
        ? service.itinerary.map(item => ({
            time: item.time,
            activity: item.activity,
            description: item.location ? `Location: ${item.location}` : item.description || '',
            location: item.location
          }))
        : [
            {
              time: "08:00 AM",
              activity: "Departure from Jetty",
              description: "Meet at the designated jetty for check-in and safety briefing",
            }
          ];

      expect(displayItinerary).toHaveLength(1);
      expect(displayItinerary[0].activity).toBe('Departure from Jetty');
    });

    it('should preserve original database structure while adding display fields', () => {
      const databaseItinerary = [
        {
          time: '09:00',
          activity: 'Meet at jetty and safety briefing',
          location: 'Departure Jetty'
        }
      ];

      const displayItinerary = databaseItinerary.map(item => ({
        time: item.time,
        activity: item.activity,
        description: item.location ? `Location: ${item.location}` : item.description || '',
        location: item.location
      }));

      // Verify original data is preserved
      expect(displayItinerary[0].time).toBe(databaseItinerary[0].time);
      expect(displayItinerary[0].activity).toBe(databaseItinerary[0].activity);
      expect(displayItinerary[0].location).toBe(databaseItinerary[0].location);

      // Verify display field is added
      expect(displayItinerary[0].description).toBe('Location: Departure Jetty');
    });
  });
});
