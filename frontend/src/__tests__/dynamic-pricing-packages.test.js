/**
 * Test for dynamic pricing packages system
 * This test verifies that the system correctly processes and displays
 * configurable package types and pricing data
 */

describe('Dynamic Pricing Packages System', () => {
  describe('Package Data Structure Validation', () => {
    it('should validate package structure from API response', () => {
      // Mock API response structure
      const mockServiceData = {
        success: true,
        service: {
          id: 'ps_coral_snorkeling_half',
          name: 'Coral Reef Snorkeling',
          packages: [
            {
              id: 'sp_test_standard',
              packageType: {
                id: 'pkg_standard',
                name: 'Standard Package',
                code: 'STANDARD',
                description: 'Essential experience with basic inclusions',
                isDefault: true
              },
              basePrice: 95.00,
              agePricing: {
                adult: 95.00,
                child: 66.50,
                toddler: 0
              },
              priceModifier: 1.0,
              includedItems: ['Equipment', 'Guide', 'Safety briefing'],
              excludedItems: []
            },
            {
              id: 'sp_test_premium',
              packageType: {
                id: 'pkg_premium',
                name: 'Premium Package',
                code: 'PREMIUM',
                description: 'Enhanced experience with additional amenities',
                isDefault: false
              },
              basePrice: 118.75,
              agePricing: {
                adult: 118.75,
                child: 83.13,
                toddler: 0
              },
              priceModifier: 1.25,
              includedItems: ['Equipment', 'Guide', 'Safety briefing', 'Premium refreshments', 'Professional photography'],
              excludedItems: []
            }
          ]
        }
      };

      // Verify package structure
      expect(mockServiceData.service.packages).toHaveLength(2);

      const firstPackage = mockServiceData.service.packages[0];
      expect(firstPackage).toHaveProperty('id');
      expect(firstPackage).toHaveProperty('packageType');
      expect(firstPackage).toHaveProperty('basePrice');
      expect(firstPackage).toHaveProperty('priceModifier');
      expect(firstPackage).toHaveProperty('includedItems');
      expect(firstPackage).toHaveProperty('excludedItems');

      // Verify package type structure
      expect(firstPackage.packageType).toHaveProperty('id');
      expect(firstPackage.packageType).toHaveProperty('name');
      expect(firstPackage.packageType).toHaveProperty('code');
      expect(firstPackage.packageType).toHaveProperty('description');
      expect(firstPackage.packageType).toHaveProperty('isDefault');
    });

    it('should validate pricing progression across packages', () => {
      const mockPackages = [
        { packageType: { code: 'STANDARD' }, basePrice: 95.00, priceModifier: 1.0 },
        { packageType: { code: 'PREMIUM' }, basePrice: 118.75, priceModifier: 1.25 },
        { packageType: { code: 'LUXURY' }, basePrice: 142.50, priceModifier: 1.5 }
      ];

      // Sort packages by price modifier
      const sortedPackages = mockPackages.sort((a, b) => a.priceModifier - b.priceModifier);

      expect(sortedPackages[0].packageType.code).toBe('STANDARD');
      expect(sortedPackages[1].packageType.code).toBe('PREMIUM');
      expect(sortedPackages[2].packageType.code).toBe('LUXURY');

      // Verify price progression
      expect(sortedPackages[0].basePrice).toBeLessThan(sortedPackages[1].basePrice);
      expect(sortedPackages[1].basePrice).toBeLessThan(sortedPackages[2].basePrice);
    });

    it('should validate included items progression', () => {
      const mockPackages = [
        {
          packageType: { code: 'STANDARD' },
          includedItems: ['Equipment', 'Guide']
        },
        {
          packageType: { code: 'PREMIUM' },
          includedItems: ['Equipment', 'Guide', 'Premium refreshments', 'Professional photography']
        },
        {
          packageType: { code: 'LUXURY' },
          includedItems: ['Equipment', 'Guide', 'Luxury refreshments & gourmet meal', 'Professional photography & videography', 'Private guide', 'Complimentary transport']
        }
      ];

      const standardPackage = mockPackages.find(pkg => pkg.packageType.code === 'STANDARD');
      const premiumPackage = mockPackages.find(pkg => pkg.packageType.code === 'PREMIUM');
      const luxuryPackage = mockPackages.find(pkg => pkg.packageType.code === 'LUXURY');

      // Premium should have more items than standard
      expect(premiumPackage.includedItems.length).toBeGreaterThan(standardPackage.includedItems.length);

      // Luxury should have more items than premium
      expect(luxuryPackage.includedItems.length).toBeGreaterThan(premiumPackage.includedItems.length);

      // Verify specific inclusions
      expect(premiumPackage.includedItems).toContain('Premium refreshments');
      expect(luxuryPackage.includedItems).toContain('Private guide');
    });
  });

  describe('Default Package Selection', () => {
    it('should identify default package correctly', () => {
      const mockPackages = [
        { packageType: { code: 'STANDARD', isDefault: true } },
        { packageType: { code: 'PREMIUM', isDefault: false } },
        { packageType: { code: 'LUXURY', isDefault: false } }
      ];

      const defaultPackages = mockPackages.filter(pkg => pkg.packageType.isDefault);
      expect(defaultPackages).toHaveLength(1);
      expect(defaultPackages[0].packageType.code).toBe('STANDARD');
    });

    it('should handle case with no default package', () => {
      const mockPackages = [
        { packageType: { code: 'STANDARD', isDefault: false } },
        { packageType: { code: 'PREMIUM', isDefault: false } }
      ];

      const defaultPackages = mockPackages.filter(pkg => pkg.packageType.isDefault);
      expect(defaultPackages).toHaveLength(0);

      // Should fall back to first package
      const fallbackPackage = mockPackages[0];
      expect(fallbackPackage.packageType.code).toBe('STANDARD');
    });
  });

  describe('Data Transformation', () => {
    it('should transform package data correctly for frontend display', () => {
      // Mock API response
      const mockPackage = {
        id: 'sp_test_standard',
        packageType: {
          id: 'pkg_standard',
          name: 'Standard Package',
          code: 'STANDARD',
          description: 'Essential experience with basic inclusions',
          isDefault: true
        },
        basePrice: 95.00,
        agePricing: {
          adult: 95.00,
          child: 66.50,
          toddler: 0
        },
        priceModifier: 1.0,
        includedItems: ['Equipment', 'Guide', 'Safety briefing'],
        excludedItems: []
      };

      // Verify the structure matches what frontend expects
      expect(mockPackage.packageType.code.toLowerCase()).toBe('standard');
      expect(mockPackage.includedItems).toBeInstanceOf(Array);
      expect(mockPackage.agePricing).toHaveProperty('adult');
      expect(mockPackage.agePricing).toHaveProperty('child');
      expect(mockPackage.agePricing).toHaveProperty('toddler');
    });

    it('should handle packages without age pricing', () => {
      const mockPackage = {
        id: 'sp_test_standard',
        packageType: {
          id: 'pkg_standard',
          name: 'Standard Package',
          code: 'STANDARD',
          description: 'Essential experience',
          isDefault: true
        },
        basePrice: 95.00,
        agePricing: null,
        priceModifier: 1.0,
        includedItems: ['Equipment'],
        excludedItems: []
      };

      // Should fall back to base price when no age pricing
      expect(mockPackage.agePricing).toBeNull();
      expect(mockPackage.basePrice).toBe(95.00);
    });

    it('should calculate correct pricing with modifiers', () => {
      const basePrice = 100;
      const packages = [
        { packageType: { code: 'STANDARD' }, priceModifier: 1.0 },
        { packageType: { code: 'PREMIUM' }, priceModifier: 1.25 },
        { packageType: { code: 'LUXURY' }, priceModifier: 1.5 }
      ];

      packages.forEach(pkg => {
        const calculatedPrice = basePrice * pkg.priceModifier;

        if (pkg.packageType.code === 'STANDARD') {
          expect(calculatedPrice).toBe(100);
        } else if (pkg.packageType.code === 'PREMIUM') {
          expect(calculatedPrice).toBe(125);
        } else if (pkg.packageType.code === 'LUXURY') {
          expect(calculatedPrice).toBe(150);
        }
      });
    });
  });

  describe('Package Type Configuration', () => {
    it('should validate package type properties', () => {
      const mockPackageTypes = [
        {
          id: 'pkg_standard',
          name: 'Standard Package',
          code: 'STANDARD',
          description: 'Essential experience with basic inclusions',
          isDefault: true,
          sortOrder: 1
        },
        {
          id: 'pkg_premium',
          name: 'Premium Package',
          code: 'PREMIUM',
          description: 'Enhanced experience with additional amenities',
          isDefault: false,
          sortOrder: 2
        },
        {
          id: 'pkg_luxury',
          name: 'Luxury Package',
          code: 'LUXURY',
          description: 'Ultimate experience with premium services',
          isDefault: false,
          sortOrder: 3
        }
      ];

      // Verify all required package types exist
      const standardType = mockPackageTypes.find(pt => pt.code === 'STANDARD');
      const premiumType = mockPackageTypes.find(pt => pt.code === 'PREMIUM');
      const luxuryType = mockPackageTypes.find(pt => pt.code === 'LUXURY');

      expect(standardType).toBeDefined();
      expect(premiumType).toBeDefined();
      expect(luxuryType).toBeDefined();

      // Verify they have proper descriptions
      expect(standardType.description).toContain('Essential experience');
      expect(premiumType.description).toContain('Enhanced experience');
      expect(luxuryType.description).toContain('Ultimate experience');

      // Verify sort order
      expect(standardType.sortOrder).toBe(1);
      expect(premiumType.sortOrder).toBe(2);
      expect(luxuryType.sortOrder).toBe(3);
    });
  });
});
