/**
 * Test for booking page pricing scenarios
 * This test verifies that the booking page correctly handles all 4 pricing scenarios
 */

describe('Booking Page Pricing Scenarios', () => {
  describe('Scenario Detection Logic', () => {
    it('should correctly identify basic pricing scenario', () => {
      const service = {
        basePrice: 85.00,
        packages: [],
        agePricing: null
      };

      const hasPackages = service.packages && service.packages.length > 0;
      const hasAgePricing = service.agePricing || (hasPackages && service.packages.some(pkg => pkg.agePricing));

      expect(hasPackages).toBe(false);
      expect(hasAgePricing).toBe(false);
      
      // Should be basic scenario
      const scenario = !hasPackages && !hasAgePricing ? 'basic' : 'other';
      expect(scenario).toBe('basic');
    });

    it('should correctly identify package-only scenario', () => {
      const service = {
        basePrice: 95.00,
        packages: [
          { id: 'pkg1', packageType: { code: 'STANDARD' }, basePrice: 95.00, agePricing: null },
          { id: 'pkg2', packageType: { code: 'PREMIUM' }, basePrice: 125.00, agePricing: null }
        ],
        agePricing: null
      };

      const hasPackages = service.packages && service.packages.length > 0;
      const hasAgePricing = service.agePricing || (hasPackages && service.packages.some(pkg => pkg.agePricing));

      expect(hasPackages).toBe(true);
      expect(hasAgePricing).toBe(false);
      
      // Should be packages-only scenario
      const scenario = hasPackages && !hasAgePricing ? 'packages-only' : 'other';
      expect(scenario).toBe('packages-only');
    });

    it('should correctly identify age-only scenario', () => {
      const service = {
        basePrice: 110.00,
        packages: [],
        agePricing: {
          adult: 110.00,
          child: 77.00,
          senior: 99.00,
          toddler: 0
        }
      };

      const hasPackages = service.packages && service.packages.length > 0;
      const hasAgePricing = !!(service.agePricing || (hasPackages && service.packages.some(pkg => pkg.agePricing)));

      expect(hasPackages).toBe(false);
      expect(hasAgePricing).toBe(true);
      
      // Should be age-only scenario
      const scenario = !hasPackages && hasAgePricing ? 'age-only' : 'other';
      expect(scenario).toBe('age-only');
    });

    it('should correctly identify full variation scenario', () => {
      const service = {
        basePrice: 120.00,
        packages: [
          { 
            id: 'pkg1', 
            packageType: { code: 'STANDARD' }, 
            basePrice: 120.00, 
            agePricing: { adult: 120.00, child: 84.00, senior: 108.00, toddler: 0 }
          },
          { 
            id: 'pkg2', 
            packageType: { code: 'PREMIUM' }, 
            basePrice: 150.00, 
            agePricing: { adult: 150.00, child: 105.00, senior: 135.00, toddler: 0 }
          }
        ],
        agePricing: {
          adult: 120.00,
          child: 84.00,
          senior: 108.00,
          toddler: 0
        }
      };

      const hasPackages = service.packages && service.packages.length > 0;
      const hasAgePricing = !!(service.agePricing || (hasPackages && service.packages.some(pkg => pkg.agePricing)));

      expect(hasPackages).toBe(true);
      expect(hasAgePricing).toBe(true);
      
      // Should be full variation scenario
      const scenario = hasPackages && hasAgePricing ? 'full-variation' : 'other';
      expect(scenario).toBe('full-variation');
    });
  });

  describe('Total Amount Calculation', () => {
    it('should calculate correctly for basic pricing scenario', () => {
      const service = {
        basePrice: 85.00,
        packages: [],
        agePricing: null
      };

      const bookingDetails = {
        adults: 2,
        children: 1,
        seniors: 0,
        toddlers: 1
      };

      // Basic pricing: same price for adults, children, seniors; toddlers free
      const expectedTotal = (2 + 1 + 0) * 85.00; // 3 * 85 = 255
      expect(expectedTotal).toBe(255.00);
    });

    it('should calculate correctly for package-only scenario', () => {
      const service = {
        basePrice: 95.00,
        packages: [
          { id: 'pkg1', packageType: { code: 'STANDARD' }, basePrice: 95.00, agePricing: null },
          { id: 'pkg2', packageType: { code: 'PREMIUM' }, basePrice: 125.00, agePricing: null }
        ],
        agePricing: null
      };

      const bookingDetails = {
        adults: 2,
        children: 1,
        seniors: 0,
        toddlers: 1,
        selectedPackage: 'premium'
      };

      // Premium package: same price for all paying passengers
      const selectedPackage = service.packages.find(pkg => 
        pkg.packageType.code.toLowerCase() === bookingDetails.selectedPackage
      );
      const expectedTotal = (2 + 1 + 0) * selectedPackage.basePrice; // 3 * 125 = 375
      expect(expectedTotal).toBe(375.00);
    });

    it('should calculate correctly for age-only scenario', () => {
      const service = {
        basePrice: 110.00,
        packages: [],
        agePricing: {
          adult: 110.00,
          child: 77.00,
          senior: 99.00,
          toddler: 0
        }
      };

      const bookingDetails = {
        adults: 2,
        children: 1,
        seniors: 1,
        toddlers: 1
      };

      // Age-based pricing
      const expectedTotal = (2 * 110.00) + (1 * 77.00) + (1 * 99.00) + (1 * 0);
      // 220 + 77 + 99 + 0 = 396
      expect(expectedTotal).toBe(396.00);
    });

    it('should calculate correctly for full variation scenario', () => {
      const service = {
        basePrice: 120.00,
        packages: [
          { 
            id: 'pkg1', 
            packageType: { code: 'STANDARD' }, 
            basePrice: 120.00, 
            agePricing: { adult: 120.00, child: 84.00, senior: 108.00, toddler: 0 }
          },
          { 
            id: 'pkg2', 
            packageType: { code: 'PREMIUM' }, 
            basePrice: 150.00, 
            agePricing: { adult: 150.00, child: 105.00, senior: 135.00, toddler: 0 }
          }
        ],
        agePricing: {
          adult: 120.00,
          child: 84.00,
          senior: 108.00,
          toddler: 0
        }
      };

      const bookingDetails = {
        adults: 2,
        children: 1,
        seniors: 1,
        toddlers: 1,
        selectedPackage: 'premium'
      };

      // Premium package with age-based pricing
      const selectedPackage = service.packages.find(pkg => 
        pkg.packageType.code.toLowerCase() === bookingDetails.selectedPackage
      );
      
      const expectedTotal = (2 * selectedPackage.agePricing.adult) + 
                           (1 * selectedPackage.agePricing.child) + 
                           (1 * selectedPackage.agePricing.senior) + 
                           (1 * selectedPackage.agePricing.toddler);
      // (2 * 150) + (1 * 105) + (1 * 135) + (1 * 0) = 300 + 105 + 135 + 0 = 540
      expect(expectedTotal).toBe(540.00);
    });
  });

  describe('Passenger Count Validation', () => {
    it('should correctly count total passengers including seniors', () => {
      const bookingDetails = {
        adults: 2,
        children: 1,
        seniors: 1,
        toddlers: 1
      };

      const totalPassengers = parseInt(bookingDetails.adults) + 
                             parseInt(bookingDetails.children) + 
                             parseInt(bookingDetails.toddlers) + 
                             parseInt(bookingDetails.seniors || 0);

      expect(totalPassengers).toBe(5);
    });

    it('should handle missing seniors field gracefully', () => {
      const bookingDetails = {
        adults: 2,
        children: 1,
        toddlers: 1
        // seniors field missing
      };

      const totalPassengers = parseInt(bookingDetails.adults) + 
                             parseInt(bookingDetails.children) + 
                             parseInt(bookingDetails.toddlers) + 
                             parseInt(bookingDetails.seniors || 0);

      expect(totalPassengers).toBe(4);
    });
  });

  describe('Package Selection Logic', () => {
    it('should find correct package by code', () => {
      const packages = [
        { id: 'pkg1', packageType: { code: 'STANDARD' }, basePrice: 120.00 },
        { id: 'pkg2', packageType: { code: 'PREMIUM' }, basePrice: 150.00 },
        { id: 'pkg3', packageType: { code: 'LUXURY' }, basePrice: 180.00 }
      ];

      const selectedPackage = packages.find(pkg => 
        pkg.packageType.code.toLowerCase() === 'premium'
      );

      expect(selectedPackage).toBeDefined();
      expect(selectedPackage.id).toBe('pkg2');
      expect(selectedPackage.basePrice).toBe(150.00);
    });

    it('should fallback to first package if selection not found', () => {
      const packages = [
        { id: 'pkg1', packageType: { code: 'STANDARD' }, basePrice: 120.00 },
        { id: 'pkg2', packageType: { code: 'PREMIUM' }, basePrice: 150.00 }
      ];

      const selectedPackage = packages.find(pkg => 
        pkg.packageType.code.toLowerCase() === 'nonexistent'
      ) || packages[0];

      expect(selectedPackage).toBeDefined();
      expect(selectedPackage.id).toBe('pkg1');
      expect(selectedPackage.basePrice).toBe(120.00);
    });
  });

  describe('Age Group Availability', () => {
    it('should show seniors only when age pricing is available', () => {
      const serviceWithAgePricing = {
        agePricing: { adult: 120, child: 84, senior: 108, toddler: 0 }
      };

      const serviceWithoutAgePricing = {
        agePricing: null
      };

      // With age pricing, seniors should be available
      const hasAgePricing1 = serviceWithAgePricing.agePricing && 
                            serviceWithAgePricing.agePricing.senior !== undefined;
      expect(hasAgePricing1).toBe(true);

      // Without age pricing, seniors should not be available
      const hasAgePricing2 = !!(serviceWithoutAgePricing.agePricing &&
                            serviceWithoutAgePricing.agePricing.senior !== undefined);
      expect(hasAgePricing2).toBe(false);
    });
  });
});
