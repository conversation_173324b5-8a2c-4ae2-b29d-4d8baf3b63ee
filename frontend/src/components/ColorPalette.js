import React from 'react';

const ColorPalette = () => {
  const colorGroups = [
    {
      name: 'Primary Colors',
      description: 'Sky Blue - Main brand color for primary actions and branding',
      colors: [
        { name: 'Primary 50', class: 'bg-primary-50', hex: '#f0f9ff' },
        { name: 'Primary 100', class: 'bg-primary-100', hex: '#e0f2fe' },
        { name: 'Primary 200', class: 'bg-primary-200', hex: '#bae6fd' },
        { name: 'Primary 300', class: 'bg-primary-300', hex: '#7dd3fc' },
        { name: 'Primary 400', class: 'bg-primary-400', hex: '#38bdf8' },
        { name: 'Primary 500', class: 'bg-primary-500', hex: '#0ea5e9' },
        { name: 'Primary 600', class: 'bg-primary-600', hex: '#0284c7' },
        { name: 'Primary 700', class: 'bg-primary-700', hex: '#0369a1' },
        { name: 'Primary 800', class: 'bg-primary-800', hex: '#075985' },
        { name: 'Primary 900', class: 'bg-primary-900', hex: '#0c4a6e' },
      ]
    },
    {
      name: 'Secondary Colors',
      description: 'Amber - Secondary actions and warm accents',
      colors: [
        { name: 'Secondary 50', class: 'bg-secondary-50', hex: '#fffbeb' },
        { name: 'Secondary 100', class: 'bg-secondary-100', hex: '#fef3c7' },
        { name: 'Secondary 200', class: 'bg-secondary-200', hex: '#fde68a' },
        { name: 'Secondary 300', class: 'bg-secondary-300', hex: '#fcd34d' },
        { name: 'Secondary 400', class: 'bg-secondary-400', hex: '#fbbf24' },
        { name: 'Secondary 500', class: 'bg-secondary-500', hex: '#f59e0b' },
        { name: 'Secondary 600', class: 'bg-secondary-600', hex: '#d97706' },
        { name: 'Secondary 700', class: 'bg-secondary-700', hex: '#b45309' },
        { name: 'Secondary 800', class: 'bg-secondary-800', hex: '#92400e' },
        { name: 'Secondary 900', class: 'bg-secondary-900', hex: '#78350f' },
      ]
    },
    {
      name: 'Accent Colors',
      description: 'Cyan - Highlights and special elements',
      colors: [
        { name: 'Accent 50', class: 'bg-accent-50', hex: '#ecfeff' },
        { name: 'Accent 100', class: 'bg-accent-100', hex: '#cffafe' },
        { name: 'Accent 200', class: 'bg-accent-200', hex: '#a5f3fc' },
        { name: 'Accent 300', class: 'bg-accent-300', hex: '#67e8f9' },
        { name: 'Accent 400', class: 'bg-accent-400', hex: '#22d3ee' },
        { name: 'Accent 500', class: 'bg-accent-500', hex: '#06b6d4' },
        { name: 'Accent 600', class: 'bg-accent-600', hex: '#0891b2' },
        { name: 'Accent 700', class: 'bg-accent-700', hex: '#0e7490' },
        { name: 'Accent 800', class: 'bg-accent-800', hex: '#155e75' },
        { name: 'Accent 900', class: 'bg-accent-900', hex: '#164e63' },
      ]
    },
    {
      name: 'Neutral Colors',
      description: 'Slate - Text and neutral elements',
      colors: [
        { name: 'Neutral 50', class: 'bg-neutral-50', hex: '#f8fafc' },
        { name: 'Neutral 100', class: 'bg-neutral-100', hex: '#f1f5f9' },
        { name: 'Neutral 200', class: 'bg-neutral-200', hex: '#e2e8f0' },
        { name: 'Neutral 300', class: 'bg-neutral-300', hex: '#cbd5e1' },
        { name: 'Neutral 400', class: 'bg-neutral-400', hex: '#94a3b8' },
        { name: 'Neutral 500', class: 'bg-neutral-500', hex: '#64748b' },
        { name: 'Neutral 600', class: 'bg-neutral-600', hex: '#475569' },
        { name: 'Neutral 700', class: 'bg-neutral-700', hex: '#334155' },
        { name: 'Neutral 800', class: 'bg-neutral-800', hex: '#1e293b' },
        { name: 'Neutral 900', class: 'bg-neutral-900', hex: '#0f172a' },
      ]
    }
  ];

  const stateColors = [
    { name: 'Success', class: 'bg-success', hex: '#10b981', description: 'Success states and confirmations' },
    { name: 'Warning', class: 'bg-warning', hex: '#f59e0b', description: 'Warning states and cautions' },
    { name: 'Error', class: 'bg-error', hex: '#ef4444', description: 'Error states and alerts' },
    { name: 'Info', class: 'bg-info', hex: '#0ea5e9', description: 'Information states and tips' },
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-neutral-900 mb-4">GoSea Design System</h2>
        <p className="text-neutral-600 max-w-2xl mx-auto">
          Our color palette is inspired by the ocean and maritime themes, using sky blues, warm ambers, 
          and refreshing cyans to create a cohesive and accessible design system.
        </p>
      </div>

      {/* Color Groups */}
      {colorGroups.map((group, groupIndex) => (
        <div key={groupIndex} className="mb-12">
          <div className="mb-6">
            <h3 className="text-2xl font-semibold text-neutral-800 mb-2">{group.name}</h3>
            <p className="text-neutral-600">{group.description}</p>
          </div>
          
          <div className="grid grid-cols-2 sm:grid-cols-5 lg:grid-cols-10 gap-4">
            {group.colors.map((color, colorIndex) => (
              <div key={colorIndex} className="text-center">
                <div 
                  className={`${color.class} w-full h-16 rounded-lg shadow-sm border border-neutral-200 mb-2`}
                  title={`${color.name}: ${color.hex}`}
                ></div>
                <div className="text-xs text-neutral-600">
                  <div className="font-medium">{color.name.split(' ')[1]}</div>
                  <div className="text-neutral-400">{color.hex}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}

      {/* State Colors */}
      <div className="mb-12">
        <div className="mb-6">
          <h3 className="text-2xl font-semibold text-neutral-800 mb-2">State Colors</h3>
          <p className="text-neutral-600">Colors used for different application states and feedback</p>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {stateColors.map((color, index) => (
            <div key={index} className="text-center">
              <div 
                className={`${color.class} w-full h-20 rounded-lg shadow-sm mb-3`}
                title={`${color.name}: ${color.hex}`}
              ></div>
              <div className="text-sm">
                <div className="font-semibold text-neutral-800">{color.name}</div>
                <div className="text-neutral-600 text-xs mb-1">{color.hex}</div>
                <div className="text-neutral-500 text-xs">{color.description}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Button Examples */}
      <div className="mb-12">
        <div className="mb-6">
          <h3 className="text-2xl font-semibold text-neutral-800 mb-2">Button Examples</h3>
          <p className="text-neutral-600">How our colors work in interactive elements</p>
        </div>
        
        <div className="flex flex-wrap gap-4 justify-center">
          <button className="btn btn-primary">Primary Button</button>
          <button className="btn btn-secondary">Secondary Button</button>
          <button className="btn btn-accent">Accent Button</button>
          <button className="btn btn-success">Success Button</button>
          <button className="btn btn-warning">Warning Button</button>
          <button className="btn btn-error">Error Button</button>
          <button className="btn btn-info">Info Button</button>
          <button className="btn btn-outline btn-primary">Outline Primary</button>
          <button className="btn btn-ghost">Ghost Button</button>
        </div>
      </div>
    </div>
  );
};

export default ColorPalette;
