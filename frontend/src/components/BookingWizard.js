import React, { useState, useEffect, useMemo } from 'react';
import { 
  CalendarIcon, 
  TagIcon, 
  UsersIcon, 
  ChatBubbleLeftRightIcon,
  CheckCircleIcon 
} from '@heroicons/react/24/outline';

/**
 * BookingWizard Component
 * Multi-step booking form with step indicators and validation
 */
export default function BookingWizard({ 
  service, 
  bookingDetails, 
  setBookingDetails, 
  children,
  onStepChange 
}) {
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState(new Set());

  // Determine pricing scenario
  const getPricingScenario = () => {
    if (!service) return 'basic';
    
    const hasPackages = service.packages && service.packages.length > 0;
    const hasAgePricing = service.agePricing || (hasPackages && service.packages.some(pkg => pkg.agePricing));
    
    if (!hasPackages && !hasAgePricing) return 'basic';
    if (hasPackages && !hasAgePricing) return 'packages-only';
    if (!hasPackages && hasAgePricing) return 'age-only';
    return 'full-variation';
  };

  const scenario = getPricingScenario();

  // Define steps based on pricing scenario (memoized)
  const steps = useMemo(() => {
    const baseSteps = [
      { id: 1, name: 'Select Date & Time', icon: CalendarIcon, required: true },
    ];

    // Add package selection step only for scenarios 2 and 4
    if (scenario === 'packages-only' || scenario === 'full-variation') {
      baseSteps.push({ id: 2, name: 'Select Package', icon: TagIcon, required: true });
    }

    const nextId = baseSteps.length + 1;
    baseSteps.push(
      { id: nextId, name: 'Select Passenger', icon: UsersIcon, required: true },
      { id: nextId + 1, name: 'Add Special Request', icon: ChatBubbleLeftRightIcon, required: false }
    );

    return baseSteps;
  }, [scenario]);

  // Validation functions
  const isStepValid = (stepId) => {
    const step = steps.find(s => s.id === stepId);
    if (!step) return false;

    switch (step.name) {
      case 'Select Date':
        return bookingDetails.date && bookingDetails.time;
      case 'Select Package':
        return bookingDetails.selectedPackage && bookingDetails.selectedPackageId;
      case 'Select Passengers':
        return parseInt(bookingDetails.adults || 0) > 0;
      case 'Add Special Request':
        return true; // Optional step is always valid
      default:
        return false;
    }
  };

  // Get validation error message for a step
  const getStepValidationError = (stepId) => {
    const step = steps.find(s => s.id === stepId);
    if (!step || isStepValid(stepId)) return null;

    switch (step.name) {
      case 'Select Date':
        if (!bookingDetails.date) return 'Please select a date';
        if (!bookingDetails.time) return 'Please select a time';
        return null;
      case 'Select Package':
        if (!bookingDetails.selectedPackage) return 'Please select a package';
        return null;
      case 'Select Passengers':
        if (!bookingDetails.adults || parseInt(bookingDetails.adults) === 0) {
          return 'At least one adult passenger is required';
        }
        return null;
      default:
        return null;
    }
  };

  // Update completed steps when booking details change
  useEffect(() => {
    const newCompletedSteps = new Set();
    steps.forEach(step => {
      if (isStepValid(step.id)) {
        newCompletedSteps.add(step.id);
      }
    });
    setCompletedSteps(newCompletedSteps);
  }, [bookingDetails, steps]);

  // Auto-advance to next step when current step is completed
  useEffect(() => {
    if (isStepValid(currentStep) && currentStep < steps.length) {
      // Optional: Auto-advance after a short delay
      // setTimeout(() => {
      //   setCurrentStep(currentStep + 1);
      // }, 500);
    }
  }, [completedSteps, currentStep, steps.length]);

  // Handle step change
  const handleStepChange = (stepId) => {
    setCurrentStep(stepId);
    if (onStepChange) {
      onStepChange(stepId);
    }
  };

  // Get step status
  const getStepStatus = (stepId) => {
    if (completedSteps.has(stepId)) return 'completed';
    if (stepId === currentStep) return 'current';
    return 'pending';
  };

  return (
    <div className="space-y-6">
      {/* Step Indicators */}
      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Booking Progress</h3>

        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const status = getStepStatus(step.id);
            const Icon = step.icon;
            const isClickable = status === 'completed' || status === 'current';

            return (
              <div key={step.id} className="flex items-center flex-1">
                {/* Step Circle */}
                <div className="flex flex-col items-center">
                  <button
                    onClick={() => isClickable && handleStepChange(step.id)}
                    disabled={!isClickable}
                    className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-200 ${
                      status === 'completed'
                        ? 'bg-emerald-500 border-emerald-500 text-white shadow-lg hover:bg-emerald-600'
                        : status === 'current'
                        ? 'bg-emerald-500 border-emerald-500 text-white shadow-lg hover:bg-emerald-600'
                        : 'bg-gray-300 border-gray-300 text-gray-700 cursor-not-allowed'
                    } ${isClickable ? 'hover:scale-105' : ''}`}
                  >
                    <span className="text-lg font-bold">
                      {step.id}
                    </span>
                  </button>

                  {/* Step Number */}
                  <div className={`mt-2 text-xs font-medium px-2 py-1 rounded-full ${
                    status === 'completed'
                      ? 'bg-emerald-100 text-emerald-700'
                      : status === 'current'
                      ? 'bg-emerald-100 text-emerald-700'
                      : 'bg-gray-100 text-gray-500'
                  }`}>
                    Step {step.id}
                  </div>

                  {/* Step Label */}
                  <div className="mt-2 text-center">
                    <p className={`text-sm font-medium ${
                      status === 'completed' || status === 'current'
                        ? 'text-gray-900'
                        : 'text-gray-500'
                    }`}>
                      {step.name}
                    </p>
                    {step.required && (
                      <span className="text-xs text-red-500">Required</span>
                    )}
                    {!step.required && (
                      <span className="text-xs text-gray-400">Optional</span>
                    )}
                  </div>
                </div>

                {/* Connecting Line */}
                {index < steps.length - 1 && (
                  <div className="flex-1 mx-4 mt-6">
                    <div className={`h-1 rounded-full transition-colors duration-300 ${
                      completedSteps.has(step.id)
                        ? 'bg-gradient-to-r from-green-500 to-green-400'
                        : 'bg-gray-300'
                    }`} />
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Progress Bar */}
        <div className="mt-6">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Progress</span>
            <span>{Math.round((completedSteps.size / steps.length) * 100)}% Complete</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-amber-500 to-green-500 h-2 rounded-full transition-all duration-500"
              style={{ width: `${(completedSteps.size / steps.length) * 100}%` }}
            />
          </div>
        </div>
      </div>

      {/* Step Content */}
      <div className="bg-white rounded-lg border p-6">
        {/* Current Step Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Step {currentStep}: {steps.find(s => s.id === currentStep)?.name}
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                {steps.find(s => s.id === currentStep)?.required
                  ? 'This step is required to continue'
                  : 'This step is optional'}
              </p>
            </div>

            {/* Step Status Indicator */}
            <div className={`px-3 py-1 rounded-full text-xs font-medium ${
              isStepValid(currentStep)
                ? 'bg-green-100 text-green-700'
                : 'bg-yellow-100 text-yellow-700'
            }`}>
              {isStepValid(currentStep) ? '✓ Complete' : 'In Progress'}
            </div>
          </div>
        </div>

        {/* Step Content */}
        <div className="min-h-[300px]">
          {children}
        </div>

        {/* Validation Messages */}
        {(() => {
          const validationError = getStepValidationError(currentStep);
          const isRequired = steps.find(s => s.id === currentStep)?.required;

          if (!validationError) return null;

          return (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    {isRequired ? 'Required Field Missing' : 'Validation Error'}
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{validationError}</p>
                  </div>
                </div>
              </div>
            </div>
          );
        })()}
      </div>

      {/* Step Navigation */}
      <div className="bg-white rounded-lg border p-6">
        <div className="flex justify-between items-center">
          <button
            onClick={() => handleStepChange(Math.max(1, currentStep - 1))}
            disabled={currentStep === 1}
            className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Previous
          </button>

          <div className="text-sm text-gray-500">
            Step {currentStep} of {steps.length}
          </div>

          <button
            onClick={() => handleStepChange(Math.min(steps.length, currentStep + 1))}
            disabled={currentStep === steps.length || (!isStepValid(currentStep) && steps.find(s => s.id === currentStep)?.required)}
            className="flex items-center px-6 py-2 text-sm font-medium text-white bg-amber-500 border border-transparent rounded-md hover:bg-amber-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {currentStep === steps.length ? 'Complete Booking' : 'Next Step'}
            {currentStep < steps.length && (
              <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
