import { UsersIcon } from '@heroicons/react/24/outline';
import { useState, useEffect } from 'react';

export default function BookingPassengerSection({
  service,
  bookingDetails,
  setBookingDetails,
  getTotalPassengers,
  t
}) {
  const [ageCategories, setAgeCategories] = useState([]);

  // Fetch age categories from API
  useEffect(() => {
    const fetchAgeCategories = async () => {
      try {
        const response = await fetch('/api/services/config/age-categories');
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setAgeCategories(data.data);
          }
        }
      } catch (error) {
        console.error('Error fetching age categories:', error);
        // Fallback to hardcoded categories if API fails
        setAgeCategories([
          { id: 'age_adult', name: 'Adult', code: 'ADULT', minAge: 18, maxAge: null, sortOrder: 1 },
          { id: 'age_child', name: 'Child', code: 'CHILD', minAge: 3, maxAge: 17, sortOrder: 2 },
          { id: 'age_toddler', name: 'Todd<PERSON>', code: 'TODDLER', minAge: 0, maxAge: 2, sortOrder: 3 },
          { id: 'age_senior', name: 'Senior', code: 'SENIOR', minAge: 65, maxAge: null, sortOrder: 4 },
          { id: 'age_pwd', name: 'PWD', code: 'PWD', minAge: null, maxAge: null, sortOrder: 5 }
        ]);
      }
    };

    fetchAgeCategories();
  }, []);
  // Get pricing for display based on scenario
  const getPricingForDisplay = (ageGroup) => {
    if (!service) return 0;

    // Determine pricing scenario
    const hasPackages = service.packages && service.packages.length > 0;
    const hasAgePricing = service.agePricing || (hasPackages && service.packages.some(pkg => pkg.agePricing));

    // Scenario 1: Basic pricing (no packages, no age-based pricing)
    if (!hasPackages && !hasAgePricing) {
      return ageGroup === 'toddler' ? 0 : service.basePrice;
    }

    // Scenario 2: Package variations only (no age-based pricing)
    if (hasPackages && !hasAgePricing) {
      const selectedPackage = service.packages.find(pkg => 
        pkg.packageType.code.toLowerCase() === bookingDetails.selectedPackage
      ) || service.packages[0];
      
      return ageGroup === 'toddler' ? 0 : selectedPackage.basePrice;
    }

    // Scenario 3: Age-based pricing only (no package variations)
    if (!hasPackages && hasAgePricing) {
      return service.agePricing[ageGroup] || 0;
    }

    // Scenario 4: Full variation (both packages AND age-based pricing)
    if (hasPackages && hasAgePricing) {
      const selectedPackage = service.packages.find(pkg => 
        pkg.packageType.code.toLowerCase() === bookingDetails.selectedPackage
      ) || service.packages[0];
      
      if (selectedPackage.agePricing) {
        return selectedPackage.agePricing[ageGroup] || 0;
      }
    }

    // Fallback
    return ageGroup === 'toddler' ? 0 : service.basePrice;
  };

  // Generate passenger types dynamically from age categories
  const passengerTypes = ageCategories.map(category => {
    const ageRange = category.minAge !== null && category.maxAge !== null
      ? `(${category.minAge}-${category.maxAge})`
      : category.minAge !== null
        ? `(${category.minAge}+)`
        : category.maxAge !== null
          ? `(0-${category.maxAge})`
          : '';

    // Map category codes to booking detail keys
    const keyMapping = {
      'ADULT': 'adults',
      'CHILD': 'children',
      'TODDLER': 'toddlers',
      'SENIOR': 'seniors',
      'PWD': 'pwd'
    };

    return {
      key: keyMapping[category.code] || category.code.toLowerCase(),
      label: `${category.name} ${ageRange}`,
      ageGroup: category.code.toLowerCase(),
      required: category.code === 'ADULT',
      category: category
    };
  });

  // Check if age group pricing is available
  const isAgeGroupAvailable = (ageGroup) => {
    const hasPackages = service.packages && service.packages.length > 0;
    const hasAgePricing = service.agePricing || (hasPackages && service.packages.some(pkg => pkg.agePricing));

    if (!hasAgePricing) return ageGroup !== 'senior'; // No seniors for basic/package-only pricing

    // Check service-level age pricing
    if (service.agePricing && service.agePricing[ageGroup] !== undefined) return true;

    // Check package-level age pricing
    if (hasPackages) {
      const selectedPackage = service.packages.find(pkg => 
        pkg.packageType.code.toLowerCase() === bookingDetails.selectedPackage
      ) || service.packages[0];
      
      return selectedPackage.agePricing && selectedPackage.agePricing[ageGroup] !== undefined;
    }

    return false;
  };

  // Determine pricing scenario
  const getPricingScenario = () => {
    if (!service) return 'basic';

    const hasPackages = service.packages && service.packages.length > 0;
    const hasAgePricing = service.agePricing || (hasPackages && service.packages.some(pkg => pkg.agePricing));

    if (!hasPackages && !hasAgePricing) return 'basic';
    if (hasPackages && !hasAgePricing) return 'packages-only';
    if (!hasPackages && hasAgePricing) return 'age-only';
    return 'full-variation';
  };

  const scenario = getPricingScenario();

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-4">
        <UsersIcon className="w-4 h-4 inline mr-1" />
        Number of Passengers <span className="text-red-500">*</span>
      </label>

      <div className="space-y-4">
        {scenario === 'basic' ? (
          // Basic Pricing - Single Passenger Field
          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <div className="font-medium text-gray-900">Passengers</div>
              <div className="text-sm text-gray-500">
                RM {service.basePrice} per person
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                type="button"
                onClick={() => {
                  const currentTotal = getTotalPassengers();
                  if (currentTotal > 1) {
                    // Decrease total passengers by reducing adults first, then children
                    if (bookingDetails.adults > 1) {
                      setBookingDetails(prev => ({
                        ...prev,
                        adults: prev.adults - 1
                      }));
                    } else if (bookingDetails.children > 0) {
                      setBookingDetails(prev => ({
                        ...prev,
                        children: prev.children - 1
                      }));
                    }
                  }
                }}
                className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={getTotalPassengers() <= 1}
              >
                -
              </button>
              <span className="w-8 text-center font-medium">{getTotalPassengers()}</span>
              <button
                type="button"
                onClick={() => {
                  const currentTotal = getTotalPassengers();
                  if (currentTotal < ((service.calculatedCapacity || service.maxCapacity) || 50)) {
                    // Increase total passengers by adding to adults
                    setBookingDetails(prev => ({
                      ...prev,
                      adults: prev.adults + 1
                    }));
                  }
                }}
                className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={getTotalPassengers() >= ((service.calculatedCapacity || service.maxCapacity) || 50)}
              >
                +
              </button>
            </div>
          </div>
        ) : (
          // Age-Based Pricing - Detailed breakdown
          passengerTypes.map((type) => {
            // Skip age groups that aren't available for this service
            if (!isAgeGroupAvailable(type.ageGroup)) return null;

            const price = getPricingForDisplay(type.ageGroup);
            const count = parseInt(bookingDetails[type.key] || 0);

            return (
              <div key={type.key} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                  <div className="font-medium text-gray-900">{type.label}</div>
                  <div className="text-sm text-gray-500">
                    {price === 0 ? (
                      <span className="text-green-600 font-medium">FREE</span>
                    ) : (
                      `RM ${price} per person`
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <button
                    type="button"
                    onClick={() => setBookingDetails(prev => ({
                      ...prev,
                      [type.key]: Math.max(type.required ? 1 : 0, count - 1)
                    }))}
                    className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={count <= (type.required ? 1 : 0)}
                  >
                    -
                  </button>
                  <span className="w-8 text-center font-medium">{count}</span>
                  <button
                    type="button"
                    onClick={() => setBookingDetails(prev => ({
                      ...prev,
                      [type.key]: Math.min(((service.calculatedCapacity || service.maxCapacity) || 50), count + 1)
                    }))}
                    className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={getTotalPassengers() >= ((service.calculatedCapacity || service.maxCapacity) || 50)}
                  >
                    +
                  </button>
                </div>
              </div>
            );
          })
        )}
      </div>

      {(service.calculatedCapacity || service.maxCapacity) && (
        <p className="text-xs text-gray-500 mt-2">
          Maximum capacity: {service.calculatedCapacity || service.maxCapacity} passengers (Total: {getTotalPassengers()})
        </p>
      )}
    </div>
  );
}
