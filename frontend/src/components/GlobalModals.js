import React from 'react';
import { useModal } from '../contexts/ModalContext';
import SignInModal from './SignInModal';
import SignUpModal from './SignUpModal';

/**
 * Global Modals Component
 * Renders authentication modals that can be triggered from any page
 * Manages modal state through ModalContext
 */
const GlobalModals = () => {
  const {
    showSignInModal,
    showSignUpModal,
    closeModals,
    switchToSignUp,
    switchToSignIn
  } = useModal();

  return (
    <>
      {/* Sign In Modal */}
      <SignInModal
        isOpen={showSignInModal}
        onClose={closeModals}
        onSwitchToSignUp={switchToSignUp}
        onLoginSuccess={() => {
          // Login successful - user stays on current page
        }}
      />

      {/* Sign Up Modal */}
      <SignUpModal
        isOpen={showSignUpModal}
        onClose={closeModals}
        onSwitchToSignIn={switchToSignIn}
        onSignUpSuccess={() => {
          // Sign-up successful - user stays on current page
        }}
      />
    </>
  );
};

export default GlobalModals;
