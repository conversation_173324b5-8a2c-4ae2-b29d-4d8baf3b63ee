import React from 'react';
import { CheckIcon } from '@heroicons/react/24/outline';
import { useLanguage } from '../contexts/LanguageContext';

const DynamicPricingSection = ({ service, goToBookingPage }) => {
  const { t } = useLanguage();
  if (!service) return null;

  console.log(service);

  // Determine pricing scenario
  const getPricingScenario = () => {
    if (!service) return 'basic';

    const hasPackages = service.packages && service.packages.length > 0;
    const hasAgePricing = service.agePricing || (hasPackages && service.packages.some(pkg => pkg.agePricing));

    if (!hasPackages && !hasAgePricing) return 'basic';
    if (hasPackages && !hasAgePricing) return 'packages-only';
    if (!hasPackages && hasAgePricing) return 'age-only';
    return 'full-variation';
  };

  const scenario = getPricingScenario();



  // If service has dynamic packages, use them
  if (service.packages && service.packages.length > 0) {
    // Find package with highest base price for "Most Popular" tag
    const getHighestPricePackage = (packages) => {
      return packages.reduce((highest, current) => {
        const currentPrice = parseFloat(current.basePrice) || 0;
        const highestPrice = parseFloat(highest.basePrice) || 0;
        return currentPrice > highestPrice ? current : highest;
      });
    };
    
    const mostPopularPackage = getHighestPricePackage(service.packages);

    return (
      <div className="bg-white rounded-lg shadow-sm p-4 mb-6 sm:sticky sm:top-16">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
          {t('pricingPackages')}
        </h2>

        {/* Package Display */}
        <div className="flex flex-col gap-4">
          {service.packages.map((pkg, index) => (
            <div
              key={pkg.id}
              className="border-2 rounded-lg p-4 relative transition-colors border-gray-200 hover:border-amber-300 hover:bg-amber-50"
            >
              {/* Popular Badge for Highest Price Package */}
              {pkg.id === mostPopularPackage.id && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-amber-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                    {t('mostPopular')}
                  </span>
                </div>
              )}

              <div className="flex items-center justify-between mb-3 mt-2">
                <div className={`${!pkg.agePricing ? 'max-w-[60%]' : 'max-w-full'} sm:max-w-auto`}>
                  <h3 className="text-base font-semibold text-gray-900">
                    {pkg.packageType.name}
                  </h3>
                  <p className="text-xs text-gray-600">
                    {pkg.description || pkg.packageType.description}
                  </p>
                </div>

                {/* Only display if age pricing is not available */}
                {!pkg.agePricing && (
                <div className="text-right">
                  <div className="text-lg font-bold text-amber-600">
                    RM {pkg.agePricing?.adult || pkg.basePrice}
                  </div>
                  <div className="text-xs text-gray-500">{t('perPerson')}</div>
                </div>
                )}
              </div>
              

              {/* Dynamic Pricing Grid */}
              {pkg.agePricing && (
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 text-sm mb-3">
                  {pkg.agePricing.adult && (
                    <div className="text-center p-2 bg-gray-50 rounded border border-gray-200">
                      <div className="font-medium text-gray-900">{t('adults')}</div>
                      <div className="text-amber-600 font-bold">RM {pkg.agePricing.adult}</div>
                    </div>
                  )}
                  {pkg.agePricing.child && (
                    <div className="text-center p-2 bg-gray-50 rounded border border-gray-200">
                      <div className="font-medium text-gray-900">{t('children')}</div>
                      <div className="text-amber-600 font-bold">RM {pkg.agePricing.child}</div>
                    </div>
                  )}
                  {pkg.agePricing.toddler !== undefined && (
                    <div className="text-center p-2 bg-gray-50 rounded border border-gray-200">
                      <div className="font-medium text-gray-900">{t('toddlers')}</div>
                      <div className="text-emerald-600 font-bold">
                        {pkg.agePricing.toddler === 0 ? 'FREE' : `RM ${pkg.agePricing.toddler}`}
                      </div>
                    </div>
                  )}
                  {pkg.agePricing.senior && (
                    <div className="text-center p-2 bg-gray-50 rounded border border-gray-200">
                      <div className="font-medium text-gray-900">{t('seniors')}</div>
                      <div className="text-amber-600 font-bold">RM {pkg.agePricing.senior}</div>
                    </div>
                  )}
                  {pkg.agePricing.pwd && (
                    <div className="text-center p-2 bg-gray-50 rounded border border-gray-200">
                      <div className="font-medium text-gray-900">{t('pwd')}</div>
                      <div className="text-amber-600 font-bold">RM {pkg.agePricing.pwd}</div>
                    </div>
                  )}
                </div>
              )}

              {/* Dynamic Includes with Overflow Handling */}
              <div className="mt-3">
                <div className="flex flex-wrap gap-1">
                  {pkg.includedItems.slice(0, 100).map((item, idx) => (
                    <span
                      key={idx}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-sky-100 text-sky-600"
                    >
                      ✓ {item}
                    </span>
                  ))}
                  {pkg.includedItems.length > 100 && (
                    <div className="relative group">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-sky-100 text-sky-600 cursor-help">
                        +{pkg.includedItems.length - 100} more
                      </span>

                      {/* Hover Tooltip */}
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 whitespace-nowrap max-w-xs">
                        <div className="text-center">
                          <div className="font-medium mb-1">Additional Items:</div>
                          <div className="space-y-1">
                            {pkg.includedItems.slice(6).map((item, idx) => (
                              <div key={idx} className="text-left">• {item}</div>
                            ))}
                          </div>
                        </div>
                        {/* Tooltip Arrow */}
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>



        {/* Package Comparison Note */}
        {service.packages.length > 1 && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <p className="text-xs text-gray-600 text-center">
              <strong>Compare packages</strong> to find the best value for your experience!
            </p>
          </div>
        )}

        <button
            onClick={goToBookingPage}
            className="mt-6 px-8 py-3 bg-amber-500 text-white font-semibold rounded-md hover:bg-amber-600 transition-colors w-full"
        >
          Book This Service
        </button>
      </div>
    );
  }

  // Fallback to legacy pricing display
  return (
    <div className="bg-white rounded-lg shadow-sm p-4 mb-6 sm:sticky sm:top-16">
      <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
        {t('pricing')}
      </h2>
      
      {/* Age-based pricing if available */}
      {service.agePricing ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 text-sm mb-3">
          <div className="text-center p-2 bg-gray-50 rounded border border-gray-200">
            <div className="font-medium text-gray-900">{t('adults')}</div>
            <div className="text-amber-600 font-bold">RM {service.agePricing.adult || service.basePrice}</div>
          </div>
          <div className="text-center p-2 bg-gray-50 rounded border border-gray-200">
            <div className="font-medium text-gray-900">{t('children')}</div>
            <div className="text-amber-600 font-bold">
              {service.agePricing.child ? `RM ${service.agePricing.child}` : `RM ${(service.basePrice * 0.7).toFixed(0)}`}
            </div>
          </div>
          <div className="text-center p-2 bg-gray-50 rounded border border-gray-200">
            <div className="font-medium text-gray-900">{t('toddlers')}</div>
            <div className="text-amber-600 font-bold">
              {service.agePricing.toddler === 0 ? 'FREE' : `RM ${service.agePricing.toddler || 0}`}
            </div>
          </div>
          {service.agePricing.senior && (
            <div className="text-center p-2 bg-gray-50 rounded border border-gray-200">
              <div className="font-medium text-gray-900">{t('seniors')}</div>
              <div className="text-amber-600 font-bold">RM {service.agePricing.senior}</div>
            </div>
          )}
          {service.agePricing.pwd && (
            <div className="text-center p-2 bg-gray-50 rounded border border-gray-200">
              <div className="font-medium text-gray-900">{t('pwd')}</div>
              <div className="text-amber-600 font-bold">RM {service.agePricing.pwd}</div>
            </div>
          )}
        </div>
      ) : (
        /* Simple base price display */
        <div className="text-center bg-gray-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-amber-600 mb-2">
            RM {service.basePrice}
          </div>
          <div className="text-sm text-gray-500">{t('perPerson')}</div>
        </div>
      )}
      
      {/* Included items */}
      {service.includedItems && service.includedItems.length > 0 && (
        <div className="mt-4">
          <div className="font-medium text-gray-900 mb-2">{t('includes')}:</div>
          <div className="flex flex-wrap gap-1">
            {service.includedItems.map((item, idx) => (
              <span
                key={idx}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-sky-100 text-sky-600"
              >
                ✓ {item}
              </span>
            ))}
          </div>
        </div>
      )}

      <button
          onClick={goToBookingPage}
          className="mt-6 px-8 py-3 bg-amber-500 text-white font-semibold rounded-md hover:bg-amber-600 transition-colors w-full"
      >
        {t('bookThisService')}
      </button>
    </div>
  );
};

export default DynamicPricingSection;
