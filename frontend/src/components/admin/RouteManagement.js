import { useState, useEffect } from 'react';
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon, 
  MapPinIcon,
  ArrowRightIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import StandardModal from '../StandardModal';

export default function RouteManagement() {
  const [routes, setRoutes] = useState([]);
  const [jetties, setJetties] = useState([]);
  const [destinations, setDestinations] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [modalConfig, setModalConfig] = useState({});
  const [editingRoute, setEditingRoute] = useState(null);
  const [formData, setFormData] = useState({
    departureJettyId: '',
    destinationId: '',
    distance: '',
    estimatedDuration: '',
    description: '',
    isActive: true
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      
      // Load routes, jetties, and destinations in parallel
      const [routesRes, jettiesRes, destinationsRes] = await Promise.all([
        fetch('/api/search/routes'),
        fetch('/api/search/jetties'),
        fetch('/api/search/destinations')
      ]);

      const [routesData, jettiesData, destinationsData] = await Promise.all([
        routesRes.json(),
        jettiesRes.json(),
        destinationsRes.json()
      ]);

      if (routesData.success) setRoutes(routesData.data);
      if (jettiesData.success) setJetties(jettiesData.data);
      if (destinationsData.success) setDestinations(destinationsData.data);

    } catch (error) {
      console.error('Error loading data:', error);
      showErrorModal('Error', 'Failed to load route data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddRoute = () => {
    setEditingRoute(null);
    setFormData({
      departureJettyId: '',
      destinationId: '',
      distance: '',
      estimatedDuration: '',
      description: '',
      isActive: true
    });
    setShowModal(true);
  };

  const handleEditRoute = (route) => {
    setEditingRoute(route);
    setFormData({
      departureJettyId: route.departureJettyId,
      destinationId: route.destinationId,
      distance: route.distance || '',
      estimatedDuration: route.estimatedDuration || '',
      description: route.description || '',
      isActive: route.isActive
    });
    setShowModal(true);
  };

  const handleSaveRoute = async () => {
    try {
      const url = editingRoute 
        ? `/api/routes/${editingRoute.id}`
        : '/api/routes';
      
      const method = editingRoute ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        setShowModal(false);
        loadData(); // Reload the data
        showSuccessModal(
          'Success', 
          `Route ${editingRoute ? 'updated' : 'created'} successfully`
        );
      } else {
        showErrorModal('Error', data.message || 'Failed to save route');
      }
    } catch (error) {
      console.error('Error saving route:', error);
      showErrorModal('Error', 'Failed to save route');
    }
  };

  const handleDeleteRoute = async (routeId) => {
    if (!confirm('Are you sure you want to delete this route?')) {
      return;
    }

    try {
      const response = await fetch(`/api/routes/${routeId}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (data.success) {
        loadData(); // Reload the data
        showSuccessModal('Success', 'Route deleted successfully');
      } else {
        showErrorModal('Error', data.message || 'Failed to delete route');
      }
    } catch (error) {
      console.error('Error deleting route:', error);
      showErrorModal('Error', 'Failed to delete route');
    }
  };

  const showErrorModal = (title, message) => {
    setModalConfig({ type: 'error', title, message });
    setShowModal(true);
  };

  const showSuccessModal = (title, message) => {
    setModalConfig({ type: 'success', title, message });
    setShowModal(true);
  };

  if (isLoading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-500 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading routes...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Route Management</h2>
        <button
          onClick={handleAddRoute}
          className="flex items-center px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Add Route
        </button>
      </div>

      {/* Routes List */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Routes ({routes.length})
          </h3>
        </div>

        {routes.length === 0 ? (
          <div className="text-center py-12">
            <MapPinIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No routes found</h3>
            <p className="text-gray-600 mb-4">Get started by creating your first route.</p>
            <button
              onClick={handleAddRoute}
              className="px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors"
            >
              Add Route
            </button>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {routes.map((route) => (
              <div key={route.id} className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <span className="font-medium text-gray-900">
                        {route.departureJetty.name}
                      </span>
                      <ArrowRightIcon className="h-4 w-4 text-gray-400 mx-2" />
                      <span className="font-medium text-gray-900">
                        {route.destination.name}
                      </span>
                      
                      <div className="ml-3">
                        {route.isActive ? (
                          <CheckCircleIcon className="h-5 w-5 text-green-500" />
                        ) : (
                          <XCircleIcon className="h-5 w-5 text-red-500" />
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      {route.distance && (
                        <span>Distance: {route.distance}km</span>
                      )}
                      {route.estimatedDuration && (
                        <span>Duration: {route.estimatedDuration} mins</span>
                      )}
                    </div>

                    {route.description && (
                      <p className="text-sm text-gray-600 mt-1">
                        {route.description}
                      </p>
                    )}
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => handleEditRoute(route)}
                      className="p-2 text-gray-400 hover:text-amber-600 transition-colors"
                      title="Edit route"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    
                    <button
                      onClick={() => handleDeleteRoute(route.id)}
                      className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                      title="Delete route"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Route Form Modal */}
      {showModal && modalConfig.type !== 'error' && modalConfig.type !== 'success' && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={() => setShowModal(false)}></div>
            
            <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingRoute ? 'Edit Route' : 'Add New Route'}
              </h3>

              <div className="space-y-4">
                {/* Departure Jetty */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Departure Jetty
                  </label>
                  <select
                    value={formData.departureJettyId}
                    onChange={(e) => setFormData(prev => ({ ...prev, departureJettyId: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    required
                  >
                    <option value="">Select jetty</option>
                    {jetties.map((jetty) => (
                      <option key={jetty.id} value={jetty.id}>
                        {jetty.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Destination */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Destination
                  </label>
                  <select
                    value={formData.destinationId}
                    onChange={(e) => setFormData(prev => ({ ...prev, destinationId: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    required
                  >
                    <option value="">Select destination</option>
                    {destinations.map((destination) => (
                      <option key={destination.id} value={destination.id}>
                        {destination.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Distance */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Distance (km)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={formData.distance}
                    onChange={(e) => setFormData(prev => ({ ...prev, distance: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    placeholder="e.g., 15.5"
                  />
                </div>

                {/* Estimated Duration */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Estimated Duration (minutes)
                  </label>
                  <input
                    type="number"
                    value={formData.estimatedDuration}
                    onChange={(e) => setFormData(prev => ({ ...prev, estimatedDuration: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    placeholder="e.g., 45"
                  />
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    placeholder="Optional description..."
                  />
                </div>

                {/* Active Status */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={formData.isActive}
                    onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                    className="h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isActive" className="ml-2 text-sm text-gray-700">
                    Active route
                  </label>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveRoute}
                  className="px-4 py-2 bg-amber-500 text-white rounded-md hover:bg-amber-600 transition-colors"
                >
                  {editingRoute ? 'Update' : 'Create'} Route
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Standard Modal for success/error messages */}
      <StandardModal
        isOpen={showModal && (modalConfig.type === 'error' || modalConfig.type === 'success')}
        onClose={() => setShowModal(false)}
        type={modalConfig.type}
        title={modalConfig.title}
        message={modalConfig.message}
      />
    </div>
  );
}
