import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Image from 'next/image';
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  MapPinIcon, 
  UsersIcon, 
  CurrencyDollarIcon,
  StarIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';
import { Ship, Hotel } from 'lucide-react';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';
import { TbScubaMask } from "react-icons/tb";

// Predefined list of boat images for better performance
const BOAT_IMAGES = [
  '/images/templates/boats/boat1.png',
  '/images/templates/boats/boat2.png',
  '/images/templates/boats/boat3.png',
  '/images/templates/boats/boat4.png',
  '/images/templates/boats/boat5.png',
  '/images/templates/boats/boat6.png',
  '/images/templates/boats/boat7.png',
  '/images/templates/boats/boat8.png',
  '/images/templates/boats/boat9.png'
];

// Function to get a random boat image
const getRandomBoatImage = () => {
  const randomIndex = Math.floor(Math.random() * BOAT_IMAGES.length);
  return BOAT_IMAGES[randomIndex];
};

export default function ProviderCard({ provider, searchFilters = {} }) {
  const { t } = useLanguage();
  const router = useRouter();
  const [imageSrc, setImageSrc] = useState(null);
  const [imageError, setImageError] = useState(false);

  if (!provider) return null;

  const {
    id,
    displayName,
    description,
    rating,
    totalReviews,
    user,
    services = [],
    boats = [],
    boatCount = 0,
    serviceCount = 0,
    startingPrice,
    totalCapacity,
    logoUrl
  } = provider;

  // Set initial image source
  useEffect(() => {
    if (logoUrl) {
      setImageSrc(logoUrl);
      setImageError(false);
    } else {
      setImageSrc(getRandomBoatImage());
    }
  }, [logoUrl]);

  // Process destinations by state
  const getDestinationsByState = (services) => {
    const destinationsByState = {};
    
    services.forEach(service => {
      if (service.serviceRoutes && service.serviceRoutes.length > 0) {
        service.serviceRoutes.forEach(serviceRoute => {
          if (serviceRoute.route && serviceRoute.route.destination) {
            const state = serviceRoute.route.destination.state || 'Other';
            if (!destinationsByState[state]) {
              destinationsByState[state] = new Set();
            }
            destinationsByState[state].add(serviceRoute.route.destination.name);
          }
        });
      }
    });
    
    // Convert Sets to arrays and limit results
    const result = {};
    Object.keys(destinationsByState).slice(0, 2).forEach(state => {
      result[state] = Array.from(destinationsByState[state]).slice(0, 3);
    });
    
    return result;
  };

  const destinationsByState = getDestinationsByState(services);

  // Format rating display
  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <StarSolidIcon key={i} className="h-4 w-4 text-yellow-400" />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className="relative h-4 w-4">
            <StarIcon className="h-4 w-4 text-gray-300 absolute" />
            <div className="overflow-hidden w-1/2">
              <StarSolidIcon className="h-4 w-4 text-yellow-400" />
            </div>
          </div>
        );
      } else {
        stars.push(
          <StarIcon key={i} className="h-4 w-4 text-gray-300" />
        );
      }
    }
    return stars;
  };

  const handleCardClick = () => {
    const queryParams = new URLSearchParams();

    if (searchFilters.jettyId) queryParams.set('jetty', searchFilters.jettyId);
    if (searchFilters.serviceCategory) queryParams.set('category', searchFilters.serviceCategory);
    if (searchFilters.destinationId) queryParams.set('destination', searchFilters.destinationId);
    if (searchFilters.datetime) queryParams.set('datetime', searchFilters.datetime);

    const url = `/providers/${id}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    router.push(url);
  };

  // Handle image error - fallback to random boat image
  const handleImageError = () => {
    if (!imageError) {
      setImageSrc(getRandomBoatImage());
      setImageError(true);
    }
  };

  return (
    <div 
      className="bg-white rounded-lg shadow-sm border hover:shadow-lg transition-all duration-200 cursor-pointer group"
      onClick={handleCardClick}
    >
      {/* Provider Image */}
      <div className="relative aspect-w-16 aspect-h-9 bg-gray-200 rounded-t-lg overflow-hidden">
        {imageSrc ? (
          <div className="w-full h-48 flex items-center justify-center bg-gradient-to-br from-sky-50 to-sky-100">
            <div className="relative w-full h-full flex items-center justify-center">
              <Image
                src={imageSrc}
                alt={displayName}
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                style={{ 
                  objectFit: 'contain',
                  padding: '1rem'
                }}
                className="group-hover:scale-105 transition-transform duration-200"
                onError={handleImageError}
                priority={false} // Disable priority loading for better performance
                quality={75} // Reduce image quality for faster loading
              />
            </div>
          </div>
        ) : (
          <div className="w-full h-48 bg-gradient-to-br from-sky-50 to-sky-100 flex items-center justify-center">
            <Hotel className="h-12 w-12 text-sky-300" />
          </div>
        )}
        
        {/* Starting Price Badge */}
        {startingPrice && (
          <div className="absolute top-3 right-3">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-emerald-500 text-white">
              {t('from')} RM{startingPrice}
            </span>
          </div>
        )}
      </div>

      {/* Provider Info */}
      <div className="p-4">
        {/* Provider Name & Rating */}
        <div className="flex items-start justify-between mb-2">
          <h3 className="text-lg font-semibold text-gray-900 group-hover:text-sky-600 transition-colors">
            {displayName}
          </h3>
          
          {rating > 0 && (
            <div className="flex items-center space-x-1">
              <div className="flex items-center">
                {renderStars(rating)}
              </div>
              <span className="text-sm text-gray-600">
                {typeof rating === 'number' ? rating.toFixed(1) : '0.0'}
              </span>
              {totalReviews > 0 && (
                <span className="text-xs text-gray-500">
                  ({totalReviews})
                </span>
              )}
            </div>
          )}
        </div>

        {/* Description */}
        {description && (
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {description}
          </p>
        )}

        {/* Provider Stats */}
        <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <Ship className="h-4 w-4 text-amber-500" />
              <span>{boatCount} {boatCount === 1 ? t('boat') : t('boats')}</span>
            </div>
            
            <div className="flex items-center space-x-1">
              <TbScubaMask className="h-4 w-4 text-amber-500" />
              <span>{serviceCount} {serviceCount === 1 ? t('service') : t('services')}</span>
            </div>

            {totalCapacity > 0 && (
              <div className="flex items-center space-x-1">
                <UsersIcon className="h-4 w-4 text-amber-500" />
                <span>{t('upTo')}: {totalCapacity}</span>
              </div>
            )}
          </div>
        </div>

        {/* Services Preview */}
        {services.length > 0 && (
          <div className="border-t pt-3">
            <div className="flex flex-wrap gap-1">
              {services.map((service, index) => (
                <span
                  key={service.id || index}
                  className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-600"
                >
                  {service.serviceType.category.name}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Destination States */}
        {Object.keys(destinationsByState).length > 0 && (
          <div className="mt-3">
            <div className="space-y-2">
              {Object.entries(destinationsByState).map(([state, destinations]) => (
                <div key={state}>
                  <div className="flex flex-wrap gap-1">
                    {destinations.map((destination, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-sky-50 text-sky-700"
                      >
                        {destination}, {state}
                      </span>
                    ))}
                    {Array.from(new Set(services.flatMap(s => 
                      s.serviceRoutes?.filter(sr => sr.route?.destination?.state === state).map(sr => sr.route.destination.name) || []
                    ))).length > 3 && (
                      <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-600">
                        +{Array.from(new Set(services.flatMap(s => 
                          s.serviceRoutes?.filter(sr => sr.route?.destination?.state === state).map(sr => sr.route.destination.name) || []
                        ))).length - 3} more
                      </span>
                    )}
                  </div>
                </div>
              ))}
              {Object.keys(services.reduce((acc, service) => {
                if (service.serviceRoutes) {
                  service.serviceRoutes.forEach(serviceRoute => {
                    if (serviceRoute.route && serviceRoute.route.destination) {
                      const state = serviceRoute.route.destination.state || 'Other';
                      acc[state] = true;
                    }
                  });
                }
                return acc;
              }, {})).length > 2 && (
                <div className="text-xs text-gray-500">
                  +{Object.keys(services.reduce((acc, service) => {
                    if (service.serviceRoutes) {
                      service.serviceRoutes.forEach(serviceRoute => {
                        if (serviceRoute.route && serviceRoute.route.destination) {
                          const state = serviceRoute.route.destination.state || 'Other';
                          acc[state] = true;
                        }
                      });
                    }
                    return acc;
                  }, {})).length - 2} more states
                </div>
              )}
            </div>
          </div>
        )}

        {/* Owner Info */}
        {user?.profile && (
          <div className="border-t pt-3 mt-3">
            <div className="flex items-center space-x-2">
              <div className="h-6 w-6 bg-sky-500 rounded-full flex items-center justify-center">
                <span className="text-xs font-medium text-white">
                  {user.profile.firstName?.[0]}{user.profile.lastName?.[0]}
                </span>
              </div>
              <span className="text-sm text-gray-600">
                {user.profile.firstName} {user.profile.lastName}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}