import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { useAuth } from "../contexts/AuthContext";
import { useLanguage } from "../contexts/LanguageContext";
import { useModal } from "../contexts/ModalContext";
import ContactModal from "./ContactModal";
import { GlobeAltIcon } from "@heroicons/react/24/outline";
import { ShieldUser, LogOut } from 'lucide-react';

/**
 * Unified Navbar Component for GoSea Platform
 * Redesigned layout: Logo (left) | Main Menu (center) | Language & Avatar (right)
 */

// Utility function to construct proper image URL
const getImageUrl = (imagePath) => {
  if (!imagePath) return null;

  // If it's already a full URL (Google profile pictures), return as is
  if (imagePath.startsWith("http")) {
    return imagePath;
  }

  // If it's a relative path, construct the full URL
  if (imagePath.startsWith("/uploads/")) {
    return `${process.env.NEXT_PUBLIC_API_URL}${imagePath}`;
  }

  return imagePath;
};

const Navbar = ({
  currentPage = "home",
  onSignInClick = null,
  onSignUpClick = null,
}) => {
  const { user, isAuthenticated, logout } = useAuth();
  const { t, language, changeLanguage } = useLanguage();
  const { openSignInModal, openSignUpModal } = useModal();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [languageMenuOpen, setLanguageMenuOpen] = useState(false);
  const [contactModalOpen, setContactModalOpen] = useState(false);
  // New state for boat owner menu
  const [boatOwnerMenuOpen, setBoatOwnerMenuOpen] = useState(false);

  const handleLogout = async () => {
    try {
      // Set logout flag to prevent redirect to login
      sessionStorage.setItem("gosea_logging_out", "true");
      await logout();
      setUserMenuOpen(false);
      setMobileMenuOpen(false);
      setBoatOwnerMenuOpen(false); // Close boat owner menu on logout
      // Redirect to home page
      window.location.href = "/";
    } catch (error) {
      console.error("Logout error:", error);
      // Force redirect even if logout fails
      sessionStorage.setItem("gosea_logging_out", "true");
      window.location.href = "/";
    }
  };

  // Close other dropdowns when one opens
  const handleLanguageMenuToggle = () => {
    setLanguageMenuOpen(!languageMenuOpen);
    if (userMenuOpen) setUserMenuOpen(false);
    if (boatOwnerMenuOpen) setBoatOwnerMenuOpen(false);
  };

  const handleUserMenuToggle = () => {
    setUserMenuOpen(!userMenuOpen);
    if (languageMenuOpen) setLanguageMenuOpen(false);
    if (boatOwnerMenuOpen) setBoatOwnerMenuOpen(false);
  };

  // New function to handle boat owner menu toggle
  const handleBoatOwnerMenuToggle = () => {
    setBoatOwnerMenuOpen(!boatOwnerMenuOpen);
    if (userMenuOpen) setUserMenuOpen(false);
    if (languageMenuOpen) setLanguageMenuOpen(false);
  };

  const handleSignInClick = () => {
    // For home page, use provided callback if available, otherwise use global modal
    if (currentPage === "home" && onSignInClick) {
      onSignInClick();
    } else {
      // For all pages, use global modal
      openSignInModal();
    }
  };

  const handleSignUpClick = () => {
    // For home page, use provided callback if available, otherwise use global modal
    if (currentPage === "home" && onSignUpClick) {
      onSignUpClick();
    } else {
      // For all pages, use global modal
      openSignUpModal();
    }
  };

  // Get menu items based on current page context
  const getMenuItems = () => {
    // Determine page context
    const isHomepage = currentPage === "home";
    const isProfilePage =
      currentPage === "profile" || currentPage === "profile-edit";

    // console.log("Current page:", currentPage);

    // Base menu items for all non-homepage pages
    const baseMenuItems = [
      { key: "home", href: "/", label: t("home") },
      // { key: "dashboard", href: "/dashboard", label: t("dashboard") },
      // { key: "profile", href: "/profile", label: t("profile") },
      { key: "boats", href: "/boats", label: t("boats") },
      {
        key: "contact",
        href: "#",
        label: t("contact"),
        onClick: () => setContactModalOpen(true),
      },
    ];

    // Profile pages menu items (exclude profile link since user is already on profile page)
    const profilePageMenuItems = [
      { key: "home", href: "/", label: t("home") },
      // { key: "dashboard", href: "/dashboard", label: t("dashboard") },
      { key: "boats", href: "/boats", label: t("boats") },
      {
        key: "contact",
        href: "#",
        label: t("contact"),
        onClick: () => setContactModalOpen(true),
      },
    ];

    // Homepage specific menu items
    const homepageMenuItems = [
      { key: "features", href: "#user-types", label: t("features") },
      // { key: "dashboard", href: "/dashboard", label: t("dashboard") },
      // { key: "profile", href: "/profile", label: t("profile") },
      { key: "boats", href: "/boats", label: t("boats") },
      {
        key: "contact",
        href: "#",
        label: t("contact"),
        onClick: () => setContactModalOpen(true),
      },
    ];

    if (isHomepage) {
      return homepageMenuItems;
    } else if (isProfilePage) {
      return profilePageMenuItems;
    } else {
      return baseMenuItems;
    }
  };

  // Get user initials for avatar
  const getUserInitials = () => {
    if (user?.profile?.firstName && user?.profile?.lastName) {
      return `${user.profile.firstName.charAt(0)}${user.profile.lastName.charAt(0)}`.toUpperCase();
    } else if (user?.firstName && user?.lastName) {
      return `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`.toUpperCase();
    } else if (user?.email) {
      return user.email.charAt(0).toUpperCase();
    }
    return "U";
  };

  // Get user full name for dropdown
  const getUserFullName = () => {
    if (user?.profile?.firstName && user?.profile?.lastName) {
      return `${user.profile.firstName} ${user.profile.lastName}`;
    } else if (user?.firstName && user?.lastName) {
      return `${user.firstName} ${user.lastName}`;
    } else if (user?.email) {
      return user.email.split("@")[0];
    }
    return "User";
  };

  return (
    <nav
      className="sticky top-0 z-40 border-b shadow-sm bg-white border-slate-200"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo - Left Side */}
          <div className="flex items-center">
            <Link
              // href={isAuthenticated ? "/dashboard" : "/"}
              href="/"
              className="flex items-center space-x-3"
            >
              <Image
                src="/images/templates/logo/gosea_logo.png"
                alt="GoSea Logo"
                width={143}
                height={143}
                className="h-10 w-auto object-contain"
                priority
              />
            </Link>
          </div>

          {/* Main Menu - Center */}
          <div className="hidden md:flex items-center space-x-8">
            {getMenuItems().map((item) => {
              if (item.onClick) {
                return (
                  <button
                    key={item.key}
                    onClick={item.onClick}
                    className="font-medium transition-colors text-slate-500 hover:text-amber-500"
                  >
                    {item.label}
                  </button>
                );
              } else if (item.href.startsWith("#")) {
                return (
                  <a
                    key={item.key}
                    href={item.href}
                    className="font-medium transition-colors text-slate-500 hover:text-amber-500"
                    onClick={
                      item.key === "services"
                        ? (e) => {
                          e.preventDefault();
                          window.scrollTo({ top: 0, behavior: "smooth" });
                        }
                        : undefined
                    }
                  >
                    {item.label}
                  </a>
                );
              } else {
                return (
                  <Link
                    key={item.key}
                    href={item.href}
                    className="font-medium transition-colors text-slate-500 hover:text-amber-500"
                  >
                    {item.label}
                  </Link>
                );
              }
            })}
            
            {/* Boat Owner Registration Link - Hidden for authenticated users (boat owners, admins, and customers) */}
            {!(isAuthenticated && (user?.role === 'BOAT_OWNER' || user?.role === 'ADMIN' || user?.role === 'CUSTOMER')) && (
              <Link
                href="/boat-owner/signup"
                className="font-medium text-slate-500 hover:text-amber-500 transition-colors"
              >
                {t('boatOwner')}
              </Link>
            )}
            
            {/* New Boat Owner Menu - Visible only for authenticated and approved boat owners */}
            {isAuthenticated && user?.role === 'BOAT_OWNER' && user?.isApproved && (
              <div className="relative">
                <button
                  onClick={handleBoatOwnerMenuToggle}
                  className={`flex items-center space-x-1 px-3 py-2 rounded-lg font-medium transition-colors ${boatOwnerMenuOpen ? "text-amber-500" : "text-slate-500 hover:text-amber-500"}`}
                >
                  <span>{t('boatOwner')}</span>
                  <svg
                    className={`w-4 h-4 transition-transform ${boatOwnerMenuOpen ? "rotate-180" : ""}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>

                {/* Boat Owner Dropdown Menu */}
                {boatOwnerMenuOpen && (
                  <div
                    className="absolute left-0 mt-2 w-56 rounded-lg shadow-lg border z-50 bg-white border-slate-200"
                  >
                    <div className="py-1">
                      <Link
                        href="/boat-owner/dashboard"
                        className="block px-4 py-2 text-sm font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                        onClick={() => setBoatOwnerMenuOpen(false)}
                      >
                        {t('dashboard')}
                      </Link>
                      <Link
                        href="/boat-owner/profile"
                        className="block px-4 py-2 text-sm font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                        onClick={() => setBoatOwnerMenuOpen(false)}
                      >
                        {t('businessProfile')}
                      </Link>
                      <Link
                        href="/boat-owner/boats"
                        className="block px-4 py-2 text-sm font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                        onClick={() => setBoatOwnerMenuOpen(false)}
                      >
                        {t('myBoats')}
                      </Link>
                      <Link
                        href="/boat-owner/services"
                        className="block px-4 py-2 text-sm font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                        onClick={() => setBoatOwnerMenuOpen(false)}
                      >
                        {t('myServices')}
                      </Link>
                      <Link
                        href="/boat-owner/bookings"
                        className="block px-4 py-2 text-sm font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                        onClick={() => setBoatOwnerMenuOpen(false)}
                      >
                        {t('bookings')}
                      </Link>
                      <Link
                        href="/boat-owner/analytics"
                        className="block px-4 py-2 text-sm font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                        onClick={() => setBoatOwnerMenuOpen(false)}
                      >
                        {t('analytics')}
                      </Link>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Language Selection & Avatar - Right Side */}
          <div className="hidden md:flex items-center space-x-4">
            {/* Language Dropdown */}
            <div className="relative">
              <button
                onClick={handleLanguageMenuToggle}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-colors ${languageMenuOpen ? "text-amber-500" : "text-slate-500 hover:text-amber-500"}`}
              >
                <GlobeAltIcon className="w-4 h-4" />
                <span>{language === "en" ? "EN" : "BM"}</span>
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>

              {/* Language Dropdown Menu */}
              {languageMenuOpen && (
                <div
                  className="absolute right-0 mt-2 w-40 rounded-lg shadow-lg border z-50 bg-white border-slate-200"
                >
                  <div className="py-1">
                    <button
                      onClick={() => {
                        changeLanguage("en");
                        setLanguageMenuOpen(false);
                      }}
                      className="w-full text-left px-4 py-2 text-sm font-medium transition-colors whitespace-nowrap text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                    >
                      English
                    </button>
                    <button
                      onClick={() => {
                        changeLanguage("ms");
                        setLanguageMenuOpen(false);
                      }}
                      className="w-full text-left px-4 py-2 text-sm font-medium transition-colors whitespace-nowrap text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                    >
                      Bahasa Melayu
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* User Avatar or Auth Buttons */}
            {isAuthenticated ? (
              <div className="relative">
                <button
                  onClick={handleUserMenuToggle}
                  className={`flex items-center justify-center w-10 h-10 rounded-full font-semibold text-white transition-all duration-200 overflow-hidden ${
                    user?.profile?.profilePicture
                      ? "bg-transparent"
                      : "bg-amber-500 border-2 border-slate-200 hover:bg-amber-600"
                  }`}
                  style={{
                    fontSize: "14px",
                    lineHeight: "1",
                  }}
                >
                  {user?.profile?.profilePicture ? (
                    <img
                      src={getImageUrl(user.profile.profilePicture)}
                      alt="Profile"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    getUserInitials()
                  )}
                </button>

                {/* User Dropdown Menu */}
                {userMenuOpen && (
                  <div
                    className="absolute right-0 mt-2 w-56 rounded-lg shadow-lg border z-50 bg-white border-slate-200"
                  >
                    <div className="py-2">
                      {/* User Info */}
                      <div
                        className="px-4 py-3 border-b border-slate-200"
                      >
                        <p
                          className="text-sm font-semibold text-slate-900"
                        >
                          {getUserFullName()}
                        </p>
                        <p className="text-xs text-slate-500">
                          {user?.email}
                        </p>
                        <p
                          className="text-xs mt-1 px-2 py-1 rounded-full inline-block bg-green-100 text-green-800"
                        >
                          {user?.role ? user.role.replace(/_/g, ' ') : "Customer"}
                        </p>
                      </div>

                      {/* Menu Items - Moved boat owner items to separate menu */}
                      <div className="py-1">
                        <Link
                          href="/profile"
                          className="block px-4 py-2 text-sm font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                          onClick={() => setUserMenuOpen(false)}
                        >
                          {t('profile')}
                        </Link>
                        <Link
                          href="/dashboard"
                          className="block px-4 py-2 text-sm font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                          onClick={() => setUserMenuOpen(false)}
                        >
                          {t('dashboard')}
                        </Link>
                        {/* Admin Dashboard Link - Only for ADMIN users */}
                        {user?.role === 'ADMIN' && (
                          <Link
                            href="/admin/dashboard"
                            className="flex items-center px-4 py-2 text-sm font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                            onClick={() => setUserMenuOpen(false)}
                          >
                            <ShieldUser className="mr-1 h-4 w-4" />
                            <span>{t('adminDashboard')}</span>
                          </Link>
                        )}
                        <button
                          onClick={handleLogout}
                          className="w-full flex items-center px-4 py-2 text-sm font-medium transition-colors text-slate-500 hover:text-red-600 hover:bg-slate-50"
                        >
                          <LogOut className="mr-1 h-4 w-4" />
                          {t("logout")}
                        </button>
                      </div>

                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleSignInClick}
                  className="px-4 py-2 font-medium rounded-lg transition-colors text-slate-500 hover:text-amber-500"
                >
                  {t("signIn")}
                </button>
                <button
                  onClick={handleSignUpClick}
                  className="px-4 py-2 font-medium rounded-lg transition-colors bg-sky-500 text-white hover:bg-sky-600"
                >
                  {t("signUp")}
                </button>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="p-2 rounded-lg transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d={
                    mobileMenuOpen
                      ? "M6 18L18 6M6 6l12 12"
                      : "M4 6h16M4 12h16M4 18h16"
                  }
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div
            className="md:hidden border-t border-slate-200"
          >
            <div className="px-2 pt-2 pb-3 space-y-1">
              {/* Mobile Navigation Links */}
              {getMenuItems().map((item) => {
                if (item.onClick) {
                  return (
                    <button
                      key={item.key}
                      onClick={() => {
                        item.onClick();
                        setMobileMenuOpen(false);
                      }}
                      className="block w-full text-left px-3 py-2 rounded-lg font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                    >
                      {item.label}
                    </button>
                  );
                } else if (item.href.startsWith("#")) {
                  return (
                    <a
                      key={item.key}
                      href={item.href}
                      className="block px-3 py-2 rounded-lg font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                      onClick={
                        item.key === "services"
                          ? (e) => {
                            e.preventDefault();
                            setMobileMenuOpen(false);
                            window.scrollTo({ top: 0, behavior: "smooth" });
                          }
                          : () => setMobileMenuOpen(false)
                      }
                    >
                      {item.label}
                    </a>
                  );
                } else {
                  return (
                    <Link
                      key={item.key}
                      href={item.href}
                      className="block px-3 py-2 rounded-lg font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      {item.label}
                    </Link>
                  );
                }
              })}
              
              {/* Boat Owner Registration Link - Hidden for authenticated users (boat owners, admins, and customers) */}
              {!(isAuthenticated && (user?.role === 'BOAT_OWNER' || user?.role === 'ADMIN' || user?.role === 'CUSTOMER')) && (
                <Link
                  href="/boat-owner/signup"
                  className="block px-3 py-2 rounded-lg font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {t('boatOwner')}
                </Link>
              )}
              
              {/* New Boat Owner Menu for Mobile - Visible only for authenticated and approved boat owners */}
              {isAuthenticated && user?.role === 'BOAT_OWNER' && user?.isApproved && (
                <>
                  <div className="border-t border-gray-200 my-2"></div>
                  <div className="px-3 py-2">
                    <p className="text-xs font-semibold text-gray-400 uppercase tracking-wide">
                      {t('boatOwnerModule')}
                    </p>
                  </div>
                  <Link
                    href="/boat-owner/dashboard"
                    className="block px-3 py-2 rounded-lg text-sm font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {t('dashboard')}
                  </Link>
                  <Link
                    href="/boat-owner/profile"
                    className="block px-3 py-2 rounded-lg text-sm font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {t('businessProfile')}
                  </Link>
                  <Link
                    href="/boat-owner/boats"
                    className="block px-3 py-2 rounded-lg text-sm font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {t('myBoats')}
                  </Link>
                  <Link
                    href="/boat-owner/services"
                    className="block px-3 py-2 rounded-lg text-sm font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {t('myServices')}
                  </Link>
                  <Link
                    href="/boat-owner/bookings"
                    className="block px-3 py-2 rounded-lg text-sm font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {t('bookings')}
                  </Link>
                  <Link
                    href="/boat-owner/analytics"
                    className="block px-3 py-2 rounded-lg text-sm font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {t('analytics')}
                  </Link>
                </>
              )}
              
              {isAuthenticated && (
                <>
                  <div className="border-t border-gray-200 my-2"></div>

                  {/* Regular User Menu Items */}
                  <Link
                    href="/profile"
                    className="block px-3 py-2 rounded-lg text-sm font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {t('profile')}
                  </Link>

                  <Link
                    href="/dashboard"
                    className="block px-3 py-2 rounded-lg text-sm font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {t('dashboard')}
                  </Link>

                  {/* Admin Menu Items - Only for ADMIN users */}
                  {user?.role === 'ADMIN' && (
                    <Link
                      href="/admin/dashboard"
                      className="flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <ShieldUser className="mr-2 h-4 w-4" />
                      {t('adminDashboard')}
                    </Link>
                  )}

                  {/* Logout Button */}
                  <div className="border-t border-gray-200 my-2"></div>
                  <button
                    onClick={() => {
                      handleLogout();
                      setMobileMenuOpen(false);
                    }}
                    className="flex items-center w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors text-red-600 hover:text-red-600 hover:bg-slate-50"
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    {t('logout')}
                  </button>
                </>
              )}

              {/* Mobile Language Selection */}
              <div className="px-3 py-2">
                <div className="flex items-center justify-between">
                  <p className="font-medium text-slate-500">
                    {t("language")}
                  </p>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => {
                        changeLanguage("en");
                        setMobileMenuOpen(false);
                      }}
                      className={`px-3 py-1 text-sm font-medium rounded transition-colors ${language === "en" ? "bg-amber-500 text-white" : "bg-slate-100 text-slate-500 hover:bg-amber-500 hover:text-white"}`}
                    >
                      EN
                    </button>
                    <button
                      onClick={() => {
                        changeLanguage("ms");
                        setMobileMenuOpen(false);
                      }}
                      className={`px-3 py-1 text-sm font-medium rounded transition-colors ${language === "ms" ? "bg-amber-500 text-white" : "bg-slate-100 text-slate-500 hover:bg-amber-500 hover:text-white"}`}
                    >
                      BM
                    </button>
                  </div>
                </div>
              </div>

              {/* Mobile Auth Buttons */}
              {!isAuthenticated && (
                <div className="px-3 py-2 space-y-2">
                  <button
                    onClick={() => {
                      handleSignInClick();
                      setMobileMenuOpen(false);
                    }}
                    className="w-full px-4 py-2 font-medium rounded-lg transition-colors text-slate-500 hover:text-amber-500 hover:bg-slate-50"
                  >
                    {t("signIn")}
                  </button>
                  <button
                    onClick={() => {
                      handleSignUpClick();
                      setMobileMenuOpen(false);
                    }}
                    className="w-full px-4 py-2 font-medium rounded-lg transition-colors bg-sky-500 text-white hover:bg-sky-600"
                  >
                    {t("signUp")}
                  </button>
                </div>
              )}


            </div>
          </div>
        )}
      </div>

      {/* Contact Modal */}
      <ContactModal
        isOpen={contactModalOpen}
        onClose={() => setContactModalOpen(false)}
      />

    </nav>
  );
};

export default Navbar;