import React, { useState, useRef, useEffect } from 'react';
import { useLanguage } from '../contexts/LanguageContext';

/**
 * Custom Date Picker Component
 * Replaces HTML5 date input with better UX and design system integration
 */

const CustomDatePicker = ({
  value,
  onChange,
  name = 'dateOfBirth',
  placeholder = 'Select date',
  className = '',
  error = false,
  disabled = false
}) => {
  const { t } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState(value ? new Date(value) : null);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [showYearDropdown, setShowYearDropdown] = useState(false);
  const [manualInput, setManualInput] = useState('');
  const [showManualInput, setShowManualInput] = useState(false);
  const dropdownRef = useRef(null);
  const inputRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Update selected date when value prop changes
  useEffect(() => {
    if (value) {
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        setSelectedDate(date);
        setCurrentMonth(date);
        setManualInput(formatDate(date));
      }
    } else {
      setSelectedDate(null);
      setManualInput('');
    }
  }, [value]);

  // Generate year options (from 1900 to current year + 10)
  const generateYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let year = 1900; year <= currentYear + 10; year++) {
      years.push(year);
    }
    return years.reverse(); // Most recent years first
  };

  // Format date for display
  const formatDate = (date) => {
    if (!date) return '';
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Get days in month
  const getDaysInMonth = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }
    
    return days;
  };

  // Helper function to format date without timezone issues
  const formatDateForForm = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Handle date selection
  const handleDateSelect = (date) => {
    setSelectedDate(date);
    setIsOpen(false);
    setShowManualInput(false);

    // Format date as YYYY-MM-DD for form compatibility without timezone conversion
    const formattedDate = formatDateForForm(date);
    setManualInput(formatDate(date));
    onChange({ target: { name, value: formattedDate } });
  };

  // Handle year selection
  const handleYearSelect = (year) => {
    const newDate = new Date(currentMonth);
    newDate.setFullYear(year);
    setCurrentMonth(newDate);
    setShowYearDropdown(false);
  };

  // Handle manual input
  const handleManualInputChange = (e) => {
    setManualInput(e.target.value);
  };

  // Handle manual input submission
  const handleManualInputSubmit = () => {
    const dateFormats = [
      /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/, // DD/MM/YYYY or D/M/YYYY
      /^(\d{1,2})-(\d{1,2})-(\d{4})$/, // DD-MM-YYYY or D-M-YYYY
      /^(\d{4})-(\d{1,2})-(\d{1,2})$/, // YYYY-MM-DD or YYYY-M-D
    ];

    let parsedDate = null;

    // Try different date formats
    for (const format of dateFormats) {
      const match = manualInput.match(format);
      if (match) {
        if (format === dateFormats[2]) { // YYYY-MM-DD
          parsedDate = new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));
        } else { // DD/MM/YYYY or DD-MM-YYYY
          parsedDate = new Date(parseInt(match[3]), parseInt(match[2]) - 1, parseInt(match[1]));
        }
        break;
      }
    }

    if (parsedDate && !isNaN(parsedDate.getTime())) {
      // Validate age (13-120 years old)
      const today = new Date();
      const age = today.getFullYear() - parsedDate.getFullYear();

      if (age >= 13 && age <= 120) {
        handleDateSelect(parsedDate);
      } else {
        alert(t('Please enter a valid date of birth (age must be between 13 and 120)'));
      }
    } else {
      alert(t('Please enter a valid date format (DD/MM/YYYY, DD-MM-YYYY, or YYYY-MM-DD)'));
    }
  };

  // Navigate months
  const navigateMonth = (direction) => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(currentMonth.getMonth() + direction);
    setCurrentMonth(newMonth);
  };

  // Month and year names - using translations
  const monthNames = [
    t('january'), t('february'), t('march'), t('april'), t('may'), t('june'),
    t('july'), t('august'), t('september'), t('october'), t('november'), t('december')
  ];

  const dayNames = [t('sun'), t('mon'), t('tue'), t('wed'), t('thu'), t('fri'), t('sat')];

  const days = getDaysInMonth(currentMonth);

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Input Field */}
      <div
        ref={inputRef}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        className={`w-full px-4 py-3 border rounded-lg cursor-pointer transition-colors ${
          disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white hover:border-gray-400'
        } ${error ? 'border-red-500' : 'border-gray-300'} ${
          isOpen ? 'ring-2 ring-amber-500 border-transparent' : ''
        }`}
        style={{
          borderColor: error ? '#ef4444' : isOpen ? '#f59e0b' : '#d1d5db',
          outline: 'none'
        }}
      >
        <div className="flex items-center justify-between">
          <span style={{ color: selectedDate ? '#374151' : '#9ca3af' }}>
            {selectedDate ? formatDate(selectedDate) : placeholder}
          </span>
          <svg 
            className={`w-5 h-5 transition-transform ${isOpen ? 'rotate-180' : ''}`}
            style={{ color: '#6b7280' }}
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>

      {/* Dropdown Calendar */}
      {isOpen && (
        <div 
          className="absolute z-50 mt-1 bg-white border border-gray-300 rounded-md shadow-lg"
          style={{ 
            minWidth: '280px',
            borderColor: '#d1d5db',
            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
          }}
        >
          {/* Header */}
          <div className="p-3 border-b" style={{ borderColor: '#e5e7eb' }}>
            {/* Navigation and Manual Input Toggle */}
            <div className="flex items-center justify-between mb-2">
              <button
                type="button"
                onClick={() => navigateMonth(-1)}
                className="p-1 rounded hover:bg-gray-100 transition-colors"
                style={{ color: '#6b7280' }}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>

              <div className="flex items-center space-x-2">
                <button
                  type="button"
                  onClick={() => setShowManualInput(!showManualInput)}
                  className="text-xs px-2 py-1 rounded transition-colors"
                  style={{
                    backgroundColor: showManualInput ? '#f59e0b' : '#f3f4f6',
                    color: showManualInput ? '#ffffff' : '#6b7280'
                  }}
                >
                  {showManualInput ? t('Calendar') : t('Type Date')}
                </button>
              </div>

              <button
                type="button"
                onClick={() => navigateMonth(1)}
                className="p-1 rounded hover:bg-gray-100 transition-colors"
                style={{ color: '#6b7280' }}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>

            {/* Month/Year Display with Year Dropdown */}
            <div className="flex items-center justify-center space-x-2">
              <span className="font-medium" style={{ color: '#374151' }}>
                {monthNames[currentMonth.getMonth()]}
              </span>
              <div className="relative">
                <button
                  type="button"
                  onClick={() => setShowYearDropdown(!showYearDropdown)}
                  className="font-medium px-2 py-1 rounded hover:bg-gray-100 transition-colors flex items-center space-x-1"
                  style={{ color: '#374151' }}
                >
                  <span>{currentMonth.getFullYear()}</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {/* Year Dropdown */}
                {showYearDropdown && (
                  <div
                    className="absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-10"
                    style={{
                      maxHeight: '200px',
                      overflowY: 'auto',
                      minWidth: '80px',
                      borderColor: '#d1d5db'
                    }}
                  >
                    {generateYearOptions().map(year => (
                      <button
                        key={year}
                        type="button"
                        onClick={() => handleYearSelect(year)}
                        className="w-full px-3 py-1 text-left hover:bg-gray-100 transition-colors"
                        style={{
                          color: year === currentMonth.getFullYear() ? '#f59e0b' : '#374151',
                          fontWeight: year === currentMonth.getFullYear() ? 'bold' : 'normal'
                        }}
                      >
                        {year}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Content Area */}
          <div className="p-3">
            {showManualInput ? (
              /* Manual Input Section */
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: '#374151' }}>
                    {t('Enter Date Manually')}
                  </label>
                  <input
                    type="text"
                    value={manualInput}
                    onChange={handleManualInputChange}
                    placeholder="DD/MM/YYYY, DD-MM-YYYY, or YYYY-MM-DD"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    style={{ borderColor: '#d1d5db' }}
                  />
                  <p className="text-xs mt-1" style={{ color: '#6b7280' }}>
                    {t('Supported formats: DD/MM/YYYY, DD-MM-YYYY, YYYY-MM-DD')}
                  </p>
                </div>
                <div className="flex space-x-2">
                  <button
                    type="button"
                    onClick={handleManualInputSubmit}
                    className="px-4 py-2 text-sm rounded transition-colors"
                    style={{ backgroundColor: '#f59e0b', color: '#ffffff' }}
                  >
                    {t('Apply')}
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowManualInput(false)}
                    className="px-4 py-2 text-sm rounded transition-colors"
                    style={{ backgroundColor: '#f3f4f6', color: '#6b7280' }}
                  >
                    {t('Cancel')}
                  </button>
                </div>
              </div>
            ) : (
              /* Calendar Grid */
              <div>
                {/* Day Headers */}
                <div className="grid grid-cols-7 gap-1 mb-2">
                  {dayNames.map(day => (
                    <div
                      key={day}
                      className="text-center text-xs font-medium py-2"
                      style={{ color: '#6b7280' }}
                    >
                      {day}
                    </div>
                  ))}
                </div>

                {/* Days Grid */}
                <div className="grid grid-cols-7 gap-1">
                  {days.map((day, index) => {
                    if (!day) {
                      return <div key={index} className="h-8"></div>;
                    }

                    const isSelected = selectedDate &&
                      day.getDate() === selectedDate.getDate() &&
                      day.getMonth() === selectedDate.getMonth() &&
                      day.getFullYear() === selectedDate.getFullYear();

                    const isToday = new Date().toDateString() === day.toDateString();

                    return (
                      <button
                        key={index}
                        type="button"
                        onClick={() => handleDateSelect(day)}
                        className={`h-8 w-8 text-sm rounded transition-colors ${
                          isSelected
                            ? 'text-white'
                            : isToday
                            ? 'font-bold'
                            : 'hover:bg-gray-100'
                        }`}
                        style={{
                          backgroundColor: isSelected ? '#f59e0b' : 'transparent',
                          color: isSelected ? '#ffffff' : isToday ? '#f59e0b' : '#374151'
                        }}
                      >
                        {day.getDate()}
                      </button>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomDatePicker;
