import { useState } from 'react';
import { CheckCircleIcon, CurrencyDollarIcon } from '@heroicons/react/24/outline';

export default function BookingPricingSection({ 
  service, 
  bookingDetails, 
  setBookingDetails, 
  calculateTotalAmount 
}) {
  const [selectedPackageId, setSelectedPackageId] = useState(null);

  // Determine pricing scenario
  const getPricingScenario = () => {
    if (!service) return 'basic';
    
    const hasPackages = service.packages && service.packages.length > 0;
    const hasAgePricing = service.agePricing || (hasPackages && service.packages.some(pkg => pkg.agePricing));
    
    if (!hasPackages && !hasAgePricing) return 'basic';
    if (hasPackages && !hasAgePricing) return 'packages-only';
    if (!hasPackages && hasAgePricing) return 'age-only';
    return 'full-variation';
  };

  const scenario = getPricingScenario();

  // Handle package selection
  const handlePackageSelection = (packageId, packageCode) => {
    setSelectedPackageId(packageId);
    setBookingDetails(prev => ({ 
      ...prev, 
      selectedPackage: packageCode.toLowerCase(),
      selectedPackageId: packageId 
    }));
  };

  // Get selected package data
  const getSelectedPackage = () => {
    if (!service.packages || service.packages.length === 0) return null;
    return service.packages.find(pkg => pkg.id === selectedPackageId) || service.packages[0];
  };

  // Render package selection for scenarios with packages
  const renderPackageSelection = () => {
    if (!service.packages || service.packages.length === 0) return null;

    return (
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-4">
          <CurrencyDollarIcon className="w-4 h-4 inline mr-1" />
          Select Package <span className="text-red-500">*</span>
        </label>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {service.packages.map((pkg) => (
            <div
              key={pkg.id}
              className={`border-2 rounded-lg p-4 cursor-pointer transition-colors relative ${
                selectedPackageId === pkg.id
                  ? 'border-amber-500 bg-amber-50'
                  : 'border-gray-200 hover:border-amber-300'
              }`}
              onClick={() => handlePackageSelection(pkg.id, pkg.packageType.code)}
            >
              {/* Popular Badge for Premium Package */}
              {pkg.packageType.code === 'PREMIUM' && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-amber-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="flex items-center justify-between mb-2">
                <h3 className="font-semibold text-gray-900">{pkg.packageType.name}</h3>
                <div className="text-lg font-bold text-amber-600">
                  {scenario === 'packages-only' ? (
                    `RM ${pkg.basePrice}`
                  ) : (
                    <span className="text-sm">From RM {pkg.agePricing?.adult || pkg.basePrice}</span>
                  )}
                </div>
              </div>

              <p className="text-sm text-gray-600 mb-3">{pkg.description || pkg.packageType.description}</p>

              {/* Package Inclusions with Overflow Handling */}
              <div className="mt-3">
                <div className="flex flex-wrap gap-1">
                  {pkg.includedItems.slice(0, 6).map((item, idx) => (
                    <span
                      key={idx}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
                    >
                      ✓ {item}
                    </span>
                  ))}
                  {pkg.includedItems.length > 6 && (
                    <div className="relative group">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 cursor-help">
                        +{pkg.includedItems.length - 6} more
                      </span>
                      
                      {/* Hover Tooltip */}
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 whitespace-nowrap max-w-xs">
                        <div className="text-center">
                          <div className="font-medium mb-1">Additional Items:</div>
                          <div className="space-y-1">
                            {pkg.includedItems.slice(6).map((item, idx) => (
                              <div key={idx} className="text-left">• {item}</div>
                            ))}
                          </div>
                        </div>
                        {/* Tooltip Arrow */}
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Age-based pricing preview for full variation */}
              {scenario === 'full-variation' && pkg.agePricing && (
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <div className="text-xs text-gray-600 space-y-1">
                    <div className="flex justify-between">
                      <span>Adults:</span>
                      <span className="font-medium">RM {pkg.agePricing.adult}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Children:</span>
                      <span className="font-medium">RM {pkg.agePricing.child}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Seniors:</span>
                      <span className="font-medium">RM {pkg.agePricing.senior}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Toddlers:</span>
                      <span className="font-medium text-green-600">
                        {pkg.agePricing.toddler === 0 ? 'FREE' : `RM ${pkg.agePricing.toddler}`}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render pricing summary based on scenario
  const renderPricingSummary = () => {
    switch (scenario) {
      case 'basic':
        return (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Pricing</h4>
            <div className="text-lg font-bold text-amber-600">
              RM {service.basePrice} per person
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Simple pricing for all passengers
            </p>
          </div>
        );

      case 'packages-only':
        const selectedPkg = getSelectedPackage();
        return selectedPkg ? (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">
              {selectedPkg.packageType.name} - RM {selectedPkg.basePrice} per person
            </h4>
            <p className="text-sm text-gray-600">
              Same price for all passengers
            </p>
          </div>
        ) : null;

      case 'age-only':
        return service.agePricing ? (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Age-based Pricing</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="flex justify-between">
                <span>Adults:</span>
                <span className="font-medium">RM {service.agePricing.adult}</span>
              </div>
              <div className="flex justify-between">
                <span>Children:</span>
                <span className="font-medium">RM {service.agePricing.child}</span>
              </div>
              <div className="flex justify-between">
                <span>Seniors:</span>
                <span className="font-medium">RM {service.agePricing.senior}</span>
              </div>
              <div className="flex justify-between">
                <span>Toddlers:</span>
                <span className="font-medium text-green-600">
                  {service.agePricing.toddler === 0 ? 'FREE' : `RM ${service.agePricing.toddler}`}
                </span>
              </div>
            </div>
          </div>
        ) : null;

      case 'full-variation':
        const fullSelectedPkg = getSelectedPackage();
        return fullSelectedPkg && fullSelectedPkg.agePricing ? (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">
              {fullSelectedPkg.packageType.name} - Age-based Pricing
            </h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="flex justify-between">
                <span>Adults:</span>
                <span className="font-medium">RM {fullSelectedPkg.agePricing.adult}</span>
              </div>
              <div className="flex justify-between">
                <span>Children:</span>
                <span className="font-medium">RM {fullSelectedPkg.agePricing.child}</span>
              </div>
              <div className="flex justify-between">
                <span>Seniors:</span>
                <span className="font-medium">RM {fullSelectedPkg.agePricing.senior}</span>
              </div>
              <div className="flex justify-between">
                <span>Toddlers:</span>
                <span className="font-medium text-green-600">
                  {fullSelectedPkg.agePricing.toddler === 0 ? 'FREE' : `RM ${fullSelectedPkg.agePricing.toddler}`}
                </span>
              </div>
            </div>
          </div>
        ) : null;

      default:
        return null;
    }
  };

  // Initialize selected package on first render
  useState(() => {
    if (service && service.packages && service.packages.length > 0 && !selectedPackageId) {
      const defaultPackage = service.packages.find(pkg => pkg.packageType.isDefault) || service.packages[0];
      setSelectedPackageId(defaultPackage.id);
      setBookingDetails(prev => ({ 
        ...prev, 
        selectedPackage: defaultPackage.packageType.code.toLowerCase(),
        selectedPackageId: defaultPackage.id 
      }));
    }
  }, [service]);

  return (
    <div className="space-y-6">
      {/* Package Selection (for scenarios with packages) */}
      {(scenario === 'packages-only' || scenario === 'full-variation') && renderPackageSelection()}
      
      {/* Pricing Summary */}
      {renderPricingSummary()}
      
      {/* Scenario Debug Info (remove in production) */}
      <div className="text-xs text-gray-400 p-2 bg-gray-100 rounded">
        Pricing Scenario: {scenario}
        {service.packages && ` | Packages: ${service.packages.length}`}
        {service.agePricing && ` | Age Pricing: Yes`}
      </div>
    </div>
  );
}
