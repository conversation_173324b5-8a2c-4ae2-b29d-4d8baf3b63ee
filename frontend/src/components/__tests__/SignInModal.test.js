import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import SignInModal from '../SignInModal';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Mock AuthContext
jest.mock('../../contexts/AuthContext', () => ({
  useAuth: jest.fn(),
}));

// Mock LanguageContext
jest.mock('../../contexts/LanguageContext', () => ({
  useLanguage: jest.fn(),
}));

// Mock sessionStorage
const mockSessionStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage,
});

// Mock window.location
delete window.location;
window.location = {
  pathname: '/test-page',
  search: '?param=value',
  href: '',
};

describe('SignInModal', () => {
  const mockRouter = {
    push: jest.fn(),
    query: {},
  };
  
  const mockAuth = {
    login: jest.fn(),
    isLoading: false,
    error: null,
  };

  const mockLanguage = {
    t: jest.fn((key) => key), // Simple mock that returns the key
    language: 'en',
    changeLanguage: jest.fn(),
  };

  const mockOnClose = jest.fn();
  const mockOnSwitchToSignUp = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockReturnValue(mockRouter);
    useAuth.mockReturnValue(mockAuth);
    useLanguage.mockReturnValue(mockLanguage);
    mockSessionStorage.getItem.mockClear();
    mockSessionStorage.setItem.mockClear();
    mockSessionStorage.removeItem.mockClear();
  });

  describe('Email/Password Sign-in', () => {
    it('should stay on current page after successful email/password login', async () => {
      // Mock successful login
      mockAuth.login.mockResolvedValue({ success: true });

      render(
        <SignInModal
          isOpen={true}
          onClose={mockOnClose}
          onSwitchToSignUp={mockOnSwitchToSignUp}
        />
      );

      // Fill in email and password
      const emailInput = screen.getByPlaceholderText(/email/i);
      const passwordInput = screen.getByPlaceholderText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockAuth.login).toHaveBeenCalledWith('<EMAIL>', 'password123');
      });

      // Should close modal but NOT redirect
      expect(mockOnClose).toHaveBeenCalled();
      expect(mockRouter.push).not.toHaveBeenCalled();
    });

    it('should show error message on failed login without redirecting', async () => {
      // Mock failed login
      mockAuth.login.mockResolvedValue({ 
        success: false, 
        error: 'Invalid credentials' 
      });

      render(
        <SignInModal
          isOpen={true}
          onClose={mockOnClose}
          onSwitchToSignUp={mockOnSwitchToSignUp}
        />
      );

      const emailInput = screen.getByPlaceholderText(/email/i);
      const passwordInput = screen.getByPlaceholderText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Invalid credentials')).toBeInTheDocument();
      });

      // Should NOT close modal or redirect
      expect(mockOnClose).not.toHaveBeenCalled();
      expect(mockRouter.push).not.toHaveBeenCalled();
    });
  });

  describe('Google OAuth Sign-in', () => {
    it('should store current URL in sessionStorage before redirecting to Google OAuth', () => {
      render(
        <SignInModal
          isOpen={true}
          onClose={mockOnClose}
          onSwitchToSignUp={mockOnSwitchToSignUp}
        />
      );

      const googleButton = screen.getByText(/continue with google/i);
      fireEvent.click(googleButton);

      // Should store current URL in sessionStorage
      expect(mockSessionStorage.setItem).toHaveBeenCalledWith(
        'gosea_return_url',
        '/test-page?param=value'
      );

      // Should redirect to Google OAuth
      expect(window.location.href).toBe('http://localhost:5001/api/auth/google?role=CUSTOMER');
    });
  });
});
