import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import BookingWizard from '../BookingWizard';

// Mock the icons
jest.mock('@heroicons/react/24/outline', () => ({
  CalendarIcon: () => <div data-testid="calendar-icon">Calendar</div>,
  TagIcon: () => <div data-testid="tag-icon">Tag</div>,
  UsersIcon: () => <div data-testid="users-icon">Users</div>,
  ChatBubbleLeftRightIcon: () => <div data-testid="chat-icon">Chat</div>,
  CheckCircleIcon: () => <div data-testid="check-icon">Check</div>,
}));

describe('BookingWizard', () => {
  const mockService = {
    id: 1,
    serviceType: { name: 'Test Service' },
    basePrice: 100,
    packages: [
      {
        id: 1,
        packageType: { code: 'BASIC', name: 'Basic Package', isDefault: true },
        basePrice: 80,
        agePricing: { adult: 80, child: 60, toddler: 0 }
      },
      {
        id: 2,
        packageType: { code: 'PREMIUM', name: 'Premium Package', isDefault: false },
        basePrice: 120,
        agePricing: { adult: 120, child: 90, toddler: 0 }
      }
    ]
  };

  const mockBookingDetails = {
    date: '',
    time: '',
    adults: '0',
    children: '0',
    toddlers: '0',
    selectedPackage: '',
    selectedPackageId: null,
    specialRequests: ''
  };

  const mockSetBookingDetails = jest.fn();
  const mockOnStepChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Pricing Scenario Detection', () => {
    test('should show package step for package-based pricing (scenario 2)', () => {
      const serviceWithPackages = {
        ...mockService,
        packages: [
          {
            id: 1,
            packageType: { code: 'BASIC', name: 'Basic Package' },
            basePrice: 80
          }
        ]
      };

      render(
        <BookingWizard
          service={serviceWithPackages}
          bookingDetails={mockBookingDetails}
          setBookingDetails={mockSetBookingDetails}
          onStepChange={mockOnStepChange}
        >
          <div>Test Content</div>
        </BookingWizard>
      );

      // Should show 4 steps including package selection
      expect(screen.getByText('Step 1')).toBeInTheDocument();
      expect(screen.getByText('Step 2')).toBeInTheDocument();
      expect(screen.getByText('Step 3')).toBeInTheDocument();
      expect(screen.getByText('Step 4')).toBeInTheDocument();
      expect(screen.getByText('Select Package')).toBeInTheDocument();
    });

    test('should hide package step for basic pricing (scenario 1)', () => {
      const serviceBasic = {
        ...mockService,
        packages: []
      };

      render(
        <BookingWizard
          service={serviceBasic}
          bookingDetails={mockBookingDetails}
          setBookingDetails={mockSetBookingDetails}
          onStepChange={mockOnStepChange}
        >
          <div>Test Content</div>
        </BookingWizard>
      );

      // Should show 3 steps without package selection
      expect(screen.getByText('Step 1')).toBeInTheDocument();
      expect(screen.getByText('Step 2')).toBeInTheDocument();
      expect(screen.getByText('Step 3')).toBeInTheDocument();
      expect(screen.queryByText('Step 4')).not.toBeInTheDocument();
      expect(screen.queryByText('Select Package')).not.toBeInTheDocument();
    });
  });

  describe('Step Indicators', () => {
    test('should show correct step indicators', () => {
      render(
        <BookingWizard
          service={mockService}
          bookingDetails={mockBookingDetails}
          setBookingDetails={mockSetBookingDetails}
          onStepChange={mockOnStepChange}
        >
          <div>Test Content</div>
        </BookingWizard>
      );

      expect(screen.getByText('Select Date')).toBeInTheDocument();
      expect(screen.getByText('Select Package')).toBeInTheDocument();
      expect(screen.getByText('Select Passengers')).toBeInTheDocument();
      expect(screen.getByText('Add Special Request')).toBeInTheDocument();
    });

    test('should show progress bar', () => {
      render(
        <BookingWizard
          service={mockService}
          bookingDetails={mockBookingDetails}
          setBookingDetails={mockSetBookingDetails}
          onStepChange={mockOnStepChange}
        >
          <div>Test Content</div>
        </BookingWizard>
      );

      expect(screen.getByText('Progress')).toBeInTheDocument();
      expect(screen.getByText('0% Complete')).toBeInTheDocument();
    });
  });

  describe('Step Validation', () => {
    test('should validate date selection step', () => {
      const bookingWithDate = {
        ...mockBookingDetails,
        date: '2024-12-25',
        time: '10:00'
      };

      render(
        <BookingWizard
          service={mockService}
          bookingDetails={bookingWithDate}
          setBookingDetails={mockSetBookingDetails}
          onStepChange={mockOnStepChange}
        >
          <div>Test Content</div>
        </BookingWizard>
      );

      expect(screen.getByText('25% Complete')).toBeInTheDocument();
    });

    test('should validate passenger selection step', () => {
      const bookingWithPassengers = {
        ...mockBookingDetails,
        adults: '2'
      };

      render(
        <BookingWizard
          service={mockService}
          bookingDetails={bookingWithPassengers}
          setBookingDetails={mockSetBookingDetails}
          onStepChange={mockOnStepChange}
        >
          <div>Test Content</div>
        </BookingWizard>
      );

      // Should show validation error for missing date
      expect(screen.getByText('Please complete this step to continue')).toBeInTheDocument();
    });
  });

  describe('Navigation', () => {
    test('should disable previous button on first step', () => {
      render(
        <BookingWizard
          service={mockService}
          bookingDetails={mockBookingDetails}
          setBookingDetails={mockSetBookingDetails}
          onStepChange={mockOnStepChange}
        >
          <div>Test Content</div>
        </BookingWizard>
      );

      const previousButton = screen.getByText('Previous');
      expect(previousButton).toBeDisabled();
    });

    test('should disable next button when step is invalid', () => {
      render(
        <BookingWizard
          service={mockService}
          bookingDetails={mockBookingDetails}
          setBookingDetails={mockSetBookingDetails}
          onStepChange={mockOnStepChange}
        >
          <div>Test Content</div>
        </BookingWizard>
      );

      const nextButton = screen.getByText('Next Step');
      expect(nextButton).toBeDisabled();
    });

    test('should enable next button when step is valid', () => {
      const validBookingDetails = {
        ...mockBookingDetails,
        date: '2024-12-25',
        time: '10:00'
      };

      render(
        <BookingWizard
          service={mockService}
          bookingDetails={validBookingDetails}
          setBookingDetails={mockSetBookingDetails}
          onStepChange={mockOnStepChange}
        >
          <div>Test Content</div>
        </BookingWizard>
      );

      const nextButton = screen.getByText('Next Step');
      expect(nextButton).not.toBeDisabled();
    });
  });

  describe('Step Content', () => {
    test('should render children content', () => {
      render(
        <BookingWizard
          service={mockService}
          bookingDetails={mockBookingDetails}
          setBookingDetails={mockSetBookingDetails}
          onStepChange={mockOnStepChange}
        >
          <div data-testid="step-content">Custom Step Content</div>
        </BookingWizard>
      );

      expect(screen.getByTestId('step-content')).toBeInTheDocument();
      expect(screen.getByText('Custom Step Content')).toBeInTheDocument();
    });

    test('should show current step header', () => {
      render(
        <BookingWizard
          service={mockService}
          bookingDetails={mockBookingDetails}
          setBookingDetails={mockSetBookingDetails}
          onStepChange={mockOnStepChange}
        >
          <div>Test Content</div>
        </BookingWizard>
      );

      expect(screen.getByText('Step 1: Select Date')).toBeInTheDocument();
      expect(screen.getByText('This step is required to continue')).toBeInTheDocument();
    });
  });
});
