import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';
import LoginForm from '../auth/LoginForm';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Mock AuthContext
jest.mock('../../contexts/AuthContext', () => ({
  useAuth: jest.fn(),
}));

describe('LoginForm', () => {
  const mockRouter = {
    push: jest.fn(),
    query: {},
  };
  
  const mockAuth = {
    login: jest.fn(),
    isLoading: false,
    error: null,
    clearError: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockReturnValue(mockRouter);
    useAuth.mockReturnValue(mockAuth);
  });

  describe('Redirect behavior', () => {
    it('should redirect to home page by default after successful login', async () => {
      // Mock successful login
      mockAuth.login.mockResolvedValue({ success: true });

      render(<LoginForm />);

      // Fill in email and password
      const emailInput = screen.getByPlaceholderText(/email/i);
      const passwordInput = screen.getByPlaceholderText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockAuth.login).toHaveBeenCalledWith('<EMAIL>', 'password123');
      });

      // Should redirect to home page (not dashboard)
      expect(mockRouter.push).toHaveBeenCalledWith('/');
    });

    it('should redirect to specified redirect parameter when provided', async () => {
      // Mock successful login with redirect parameter
      mockAuth.login.mockResolvedValue({ success: true });
      mockRouter.query = { redirect: '/boats/123' };

      render(<LoginForm />);

      // Fill in email and password
      const emailInput = screen.getByPlaceholderText(/email/i);
      const passwordInput = screen.getByPlaceholderText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockAuth.login).toHaveBeenCalledWith('<EMAIL>', 'password123');
      });

      // Should redirect to specified page
      expect(mockRouter.push).toHaveBeenCalledWith('/boats/123');
    });

    it('should not redirect on failed login', async () => {
      // Mock failed login
      mockAuth.login.mockResolvedValue({ 
        success: false, 
        error: 'Invalid credentials' 
      });

      render(<LoginForm />);

      const emailInput = screen.getByPlaceholderText(/email/i);
      const passwordInput = screen.getByPlaceholderText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockAuth.login).toHaveBeenCalledWith('<EMAIL>', 'wrongpassword');
      });

      // Should NOT redirect on failed login
      expect(mockRouter.push).not.toHaveBeenCalled();
    });
  });

  describe('Form validation', () => {
    it('should show validation errors for empty fields', async () => {
      render(<LoginForm />);

      const submitButton = screen.getByRole('button', { name: /sign in/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/email is required/i)).toBeInTheDocument();
        expect(screen.getByText(/password is required/i)).toBeInTheDocument();
      });

      // Should not attempt login with empty fields
      expect(mockAuth.login).not.toHaveBeenCalled();
      expect(mockRouter.push).not.toHaveBeenCalled();
    });

    it('should show validation error for invalid email format', async () => {
      render(<LoginForm />);

      const emailInput = screen.getByPlaceholderText(/email/i);
      const passwordInput = screen.getByPlaceholderText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument();
      });

      // Should not attempt login with invalid email
      expect(mockAuth.login).not.toHaveBeenCalled();
      expect(mockRouter.push).not.toHaveBeenCalled();
    });
  });
});
