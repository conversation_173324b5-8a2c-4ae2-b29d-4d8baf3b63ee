import React, { useState } from 'react';
import { useRouter } from 'next/router';
import { useLanguage } from '../contexts/LanguageContext';
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline';
import CustomDateTimePicker from './CustomDateTimePicker';
import JettySelector from './search/JettySelector';
import ServiceCategorySelector from './search/ServiceCategorySelector';
import DestinationSelector from './search/DestinationSelector';
import RouteSelector from './search/RouteSelector';

/**
 * Reusable Boat Search Form Component
 * Can be used on homepage hero section and search page
 */
export default function BoatSearchForm({ 
  initialFilters = {}, 
  onSearch = null, 
  variant = 'hero', // 'hero' or 'page'
  className = '' 
}) {
  const { t } = useLanguage();
  const router = useRouter();

  // Detect mobile device
  const isMobile = typeof window !== 'undefined' &&
    (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
     window.innerWidth <= 768);

  // Convert legacy date/time to datetime format for backward compatibility
  const convertLegacyDateTime = (filters) => {
    if (filters.date && filters.time) {
      return `${filters.date}T${filters.time}`;
    } else if (filters.date) {
      return filters.date;
    } else if (filters.datetime) {
      return filters.datetime;
    }
    return '';
  };

  const [searchFilters, setSearchFilters] = useState({
    jettyId: initialFilters.jettyId || initialFilters.jetty || '',
    serviceCategory: initialFilters.serviceCategory || initialFilters.serviceType || '',
    destinationId: initialFilters.destinationId || initialFilters.destination || '',
    datetime: convertLegacyDateTime(initialFilters)
  });

  // No longer need to track passenger transport since destination is always required

  const [errors, setErrors] = useState({});

  // Handle filter changes
  const handleFilterChange = (field, value) => {
    setSearchFilters(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Validate form fields
  const validateForm = () => {
    const newErrors = {};

    // Destination is now required as primary filter
    if (!searchFilters.destinationId) {
      newErrors.destinationId = t('destinationRequired');
    }

    // Service category becomes required if jetty is selected
    if (searchFilters.jettyId && !searchFilters.serviceCategory) {
      newErrors.serviceCategory = t('serviceTypeRequired');
    }

    // Only require datetime for search page variant
    if (variant === 'page') {
      if (!searchFilters.datetime) {
        newErrors.datetime = t('dateTimeRequired');
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle search submission
  const handleSearch = (e) => {
    e.preventDefault();
    e.stopPropagation();

    console.log('Search form submitted', { searchFilters, variant });

    // Validate form before proceeding
    if (!validateForm()) {
      console.log('Form validation failed');
      return;
    }

    console.log('Form validation passed');

    if (onSearch) {
      // If onSearch callback is provided, use it (for search page)
      console.log('Using onSearch callback');
      onSearch(searchFilters);
    } else {
      // Otherwise, navigate to boats page with filters (for homepage)
      console.log('Navigating to boats page');
      const queryParams = new URLSearchParams();
      Object.entries(searchFilters).forEach(([key, value]) => {
        if (value) queryParams.append(key, value);
      });

      const searchUrl = queryParams.toString()
        ? `/boats?${queryParams.toString()}`
        : '/boats';

      console.log('Navigating to:', searchUrl);

      // Use window.location for mobile devices for more reliable navigation
      if (isMobile) {
        console.log('Using window.location for mobile');
        window.location.href = searchUrl;
      } else {
        console.log('Using router.push for desktop');
        router.push(searchUrl);
      }
    }
  };

  // Clear all filters
  const clearFilters = () => {
    const emptyFilters = {
      jettyId: '',
      serviceCategory: '',
      destinationId: '',
      datetime: ''
    };
    setSearchFilters(emptyFilters);

    if (onSearch) {
      onSearch(emptyFilters);
    }
  };

  const isHeroVariant = variant === 'hero';
  const containerClass = isHeroVariant
    ? 'bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl p-6 border border-white/20 w-full max-w-6xl mx-auto'
    : 'bg-white rounded-lg shadow-sm border p-6 w-full max-w-6xl mx-auto';

  return (
    <div className={`${containerClass} ${className} w-full overflow-visible`}>
      {!isHeroVariant && (
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">
            {t('searchFilters')}
          </h2>
        </div>
      )}

      <form onSubmit={handleSearch} className="overflow-visible">
        <div className={`grid gap-4 overflow-visible ${isHeroVariant ? 'grid-cols-1 md:grid-cols-4' : 'grid-cols-1 md:grid-cols-4'}`}>
          {/* Destination (Primary Filter) */}
          <div className="min-w-0">
            <label className="hidden sm:block text-sm font-medium text-gray-700 mb-2">
              {t('destination')} <span className="text-red-500">*</span>
            </label>
            <DestinationSelector
              value={searchFilters.destinationId}
              onChange={(value) => handleFilterChange('destinationId', value)}
              jettyId={searchFilters.jettyId}
              error={!!errors.destinationId}
              className="h-12 w-full min-w-0"
            />
            {errors.destinationId && (
              <p className="mt-1 text-sm text-red-600">{errors.destinationId}</p>
            )}
          </div>

          {/* Service Category */}
          <div className="min-w-0">
            <label className="hidden sm:block text-sm font-medium text-gray-700 mb-2">
              {t('serviceType')} {searchFilters.jettyId && <span className="text-red-500">*</span>}
            </label>
            <ServiceCategorySelector
              value={searchFilters.serviceCategory}
              onChange={(value) => handleFilterChange('serviceCategory', value)}
              error={!!errors.serviceCategory}
              className="h-12 w-full min-w-0"
            />
            {errors.serviceCategory && (
              <p className="mt-1 text-sm text-red-600">{errors.serviceCategory}</p>
            )}
          </div>

          {/* Jetty (Departure Point) */}
          <div className="min-w-0">
            <label className="hidden sm:block text-sm font-medium text-gray-700 mb-2">
              {t('jetty')}
            </label>
            <JettySelector
              value={searchFilters.jettyId}
              onChange={(value) => handleFilterChange('jettyId', value)}
              error={!!errors.jettyId}
              className="h-12 w-full min-w-0"
            />
            {errors.jettyId && (
              <p className="mt-1 text-sm text-red-600">{errors.jettyId}</p>
            )}
          </div>

          {/* DateTime */}
          <div className="min-w-0">
            <label className="hidden sm:block text-sm font-medium text-gray-700 mb-2">
              {t('when')} {variant === 'page' && <span className="text-red-500">*</span>}
            </label>
            <CustomDateTimePicker
              name="datetime"
              value={searchFilters.datetime}
              onChange={(e) => handleFilterChange('datetime', e.target.value)}
              placeholder={t('selectDateTime')}
              className="w-full min-w-0"
              error={!!errors.datetime}
            />
            {errors.datetime && (
              <p className="mt-1 text-sm text-red-600">{errors.datetime}</p>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className={`flex flex-col sm:flex-row justify-center gap-3 ${isHeroVariant ? 'mt-6' : 'mt-6'} relative z-10`}>
          <button
            type="submit"
            onClick={(e) => {
              // Fallback click handler for mobile devices
              console.log('Button clicked directly');

              // On mobile, sometimes form submission doesn't work properly
              // So we handle it directly in the click handler as a fallback
              if (isMobile) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Mobile click handler triggered');
                handleSearch(e);
              }
              // Let the form submission handle the logic for desktop
            }}
            className="flex items-center justify-center space-x-2 px-4 py-3 bg-amber-500 text-white rounded-md hover:bg-amber-600 active:bg-amber-700 transition-colors font-medium text-sm touch-manipulation min-h-[44px] w-full sm:w-auto sm:min-w-[120px]"
            style={{
              WebkitTapHighlightColor: 'transparent',
              touchAction: 'manipulation',
              cursor: 'pointer',
              userSelect: 'none'
            }}
          >
            <MagnifyingGlassIcon className="w-4 h-4" />
            <span>{t('searchBoats')}</span>
          </button>

          <button
            type="button"
            onClick={clearFilters}
            className="flex items-center justify-center space-x-2 px-4 py-3 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 active:bg-gray-100 transition-colors font-medium text-sm touch-manipulation min-h-[44px] w-full sm:w-auto sm:min-w-[120px]"
            style={{
              WebkitTapHighlightColor: 'transparent',
              touchAction: 'manipulation'
            }}
          >
            <XMarkIcon className="w-4 h-4" />
            <span>{t('clearFilters')}</span>
          </button>
        </div>
      </form>


    </div>
  );
}
