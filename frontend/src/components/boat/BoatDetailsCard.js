import React from 'react';
import { MapPinIcon, UsersIcon, StarIcon } from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

/**
 * BoatDetailsCard Component
 * Displays main boat information including name, location, capacity, and rating
 */
export default function BoatDetailsCard({ boat }) {
  const { t } = useLanguage();

  if (!boat) return null;

  const formatLocation = (location) => {
    switch (location) {
      case 'REDANG':
        return t('redangIsland');
      case 'PERHENTIAN':
        return t('perhentianIsland');
      default:
        return location;
    }
  };

  const formatServiceType = (serviceType) => {
    switch (serviceType) {
      case 'SNORKELING':
        return t('snorkeling');
      case 'PASSENGER':
        return t('passengerBoat');
      default:
        return serviceType;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between">
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-gray-900 mb-3">
              {boat.name}
            </h1>
            
            {/* Boat Info */}
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
              <div className="flex items-center space-x-1">
                <MapPinIcon className="w-4 h-4" />
                <span>{formatLocation(boat.location)}</span>
              </div>
              
              <div className="flex items-center space-x-1">
                <UsersIcon className="w-4 h-4" />
                <span>{boat.capacity} {t('people')}</span>
              </div>
              
              <div className="flex items-center space-x-1">
                <span className="px-2 py-1 bg-amber-100 text-amber-800 rounded-full text-xs font-medium">
                  {formatServiceType(boat.serviceType)}
                </span>
              </div>
            </div>

            {/* Rating */}
            {boat.owner?.rating && (
              <div className="flex items-center space-x-2 mb-4">
                <div className="flex items-center space-x-1">
                  <StarIcon className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="font-medium text-gray-900">
                    {boat.owner.rating}
                  </span>
                </div>
                {boat.owner.reviewCount && (
                  <span className="text-sm text-gray-600">
                    ({boat.owner.reviewCount} {t('reviews')})
                  </span>
                )}
              </div>
            )}
          </div>

          {/* Price Display */}
          <div className="mt-4 lg:mt-0 text-right">
            <div className="text-3xl font-bold text-amber-600">
              RM {boat.basePrice}
            </div>
            <div className="text-sm text-gray-500">
              {t('startingPrice')}
            </div>
          </div>
        </div>
      </div>

      {/* Description */}
      {boat.description && (
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">
            {t('description')}
          </h2>
          <p className="text-gray-600 leading-relaxed">
            {boat.description}
          </p>
        </div>
      )}

      {/* Package Details */}
      {boat.packageDetails && (
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">
            {t('packageDetails')}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {boat.packageDetails.duration && (
              <div className="flex justify-between">
                <span className="text-gray-600">{t('duration')}:</span>
                <span className="font-medium">{boat.packageDetails.duration}</span>
              </div>
            )}
            
            {boat.packageDetails.departureTime && (
              <div className="flex justify-between">
                <span className="text-gray-600">{t('departureTime')}:</span>
                <span className="font-medium">{boat.packageDetails.departureTime}</span>
              </div>
            )}
            
            {boat.packageDetails.returnTime && (
              <div className="flex justify-between">
                <span className="text-gray-600">{t('returnTime')}:</span>
                <span className="font-medium">{boat.packageDetails.returnTime}</span>
              </div>
            )}
            
            {boat.packageDetails.meetingPoint && (
              <div className="flex justify-between">
                <span className="text-gray-600">{t('meetingPoint')}:</span>
                <span className="font-medium">{boat.packageDetails.meetingPoint}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Owner Information */}
      {boat.owner && (
        <div className="border-t pt-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">
            {t('operatedBy')}
          </h2>
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-amber-500 rounded-full flex items-center justify-center">
              <span className="text-white font-semibold text-lg">
                {boat.owner.companyName ? boat.owner.companyName.charAt(0).toUpperCase() : 'B'}
              </span>
            </div>
            <div>
              <div className="font-medium text-gray-900">
                {boat.owner.companyName || t('boatOperator')}
              </div>
              <div className="text-sm text-gray-600">
                {t('verifiedOperator')}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
