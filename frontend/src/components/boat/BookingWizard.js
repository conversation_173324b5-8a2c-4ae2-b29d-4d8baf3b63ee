import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  CalendarIcon, 
  ClockIcon, 
  UsersIcon, 
  CurrencyDollarIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CheckIcon
} from '@heroicons/react/24/outline';

/**
 * BookingWizard Component
 * Multi-step booking form with calendar, package selection, and passenger count
 */
export default function BookingWizard({ boat, onBookingComplete }) {
  const { t } = useLanguage();
  const [currentStep, setCurrentStep] = useState(1);
  const [bookingData, setBookingData] = useState({
    selectedDate: null,
    selectedPackage: null,
    adults: 1,
    children: 0,
    toddlers: 0,
    totalPrice: 0
  });

  // Calculate total price when booking data changes
  useEffect(() => {
    if (bookingData.selectedPackage) {
      const adultPrice = boat.agePricing?.adult || boat.basePrice;
      const childPrice = boat.agePricing?.child || (boat.basePrice * 0.7);
      const toddlerPrice = boat.agePricing?.toddler || 0;
      const packageMultiplier = bookingData.selectedPackage.priceMultiplier || 1;

      const totalPrice = (
        (bookingData.adults * adultPrice * packageMultiplier) +
        (bookingData.children * childPrice * packageMultiplier) +
        (bookingData.toddlers * toddlerPrice * packageMultiplier)
      );

      setBookingData(prev => ({ ...prev, totalPrice }));
    }
  }, [bookingData.adults, bookingData.children, bookingData.toddlers, bookingData.selectedPackage, boat]);

  // Generate available dates (next 30 days)
  const generateAvailableDates = () => {
    const dates = [];
    const today = new Date();
    
    for (let i = 1; i <= 30; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      
      // Skip Mondays (example of unavailable days)
      if (date.getDay() !== 1) {
        dates.push({
          date: date,
          available: Math.random() > 0.3, // 70% availability simulation
          price: boat.basePrice + (Math.random() > 0.5 ? 20 : 0) // Price variation
        });
      }
    }
    
    return dates;
  };

  const availableDates = generateAvailableDates();

  // Enhanced packages with pricing multipliers
  const enhancedPackages = boat.packages?.map(pkg => ({
    ...pkg,
    priceMultiplier: pkg.name.includes('Premium') ? 1.5 : 
                    pkg.name.includes('Full Day') ? 1.3 : 1.0,
    features: pkg.includedItems || []
  })) || [];

  const handleDateSelect = (dateInfo) => {
    setBookingData(prev => ({ 
      ...prev, 
      selectedDate: dateInfo 
    }));
  };

  const handlePackageSelect = (packageInfo) => {
    setBookingData(prev => ({ 
      ...prev, 
      selectedPackage: packageInfo 
    }));
  };

  const handlePassengerChange = (type, value) => {
    const newValue = Math.max(0, value);
    if (type === 'adults' && newValue === 0) return; // At least 1 adult required
    
    setBookingData(prev => ({ 
      ...prev, 
      [type]: newValue 
    }));
  };

  const canProceedToNextStep = () => {
    switch (currentStep) {
      case 1: return bookingData.selectedDate;
      case 2: return bookingData.selectedPackage;
      case 3: return bookingData.adults > 0;
      default: return false;
    }
  };

  const handleNext = () => {
    if (canProceedToNextStep()) {
      if (currentStep === 3) {
        // Final step - trigger booking
        onBookingComplete && onBookingComplete(bookingData);
      } else {
        setCurrentStep(prev => prev + 1);
      }
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(1, prev - 1));
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {[1, 2, 3].map((step) => (
        <div key={step} className="flex items-center">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
            step <= currentStep 
              ? 'bg-amber-500 text-white' 
              : 'bg-gray-200 text-gray-500'
          }`}>
            {step < currentStep ? <CheckIcon className="w-4 h-4" /> : step}
          </div>
          {step < 3 && (
            <div className={`w-12 h-0.5 mx-2 ${
              step < currentStep ? 'bg-amber-500' : 'bg-gray-200'
            }`} />
          )}
        </div>
      ))}
    </div>
  );

  const renderDateSelection = () => (
    <div>
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <CalendarIcon className="w-5 h-5 mr-2 text-amber-500" />
        {t('selectDate')}
      </h3>
      
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
        {availableDates.slice(0, 12).map((dateInfo, index) => (
          <button
            key={index}
            onClick={() => dateInfo.available && handleDateSelect(dateInfo)}
            disabled={!dateInfo.available}
            className={`p-3 rounded-lg border text-sm transition-colors ${
              bookingData.selectedDate?.date?.getTime() === dateInfo.date.getTime()
                ? 'border-amber-500 bg-amber-50 text-amber-700'
                : dateInfo.available
                ? 'border-gray-200 hover:border-amber-300 hover:bg-amber-50'
                : 'border-gray-100 bg-gray-50 text-gray-400 cursor-not-allowed'
            }`}
          >
            <div className="font-medium">{formatDate(dateInfo.date)}</div>
            <div className="text-xs mt-1">
              {dateInfo.available ? `RM ${dateInfo.price}` : t('unavailable')}
            </div>
          </button>
        ))}
      </div>
      
      {bookingData.selectedDate && (
        <div className="mt-4 p-3 bg-amber-50 rounded-lg">
          <p className="text-sm text-amber-700">
            <strong>{t('selectedDate')}:</strong> {formatDate(bookingData.selectedDate.date)}
          </p>
        </div>
      )}
    </div>
  );

  const renderPackageSelection = () => (
    <div>
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <ClockIcon className="w-5 h-5 mr-2 text-amber-500" />
        {t('selectPackage')}
      </h3>
      
      <div className="space-y-4">
        {enhancedPackages.map((pkg) => (
          <div
            key={pkg.id}
            onClick={() => handlePackageSelect(pkg)}
            className={`p-4 rounded-lg border cursor-pointer transition-colors ${
              bookingData.selectedPackage?.id === pkg.id
                ? 'border-amber-500 bg-amber-50'
                : 'border-gray-200 hover:border-amber-300 hover:bg-amber-50'
            }`}
          >
            <div className="flex justify-between items-start mb-2">
              <h4 className="font-medium text-gray-900">{pkg.name}</h4>
              <span className="text-lg font-semibold text-amber-600">
                RM {Math.round(boat.basePrice * pkg.priceMultiplier)}
              </span>
            </div>
            <p className="text-sm text-gray-600 mb-3">{pkg.description}</p>
            <div className="flex flex-wrap gap-2">
              {pkg.features.map((feature, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
                >
                  {feature}
                </span>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderPassengerSelection = () => (
    <div>
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <UsersIcon className="w-5 h-5 mr-2 text-amber-500" />
        {t('selectPassengers')}
      </h3>
      
      <div className="space-y-6">
        {/* Adults */}
        <div className="flex items-center justify-between p-4 border rounded-lg">
          <div>
            <h4 className="font-medium text-gray-900">{t('adultsAge')}</h4>
            <p className="text-sm text-gray-600">
              RM {boat.agePricing?.adult || boat.basePrice} per person
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => handlePassengerChange('adults', bookingData.adults - 1)}
              disabled={bookingData.adults <= 1}
              className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center disabled:opacity-50"
            >
              -
            </button>
            <span className="w-8 text-center font-medium">{bookingData.adults}</span>
            <button
              onClick={() => handlePassengerChange('adults', bookingData.adults + 1)}
              className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center"
            >
              +
            </button>
          </div>
        </div>

        {/* Children */}
        <div className="flex items-center justify-between p-4 border rounded-lg">
          <div>
            <h4 className="font-medium text-gray-900">{t('childrenAge')}</h4>
            <p className="text-sm text-gray-600">
              RM {boat.agePricing?.child || Math.round(boat.basePrice * 0.7)} per person
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => handlePassengerChange('children', bookingData.children - 1)}
              disabled={bookingData.children <= 0}
              className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center disabled:opacity-50"
            >
              -
            </button>
            <span className="w-8 text-center font-medium">{bookingData.children}</span>
            <button
              onClick={() => handlePassengerChange('children', bookingData.children + 1)}
              className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center"
            >
              +
            </button>
          </div>
        </div>

        {/* Toddlers */}
        <div className="flex items-center justify-between p-4 border rounded-lg">
          <div>
            <h4 className="font-medium text-gray-900">{t('toddlersAge')}</h4>
            <p className="text-sm text-green-600">
              {t('free')}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => handlePassengerChange('toddlers', bookingData.toddlers - 1)}
              disabled={bookingData.toddlers <= 0}
              className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center disabled:opacity-50"
            >
              -
            </button>
            <span className="w-8 text-center font-medium">{bookingData.toddlers}</span>
            <button
              onClick={() => handlePassengerChange('toddlers', bookingData.toddlers + 1)}
              className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center"
            >
              +
            </button>
          </div>
        </div>

        {/* Total */}
        <div className="bg-amber-50 p-4 rounded-lg">
          <div className="flex justify-between items-center">
            <span className="font-medium text-gray-900">{t('totalPrice')}:</span>
            <span className="text-xl font-bold text-amber-600">
              RM {bookingData.totalPrice.toFixed(2)}
            </span>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            {bookingData.adults} {t('adults')} + {bookingData.children} {t('children')} + {bookingData.toddlers} {t('toddlers')}
          </p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-6">
        {t('bookThisBoat')}
      </h2>

      {renderStepIndicator()}

      <div className="min-h-[400px]">
        {currentStep === 1 && renderDateSelection()}
        {currentStep === 2 && renderPackageSelection()}
        {currentStep === 3 && renderPassengerSelection()}
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between mt-8">
        <button
          onClick={handlePrevious}
          disabled={currentStep === 1}
          className="flex items-center px-4 py-2 text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeftIcon className="w-4 h-4 mr-1" />
          {t('previous')}
        </button>

        <button
          onClick={handleNext}
          disabled={!canProceedToNextStep()}
          className="flex items-center px-6 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {currentStep === 3 ? t('bookNow') : t('next')}
          {currentStep < 3 && <ChevronRightIcon className="w-4 h-4 ml-1" />}
        </button>
      </div>
    </div>
  );
}
