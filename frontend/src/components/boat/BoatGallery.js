import React, { useState } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

/**
 * BoatGallery Component
 * Photo gallery with zoom functionality and mobile-responsive design
 */
export default function BoatGallery({ images = [] }) {
  const { t } = useLanguage();
  const [selectedImage, setSelectedImage] = useState(0);
  const [isZoomOpen, setIsZoomOpen] = useState(false);
  const [zoomImageIndex, setZoomImageIndex] = useState(0);

  if (!images || images.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="w-full h-64 bg-gray-200 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <span className="text-4xl text-gray-400 mb-2 block">⛵</span>
            <p className="text-gray-500">{t('noImagesAvailable')}</p>
          </div>
        </div>
      </div>
    );
  }

  const openZoom = (index) => {
    setZoomImageIndex(index);
    setIsZoomOpen(true);
  };

  const closeZoom = () => {
    setIsZoomOpen(false);
  };

  const nextZoomImage = () => {
    setZoomImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevZoomImage = () => {
    setZoomImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const handleKeyDown = (e) => {
    if (!isZoomOpen) return;
    
    switch (e.key) {
      case 'Escape':
        closeZoom();
        break;
      case 'ArrowRight':
        nextZoomImage();
        break;
      case 'ArrowLeft':
        prevZoomImage();
        break;
    }
  };

  // Add keyboard event listener
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isZoomOpen]);

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          {t('photos')}
        </h2>
        
        {/* Main Image */}
        <div className="mb-4">
          <div 
            className="relative w-full h-64 md:h-80 lg:h-96 rounded-lg overflow-hidden cursor-pointer group"
            onClick={() => openZoom(selectedImage)}
          >
            <img
              src={images[selectedImage].url}
              alt={images[selectedImage].alt}
              className="w-full h-full object-cover transition-transform group-hover:scale-105"
              loading="lazy"
            />
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-opacity flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="bg-white bg-opacity-90 rounded-full p-2">
                  <svg className="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Thumbnail Grid */}
        {images.length > 1 && (
          <div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2">
            {images.map((image, index) => (
              <button
                key={index}
                onClick={() => setSelectedImage(index)}
                className={`relative aspect-square rounded-lg overflow-hidden border-2 transition-all ${
                  selectedImage === index 
                    ? 'border-amber-500 ring-2 ring-amber-200' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <img
                  src={image.url}
                  alt={image.alt}
                  className="w-full h-full object-cover"
                  loading="lazy"
                />
                {selectedImage === index && (
                  <div className="absolute inset-0 bg-amber-500 bg-opacity-20"></div>
                )}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Zoom Modal */}
      {isZoomOpen && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center">
          <div className="relative w-full h-full flex items-center justify-center p-4">
            {/* Close Button */}
            <button
              onClick={closeZoom}
              className="absolute top-4 right-4 z-10 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-2 transition-colors"
            >
              <XMarkIcon className="w-6 h-6 text-white" />
            </button>

            {/* Navigation Buttons */}
            {images.length > 1 && (
              <>
                <button
                  onClick={prevZoomImage}
                  className="absolute left-4 z-10 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-2 transition-colors"
                >
                  <ChevronLeftIcon className="w-6 h-6 text-white" />
                </button>
                
                <button
                  onClick={nextZoomImage}
                  className="absolute right-4 z-10 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-2 transition-colors"
                >
                  <ChevronRightIcon className="w-6 h-6 text-white" />
                </button>
              </>
            )}

            {/* Zoomed Image */}
            <img
              src={images[zoomImageIndex].url}
              alt={images[zoomImageIndex].alt}
              className="max-w-full max-h-full object-contain"
            />

            {/* Image Counter */}
            {images.length > 1 && (
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-white bg-opacity-20 rounded-full px-3 py-1">
                <span className="text-white text-sm">
                  {zoomImageIndex + 1} / {images.length}
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
}
