import React, { useState } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';

/**
 * BoatPricing Component
 * Displays age-based pricing and booking interface
 */
export default function BoatPricing({ pricing, basePrice, onBookNow }) {
  const { t } = useLanguage();
  const [passengerCounts, setPassengerCounts] = useState({
    adult: 1,
    child: 0,
    infant: 0
  });

  if (!pricing && !basePrice) return null;

  const updatePassengerCount = (type, increment) => {
    setPassengerCounts(prev => {
      const newCount = Math.max(0, prev[type] + increment);
      // Ensure at least one adult
      if (type === 'adult' && newCount === 0) return prev;
      
      return {
        ...prev,
        [type]: newCount
      };
    });
  };

  const calculateTotal = () => {
    if (!pricing) return basePrice;
    
    const adultTotal = (pricing.adult || 0) * passengerCounts.adult;
    const childTotal = (pricing.child || 0) * passengerCounts.child;
    const infantTotal = (pricing.infant || 0) * passengerCounts.infant;
    
    return adultTotal + childTotal + infantTotal;
  };

  const getTotalPassengers = () => {
    return passengerCounts.adult + passengerCounts.child + passengerCounts.infant;
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(price).replace('MYR', 'RM');
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6 sticky top-8">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        {t('bookThisBoat')}
      </h3>

      {/* Pricing Table */}
      {pricing ? (
        <div className="space-y-4 mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">
            {t('selectPassengers')}
          </h4>
          
          {/* Adult Pricing */}
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium text-gray-900">{t('adult')}</div>
              <div className="text-sm text-gray-500">
                {formatPrice(pricing.adult)} {t('perPerson')}
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => updatePassengerCount('adult', -1)}
                disabled={passengerCounts.adult <= 1}
                className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                -
              </button>
              <span className="w-8 text-center font-medium">
                {passengerCounts.adult}
              </span>
              <button
                onClick={() => updatePassengerCount('adult', 1)}
                className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
              >
                +
              </button>
            </div>
          </div>

          {/* Child Pricing */}
          {pricing.child > 0 && (
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-gray-900">{t('child')}</div>
                <div className="text-sm text-gray-500">
                  {formatPrice(pricing.child)} {t('perPerson')}
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => updatePassengerCount('child', -1)}
                  disabled={passengerCounts.child <= 0}
                  className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  -
                </button>
                <span className="w-8 text-center font-medium">
                  {passengerCounts.child}
                </span>
                <button
                  onClick={() => updatePassengerCount('child', 1)}
                  className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                >
                  +
                </button>
              </div>
            </div>
          )}

          {/* Infant Pricing */}
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium text-gray-900">{t('infant')}</div>
              <div className="text-sm text-gray-500">
                {pricing.infant === 0 ? t('free') : `${formatPrice(pricing.infant)} ${t('perPerson')}`}
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => updatePassengerCount('infant', -1)}
                disabled={passengerCounts.infant <= 0}
                className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                -
              </button>
              <span className="w-8 text-center font-medium">
                {passengerCounts.infant}
              </span>
              <button
                onClick={() => updatePassengerCount('infant', 1)}
                className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
              >
                +
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <span className="text-gray-600">{t('basePrice')}:</span>
            <span className="font-semibold">{formatPrice(basePrice)}</span>
          </div>
        </div>
      )}

      {/* Summary */}
      <div className="border-t pt-4 mb-6">
        <div className="space-y-2">
          {pricing && (
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">{t('totalPassengers')}:</span>
              <span className="font-medium">{getTotalPassengers()}</span>
            </div>
          )}
          
          <div className="flex items-center justify-between text-lg">
            <span className="font-semibold">{t('total')}:</span>
            <span className="font-bold text-amber-600">
              {formatPrice(calculateTotal())}
            </span>
          </div>
        </div>
      </div>

      {/* Book Now Button */}
      <button
        onClick={() => onBookNow && onBookNow(passengerCounts, calculateTotal())}
        className="w-full px-6 py-3 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors font-semibold"
      >
        {t('bookNow')}
      </button>

      {/* Terms */}
      <p className="text-xs text-gray-500 mt-3 text-center">
        {t('bookingTermsShort')}
      </p>
    </div>
  );
}
