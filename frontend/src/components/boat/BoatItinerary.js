import React from 'react';
import { ClockIcon, CheckIcon } from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

/**
 * BoatItinerary Component
 * Displays schedule timeline and included items
 */
export default function BoatItinerary({ itinerary = [], includedItems = [], packageDetails }) {
  const { t } = useLanguage();

  if (!itinerary.length && !includedItems.length && !packageDetails) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-6">
        {t('itineraryAndInclusions')}
      </h2>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Itinerary Timeline */}
        {itinerary.length > 0 && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <ClockIcon className="w-5 h-5 mr-2 text-amber-500" />
              {t('schedule')}
            </h3>
            
            <div className="space-y-4">
              {itinerary.map((item, index) => (
                <div key={index} className="flex items-start space-x-4">
                  {/* Timeline Dot */}
                  <div className="flex flex-col items-center">
                    <div className="w-3 h-3 bg-amber-500 rounded-full"></div>
                    {index < itinerary.length - 1 && (
                      <div className="w-0.5 h-8 bg-gray-200 mt-2"></div>
                    )}
                  </div>
                  
                  {/* Timeline Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-amber-600">
                        {item.time}
                      </span>
                      {item.duration && (
                        <span className="text-xs text-gray-500">
                          {item.duration}
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-900 font-medium">
                      {item.activity}
                    </p>
                    {item.description && (
                      <p className="text-xs text-gray-600 mt-1">
                        {item.description}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Included Items */}
        {includedItems.length > 0 && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <CheckIcon className="w-5 h-5 mr-2 text-green-500" />
              {t('whatsIncluded')}
            </h3>
            
            <div className="space-y-3">
              {includedItems.map((item, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <CheckIcon className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-gray-700">{item}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Package Details */}
      {packageDetails && (
        <div className="mt-8 pt-6 border-t">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {t('importantInformation')}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {packageDetails.duration && (
              <div className="flex justify-between">
                <span className="text-gray-600">{t('duration')}:</span>
                <span className="font-medium text-gray-900">{packageDetails.duration}</span>
              </div>
            )}
            
            {packageDetails.departureTime && (
              <div className="flex justify-between">
                <span className="text-gray-600">{t('departureTime')}:</span>
                <span className="font-medium text-gray-900">{packageDetails.departureTime}</span>
              </div>
            )}
            
            {packageDetails.returnTime && (
              <div className="flex justify-between">
                <span className="text-gray-600">{t('returnTime')}:</span>
                <span className="font-medium text-gray-900">{packageDetails.returnTime}</span>
              </div>
            )}
            
            {packageDetails.meetingPoint && (
              <div className="flex justify-between">
                <span className="text-gray-600">{t('meetingPoint')}:</span>
                <span className="font-medium text-gray-900">{packageDetails.meetingPoint}</span>
              </div>
            )}
          </div>

          {/* Additional Information */}
          <div className="mt-6 p-4 bg-amber-50 rounded-lg">
            <h4 className="text-sm font-medium text-amber-800 mb-2">
              {t('pleaseNote')}
            </h4>
            <ul className="text-xs text-amber-700 space-y-1">
              <li>• {t('arriveEarly')}</li>
              <li>• {t('weatherDependent')}</li>
              <li>• {t('bringTowel')}</li>
              <li>• {t('followInstructions')}</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}
