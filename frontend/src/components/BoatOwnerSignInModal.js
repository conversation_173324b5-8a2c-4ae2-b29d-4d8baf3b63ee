import { useState } from 'react';
import { useRouter } from 'next/router';
import { useLanguage } from '../contexts/LanguageContext';

const BoatOwnerSignInModal = ({ isOpen, onClose }) => {
  const { t } = useLanguage();
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/auth/boat-owner/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (data.success) {
        // Store tokens
        localStorage.setItem('accessToken', data.accessToken);
        localStorage.setItem('refreshToken', data.refreshToken);
        localStorage.setItem('sessionToken', data.sessionToken);
        
        console.log('Boat owner login successful:', data.user.email);
        
        // Close modal
        onClose();
        
        // Redirect to boat owner dashboard
        router.push('/boat-owner/dashboard');
      } else {
        // Handle specific error codes
        switch (data.code) {
          case 'EMAIL_NOT_VERIFIED':
            setError('Please verify your email address first. Check your inbox for the verification email.');
            break;
          case 'PENDING_ADMIN_APPROVAL':
            setError('Your account is pending admin approval. You will receive an email once approved.');
            break;
          case 'ACCOUNT_LOCKED':
            setError(data.message);
            break;
          case 'ACCOUNT_DEACTIVATED':
            setError('Your account has been deactivated. Please contact support.');
            break;
          case 'INVALID_CREDENTIALS':
            setError('Invalid email or password. Please check your credentials.');
            break;
          default:
            setError(data.message || 'Sign in failed. Please try again.');
        }
      }
    } catch (err) {
      console.error('Boat owner sign in error:', err);
      setError('Network error. Please check your connection and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignUpRedirect = () => {
    onClose();
    router.push('/boat-owner/signup');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b" style={{ borderColor: '#e0f2fe' }}>
          <h2 className="text-2xl font-bold" style={{ color: '#0f172a' }}>
            {t('boatOwnerSignIn')}
          </h2>
          <button
            onClick={onClose}
            className="transition-colors"
            style={{ color: '#94a3b8' }}
            onMouseEnter={(e) => e.target.style.color = '#64748b'}
            onMouseLeave={(e) => e.target.style.color = '#94a3b8'}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <div className="px-4 py-3 rounded-md" style={{ backgroundColor: '#fef2f2', borderColor: '#fecaca', color: '#ef4444', border: '1px solid' }}>
                {error}
              </div>
            )}

            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-1" style={{ color: '#475569' }}>
                {t('email')} <span style={{ color: '#ef4444' }}>*</span>
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:border-transparent"
                style={{ borderColor: '#d1d5db' }}
                onFocus={(e) => {
                  e.target.style.borderColor = '#f59e0b';
                  e.target.style.boxShadow = '0 0 0 2px rgba(245, 158, 11, 0.2)';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#d1d5db';
                  e.target.style.boxShadow = 'none';
                }}
                placeholder={t('enterEmail')}
                required
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium mb-1" style={{ color: '#475569' }}>
                {t('password')} <span style={{ color: '#ef4444' }}>*</span>
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:border-transparent pr-10"
                  style={{ borderColor: '#d1d5db' }}
                  onFocus={(e) => {
                    e.target.style.borderColor = '#f59e0b';
                    e.target.style.boxShadow = '0 0 0 2px rgba(245, 158, 11, 0.2)';
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = '#d1d5db';
                    e.target.style.boxShadow = 'none';
                  }}
                  placeholder={t('enterPassword')}
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center transition-colors"
                  style={{ color: '#94a3b8' }}
                  onMouseEnter={(e) => e.target.style.color = '#64748b'}
                  onMouseLeave={(e) => e.target.style.color = '#94a3b8'}
                >
                  {showPassword ? (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  )}
                </button>
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full px-4 py-2 rounded-md font-medium transition-colors"
              style={{
                backgroundColor: isLoading ? '#94a3b8' : '#0ea5e9',
                color: '#ffffff'
              }}
              onMouseEnter={(e) => !isLoading && (e.target.style.backgroundColor = '#0284c7')}
              onMouseLeave={(e) => !isLoading && (e.target.style.backgroundColor = '#0ea5e9')}
            >
              {isLoading ? t('signingIn') : t('signIn')}
            </button>
          </form>

          {/* Divider */}
          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t" style={{ borderColor: '#e0f2fe' }} />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white" style={{ color: '#64748b' }}>{t('newToGoSea')}</span>
            </div>
          </div>

          {/* Switch to Sign Up */}
          <div className="text-center">
            <span style={{ color: '#64748b' }}>{t('dontHaveBusinessAccount')} </span>
            <button
              onClick={handleSignUpRedirect}
              className="font-medium transition-colors"
              style={{ color: '#f59e0b' }}
              onMouseEnter={(e) => e.target.style.color = '#d97706'}
              onMouseLeave={(e) => e.target.style.color = '#f59e0b'}
            >
              {t('registerBusiness')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BoatOwnerSignInModal;