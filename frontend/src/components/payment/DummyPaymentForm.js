import React, { useState } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';

const DummyPaymentForm = ({ 
  amount, 
  currency = 'MYR', 
  bookingId, 
  onPaymentSuccess, 
  onPaymentError,
  isDeposit = false 
}) => {
  const { t } = useLanguage();
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('online_banking');

  const handlePayment = async () => {
    setIsProcessing(true);

    try {
      // Step 1: Create payment intent
      const intentResponse = await fetch('/api/payments/create-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          bookingId,
          amount,
          currency
        })
      });

      const intentData = await intentResponse.json();

      if (!intentData.success) {
        throw new Error(intentData.message || 'Failed to create payment intent');
      }

      // Step 2: Simulate payment processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Step 3: Confirm payment
      const confirmResponse = await fetch('/api/payments/confirm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          paymentIntentId: intentData.paymentIntentId,
          bookingId,
          amount,
          currency,
          type: isDeposit ? 'boat_booking_deposit' : 'boat_booking'
        })
      });

      const confirmData = await confirmResponse.json();

      if (!confirmData.success) {
        throw new Error(confirmData.message || 'Payment confirmation failed');
      }

      // Success
      onPaymentSuccess && onPaymentSuccess(confirmData);

    } catch (error) {
      console.error('Payment error:', error);
      onPaymentError && onPaymentError(error.message);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold mb-4 text-gray-900">
        {isDeposit ? 'Pay Deposit' : 'Complete Payment'}
      </h3>

      {/* Payment Summary */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <div className="flex justify-between items-center mb-2">
          <span className="text-gray-600">
            {isDeposit ? 'Deposit Amount (30%)' : 'Total Amount'}:
          </span>
          <span className="text-xl font-bold text-gray-900">
            {currency} {amount.toFixed(2)}
          </span>
        </div>
        {isDeposit && (
          <div className="text-sm text-gray-500">
            Remaining balance will be collected later
          </div>
        )}
      </div>

      {/* Payment Method Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Select Payment Method
        </label>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="radio"
              name="paymentMethod"
              value="online_banking"
              checked={paymentMethod === 'online_banking'}
              onChange={(e) => setPaymentMethod(e.target.value)}
              className="mr-3"
            />
            <span>Online Banking (FPX)</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="paymentMethod"
              value="credit_card"
              checked={paymentMethod === 'credit_card'}
              onChange={(e) => setPaymentMethod(e.target.value)}
              className="mr-3"
            />
            <span>Credit/Debit Card</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="paymentMethod"
              value="ewallet"
              checked={paymentMethod === 'ewallet'}
              onChange={(e) => setPaymentMethod(e.target.value)}
              className="mr-3"
            />
            <span>E-Wallet (GrabPay, Touch 'n Go, etc.)</span>
          </label>
        </div>
      </div>

      {/* Dummy Payment Notice */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h4 className="text-sm font-medium text-yellow-800">
              Development Mode
            </h4>
            <p className="text-sm text-yellow-700 mt-1">
              This is a dummy payment gateway for development. No actual payment will be processed.
              The payment will be automatically marked as successful after a 2-second simulation.
            </p>
          </div>
        </div>
      </div>

      {/* Payment Button */}
      <button
        onClick={handlePayment}
        disabled={isProcessing}
        className={`w-full py-3 px-4 rounded-lg font-medium text-white transition-colors ${
          isProcessing
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
        }`}
      >
        {isProcessing ? (
          <div className="flex items-center justify-center">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing Payment...
          </div>
        ) : (
          `Pay ${currency} ${amount.toFixed(2)}`
        )}
      </button>

      {/* Security Notice */}
      <div className="mt-4 text-xs text-gray-500 text-center">
        <div className="flex items-center justify-center">
          <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
          </svg>
          Secured by SSL encryption
        </div>
      </div>
    </div>
  );
};

export default DummyPaymentForm;
