import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/router";
import { useLanguage } from "../contexts/LanguageContext";
import SignInModal from "./SignInModal";

const Footer = () => {
    const { t } = useLanguage();
    const router = useRouter();
    const [showSignInModal, setShowSignInModal] = useState(false);
    const [isClient, setIsClient] = useState(false);

    const currentYear = new Date().getFullYear();

    // Ensure client-side rendering for hash links
    useEffect(() => {
        setIsClient(true);
    }, []);

    // Handle hash link navigation
    const handleHashLink = (hash) => {
        if (router.pathname === '/') {
            // If on home page, scroll to section
            const element = document.getElementById(hash.replace('#', ''));
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
            }
        } else {
            // If on other page, navigate to home page with hash
            router.push(`/${hash}`);
        }
    };

    return (

        <footer style={{ backgroundColor: '#334155', color: '#ffffff' }}> {/* neutral-dark, white */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                    {/* Company Info */}
                    <div className="col-span-1 md:col-span-2">
                        <div className="flex items-center mb-4">
                            <Image
                                src="/images/templates/logo/gosea_logo.png"
                                alt="GoSea - Your Gateway to Malaysian Sea Adventures"
                                width={143}
                                height={143}
                                className="h-10"
                                style={{
                                    width: 'auto',
                                    objectFit: 'contain',
                                    filter: 'brightness(0) invert(1)', // Make logo white for dark footer
                                }}
                            />
                        </div>
                        <p className="mb-4 max-w-md" style={{ color: '#94a3b8' }}> {/* neutral-light */}
                            {t('footerDescription')}
                        </p>
                        {/* <div className="flex space-x-4">
                <a
                href="#"
                className="transition-colors"
                style={{ color: '#94a3b8' }} // neutral-light
                onMouseEnter={(e) => {
                    e.target.style.color = '#ffffff'; // white
                }}
                onMouseLeave={(e) => {
                    e.target.style.color = '#94a3b8'; // neutral-light
                }}
                >
                <span className="sr-only">Facebook</span>
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                </a>
                <a
                href="#"
                className="transition-colors"
                style={{ color: '#94a3b8' }} // neutral-light
                onMouseEnter={(e) => {
                    e.target.style.color = '#ffffff'; // white
                }}
                onMouseLeave={(e) => {
                    e.target.style.color = '#94a3b8'; // neutral-light
                }}
                >
                <span className="sr-only">Instagram</span>
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323C6.001 8.198 7.152 7.708 8.449 7.708s2.448.49 3.323 1.416c.875.926 1.365 2.077 1.365 3.374s-.49 2.448-1.365 3.323c-.875.875-2.026 1.167-3.323 1.167z"/>
                </svg>
                </a>
            </div> */}
                    </div>

                    {/* Quick Links */}
                    <div>
                        <h3 className="text-lg font-semibold mb-4" style={{ color: '#ffffff' }}>{t('quickLinks')}</h3> {/* white */}
                        <ul className="space-y-2">
                            <li>
                                {isClient ? (
                                    <button
                                        onClick={() => handleHashLink('#user-types')}
                                        className="transition-colors text-left"
                                        style={{ color: '#94a3b8' }} // neutral-light
                                        onMouseEnter={(e) => {
                                            e.target.style.color = '#ffffff'; // white
                                        }}
                                        onMouseLeave={(e) => {
                                            e.target.style.color = '#94a3b8'; // neutral-light
                                        }}
                                    >
                                        {t('features')}
                                    </button>
                                ) : (
                                    <span style={{ color: '#94a3b8' }}>
                                        {t('features')}
                                    </span>
                                )}
                            </li>
                            <li>
                                {isClient ? (
                                    <button
                                        onClick={() => router.push('/boats')}
                                        className="transition-colors text-left"
                                        style={{ color: '#94a3b8' }} // neutral-light
                                        onMouseEnter={(e) => {
                                            e.target.style.color = '#ffffff'; // white
                                        }}
                                        onMouseLeave={(e) => {
                                            e.target.style.color = '#94a3b8'; // neutral-light
                                        }}
                                    >
                                        {t("boats")}
                                    </button>
                                ) : (
                                    <span style={{ color: '#94a3b8' }}>
                                        {t("boats")}
                                    </span>
                                )}
                            </li>
                            <li>
                                <Link
                                    href="/contact"
                                    className="transition-colors"
                                    style={{ color: '#94a3b8' }} // neutral-light
                                    onMouseEnter={(e) => {
                                        e.target.style.color = '#ffffff'; // white
                                    }}
                                    onMouseLeave={(e) => {
                                        e.target.style.color = '#94a3b8'; // neutral-light
                                    }}
                                >
                                    {t('contact')}
                                </Link>
                            </li>
                            <li>
                                <button
                                    onClick={() => setShowSignInModal(true)}
                                    className="transition-colors cursor-pointer"
                                    style={{ color: '#94a3b8' }} // neutral-light
                                    onMouseEnter={(e) => {
                                        e.target.style.color = '#ffffff'; // white
                                    }}
                                    onMouseLeave={(e) => {
                                        e.target.style.color = '#94a3b8'; // neutral-light
                                    }}
                                >
                                    {t('signIn')}
                                </button>
                            </li>
                        </ul>
                    </div>

                    {/* Support */}
                    <div>
                        <h3 className="text-lg font-semibold mb-4" style={{ color: '#ffffff' }}>{t('support')}</h3> {/* white */}
                        <ul className="space-y-2">
                            <li>
                                <a
                                    href="#"
                                    className="transition-colors"
                                    style={{ color: '#94a3b8' }} // neutral-light
                                    onMouseEnter={(e) => {
                                        e.target.style.color = '#ffffff'; // white
                                    }}
                                    onMouseLeave={(e) => {
                                        e.target.style.color = '#94a3b8'; // neutral-light
                                    }}
                                >
                                    {t('helpCenter')}
                                </a>
                            </li>
                            <li>
                                <a
                                    href="#"
                                    className="transition-colors"
                                    style={{ color: '#94a3b8' }} // neutral-light
                                    onMouseEnter={(e) => {
                                        e.target.style.color = '#ffffff'; // white
                                    }}
                                    onMouseLeave={(e) => {
                                        e.target.style.color = '#94a3b8'; // neutral-light
                                    }}
                                >
                                    {t('safetyGuidelines')}
                                </a>
                            </li>
                            <li>
                                <a
                                    href="#"
                                    className="transition-colors"
                                    style={{ color: '#94a3b8' }} // neutral-light
                                    onMouseEnter={(e) => {
                                        e.target.style.color = '#ffffff'; // white
                                    }}
                                    onMouseLeave={(e) => {
                                        e.target.style.color = '#94a3b8'; // neutral-light
                                    }}
                                >
                                    {t('termsOfService')}
                                </a>
                            </li>
                            <li>
                                <a
                                    href="#"
                                    className="transition-colors"
                                    style={{ color: '#94a3b8' }} // neutral-light
                                    onMouseEnter={(e) => {
                                        e.target.style.color = '#ffffff'; // white
                                    }}
                                    onMouseLeave={(e) => {
                                        e.target.style.color = '#94a3b8'; // neutral-light
                                    }}
                                >
                                    {t('privacyPolicy')}
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

                <div className="border-t border-gray-800 mt-8 pt-8 text-center">
                    <p className="text-gray-400">
                        &copy; {currentYear} GoSea Platform. {t('allRightsReserved')}
                    </p>
                </div>
            </div>

            {/* Sign In Modal */}
            <SignInModal
                isOpen={showSignInModal}
                onClose={() => setShowSignInModal(false)}
                onSwitchToSignUp={() => {
                    setShowSignInModal(false);
                    // Could add SignUpModal here if needed
                }}
            />
        </footer>

    );
};

export default Footer;
