import React, { useState, useEffect } from 'react';
import { ChevronUpIcon } from '@heroicons/react/24/outline';

/**
 * Scroll to Top Floating Button Component
 * Appears in bottom-right corner when user scrolls down
 * Smoothly scrolls back to top when clicked
 * Follows GoSea design system with sky color scheme
 */

const ScrollToTopButton = ({
  showAfter = 300, // Show button after scrolling this many pixels
  scrollDuration = 500, // Smooth scroll duration in milliseconds
  className = '',
  style = {}
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);

  // Monitor scroll position
  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > showAfter) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    // Add scroll event listener
    window.addEventListener('scroll', toggleVisibility);

    // Cleanup
    return () => {
      window.removeEventListener('scroll', toggleVisibility);
    };
  }, [showAfter]);

  // Smooth scroll to top function
  const scrollToTop = () => {
    if (isScrolling) return; // Prevent multiple simultaneous scrolls

    setIsScrolling(true);
    
    const startPosition = window.pageYOffset;
    const startTime = performance.now();

    const easeInOutCubic = (t) => {
      return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    };

    const animateScroll = (currentTime) => {
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / scrollDuration, 1);
      const ease = easeInOutCubic(progress);
      
      window.scrollTo(0, startPosition * (1 - ease));

      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      } else {
        setIsScrolling(false);
      }
    };

    requestAnimationFrame(animateScroll);
  };

  // Handle keyboard navigation
  const handleKeyDown = (event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      scrollToTop();
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <button
      onClick={scrollToTop}
      onKeyDown={handleKeyDown}
      className={`
        fixed z-50
        w-12 h-12
        bg-sky-500 hover:bg-sky-600
        text-white
        rounded-full
        shadow-lg hover:shadow-xl
        transition-all duration-300 ease-in-out
        transform hover:scale-110
        focus:outline-none focus:ring-4 focus:ring-sky-300 focus:ring-opacity-50
        active:scale-95
        ${isScrolling ? 'cursor-wait opacity-75' : 'cursor-pointer'}
        ${className}
      `}
      style={{
        ...style,
        // Fixed positioning that accounts for mobile scrollbars
        bottom: '1.5rem',
        right: '1.5rem',
        // Ensure button stays above other floating elements
        zIndex: 1000,
        // Prevent button from being affected by horizontal scroll
        position: 'fixed'
      }}
      aria-label="Scroll to top"
      title="Scroll to top"
      disabled={isScrolling}
    >
      <ChevronUpIcon 
        className={`
          w-6 h-6 mx-auto
          transition-transform duration-200
          ${isScrolling ? 'animate-bounce' : ''}
        `} 
      />
    </button>
  );
};

export default ScrollToTopButton;
