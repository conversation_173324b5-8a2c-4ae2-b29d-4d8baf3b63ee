import React, { useEffect } from 'react';
import { XMarkIcon, CheckCircleIcon, ExclamationTriangleIcon, InformationCircleIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline';
import { useLanguage } from '../contexts/LanguageContext';

/**
 * Standardized Modal Component for GoSea Platform
 * Provides consistent styling, behavior, and accessibility for all modal dialogs
 */

const StandardModal = ({
  isOpen,
  onClose,
  type = 'info', // 'success', 'error', 'warning', 'info', 'custom'
  title,
  message,
  details = null,
  children = null, // For custom content
  primaryButton = null,
  secondaryButton = null,
  showCloseButton = true,
  autoClose = false,
  autoCloseDelay = 3000,
  size = 'md', // 'sm', 'md', 'lg', 'xl'
  preventBackdropClose = false,
  className = ''
}) => {
  const { t } = useLanguage();

  // Auto-close functionality
  useEffect(() => {
    if (isOpen && autoClose) {
      const timer = setTimeout(() => {
        onClose();
      }, autoCloseDelay);
      
      return () => clearTimeout(timer);
    }
  }, [isOpen, autoClose, autoCloseDelay, onClose]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  // Type-based styling
  const getTypeConfig = () => {
    switch (type) {
      case 'success':
        return {
          icon: CheckCircleIcon,
          iconColor: '#10b981',
          borderColor: '#d1fae5',
          bgColor: '#f0fdf4',
          primaryButtonColor: '#10b981'
        };
      case 'error':
        return {
          icon: ExclamationCircleIcon,
          iconColor: '#ef4444',
          borderColor: '#fee2e2',
          bgColor: '#fef2f2',
          primaryButtonColor: '#ef4444'
        };
      case 'warning':
        return {
          icon: ExclamationTriangleIcon,
          iconColor: '#f59e0b',
          borderColor: '#fef3c7',
          bgColor: '#fffbeb',
          primaryButtonColor: '#f59e0b'
        };
      case 'info':
        return {
          icon: InformationCircleIcon,
          iconColor: '#0ea5e9',
          borderColor: '#e0f2fe',
          bgColor: '#f0f9ff',
          primaryButtonColor: '#0ea5e9'
        };
      default:
        return {
          icon: null,
          iconColor: '#6b7280',
          borderColor: '#e5e7eb',
          bgColor: '#f9fafb',
          primaryButtonColor: '#6b7280'
        };
    }
  };

  // Size configurations
  const getSizeConfig = () => {
    switch (size) {
      case 'sm':
        return 'max-w-sm';
      case 'md':
        return 'max-w-md';
      case 'lg':
        return 'max-w-lg';
      case 'xl':
        return 'max-w-xl';
      default:
        return 'max-w-md';
    }
  };

  const typeConfig = getTypeConfig();
  const sizeClass = getSizeConfig();
  const IconComponent = typeConfig.icon;

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget && !preventBackdropClose) {
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity duration-300"
        onClick={handleBackdropClick}
      />
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div 
          className={`relative w-full ${sizeClass} transform overflow-hidden rounded-lg bg-white shadow-xl transition-all duration-300 ${className}`}
          role="dialog"
          aria-modal="true"
          aria-labelledby="modal-title"
          aria-describedby="modal-description"
        >
          {/* Header */}
          {(title || showCloseButton) && (
            <div 
              className="px-6 py-4 border-b"
              style={{ 
                backgroundColor: typeConfig.bgColor,
                borderColor: typeConfig.borderColor,
                borderBottomWidth: '1px'
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {IconComponent && (
                    <IconComponent 
                      className="w-6 h-6" 
                      style={{ color: typeConfig.iconColor }}
                    />
                  )}
                  {title && (
                    <h3 
                      id="modal-title"
                      className="text-lg font-semibold"
                      style={{ color: '#374151' }}
                    >
                      {title}
                    </h3>
                  )}
                </div>
                {showCloseButton && (
                  <button
                    onClick={onClose}
                    className="rounded-full p-1 hover:bg-gray-100 transition-colors"
                    style={{ color: '#6b7280' }}
                    aria-label={t('close')}
                  >
                    <XMarkIcon className="w-5 h-5" />
                  </button>
                )}
              </div>
            </div>
          )}

          {/* Content */}
          <div className="px-6 py-4">
            {message && (
              <p 
                id="modal-description"
                className="text-sm leading-relaxed"
                style={{ color: '#4b5563' }}
              >
                {message}
              </p>
            )}
            
            {details && (
              <div 
                className="mt-3 p-3 rounded-md text-xs"
                style={{ 
                  backgroundColor: '#f9fafb',
                  color: '#6b7280',
                  border: '1px solid #e5e7eb'
                }}
              >
                <strong>{t('details')}:</strong> {details}
              </div>
            )}

            {children && (
              <div className={message || details ? 'mt-4' : ''}>
                {children}
              </div>
            )}
          </div>

          {/* Actions */}
          {(primaryButton || secondaryButton || (!primaryButton && !secondaryButton)) && (
            <div 
              className="px-6 py-4 border-t flex justify-end space-x-3"
              style={{ 
                backgroundColor: '#f9fafb',
                borderColor: '#e5e7eb'
              }}
            >
              {secondaryButton}
              {primaryButton || (
                <button
                  onClick={onClose}
                  className="px-4 py-2 text-sm font-medium rounded-lg transition-colors"
                  style={{
                    backgroundColor: typeConfig.primaryButtonColor,
                    color: '#ffffff'
                  }}
                >
                  {type === 'success' ? t('continue') : t('close')}
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StandardModal;
