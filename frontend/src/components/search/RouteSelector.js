import { useState, useEffect } from 'react';
import { ChevronDownIcon, MapPinIcon, ArrowRightIcon } from '@heroicons/react/24/outline';

export default function RouteSelector({ 
  value, 
  onChange, 
  jettyId, 
  destinationId, 
  serviceCategory,
  error = false,
  placeholder = "Select route",
  className = "",
  disabled = false 
}) {
  const [routes, setRoutes] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  // Fetch available routes based on filters
  useEffect(() => {
    if (jettyId || destinationId || serviceCategory) {
      fetchRoutes();
    } else {
      setRoutes([]);
    }
  }, [jettyId, destinationId, serviceCategory]);

  const fetchRoutes = async () => {
    try {
      setIsLoading(true);
      
      const queryParams = new URLSearchParams();
      if (jettyId) queryParams.set('jettyId', jettyId);
      if (destinationId) queryParams.set('destinationId', destinationId);
      if (serviceCategory) queryParams.set('serviceCategory', serviceCategory);

      const response = await fetch(`/api/search/routes?${queryParams.toString()}`);
      const data = await response.json();

      if (data.success) {
        setRoutes(data.data);
      } else {
        console.error('Error fetching routes:', data.message);
        setRoutes([]);
      }
    } catch (error) {
      console.error('Error fetching routes:', error);
      setRoutes([]);
    } finally {
      setIsLoading(false);
    }
  };

  const selectedRoute = routes.find(route => route.id === value);

  const handleRouteSelect = (route) => {
    onChange(route.id);
    setIsOpen(false);
  };

  const handleClear = (e) => {
    e.stopPropagation();
    onChange('');
  };

  return (
    <div className={`relative ${className}`}>
      <div
        className={`w-full px-3 py-2 border rounded-md cursor-pointer bg-white transition-colors ${
          disabled 
            ? 'bg-gray-100 cursor-not-allowed' 
            : 'hover:border-amber-400 focus-within:border-amber-500 focus-within:ring-2 focus-within:ring-amber-500 focus-within:ring-opacity-20'
        } ${
          error ? 'border-red-500' : 'border-gray-300'
        }`}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center flex-1 min-w-0">
            <MapPinIcon className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
            
            {isLoading ? (
              <span className="text-gray-500">Loading routes...</span>
            ) : selectedRoute ? (
              <div className="flex items-center min-w-0 flex-1">
                <span className="text-sm text-gray-900 truncate">
                  {selectedRoute.departureJetty.name}
                </span>
                <ArrowRightIcon className="h-3 w-3 text-gray-400 mx-2 flex-shrink-0" />
                <span className="text-sm text-gray-900 truncate">
                  {selectedRoute.destination.name}
                </span>
              </div>
            ) : (
              <span className="text-gray-500">{placeholder}</span>
            )}
          </div>

          <div className="flex items-center ml-2">
            {selectedRoute && !disabled && (
              <button
                type="button"
                onClick={handleClear}
                className="text-gray-400 hover:text-gray-600 mr-1 p-1"
                aria-label="Clear selection"
              >
                <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
            
            <ChevronDownIcon 
              className={`h-4 w-4 text-gray-400 transition-transform ${
                isOpen ? 'transform rotate-180' : ''
              }`} 
            />
          </div>
        </div>
      </div>

      {/* Dropdown */}
      {isOpen && !disabled && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown content */}
          <div className="absolute z-20 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
            {routes.length === 0 ? (
              <div className="px-3 py-2 text-sm text-gray-500">
                {isLoading ? 'Loading routes...' : 'No routes available'}
              </div>
            ) : (
              <div className="py-1">
                {routes.map((route) => (
                  <div
                    key={route.id}
                    className={`px-3 py-2 cursor-pointer hover:bg-amber-50 transition-colors ${
                      route.id === value ? 'bg-amber-100 text-amber-900' : 'text-gray-900'
                    }`}
                    onClick={() => handleRouteSelect(route)}
                  >
                    <div className="flex items-center">
                      <div className="flex items-center flex-1 min-w-0">
                        <span className="text-sm font-medium truncate">
                          {route.departureJetty.name}
                        </span>
                        <ArrowRightIcon className="h-3 w-3 text-gray-400 mx-2 flex-shrink-0" />
                        <span className="text-sm font-medium truncate">
                          {route.destination.name}
                        </span>
                      </div>
                      
                      {route.distance && (
                        <span className="text-xs text-gray-500 ml-2 flex-shrink-0">
                          {route.distance}km
                        </span>
                      )}
                    </div>
                    
                    {route.description && (
                      <div className="text-xs text-gray-500 mt-1 truncate">
                        {route.description}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
