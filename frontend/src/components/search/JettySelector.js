import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  MapPinIcon, 
  ChevronDownIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import { Fence } from "lucide-react";


/**
 * JettySelector Component
 * Dropdown selector for departure jetties
 */
export default function JettySelector({ 
  value, 
  onChange, 
  className = '',
  error = false,
  disabled = false 
}) {
  const { t } = useLanguage();
  const [jetties, setJetties] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isOpen, setIsOpen] = useState(false);

  // Fetch jetties on component mount
  useEffect(() => {
    fetchJetties();
  }, []);

  const fetchJetties = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/search/jetties');
      const data = await response.json();
      
      if (data.success) {
        setJetties(data.data);
      } else {
        console.error('Failed to fetch jetties:', data.message);
      }
    } catch (error) {
      console.error('Error fetching jetties:', error);
    } finally {
      setLoading(false);
    }
  };

  const selectedJetty = jetties.find(jetty => jetty.id === value);

  // console.log('Jetty:', selectedJetty);

  const handleSelect = (jettyId) => {
    onChange(jettyId);
    setIsOpen(false);
  };

  if (loading) {
    return (
      <div className={`relative ${className}`}>
        <div className={`w-full px-3 py-2 border rounded-md bg-gray-50 ${
          error ? 'border-red-500' : 'border-gray-300'
        }`}>
          <div className="flex items-center space-x-2">
            <Fence className="h-4 w-4 text-gray-400" />
            <span className="text-gray-500">{t('loadingJetties')}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* Dropdown Button */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`w-full h-12 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent bg-white text-left flex items-center justify-between ${
          error ? 'border-red-500' : 'border-gray-300'
        } ${disabled ? 'bg-gray-50 cursor-not-allowed' : 'hover:border-gray-400'}`}
      >
        <div className="flex items-center space-x-2 flex-1 min-w-0">
          <Fence className="h-4 w-4 text-gray-400 flex-shrink-0" />
          {selectedJetty ? (
            <div className="flex-1 min-w-0 leading-[1.1]">
              <span className="text-gray-900 font-medium truncate block">
                {selectedJetty.name}
              </span>
              {(selectedJetty.city && selectedJetty.state) && (
                <span className="text-xs text-gray-500">
                  {selectedJetty.city}, {selectedJetty.state}
                </span>
              )}
            </div>
          ) : (
            <span className="text-gray-500 truncate">
              {t('selectJetty')}
            </span>
          )}
        </div>
        <ChevronDownIcon 
          className={`h-4 w-4 text-gray-400 transition-transform ${
            isOpen ? 'transform rotate-180' : ''
          }`} 
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute z-[9999] w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {/* Clear Selection Option */}
          <button
            type="button"
            onClick={() => handleSelect('')}
            className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center justify-between"
          >
            <div className="flex items-center space-x-2">
              {/* <LiaFenceSolid className="h-4 w-4 text-gray-400" /> */}
              <span className="text-gray-500">{t('selectJetty')}</span>
            </div>
            {!value && <CheckIcon className="h-4 w-4 text-emerald-500" />}
          </button>

          {/* Jetty Options */}
          {jetties.map((jetty) => (
            <button
              key={jetty.id}
              type="button"
              onClick={() => handleSelect(jetty.id)}
              className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center justify-between"
            >
              <div className="flex items-center space-x-2 flex-1 min-w-0">
                {/* <LiaFenceSolid className="h-4 w-4 text-blue-500 flex-shrink-0" /> */}
                <div className="flex-1 min-w-0 leading-[1.1]">
                  <div className="font-medium text-gray-900 truncate">
                    {jetty.name}
                  </div>
                  {(jetty.city && jetty.state) && (
                    <div className="text-xs text-gray-500 truncate">
                      {jetty.city}, {jetty.state}
                    </div>
                  )}
                </div>
              </div>
              {value === jetty.id && (
                <CheckIcon className="h-4 w-4 text-emerald-500 flex-shrink-0" />
              )}
            </button>
          ))}

          {jetties.length === 0 && (
            <div className="px-3 py-2 text-gray-500 text-center">
              {t('noJettiesAvailable')}
            </div>
          )}
        </div>
      )}

      {/* Click outside to close */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
