import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import {
  ChevronDownIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import { TbScubaMask } from "react-icons/tb";



/**
 * ServiceCategorySelector Component
 * Dropdown selector for service categories
 */
export default function ServiceCategorySelector({ 
  value, 
  onChange, 
  className = '',
  error = false,
  disabled = false 
}) {
  const { t } = useLanguage();
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isOpen, setIsOpen] = useState(false);

  // Fetch service categories on component mount
  useEffect(() => {
    fetchServiceCategories();
  }, []);

  const fetchServiceCategories = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/search/service-categories');
      const data = await response.json();
      
      if (data.success) {
        setCategories(data.data);
      } else {
        console.error('Failed to fetch service categories:', data.message);
      }
    } catch (error) {
      console.error('Error fetching service categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const selectedCategory = categories.find(category => category.id === value);

  const handleSelect = (categoryId) => {
    onChange(categoryId);
    setIsOpen(false);
  };

  if (loading) {
    return (
      <div className={`relative ${className}`}>
        <div className={`w-full h-12 px-3 py-2 border rounded-md bg-gray-50 ${
          error ? 'border-red-500' : 'border-gray-300'
        }`}>
          <div className="flex items-center space-x-2">
            <TbScubaMask className="h-5 w-5 text-gray-400" />
            <span className="text-gray-500">{t('loadingServices')}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* Dropdown Button */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`w-full h-12 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent bg-white text-left flex items-center justify-between ${
          error ? 'border-red-500' : 'border-gray-300'
        } ${disabled ? 'bg-gray-50 cursor-not-allowed' : 'hover:border-gray-400'}`}
      >
        <div className="flex items-center space-x-2 flex-1 min-w-0">
          <TbScubaMask className="h-5 w-5 text-gray-400 flex-shrink-0" />
          {selectedCategory ? (
            <div className="flex-1 min-w-0">
              <span className="text-gray-900 font-medium truncate block">
                {selectedCategory.name}
              </span>
              {/* {selectedCategory.description && (
                <span className="text-xs text-gray-500 truncate block">
                  {selectedCategory.description}
                </span>
              )} */}
            </div>
          ) : (
            <span className="text-gray-500 truncate">
              {t('selectServiceType')}
            </span>
          )}
        </div>
        <ChevronDownIcon 
          className={`h-4 w-4 text-gray-400 transition-transform ${
            isOpen ? 'transform rotate-180' : ''
          }`} 
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute z-[9999] w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {/* Clear Selection Option */}
          <button
            type="button"
            onClick={() => handleSelect('')}
            className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center justify-between"
          >
            <div className="flex items-center space-x-2">
              {/* <TbScubaMask className="h-4 w-4 text-gray-400" /> */}
              <span className="text-gray-500">{t('selectServiceType')}</span>
            </div>
            {!value && <CheckIcon className="h-4 w-4 text-emerald-500" />}
          </button>

          {/* Category Options */}
          {categories.map((category) => (
            <button
              key={category.id}
              type="button"
              onClick={() => handleSelect(category.id)}
              className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center justify-between"
            >
              <div className="flex items-center space-x-2 flex-1 min-w-0">
                {/* <div className="h-4 w-4 flex-shrink-0">
                  {category.iconUrl ? (
                    <img
                      src={category.iconUrl}
                      alt={category.name}
                      className="h-4 w-4 object-contain"
                    />
                  ) : (
                    <TbScubaMask className="h-4 w-4 text-blue-500" />
                  )}
                </div> */}
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-gray-900 truncate">
                    {category.name}
                  </div>
                </div>
              </div>
              {value === category.id && (
                <CheckIcon className="h-4 w-4 text-emerald-500 flex-shrink-0" />
              )}
            </button>
          ))}

          {categories.length === 0 && (
            <div className="px-3 py-2 text-gray-500 text-center">
              {t('noServicesAvailable')}
            </div>
          )}
        </div>
      )}

      {/* Click outside to close */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
