import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import {
  ChevronDownIcon,
  CheckIcon,
} from '@heroicons/react/24/outline';
import { MapPinned } from 'lucide-react';


/**
 * DestinationSelector Component
 * Dropdown selector for destinations (filtered by jetty)
 */
export default function DestinationSelector({ 
  value, 
  onChange, 
  jettyId = null,
  className = '',
  error = false,
  disabled = false 
}) {
  const { t } = useLanguage();
  const [destinations, setDestinations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isOpen, setIsOpen] = useState(false);

  // Fetch destinations when component mounts or jettyId changes
  useEffect(() => {
    fetchDestinations();
  }, [jettyId]);

  const fetchDestinations = async () => {
    try {
      setLoading(true);
      const url = jettyId 
        ? `/api/search/destinations?jettyId=${jettyId}`
        : '/api/search/destinations';
      
      const response = await fetch(url);
      const data = await response.json();
      
      if (data.success) {
        setDestinations(data.data);
      } else {
        console.error('Failed to fetch destinations:', data.message);
      }
    } catch (error) {
      console.error('Error fetching destinations:', error);
    } finally {
      setLoading(false);
    }
  };

  // console.log('Destinations:', destinations);

  const selectedDestination = destinations.find(destination => destination.id === value);

  const handleSelect = (destinationId) => {
    onChange(destinationId);
    setIsOpen(false);
  };

  // Clear selection when jetty changes and current destination is not available
  useEffect(() => {
    if (value && !destinations.find(d => d.id === value)) {
      onChange('');
    }
  }, [destinations, value, onChange]);

  // console.log('Destination:', selectedDestination);

  if (loading) {
    return (
      <div className={`relative ${className}`}>
        <div className={`w-full h-12 px-3 py-2 border rounded-md bg-gray-50 ${
          error ? 'border-red-500' : 'border-gray-300'
        }`}>
          <div className="flex items-center space-x-2">
            <MapPinned className="h-4 w-4 text-gray-400" />
            <span className="text-gray-500">{t('loadingDestinations')}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* Dropdown Button */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`w-full h-12 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent bg-white text-left flex items-center justify-between ${
          error ? 'border-red-500' : 'border-gray-300'
        } ${disabled ? 'bg-gray-50 cursor-not-allowed' : 'hover:border-gray-400'}`}
      >
        <div className="flex items-center space-x-2 flex-1 min-w-0">
          <MapPinned className="h-4 w-4 text-gray-400 flex-shrink-0" />
          {selectedDestination ? (
            <div className="flex-1 min-w-0">
              <span className="text-gray-900 font-medium truncate block">
                {selectedDestination.name}
              </span>
              {selectedDestination.state && (
                <span className="text-xs text-gray-500 truncate block">
                  {selectedDestination.state}
                </span>
              )}
            </div>
          ) : (
            <span className="text-gray-500 truncate">
              {jettyId ? t('selectDestination') : t('selectDestination')}
            </span>
          )}
        </div>
        <ChevronDownIcon 
          className={`h-4 w-4 text-gray-400 transition-transform ${
            isOpen ? 'transform rotate-180' : ''
          }`} 
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute z-[9999] w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {/* Clear Selection Option */}
          <button
            type="button"
            onClick={() => handleSelect('')}
            className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center justify-between"
          >
            <div className="flex items-center space-x-2">
              {/* <Goal className="h-4 w-4 text-gray-400" /> */}
              <span className="text-gray-500">{t('selectDestination')}</span>
            </div>
            {!value && <CheckIcon className="h-4 w-4 text-emerald-500" />}
          </button>

          {/* Destination Options */}
          {destinations.map((destination) => (
            <button
              key={destination.id}
              type="button"
              onClick={() => handleSelect(destination.id)}
              className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center justify-between"
            >        
              <div className="flex items-center space-x-2 flex-1 min-w-0">
                {/* <MapPinIcon className="h-4 w-4 text-blue-500 flex-shrink-0" /> */}
                <div className="flex-1 min-w-0 leading-[1.1]">
                  <div className="font-medium text-gray-900 truncate">
                    {destination.name}
                  </div>
                  {destination.state && (
                    <div className="text-xs text-gray-500 truncate">
                      {destination.state}
                    </div>
                  )}
                </div>
              </div>
              {value === destination.id && (
                <CheckIcon className="h-4 w-4 text-emerald-500 flex-shrink-0" />
              )}
            </button>
          ))}

          {destinations.length === 0 && (
            <div className="px-3 py-2 text-gray-500 text-center">
              {jettyId ? t('noDestinationsFromLocation') : t('noDestinationsAvailable')}
            </div>
          )}
        </div>
      )}

      {/* Click outside to close */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
