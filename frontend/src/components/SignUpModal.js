import { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import { validateMalaysianPhoneNumber } from '../utils/validation';
import StandardModal from './StandardModal';

const SignUpModal = ({ isOpen, onClose, onSwitchToSignIn, onSignUpSuccess }) => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  // Feedback modal state
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackModal, setFeedbackModal] = useState({
    type: 'success',
    title: '',
    message: '',
    details: null
  });

  // Helper functions for feedback modals
  const showSuccessModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'success',
      title,
      message,
      details
    });
    setShowFeedbackModal(true);
  };
  const [passwordValidation, setPasswordValidation] = useState({
    length: false,
    uppercase: false,
    specialChar: false
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Real-time password validation
    if (name === 'password') {
      setPasswordValidation({
        length: value.length >= 8,
        uppercase: /[A-Z]/.test(value),
        specialChar: /[!@#$%^&*(),.?":{}|<>]/.test(value)
      });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Validation
    if (!formData.phone.trim()) {
      setError(t('Phone number is required'));
      setIsLoading(false);
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setError(t('passwordsDoNotMatch'));
      setIsLoading(false);
      return;
    }

    if (formData.password.length < 8) {
      setError(t('passwordMinLengthError'));
      setIsLoading(false);
      return;
    }

    // Check password strength requirements
    const hasUppercase = /[A-Z]/.test(formData.password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(formData.password);

    if (!hasUppercase || !hasSpecialChar) {
      setError(t('passwordRequirementsError'));
      setIsLoading(false);
      return;
    }

    // Validate Malaysia phone number format (if provided)
    if (formData.phone && formData.phone.trim()) {
      if (!validateMalaysianPhoneNumber(formData.phone)) {
        setError(t('invalidPhoneNumberFormat'));
        setIsLoading(false);
        return;
      }
    }

    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password,
          firstName: formData.firstName,
          lastName: formData.lastName,
          phone: formData.phone.trim(),
          role: 'CUSTOMER'
        }),
      });

      const data = await response.json();

      if (data.success) {
        try {
          // Store current page URL to return to after email verification
          const currentUrl = window.location.pathname + window.location.search;
          sessionStorage.setItem('gosea_return_url', currentUrl);
          console.log('Stored return URL for email verification:', currentUrl);
        } catch (error) {
          console.error('Failed to store return URL:', error);
        }

        // Show success modal
        showSuccessModal(
          t('registrationSuccessful'),
          t('Please check your email to verify your account, then sign in.')
        );
      } else {
        setError(data.message || t('registrationFailed'));
      }
    } catch (err) {
      console.error('Sign up error:', err);
      setError(t('networkError'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignUp = () => {
    try {
      // Store current page URL to return to after authentication
      const currentUrl = window.location.pathname + window.location.search;
      sessionStorage.setItem('gosea_return_url', currentUrl);
      localStorage.setItem('gosea_return_url_backup', currentUrl);
      console.log('Stored return URL for Google OAuth:', currentUrl);

      // Redirect to Google OAuth endpoint with return URL in state parameter
      const backendUrl = 'http://localhost:5001';
      const stateParam = encodeURIComponent(JSON.stringify({ returnUrl: currentUrl }));
      window.location.href = `${backendUrl}/api/auth/google?role=CUSTOMER&state=${stateParam}`;
    } catch (error) {
      console.error('Failed to store return URL:', error);
      // Fallback without state parameter
      const backendUrl = 'http://localhost:5001';
      window.location.href = `${backendUrl}/api/auth/google?role=CUSTOMER`;
    }
  };

  const handleFeedbackModalClose = () => {
    setShowFeedbackModal(false);

    // Reset form data to clear all fields
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      confirmPassword: '',
      phone: ''
    });

    // Reset password validation state
    setPasswordValidation({
      length: false,
      uppercase: false,
      specialChar: false
    });

    // Clear any error messages
    setError('');

    onClose(); // Close the modal and return to home page without opening sign-in modal

    // Call success callback if provided
    if (onSignUpSuccess) {
      onSignUpSuccess();
    }
  };

  if (!isOpen && !showFeedbackModal) return null;

  return (
    <>
    {/* Feedback Modal */}
    <StandardModal
      isOpen={showFeedbackModal}
      onClose={handleFeedbackModalClose}
      type={feedbackModal.type}
      title={feedbackModal.title}
      message={feedbackModal.message}
      details={feedbackModal.details}
      autoClose={feedbackModal.type === 'success'}
      autoCloseDelay={2000}
    />

    {/* Sign Up Modal */}
    {isOpen && !showFeedbackModal && (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b" style={{ borderColor: '#e0f2fe' }}>
          <h2 className="text-2xl font-bold" style={{ color: '#0f172a' }}>
            {t('signUp')}
          </h2>
          <button
            onClick={onClose}
            className="transition-colors"
            style={{ color: '#94a3b8' }}
            onMouseEnter={(e) => e.target.style.color = '#64748b'}
            onMouseLeave={(e) => e.target.style.color = '#94a3b8'}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Email Sign Up Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <div className="px-4 py-3 rounded-md" style={{ backgroundColor: '#fef2f2', borderColor: '#fecaca', color: '#ef4444', border: '1px solid' }}>
                {error}
              </div>
            )}

            {/* Name Fields */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="firstName" className="block text-sm font-medium mb-1" style={{ color: '#475569' }}>
                  {t('firstName')} <span style={{ color: '#ef4444' }}>*</span>
                </label>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:border-transparent"
                  style={{ borderColor: '#d1d5db' }}
                  onFocus={(e) => {
                    e.target.style.borderColor = '#f59e0b';
                    e.target.style.boxShadow = '0 0 0 2px rgba(245, 158, 11, 0.2)';
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = '#d1d5db';
                    e.target.style.boxShadow = 'none';
                  }}
                  placeholder={t('enterFirstName')}
                  required
                />
              </div>
              <div>
                <label htmlFor="lastName" className="block text-sm font-medium mb-1" style={{ color: '#475569' }}>
                  {t('lastName')} <span style={{ color: '#ef4444' }}>*</span>
                </label>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:border-transparent"
                  style={{ borderColor: '#d1d5db' }}
                  onFocus={(e) => {
                    e.target.style.borderColor = '#f59e0b';
                    e.target.style.boxShadow = '0 0 0 2px rgba(245, 158, 11, 0.2)';
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = '#d1d5db';
                    e.target.style.boxShadow = 'none';
                  }}
                  placeholder={t('enterLastName')}
                  required
                />
              </div>
            </div>

            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-1" style={{ color: '#475569' }}>
                {t('email')} <span style={{ color: '#ef4444' }}>*</span>
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:border-transparent"
                style={{ borderColor: '#d1d5db' }}
                onFocus={(e) => {
                  e.target.style.borderColor = '#f59e0b';
                  e.target.style.boxShadow = '0 0 0 2px rgba(245, 158, 11, 0.2)';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#d1d5db';
                  e.target.style.boxShadow = 'none';
                }}
                placeholder={t('enterEmail')}
                required
              />
            </div>

            {/* Phone */}
            <div>
              <label htmlFor="phone" className="block text-sm font-medium mb-1" style={{ color: '#475569' }}>
                {t('phoneNumber')} <span style={{ color: '#ef4444' }}>*</span>
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:border-transparent"
                style={{ borderColor: '#d1d5db' }}
                onFocus={(e) => {
                  e.target.style.borderColor = '#f59e0b';
                  e.target.style.boxShadow = '0 0 0 2px rgba(245, 158, 11, 0.2)';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#d1d5db';
                  e.target.style.boxShadow = 'none';
                }}
                placeholder="+60123456789 or 0123456789"
                required
              />
              <p className="text-xs mt-1" style={{ color: '#64748b' }}>
                Malaysia phone number format • Contact number used for booking follow up
              </p>
            </div>

            {/* Password */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium mb-1" style={{ color: '#475569' }}>
                {t('password')} <span style={{ color: '#ef4444' }}>*</span>
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:border-transparent pr-10"
                  style={{ borderColor: '#d1d5db' }}
                  onFocus={(e) => {
                    e.target.style.borderColor = '#f59e0b';
                    e.target.style.boxShadow = '0 0 0 2px rgba(245, 158, 11, 0.2)';
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = '#d1d5db';
                    e.target.style.boxShadow = 'none';
                  }}
                  placeholder={t('enterPassword')}
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center transition-colors"
                  style={{ color: '#94a3b8' }}
                  onMouseEnter={(e) => e.target.style.color = '#64748b'}
                  onMouseLeave={(e) => e.target.style.color = '#94a3b8'}
                >
                  {showPassword ? (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  )}
                </button>
              </div>
              {/* Real-time password validation */}
              <div className="text-xs mt-2 space-y-1">
                <div className="flex items-center space-x-2">
                  <span className={`w-2 h-2 rounded-full ${passwordValidation.length ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                  <span style={{ color: passwordValidation.length ? '#10b981' : '#64748b' }}>
                    {t('passwordMinLength')}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`w-2 h-2 rounded-full ${passwordValidation.uppercase ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                  <span style={{ color: passwordValidation.uppercase ? '#10b981' : '#64748b' }}>
                    {t('passwordUppercaseSimple')}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`w-2 h-2 rounded-full ${passwordValidation.specialChar ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                  <span style={{ color: passwordValidation.specialChar ? '#10b981' : '#64748b' }}>
                    {t('passwordSpecialCharSimple')}
                  </span>
                </div>
              </div>
            </div>

            {/* Confirm Password */}
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium mb-1" style={{ color: '#475569' }}>
                {t('confirmPassword')} <span style={{ color: '#ef4444' }}>*</span>
              </label>
              <div className="relative">
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  id="confirmPassword"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:border-transparent pr-10"
                  style={{ borderColor: '#d1d5db' }}
                  onFocus={(e) => {
                    e.target.style.borderColor = '#f59e0b';
                    e.target.style.boxShadow = '0 0 0 2px rgba(245, 158, 11, 0.2)';
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = '#d1d5db';
                    e.target.style.boxShadow = 'none';
                  }}
                  placeholder={t('confirmPassword')}
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center transition-colors"
                  style={{ color: '#94a3b8' }}
                  onMouseEnter={(e) => e.target.style.color = '#64748b'}
                  onMouseLeave={(e) => e.target.style.color = '#94a3b8'}
                >
                  {showConfirmPassword ? (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  )}
                </button>
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full px-4 py-2 rounded-md font-medium transition-colors"
              style={{
                backgroundColor: isLoading ? '#94a3b8' : '#0ea5e9',
                color: '#ffffff'
              }}
              onMouseEnter={(e) => !isLoading && (e.target.style.backgroundColor = '#0284c7')}
              onMouseLeave={(e) => !isLoading && (e.target.style.backgroundColor = '#0ea5e9')}
            >
              {isLoading ? t('creatingAccount') : t('createAccount')}
            </button>
          </form>

          {/* Divider */}
          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t" style={{ borderColor: '#e0f2fe' }} />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white" style={{ color: '#64748b' }}>{t('orContinueWith')}</span>
            </div>
          </div>

          {/* Google Sign Up */}
          <button
            onClick={handleGoogleSignUp}
            className="w-full px-4 py-2 rounded-md border font-medium flex items-center justify-center space-x-2 mb-6 transition-colors"
            style={{
              borderColor: '#d1d5db',
              backgroundColor: '#ffffff',
              color: '#475569'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = '#f8fafc';
              e.target.style.borderColor = '#94a3b8';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = '#ffffff';
              e.target.style.borderColor = '#d1d5db';
            }}
          >
            <svg className="w-5 h-5" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            <span>{t('continueWithGoogle')}</span>
          </button>

          {/* Switch to Sign In */}
          <div className="text-center">
            <span style={{ color: '#64748b' }}>{t('alreadyHaveAccount')} </span>
            <button
              onClick={onSwitchToSignIn}
              className="font-medium transition-colors"
              style={{ color: '#f59e0b' }}
              onMouseEnter={(e) => e.target.style.color = '#d97706'}
              onMouseLeave={(e) => e.target.style.color = '#f59e0b'}
            >
              {t('signIn')}
            </button>
          </div>
        </div>
      </div>
    </div>
    )}
    </>
  );
};

export default SignUpModal;
