import React, { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import { useAuth } from '../contexts/AuthContext';
import { validateMalaysianPhoneNumber } from '../utils/validation';
import CustomDatePicker from './CustomDatePicker';
import StandardModal from './StandardModal';

/**
 * Profile Completion Modal Component
 * Shows on first login to prompt users to complete their profile
 */

const ProfileCompletionModal = ({ isOpen, onClose, user, mandatory = false }) => {
  const { t } = useLanguage();
  const { accessToken } = useAuth();
  const [formData, setFormData] = useState({
    phone: '',
    dateOfBirth: '',
    address1: '',
    address2: '',
    postcode: '',
    city: '',
    state: '',
    emergencyContact: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  // Feedback modal state
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackModal, setFeedbackModal] = useState({
    type: 'success',
    title: '',
    message: '',
    details: null
  });

  // Helper functions for feedback modals
  const showSuccessModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'success',
      title,
      message,
      details
    });
    setShowFeedbackModal(true);
  };

  const showErrorModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'error',
      title,
      message,
      details
    });
    setShowFeedbackModal(true);
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };



  // Validate form
  const validateForm = () => {
    const newErrors = {};

    // Phone validation (required field)
    if (!formData.phone || !formData.phone.trim()) {
      newErrors.phone = t('Phone number is required');
    } else if (!validateMalaysianPhoneNumber(formData.phone)) {
      newErrors.phone = t('Please enter a valid Malaysian phone number (e.g., +60123456789, 0123456789, or 123456789)');
    }

    // Date of birth validation (optional but if provided must be valid)
    if (formData.dateOfBirth) {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();

      if (age < 13 || age > 120) {
        newErrors.dateOfBirth = t('Please enter a valid date of birth');
      }
    }

    // Emergency contact validation (optional but if provided must be valid phone number)
    if (formData.emergencyContact && formData.emergencyContact.trim()) {
      if (!validateMalaysianPhoneNumber(formData.emergencyContact)) {
        newErrors.emergencyContact = t('Please enter a valid Malaysian phone number for emergency contact (e.g., +60123456789, 0123456789, or 123456789)');
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Helper function to format date without timezone issues
  const formatDateForForm = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Check if access token is available
      if (!accessToken) {
        throw new Error('No access token found. Please sign in again.');
      }

      // Prepare update data (only include fields that have values)
      const updateData = {};
      if (formData.phone.trim()) updateData.phone = formData.phone.trim();

      // Handle dateOfBirth with proper timezone handling
      if (formData.dateOfBirth) {
        // Ensure we send the date in YYYY-MM-DD format without timezone conversion
        if (typeof formData.dateOfBirth === 'string') {
          // If it's already in YYYY-MM-DD format, use it directly
          if (formData.dateOfBirth.match(/^\d{4}-\d{2}-\d{2}$/)) {
            updateData.dateOfBirth = formData.dateOfBirth;
          } else {
            // Parse and format to avoid timezone issues
            const dateParts = formData.dateOfBirth.split('-');
            if (dateParts.length === 3) {
              const date = new Date(parseInt(dateParts[0]), parseInt(dateParts[1]) - 1, parseInt(dateParts[2]));
              updateData.dateOfBirth = formatDateForForm(date);
            } else {
              console.warn('Invalid date format:', formData.dateOfBirth);
            }
          }
        } else if (formData.dateOfBirth instanceof Date) {
          // For Date objects, format to YYYY-MM-DD in local timezone
          updateData.dateOfBirth = formatDateForForm(formData.dateOfBirth);
        }
      }

      // Handle new address fields
      if (formData.address1.trim()) updateData.personalAddress1 = formData.address1.trim();
      if (formData.address2.trim()) updateData.personalAddress2 = formData.address2.trim();
      if (formData.postcode.trim()) updateData.personalPostcode = formData.postcode.trim();
      if (formData.city.trim()) updateData.personalCity = formData.city.trim();
      if (formData.state.trim()) updateData.personalState = formData.state.trim();

      if (formData.emergencyContact.trim()) updateData.emergencyContact = formData.emergencyContact.trim();

      // Only make API call if there's data to update
      if (Object.keys(updateData).length > 0) {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/profile`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`
          },
          body: JSON.stringify(updateData)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to update profile');
        }
      }

      // Show success modal
      showSuccessModal(
        t('profileUpdatedSuccessfully'),
        t('profileCompletionSuccess'),
        null
      );

      // Close modal after a delay to allow user to see success message
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (error) {
      console.error('Profile update error:', error);

      // Show error modal
      showErrorModal(
        t('updateProfileError'),
        error.message || t('profileUpdateFailed'),
        error.details || null
      );

      // Also set form errors for inline display
      setErrors({ submit: error.message || 'Failed to update profile. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle skip
  const handleSkip = () => {
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 transition-opacity"
          style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
          onClick={mandatory ? undefined : handleSkip}
        ></div>

        {/* Modal */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          {/* Header */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className={`flex items-center ${mandatory ? 'justify-center' : 'justify-between'}`}>
              <h3 className="text-lg font-medium" style={{ color: '#0f172a' }}>
                {mandatory ? t('Complete Your Profile') : t('Complete Your Profile')}
              </h3>
              {!mandatory && (
                <button
                  onClick={handleSkip}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
            </div>
          </div>

          {/* Content */}
          <form onSubmit={handleSubmit} className="px-6 py-4">
            <div className="mb-4">
              <p className="text-sm" style={{ color: '#64748b' }}>
                {mandatory
                  ? t('Please complete your profile information to continue with your booking.')
                  : t('Help us provide you with a better experience by completing your profile information.')
                }
              </p>
            </div>

            {/* Phone Number */}
            <div className="mb-4">
              <label htmlFor="phone" className="block text-sm font-medium mb-2" style={{ color: '#374151' }}>
                {t('Phone Number')} <span style={{ color: '#ef4444' }}>*</span>
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                placeholder={t('e.g., +60123456789, 0123456789, or 123456789')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
              />
              <p className="text-xs mt-1" style={{ color: '#64748b' }}>
                Malaysia phone number format • Contact number used for booking follow up
              </p>
              {errors.phone && (
                <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
              )}
            </div>

            {/* Date of Birth */}
            <div className="mb-4">
              <label htmlFor="dateOfBirth" className="block text-sm font-medium mb-2" style={{ color: '#374151' }}>
                {t('Date of Birth')} <span style={{ color: '#64748b', fontSize: '0.875rem' }}>({t('Optional')})</span>
              </label>
              <CustomDatePicker
                name="dateOfBirth"
                value={formData.dateOfBirth}
                onChange={handleInputChange}
                placeholder={t('Select your date of birth')}
                error={!!errors.dateOfBirth}
              />
              {errors.dateOfBirth && (
                <p className="mt-1 text-sm text-red-600">{errors.dateOfBirth}</p>
              )}
            </div>

            {/* Address Fields */}
            <div className="mb-4 space-y-3">
              <h4 className="text-sm font-medium" style={{ color: '#374151' }}>
                {t('Address Information')} <span style={{ color: '#64748b', fontSize: '0.875rem' }}>({t('Optional')})</span>
              </h4>

              {/* Address Line 1 */}
              <div>
                <label htmlFor="address1" className="block text-sm font-medium mb-1" style={{ color: '#374151' }}>
                  {t('Street Address Line 1')}
                </label>
                <input
                  type="text"
                  id="address1"
                  name="address1"
                  value={formData.address1}
                  onChange={handleInputChange}
                  placeholder={t('e.g., 123 Jalan Bukit Bintang')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                {errors.address1 && (
                  <p className="mt-1 text-sm text-red-600">{errors.address1}</p>
                )}
              </div>

              {/* Address Line 2 */}
              <div>
                <label htmlFor="address2" className="block text-sm font-medium mb-1" style={{ color: '#374151' }}>
                  {t('Street Address Line 2')} <span style={{ color: '#64748b', fontSize: '0.875rem' }}>({t('Optional')})</span>
                </label>
                <input
                  type="text"
                  id="address2"
                  name="address2"
                  value={formData.address2}
                  onChange={handleInputChange}
                  placeholder={t('e.g., Apartment, suite, unit, building, floor, etc.')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Postcode and City Row */}
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label htmlFor="postcode" className="block text-sm font-medium mb-1" style={{ color: '#374151' }}>
                    {t('Postcode')}
                  </label>
                  <input
                    type="text"
                    id="postcode"
                    name="postcode"
                    value={formData.postcode}
                    onChange={handleInputChange}
                    placeholder={t('e.g., 55100')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {errors.postcode && (
                    <p className="mt-1 text-sm text-red-600">{errors.postcode}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="city" className="block text-sm font-medium mb-1" style={{ color: '#374151' }}>
                    {t('City')}
                  </label>
                  <input
                    type="text"
                    id="city"
                    name="city"
                    value={formData.city}
                    onChange={handleInputChange}
                    placeholder={t('e.g., Kuala Lumpur')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {errors.city && (
                    <p className="mt-1 text-sm text-red-600">{errors.city}</p>
                  )}
                </div>
              </div>

              {/* State */}
              <div>
                <label htmlFor="state" className="block text-sm font-medium mb-1" style={{ color: '#374151' }}>
                  {t('State')}
                </label>
                <select
                  id="state"
                  name="state"
                  value={formData.state}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">{t('Select State')}</option>
                  <option value="Johor">Johor</option>
                  <option value="Kedah">Kedah</option>
                  <option value="Kelantan">Kelantan</option>
                  <option value="Kuala Lumpur">Kuala Lumpur</option>
                  <option value="Labuan">Labuan</option>
                  <option value="Melaka">Melaka</option>
                  <option value="Negeri Sembilan">Negeri Sembilan</option>
                  <option value="Pahang">Pahang</option>
                  <option value="Penang">Penang</option>
                  <option value="Perak">Perak</option>
                  <option value="Perlis">Perlis</option>
                  <option value="Putrajaya">Putrajaya</option>
                  <option value="Sabah">Sabah</option>
                  <option value="Sarawak">Sarawak</option>
                  <option value="Selangor">Selangor</option>
                  <option value="Terengganu">Terengganu</option>
                </select>
                {errors.state && (
                  <p className="mt-1 text-sm text-red-600">{errors.state}</p>
                )}
              </div>
            </div>

            {/* Emergency Contact */}
            <div className="mb-6">
              <label htmlFor="emergencyContact" className="block text-sm font-medium mb-2" style={{ color: '#374151' }}>
                {t('Emergency Contact Phone')} <span style={{ color: '#64748b', fontSize: '0.875rem' }}>({t('Optional')})</span>
              </label>
              <input
                type="tel"
                id="emergencyContact"
                name="emergencyContact"
                value={formData.emergencyContact}
                onChange={handleInputChange}
                placeholder={t('e.g., +60123456789, 0123456789, or 123456789')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              {errors.emergencyContact && (
                <p className="mt-1 text-sm text-red-600">{errors.emergencyContact}</p>
              )}
            </div>

            {/* Error message */}
            {errors.submit && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{errors.submit}</p>
              </div>
            )}

            {/* Buttons */}
            <div className={`flex justify-end space-x-3`}>
              {!mandatory && (
                <button
                  type="button"
                  onClick={handleSkip}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                  disabled={isSubmitting}
                >
                  {t('Skip for now')}
                </button>
              )}
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 text-sm font-medium text-white rounded-md transition-colors"
                style={{
                  backgroundColor: isSubmitting ? '#94a3b8' : '#0ea5e9',
                  cursor: isSubmitting ? 'not-allowed' : 'pointer'
                }}
              >
                {isSubmitting ? t('Updating...') : (mandatory ? t('Complete Profile') : t('Update Profile'))}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Feedback Modal */}
      <StandardModal
        isOpen={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
        type={feedbackModal.type}
        title={feedbackModal.title}
        message={feedbackModal.message}
        details={feedbackModal.details}
        autoClose={feedbackModal.type === 'success'}
        autoCloseDelay={2000}
      />
    </div>
  );
};

export default ProfileCompletionModal;
