import React, { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import StandardModal from './StandardModal';

/**
 * Confirmation Modal Component
 * Shows a confirmation dialog with amber color scheme using StandardModal
 */

const ConfirmationModal = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  title, 
  message, 
  confirmText = 'Confirm', 
  cancelText = 'Cancel',
  isSubmitting = false
}) => {
  const { t } = useLanguage();
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackModal, setFeedbackModal] = useState({
    type: 'success',
    title: '',
    message: '',
    details: null
  });

  // Helper functions for feedback modals
  const showSuccessModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'success',
      title,
      message,
      details
    });
    setShowFeedbackModal(true);
  };

  const showErrorModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'error',
      title,
      message,
      details
    });
    setShowFeedbackModal(true);
  };

  const handleConfirm = () => {
    if (isSubmitting) return;
    onConfirm();
  };

  const handleCancel = () => {
    if (isSubmitting) return;
    onClose();
  };

  // Custom buttons for the confirmation modal
  const confirmationButtons = (
    <React.Fragment>
      <button
        onClick={handleCancel}
        className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
        disabled={isSubmitting}
      >
        {cancelText}
      </button>
      <button
        onClick={handleConfirm}
        disabled={isSubmitting}
        className="px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors"
        style={{
          backgroundColor: isSubmitting ? '#94a3b8' : '#f59e0b',
          cursor: isSubmitting ? 'not-allowed' : 'pointer'
        }}
      >
        {isSubmitting ? t('Processing...') : confirmText}
      </button>
    </React.Fragment>
  );

  return (
    <React.Fragment>
      {/* Confirmation Modal */}
      <StandardModal
        isOpen={isOpen}
        onClose={handleCancel}
        title={title}
        message={message}
        primaryButton={<React.Fragment />} // Pass empty fragment to prevent default button
        secondaryButton={confirmationButtons}
        showCloseButton={false} // Hide the close button (X icon)
        preventBackdropClose={isSubmitting}
        type="warning"
      />

      {/* Feedback Modal */}
      <StandardModal
        isOpen={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
        type={feedbackModal.type}
        title={feedbackModal.title}
        message={feedbackModal.message}
        details={feedbackModal.details}
        autoClose={feedbackModal.type === 'success'}
        autoCloseDelay={2000}
      />
    </React.Fragment>
  );
};

export default ConfirmationModal;