import React, { useState, useEffect } from 'react';

/**
 * BookingPackageSelection Component
 * Package selection step for booking wizard
 */
export default function BookingPackageSelection({ 
  service, 
  bookingDetails, 
  setBookingDetails 
}) {
  const [selectedPackageId, setSelectedPackageId] = useState(bookingDetails.selectedPackageId || null);

  // Determine pricing scenario
  const getPricingScenario = () => {
    if (!service) return 'basic';
    
    const hasPackages = service.packages && service.packages.length > 0;
    const hasAgePricing = service.agePricing || (hasPackages && service.packages.some(pkg => pkg.agePricing));
    
    if (!hasPackages && !hasAgePricing) return 'basic';
    if (hasPackages && !hasAgePricing) return 'packages-only';
    if (!hasPackages && hasAgePricing) return 'age-only';
    return 'full-variation';
  };

  const scenario = getPricingScenario();

  // Handle package selection
  const handlePackageSelection = (packageId, packageCode) => {
    setSelectedPackageId(packageId);
    setBookingDetails(prev => ({ 
      ...prev, 
      selectedPackage: packageCode.toLowerCase(),
      selectedPackageId: packageId 
    }));
  };

  // Get selected package data
  const getSelectedPackage = () => {
    if (!service.packages || service.packages.length === 0) return null;
    return service.packages.find(pkg => pkg.id === selectedPackageId) || service.packages[0];
  };

  // Initialize selected package on first render
  useEffect(() => {
    if (service && service.packages && service.packages.length > 0 && !selectedPackageId) {
      const defaultPackage = service.packages.find(pkg => pkg.packageType.isDefault) || service.packages[0];
      handlePackageSelection(defaultPackage.id, defaultPackage.packageType.code);
    }
  }, [service, selectedPackageId]);

  // Don't render if no packages or wrong scenario
  if (!service.packages || service.packages.length === 0 || 
      (scenario !== 'packages-only' && scenario !== 'full-variation')) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Select Your Package
        </h3>
        <p className="text-sm text-gray-600 mb-6">
          Choose the package that best fits your needs. Each package includes different features and pricing.
        </p>
      </div>

      {/* Package Selection Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {service.packages.map((pkg) => (
          <div
            key={pkg.id}
            className={`border-2 rounded-lg p-4 cursor-pointer transition-colors relative ${
              selectedPackageId === pkg.id
                ? 'border-amber-500 bg-amber-50'
                : 'border-gray-200 hover:border-amber-300'
            }`}
            onClick={() => handlePackageSelection(pkg.id, pkg.packageType.code)}
          >
            {/* Popular Badge for Premium Package */}
            {pkg.packageType.code === 'PREMIUM' && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-amber-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                  Most Popular
                </span>
              </div>
            )}

            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold text-gray-900">{pkg.packageType.name}</h3>
              <div className="text-lg font-bold text-amber-600">
                {scenario === 'packages-only' ? (
                  `RM ${pkg.basePrice}`
                ) : (
                  <span className="text-sm">From RM {pkg.agePricing?.adult || pkg.basePrice}</span>
                )}
              </div>
            </div>

            <p className="text-sm text-gray-600 mb-3">{pkg.description || pkg.packageType.description}</p>

            {/* Age-based pricing for full-variation scenario */}
            {scenario === 'full-variation' && pkg.agePricing && (
              <div className="grid grid-cols-3 gap-2 text-xs mb-3">
                <div className="text-center p-2 bg-gray-50 rounded">
                  <div className="font-medium text-gray-900">Adults</div>
                  <div className="text-amber-600 font-bold">RM {pkg.agePricing.adult}</div>
                </div>
                <div className="text-center p-2 bg-gray-50 rounded">
                  <div className="font-medium text-gray-900">Children</div>
                  <div className="text-blue-600 font-bold">RM {pkg.agePricing.child}</div>
                </div>
                <div className="text-center p-2 bg-gray-50 rounded">
                  <div className="font-medium text-gray-900">Toddlers</div>
                  <div className="text-green-600 font-bold">
                    {pkg.agePricing.toddler === 0 ? 'FREE' : `RM ${pkg.agePricing.toddler}`}
                  </div>
                </div>
              </div>
            )}

            {/* Package Inclusions */}
            <div className="mt-3">
              <div className="flex flex-wrap gap-1">
                {pkg.includedItems.slice(0, 6).map((item, idx) => (
                  <span
                    key={idx}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
                  >
                    ✓ {item}
                  </span>
                ))}
                {pkg.includedItems.length > 6 && (
                  <div className="relative group">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-600 cursor-help">
                      +{pkg.includedItems.length - 6} more
                    </span>

                    {/* Hover Tooltip */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 whitespace-nowrap max-w-xs">
                      <div className="text-center">
                        <div className="font-medium mb-1">Additional Items:</div>
                        <div className="space-y-1">
                          {pkg.includedItems.slice(6).map((item, idx) => (
                            <div key={idx} className="text-left">• {item}</div>
                          ))}
                        </div>
                      </div>
                      {/* Tooltip Arrow */}
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Selected Package Summary */}
      {selectedPackageId && (
        <div className="mt-6 p-4 bg-amber-50 rounded-lg border border-amber-200">
          <h4 className="font-medium text-gray-900 mb-2">Selected Package</h4>
          {(() => {
            const selected = getSelectedPackage();
            if (!selected) return null;

            return (
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-900">{selected.packageType.name}</span>
                  <span className="text-lg font-bold text-amber-600">
                    {scenario === 'packages-only' 
                      ? `RM ${selected.basePrice} per person`
                      : `From RM ${selected.agePricing?.adult || selected.basePrice}`
                    }
                  </span>
                </div>
                <p className="text-sm text-gray-600">{selected.description || selected.packageType.description}</p>
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
}
