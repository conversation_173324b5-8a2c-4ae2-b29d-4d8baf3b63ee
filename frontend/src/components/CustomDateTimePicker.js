import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Calendar } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

/**
 * Custom DateTime Picker Component
 * Based on CustomDatePicker but enhanced with time selection
 * Selection-only (no manual text input) for better UX
 */

const CustomDateTimePicker = ({
  value,
  onChange,
  name = 'datetime',
  placeholder = 'Select date and time',
  className = '',
  error = false,
  disabled = false
}) => {
  const { t } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedTime, setSelectedTime] = useState({ hour: '', minute: '' });
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [showYearDropdown, setShowYearDropdown] = useState(false);
  const [activeTab, setActiveTab] = useState('date'); // 'date' or 'time'
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const [isMobile, setIsMobile] = useState(false);
  const dropdownRef = useRef(null);
  const portalRef = useRef(null);
  const inputRef = useRef(null);

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768); // md breakpoint
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Calculate dropdown position
  const calculateDropdownPosition = () => {
    if (inputRef.current && !isMobile) {
      const rect = inputRef.current.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
      const viewportHeight = window.innerHeight;
      const dropdownHeight = 400; // Estimated dropdown height

      // Check if dropdown fits below the input
      const spaceBelow = viewportHeight - rect.bottom;
      const spaceAbove = rect.top;

      let top, positioning;

      if (spaceBelow >= dropdownHeight || spaceBelow >= spaceAbove) {
        // Position below input
        top = rect.bottom + scrollTop + 4;
        positioning = 'below';
      } else {
        // Position above input
        top = rect.top + scrollTop - dropdownHeight - 4;
        positioning = 'above';
      }

      setDropdownPosition({
        top,
        left: rect.left + scrollLeft,
        width: Math.max(rect.width, 320), // Minimum width for better UX
        positioning
      });
    }
  };

  // Close dropdown when clicking outside and handle positioning
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (portalRef.current && !portalRef.current.contains(event.target) &&
          inputRef.current && !inputRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    const handleResize = () => {
      if (isOpen) {
        calculateDropdownPosition();
      }
    };

    const handleScroll = () => {
      if (isOpen && !isMobile) {
        // Recalculate position on scroll for desktop
        calculateDropdownPosition();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleScroll);
      calculateDropdownPosition();
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [isOpen]);

  // Parse datetime value when prop changes
  useEffect(() => {
    if (value) {
      const [datePart, timePart] = value.split('T');
      if (datePart) {
        const date = new Date(datePart);
        if (!isNaN(date.getTime())) {
          setSelectedDate(date);
          setCurrentMonth(date);
        }
      }
      if (timePart) {
        const [hour, minute] = timePart.split(':');
        setSelectedTime({ 
          hour: hour || '', 
          minute: minute || '' 
        });
      }
    } else {
      setSelectedDate(null);
      setSelectedTime({ hour: '', minute: '' });
    }
  }, [value]);

  // Generate year options (current year to current year + 2)
  const generateYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let year = currentYear; year <= currentYear + 2; year++) {
      years.push(year);
    }
    return years;
  };

  // Generate hour options (00-23)
  const generateHourOptions = () => {
    const hours = [];
    for (let hour = 0; hour < 24; hour++) {
      hours.push(String(hour).padStart(2, '0'));
    }
    return hours;
  };

  // Generate minute options (00, 15, 30, 45)
  const generateMinuteOptions = () => {
    return ['00', '15', '30', '45'];
  };

  // Format date for display
  const formatDate = (date) => {
    if (!date) return '';
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Format time for display
  const formatTime = (hour, minute) => {
    if (!hour || !minute) return '';
    return `${hour}:${minute}`;
  };

  // Format complete datetime for display
  const formatDateTime = () => {
    if (!selectedDate) return '';
    const dateStr = formatDate(selectedDate);
    const timeStr = formatTime(selectedTime.hour, selectedTime.minute);
    if (timeStr) {
      return `${dateStr} ${timeStr}`;
    }
    return dateStr;
  };

  // Get minimum date (today)
  const getMinDate = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return today;
  };

  // Get days in month
  const getDaysInMonth = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add days of the month
    const minDate = getMinDate();
    for (let day = 1; day <= daysInMonth; day++) {
      const currentDate = new Date(year, month, day);
      // Only add dates that are today or in the future
      if (currentDate >= minDate) {
        days.push(currentDate);
      } else {
        days.push(null); // Past dates are disabled
      }
    }
    
    return days;
  };

  // Helper function to format date without timezone issues
  const formatDateForForm = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Handle date selection
  const handleDateSelect = (date) => {
    setSelectedDate(date);
    setActiveTab('time'); // Switch to time tab after selecting date
    updateValue(date, selectedTime);
  };

  // Handle time selection
  const handleTimeSelect = (type, value) => {
    const newTime = { ...selectedTime, [type]: value };
    setSelectedTime(newTime);
    updateValue(selectedDate, newTime);
  };

  // Update the form value
  const updateValue = (date, time) => {
    if (!date) return;
    
    let formattedValue = formatDateForForm(date);
    if (time.hour && time.minute) {
      formattedValue += `T${time.hour}:${time.minute}`;
    }
    
    onChange({ target: { name, value: formattedValue } });
  };

  // Handle year selection
  const handleYearSelect = (year) => {
    const newDate = new Date(currentMonth);
    newDate.setFullYear(year);
    setCurrentMonth(newDate);
    setShowYearDropdown(false);
  };

  // Navigate months
  const navigateMonth = (direction) => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(currentMonth.getMonth() + direction);
    
    // Don't allow navigation to past months
    const today = new Date();
    if (newMonth.getFullYear() < today.getFullYear() || 
        (newMonth.getFullYear() === today.getFullYear() && newMonth.getMonth() < today.getMonth())) {
      return;
    }
    
    setCurrentMonth(newMonth);
  };

  // Complete selection and close dropdown
  const handleComplete = () => {
    if (selectedDate && selectedTime.hour && selectedTime.minute) {
      setIsOpen(false);
    }
  };

  // Month and year names - using translations
  const monthNames = [
    t('january'), t('february'), t('march'), t('april'), t('may'), t('june'),
    t('july'), t('august'), t('september'), t('october'), t('november'), t('december')
  ];

  const dayNames = [t('sun'), t('mon'), t('tue'), t('wed'), t('thu'), t('fri'), t('sat')];
  const days = getDaysInMonth(currentMonth);

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Input Field */}
      <div
        ref={inputRef}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        className={`w-full h-12 px-3 py-2 border rounded-md cursor-pointer transition-colors ${
          disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white hover:border-gray-400'
        } ${error ? 'border-red-500' : 'border-gray-300'} ${
          isOpen ? 'ring-2 ring-amber-500 border-transparent' : ''
        }`}
        style={{
          borderColor: error ? '#ef4444' : isOpen ? '#f59e0b' : '#d1d5db',
          outline: 'none'
        }}
      >
        <div className="flex items-center justify-between h-full">
          <div className="flex items-center space-x-2 flex-1">
            <Calendar className="h-4 w-4 text-gray-400 flex-shrink-0" />
            <span
              className={`flex-1 text-left truncate  ${(selectedDate || (selectedTime.hour && selectedTime.minute)) ? 'font-medium' : ''}`}
              style={{ color: (selectedDate || (selectedTime.hour && selectedTime.minute)) ? '#374151' : '#6b7280' }}
            >
              {formatDateTime() || placeholder}
            </span>
          </div>
          <svg
            className={`text-gray-400 w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''} flex-shrink-0`}
            // style={{ color: '#6b7280' }}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>

      {/* Dropdown Portal */}
      {isOpen && typeof window !== 'undefined' && typeof document !== 'undefined' && document.body && createPortal(
        <div
          ref={portalRef}
          className={isMobile
            ? "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4"
            : "fixed bg-white border border-gray-300 rounded-md shadow-lg"
          }
          style={isMobile
            ? { zIndex: 9998 }
            : {
                top: dropdownPosition.top,
                left: dropdownPosition.left,
                width: Math.max(dropdownPosition.width, 320),
                maxWidth: '400px',
                borderColor: '#d1d5db',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                zIndex: 9999,
                maxHeight: '400px',
                overflowY: 'auto'
              }
          }
          onClick={isMobile ? () => setIsOpen(false) : undefined}
        >
          <div
            className={isMobile
              ? "bg-white rounded-lg shadow-xl w-full max-w-sm max-h-[80vh] overflow-hidden"
              : ""
            }
            onClick={isMobile ? (e) => e.stopPropagation() : undefined}
            style={isMobile ? {
              borderColor: '#d1d5db',
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
            } : {}}
          >
            {/* Mobile Header */}
            {isMobile && (
              <div className="flex items-center justify-between p-4 border-b" style={{ borderColor: '#e5e7eb' }}>
                <h3 className="text-lg font-semibold" style={{ color: '#111827' }}>
                  {t('selectDateTime')}
                </h3>
                <button
                  type="button"
                  onClick={() => setIsOpen(false)}
                  className="p-1 rounded-md hover:bg-gray-100 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            {/* Content Container */}
            <div className={isMobile ? "overflow-y-auto" : ""} style={isMobile ? { maxHeight: 'calc(80vh - 80px)' } : {}}>
          {/* Tab Headers */}
          <div className="flex border-b" style={{ borderColor: '#e5e7eb' }}>
            <button
              type="button"
              onClick={() => setActiveTab('date')}
              className={`flex-1 px-3 py-2 text-sm font-medium transition-colors ${
                activeTab === 'date' ? 'border-b-2' : ''
              }`}
              style={{
                borderColor: activeTab === 'date' ? '#f59e0b' : 'transparent',
                backgroundColor: 'transparent',
                color: activeTab === 'date' ? '#f59e0b' : '#6b7280'
              }}
            >
              {t('selectDate')}
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('time')}
              className={`flex-1 px-3 py-2 text-sm font-medium transition-colors ${
                activeTab === 'time' ? 'border-b-2' : ''
              }`}
              style={{
                borderColor: activeTab === 'time' ? '#f59e0b' : 'transparent',
                backgroundColor: 'transparent',
                color: activeTab === 'time' ? '#f59e0b' : '#6b7280'
              }}
            >
              {t('selectTime')}
            </button>
          </div>

          {/* Content Area */}
          <div className="p-3">
            {activeTab === 'date' ? (
              /* Date Selection */
              <div>
                {/* Header */}
                <div className="flex items-center justify-between mb-4">
                  <button
                    type="button"
                    onClick={() => navigateMonth(-1)}
                    className="p-1 rounded hover:bg-gray-100 transition-colors"
                    style={{ color: '#6b7280' }}
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>

                  {/* Month/Year Display with Year Dropdown */}
                  <div className="flex items-center space-x-2">
                    <span className="font-medium" style={{ color: '#374151' }}>
                      {monthNames[currentMonth.getMonth()]}
                    </span>
                    <div className="relative">
                      <button
                        type="button"
                        onClick={() => setShowYearDropdown(!showYearDropdown)}
                        className="font-medium px-2 py-1 rounded hover:bg-gray-100 transition-colors flex items-center space-x-1"
                        style={{ color: '#374151' }}
                      >
                        <span>{currentMonth.getFullYear()}</span>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </button>

                      {/* Year Dropdown */}
                      {showYearDropdown && (
                        <div
                          className="absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-10"
                          style={{
                            maxHeight: '150px',
                            overflowY: 'auto',
                            minWidth: '80px',
                            borderColor: '#d1d5db'
                          }}
                        >
                          {generateYearOptions().map(year => (
                            <button
                              key={year}
                              type="button"
                              onClick={() => handleYearSelect(year)}
                              className="w-full px-3 py-1 text-left hover:bg-gray-100 transition-colors"
                              style={{
                                color: year === currentMonth.getFullYear() ? '#f59e0b' : '#374151',
                                fontWeight: year === currentMonth.getFullYear() ? 'bold' : 'normal'
                              }}
                            >
                              {year}
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  <button
                    type="button"
                    onClick={() => navigateMonth(1)}
                    className="p-1 rounded hover:bg-gray-100 transition-colors"
                    style={{ color: '#6b7280' }}
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>

                {/* Calendar Grid */}
                <div>
                  {/* Day Headers */}
                  <div className="grid grid-cols-7 gap-1 mb-2">
                    {dayNames.map(day => (
                      <div
                        key={day}
                        className="text-center text-xs font-medium py-2"
                        style={{ color: '#6b7280' }}
                      >
                        {day}
                      </div>
                    ))}
                  </div>

                  {/* Days Grid */}
                  <div className="grid grid-cols-7 gap-1">
                    {days.map((day, index) => {
                      if (!day) {
                        return <div key={index} className="h-8"></div>;
                      }

                      const isSelected = selectedDate &&
                        day.getDate() === selectedDate.getDate() &&
                        day.getMonth() === selectedDate.getMonth() &&
                        day.getFullYear() === selectedDate.getFullYear();

                      const isToday = new Date().toDateString() === day.toDateString();

                      return (
                        <button
                          key={index}
                          type="button"
                          onClick={() => handleDateSelect(day)}
                          className={`h-6 w-6 text-xs rounded transition-colors ${
                            isSelected
                              ? 'text-white'
                              : isToday
                              ? 'font-bold'
                              : 'hover:bg-gray-100'
                          }`}
                          style={{
                            backgroundColor: isSelected ? '#f59e0b' : 'transparent',
                            color: isSelected ? '#ffffff' : isToday ? '#f59e0b' : '#374151'
                          }}
                        >
                          {day.getDate()}
                        </button>
                      );
                    })}
                  </div>
                </div>
              </div>
            ) : (
              /* Time Selection */
              <div className="space-y-4">
                <div className="text-center">
                  <h3 className="text-lg font-medium mb-2" style={{ color: '#374151' }}>
                    {t('selectTime')}
                  </h3>
                  {selectedDate && (
                    <p className="text-sm" style={{ color: '#6b7280' }}>
                      {formatDate(selectedDate)}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-3">
                  {/* Hour Selection */}
                  <div>
                    <label className="block text-xs font-medium mb-1" style={{ color: '#374151' }}>
                      {t('hour')}
                    </label>
                    <div className="max-h-24 overflow-y-auto border rounded-md" style={{ borderColor: '#d1d5db' }}>
                      {generateHourOptions().map(hour => (
                        <button
                          key={hour}
                          type="button"
                          onClick={() => handleTimeSelect('hour', hour)}
                          className={`w-full px-2 py-1 text-left hover:bg-gray-100 transition-colors text-xs ${
                            selectedTime.hour === hour ? 'font-bold' : ''
                          }`}
                          style={{
                            backgroundColor: selectedTime.hour === hour ? '#fef3c7' : 'transparent',
                            color: selectedTime.hour === hour ? '#f59e0b' : '#374151'
                          }}
                        >
                          {hour}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Minute Selection */}
                  <div>
                    <label className="block text-xs font-medium mb-1" style={{ color: '#374151' }}>
                      {t('minute')}
                    </label>
                    <div className="max-h-24 overflow-y-auto border rounded-md" style={{ borderColor: '#d1d5db' }}>
                      {generateMinuteOptions().map(minute => (
                        <button
                          key={minute}
                          type="button"
                          onClick={() => handleTimeSelect('minute', minute)}
                          className={`w-full px-2 py-1 text-left hover:bg-gray-100 transition-colors text-xs ${
                            selectedTime.minute === minute ? 'font-bold' : ''
                          }`}
                          style={{
                            backgroundColor: selectedTime.minute === minute ? '#fef3c7' : 'transparent',
                            color: selectedTime.minute === minute ? '#f59e0b' : '#374151'
                          }}
                        >
                          {minute}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Selected Time Display */}
                {selectedTime.hour && selectedTime.minute && (
                  <div className="text-center p-3 rounded-md" style={{ backgroundColor: '#fef3c7' }}>
                    <p className="text-sm font-medium" style={{ color: '#f59e0b' }}>
                      {t('selectedTime')}: {formatTime(selectedTime.hour, selectedTime.minute)}
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-3 border-t flex justify-end space-x-2" style={{ borderColor: '#e5e7eb' }}>
            <button
              type="button"
              onClick={() => setIsOpen(false)}
              className="px-4 py-2 text-sm rounded transition-colors"
              style={{ backgroundColor: '#f3f4f6', color: '#6b7280' }}
            >
              {t('cancel')}
            </button>
            {selectedDate && selectedTime.hour && selectedTime.minute && (
              <button
                type="button"
                onClick={handleComplete}
                className="px-4 py-2 text-sm rounded transition-colors"
                style={{ backgroundColor: '#f59e0b', color: '#ffffff' }}
              >
                {t('done')}
              </button>
            )}
          </div>
            </div>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

export default CustomDateTimePicker;
