import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import { useRouter } from 'next/router';

/**
 * Authentication Context for GoSea Platform
 * Manages user authentication state, login, logout, and session management
 */

// Initial authentication state
const initialState = {
  user: null,
  accessToken: null,
  refreshToken: null,
  sessionToken: null,
  isAuthenticated: false,
  isLoading: true,
  error: null
};

// Authentication action types
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  REFRESH_TOKEN_SUCCESS: 'REFRESH_TOKEN_SUCCESS',
  REFRESH_TOKEN_FAILURE: 'REFRESH_TOKEN_FAILURE',
  SET_LOADING: 'SET_LOADING',
  C<PERSON><PERSON>_ERROR: 'CLEAR_ERROR',
  UPDATE_USER: 'UPDATE_USER'
};

// Authentication reducer
function authReducer(state, action) {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
      return {
        ...state,
        isLoading: true,
        error: null
      };

    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        accessToken: action.payload.accessToken,
        refreshToken: action.payload.refreshToken,
        sessionToken: action.payload.sessionToken,
        isAuthenticated: true,
        isLoading: false,
        error: null
      };

    case AUTH_ACTIONS.LOGIN_FAILURE:
      return {
        ...state,
        user: null,
        accessToken: null,
        refreshToken: null,
        sessionToken: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload.error
      };

    case AUTH_ACTIONS.LOGOUT:
      console.log('AUTH_ACTIONS.LOGOUT dispatched, resetting to initial state');
      return {
        ...initialState,
        isLoading: false
      };

    case AUTH_ACTIONS.REFRESH_TOKEN_SUCCESS:
      return {
        ...state,
        accessToken: action.payload.accessToken,
        refreshToken: action.payload.refreshToken,
        sessionToken: action.payload.sessionToken,
        user: action.payload.user,
        error: null
      };

    case AUTH_ACTIONS.REFRESH_TOKEN_FAILURE:
      return {
        ...initialState,
        isLoading: false,
        error: action.payload.error
      };

    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload.isLoading
      };

    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null
      };

    case AUTH_ACTIONS.UPDATE_USER:
      return {
        ...state,
        user: { ...state.user, ...action.payload.user }
      };

    default:
      return state;
  }
}

// Create authentication context
const AuthContext = createContext();

// Authentication provider component
export function AuthProvider({ children }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // API base URL
  const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';

  // Load authentication state from localStorage on mount
  useEffect(() => {
    const loadAuthState = () => {
      console.log('Loading auth state from localStorage');
      try {
        const storedAuth = localStorage.getItem('gosea_auth');
        console.log('Stored auth data:', storedAuth ? 'Found' : 'Not found');
        if (storedAuth) {
          const authData = JSON.parse(storedAuth);
          console.log('Parsed auth data:', { hasAccessToken: !!authData.accessToken, hasRefreshToken: !!authData.refreshToken });

          // Check if tokens are still valid (basic expiry check)
          if (authData.accessToken && authData.refreshToken) {
            console.log('Restoring auth state from localStorage');
            dispatch({
              type: AUTH_ACTIONS.LOGIN_SUCCESS,
              payload: authData
            });
            return;
          } else {
            console.log('Invalid tokens in localStorage, clearing');
            localStorage.removeItem('gosea_auth');
          }
        }
      } catch (error) {
        console.error('Error loading auth state:', error);
        localStorage.removeItem('gosea_auth');
      }

      console.log('Setting loading to false');
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: { isLoading: false } });
    };

    loadAuthState();
  }, []);

  // Save authentication state to localStorage
  const saveAuthState = (authData) => {
    try {
      localStorage.setItem('gosea_auth', JSON.stringify(authData));
    } catch (error) {
      console.error('Error saving auth state:', error);
    }
  };

  // Save state to localStorage whenever authentication state changes
  useEffect(() => {
    if (state.isAuthenticated && state.accessToken && state.refreshToken) {
      saveAuthState({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        accessToken: state.accessToken,
        refreshToken: state.refreshToken
      });
    }
  }, [state.isAuthenticated, state.user, state.accessToken, state.refreshToken]);

  // Force refresh of user data (useful after profile updates)
  const refreshUser = async () => {
    if (state.isAuthenticated && state.accessToken) {
      await getCurrentUser();
    }
  };

  // Clear authentication state from localStorage
  const clearAuthState = () => {
    try {
      console.log('Clearing localStorage auth state');
      localStorage.removeItem('gosea_auth');
      console.log('localStorage cleared successfully');
    } catch (error) {
      console.error('Error clearing auth state:', error);
    }
  };

  // Login function - supports both email/password and OAuth login
  const login = async (emailOrAuthData, password = null) => {
    dispatch({ type: AUTH_ACTIONS.LOGIN_START });

    try {
      let authData;

      // Check if this is OAuth login (object with tokens) or email/password login
      if (typeof emailOrAuthData === 'object' && emailOrAuthData.accessToken) {
        // OAuth login - use provided tokens and user data
        authData = {
          user: emailOrAuthData.user,
          accessToken: emailOrAuthData.accessToken,
          refreshToken: emailOrAuthData.refreshToken,
          sessionToken: emailOrAuthData.sessionToken || null
        };

        console.log('OAuth login successful for user:', authData.user.email);
      } else {
        // Email/password login - make API call
        const response = await fetch(`${API_URL}/api/auth/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email: emailOrAuthData, password }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.message || 'Login failed');
        }

        authData = {
          user: data.user,
          accessToken: data.accessToken,
          refreshToken: data.refreshToken,
          sessionToken: data.sessionToken
        };
      }

      dispatch({
        type: AUTH_ACTIONS.LOGIN_SUCCESS,
        payload: authData
      });

      // Note: Language preference will be applied by a higher-level component
      // that has access to both AuthContext and LanguageContext

      saveAuthState(authData);
      return { success: true, data: authData };
    } catch (error) {
      const errorMessage = error.message || 'Login failed';
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: { error: errorMessage }
      });
      return { success: false, error: errorMessage };
    }
  };

  // Register function
  const register = async (userData) => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: { isLoading: true } });

    try {
      const response = await fetch(`${API_URL}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Registration failed');
      }

      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: { isLoading: false } });
      return { success: true, data };
    } catch (error) {
      const errorMessage = error.message || 'Registration failed';
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: { error: errorMessage }
      });
      return { success: false, error: errorMessage };
    }
  };

  // Logout function
  const logout = async () => {
    console.log('Logout function called');
    try {
      if (state.sessionToken) {
        console.log('Making logout API call with session token:', state.sessionToken);
        const response = await fetch(`${API_URL}/api/auth/logout`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${state.accessToken}`,
            'X-Session-Token': state.sessionToken,
          },
        });
        console.log('Logout API response:', response.status);
      }
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      console.log('Clearing auth state and dispatching logout');
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
      clearAuthState();
      console.log('Logout completed');
    }
  };

  // Refresh token function
  const refreshToken = useCallback(async () => {
    if (!state.refreshToken) {
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
      clearAuthState();
      return false;
    }

    try {
      const response = await fetch(`${API_URL}/api/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken: state.refreshToken }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Token refresh failed');
      }

      const authData = {
        user: data.user,
        accessToken: data.accessToken,
        refreshToken: data.refreshToken,
        sessionToken: data.sessionToken
      };

      dispatch({
        type: AUTH_ACTIONS.REFRESH_TOKEN_SUCCESS,
        payload: authData
      });

      saveAuthState(authData);
      return true;
    } catch (error) {
      dispatch({
        type: AUTH_ACTIONS.REFRESH_TOKEN_FAILURE,
        payload: { error: error.message }
      });
      clearAuthState();
      return false;
    }
  }, [state.refreshToken]);

  // Get current user info
  const getCurrentUser = useCallback(async () => {
    if (!state.accessToken) {
      return null;
    }

    try {
      const response = await fetch(`${API_URL}/api/auth/me`, {
        headers: {
          'Authorization': `Bearer ${state.accessToken}`,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        if (response.status === 401) {
          // Try to refresh token
          const refreshed = await refreshToken();
          if (refreshed) {
            // Retry with new token
            return getCurrentUser();
          }
        }
        throw new Error(data.message || 'Failed to get user info');
      }

      dispatch({
        type: AUTH_ACTIONS.UPDATE_USER,
        payload: { user: data.user }
      });

      // Note: Language preference will be applied by a higher-level component
      // that has access to both AuthContext and LanguageContext

      return data.user;
    } catch (error) {
      console.error('Get current user failed:', error);
      return null;
    }
  }, [state.accessToken, refreshToken]);

  // Clear error function
  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
  };

  // Check if user has specific role
  const hasRole = (role) => {
    return state.user?.role === role;
  };

  // Check if user is admin
  const isAdmin = () => {
    return hasRole('ADMIN');
  };

  // Check if user is boat owner
  const isBoatOwner = () => {
    return hasRole('BOAT_OWNER') || isAdmin();
  };

  // Check if user is affiliate agent
  const isAffiliateAgent = () => {
    return hasRole('AFFILIATE_AGENT') || isAdmin();
  };

  // Context value
  const value = {
    // State
    ...state,

    // Actions
    login,
    register,
    logout,
    refreshToken,
    getCurrentUser,
    refreshUser,
    clearError,

    // Utilities
    hasRole,
    isAdmin,
    isBoatOwner,
    isAffiliateAgent
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use authentication context
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Higher-order component for protected routes
export function withAuth(Component, options = {}) {
  const { requireAuth = true, requiredRole = null, redirectTo = '/' } = options;

  return function AuthenticatedComponent(props) {
    const { isAuthenticated, isLoading, hasRole } = useAuth();
    const router = useRouter();

    useEffect(() => {
      if (!isLoading) {
        if (requireAuth && !isAuthenticated) {
          router.push(redirectTo);
        } else if (requiredRole && !hasRole(requiredRole)) {
          router.push('/unauthorized');
        }
      }
    }, [isAuthenticated, isLoading, hasRole, router]);

    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="loading loading-spinner loading-lg text-primary"></div>
        </div>
      );
    }

    if (requireAuth && !isAuthenticated) {
      return null;
    }

    if (requiredRole && !hasRole(requiredRole)) {
      return null;
    }

    return <Component {...props} />;
  };
}

export default AuthContext;
