import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/router';
import { useLanguage } from '../contexts/LanguageContext';
import Navbar from '../components/Navbar';
import StandardModal from '../components/StandardModal';
import BoatSearchForm from '../components/BoatSearchForm';
import ProviderCard from '../components/provider/ProviderCard';
import Footer from '../components/Footer';
import Head from 'next/head';

export default function BoatsPage() {
  const { t } = useLanguage();
  const router = useRouter();
  
  // Convert legacy date/time to datetime format for backward compatibility
  const convertLegacyDateTime = (filters) => {
    if (filters.date && filters.time) {
      return `${filters.date}T${filters.time}`;
    } else if (filters.date) {
      return filters.date;
    } else if (filters.datetime) {
      return filters.datetime;
    }
    return '';
  };

  // Search state
  const [searchFilters, setSearchFilters] = useState({
    jettyId: '',
    serviceCategory: '',
    destinationId: '',
    datetime: ''
  });
  
  const [searchResults, setSearchResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  
  // Modal state
  const [showModal, setShowModal] = useState(false);
  const [modalConfig, setModalConfig] = useState({
    type: 'info',
    title: '',
    message: '',
    details: null
  });

  // Modal helpers
  const showErrorModal = useCallback((title, message, details = null) => {
    setModalConfig({ type: 'error', title, message, details });
    setShowModal(true);
  }, []);

  // Initialize filters from URL params and load initial data
  useEffect(() => {
    // Only run if router is ready
    if (!router.isReady) return;

    const { jettyId, jetty, serviceCategory, serviceType, destinationId, destination, date, time, datetime } = router.query;
    const initialFilters = {
      jettyId: jettyId || jetty || '',
      serviceCategory: serviceCategory || serviceType || '',
      destinationId: destinationId || destination || '',
      datetime: datetime || convertLegacyDateTime({ date, time }) || ''
    };

    setSearchFilters(initialFilters);

    // Auto-search if any params are present
    if (jettyId || jetty || serviceCategory || serviceType || destinationId || destination || datetime || date || time) {
      performSearch(initialFilters);
    } else {
      // Load all providers initially
      performSearch({});
    }
  }, [router.isReady, router.query, showErrorModal]);

  // Perform provider search using the new API
  const performSearch = useCallback(async (filters = {}) => {
    setIsLoading(true);
    setHasSearched(true);

    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (filters.jettyId) queryParams.set('jettyId', filters.jettyId);
      if (filters.serviceCategory) queryParams.set('serviceCategory', filters.serviceCategory);
      if (filters.destinationId) queryParams.set('destinationId', filters.destinationId);
      if (filters.datetime) {
        const date = filters.datetime.split('T')[0];
        if (date) queryParams.set('date', date);
      }

      const searchUrl = `/api/search/providers${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

      const response = await fetch(searchUrl);
      const data = await response.json();

      if (data.success) {
        setSearchResults(data.data);
      } else {
        console.error('Search API error:', data.message);
        showErrorModal('Search Error', data.message || 'An error occurred while searching. Please try again.');
        setSearchResults([]);
      }

    } catch (error) {
      console.error('Search error:', error);
      showErrorModal('Search Error', 'An error occurred while searching. Please try again.');
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  }, [showErrorModal]);

  // Handle search from the search form component
  const handleSearchFromForm = useCallback(async (filters) => {
    setSearchFilters(filters);
    await performSearch(filters);

    // Update URL with search params
    const newQuery = { ...filters };
    Object.keys(newQuery).forEach(key => {
      if (!newQuery[key]) delete newQuery[key];
    });
    router.replace({ pathname: '/boats', query: newQuery }, undefined, { shallow: true });
  }, [router, performSearch]);

  // Clear all filters and show all providers
  const clearFilters = () => {
    const emptyFilters = {
      jettyId: '',
      serviceCategory: '',
      destinationId: '',
      datetime: ''
    };
    setSearchFilters(emptyFilters);
    performSearch({});
    router.replace('/boats', undefined, { shallow: true });
  };

  const pageName = "Search Boats";

  return (
    <>
      <Head>
        <title>{pageName} - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
    <div className="min-h-screen bg-white">
      <Navbar currentPage="boats" />

      {/* Hero-style Search Section */}
      <section
        className="relative overflow-visible"
        style={{ backgroundColor: '#f0f9ff' }} // base-200
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10 lg:py-16">
          <div className="text-center">
            <h1 className="text-3xl tracking-tight font-extrabold sm:text-4xl lg:text-5xl mb-4"
                style={{ color: '#0f172a' }} // base-content
            >
              {t('searchBoats')}
            </h1>
            <p className="mt-4 sm:mt-6 max-w-3xl mx-auto text-md lg:text-xl leading-relaxed"
               style={{ color: '#475569' }} // base-content-light
            >
              {t('findPerfectBoat')}
            </p>

            {/* Search Filters */}
            <div className="mt-6 sm:mt-10 max-w-6xl mx-auto px-4 md:px-3">
              <BoatSearchForm
                initialFilters={searchFilters}
                onSearch={handleSearchFromForm}
                variant="hero"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Search Results Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Search Results */}
        <div>
          {isLoading && (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-500 mx-auto mb-4"></div>
              <p className="text-gray-600">{t('searchingBoatProviders')}</p>
            </div>
          )}

          {!isLoading && hasSearched && searchResults.length === 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {t('noBoatProvidersFound')}
              </h3>
              <p className="text-gray-600 mb-6">
                Try adjusting your search criteria or clear filters to see all providers.
              </p>
              <button
                onClick={clearFilters}
                className="px-6 py-3 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors"
              >
                Clear Filters
              </button>
            </div>
          )}

          {!isLoading && searchResults.length > 0 && (
            <div>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  {t('boatProviders')} ({searchResults.length})
                </h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {searchResults.map((provider) => (
                  <ProviderCard
                    key={provider.id}
                    provider={provider}
                    searchFilters={searchFilters}
                  />
                ))}
              </div>
            </div>
          )}

          {!hasSearched && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">⛵</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {t('readyToSearch')}
              </h3>
              <p className="text-gray-600">
                {t('useFiltersAbove')}
              </p>
            </div>
          )}
        </div>
      </div>

      <Footer />

      {/* Modal */}
      <StandardModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        type={modalConfig.type}
        title={modalConfig.title}
        message={modalConfig.message}
        details={modalConfig.details}
      />
    </div>
    </>
  );
}
