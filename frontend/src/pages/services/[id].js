import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { useAuth } from "../../contexts/AuthContext";
import { useLanguage } from "../../contexts/LanguageContext";
import Navbar from "../../components/Navbar";
import StandardModal from "../../components/StandardModal";
import DynamicPricingSection from "../../components/DynamicPricingSection";
import {
  ClockIcon,
  UsersIcon,
  CurrencyDollarIcon,
  MapPinIcon,
  CheckIcon,
  XMarkIcon,
  CalendarIcon,
  PhotoIcon,
  ArrowLeftIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  BuildingOfficeIcon
} from "@heroicons/react/24/outline";
import Footer from "../../components/Footer";
import { NotepadText } from 'lucide-react';
import Head from 'next/head';

function ServiceDetailPage() {
  const { t } = useLanguage();
  const { user } = useAuth();
  const router = useRouter();
  const { id } = router.query;

  // All useState hooks must be declared at the top of the component
  const [service, setService] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [modalConfig, setModalConfig] = useState({});
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [selectedPackage, setSelectedPackage] = useState(null);

  // Touch/swipe support hooks moved to top
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);

  useEffect(() => {
    if (id) {
      fetchServiceDetails();
    }
  }, [id]);

  const fetchServiceDetails = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/services/${id}`);
      const data = await response.json();

      if (data.success) {
        setService(data.service);
      } else {
        showErrorModal(
          t("serviceNotFound"),
          data.message || t("serviceNotFoundMessage")
        );
      }
    } catch (error) {
      console.error("Fetch service details error:", error);
      showErrorModal(
        t("error"),
        t("errorLoadingService")
      );
    } finally {
      setIsLoading(false);
    }
  };

  const getImageUrl = (imagePath) => {
    if (!imagePath) return '/images/service-placeholder.jpg';
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/uploads/')) {
      return `${process.env.NEXT_PUBLIC_API_URL}${imagePath}`;
    }
    return imagePath;
  };

  const showErrorModal = (title, message) => {
    setModalConfig({ type: "error", title, message });
    setShowModal(true);
  };

  const showSuccessModal = (title, message, onClose = null) => {
    setModalConfig({ type: "success", title, message, onClose });
    setShowModal(true);
  };

  const handleBookService = () => {
    // Navigate to booking page with service details
    // Package selection will be handled on the booking page
    const query = {
      providerId: service.provider.id,
      serviceId: service.id,
      serviceName: service.name,
    };

    // Only include selectedPackage if one is actually selected
    if (selectedPackage) {
      query.selectedPackage = JSON.stringify(selectedPackage);
    }

    router.push({
      pathname: "/booking",
      query: query,
    });
  };

  const handleBackToProvider = () => {
    router.push(`/providers/${service.provider.id}`);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar currentPage="boats" />
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-500"></div>
        </div>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar currentPage="boats" />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {t('serviceNotFound')}
            </h1>
            <p className="text-gray-600 mb-6">
              {t('serviceNotFoundMessage')}
            </p>
            <button
              onClick={() => router.push("/search")}
              className="px-6 py-2 bg-amber-500 text-white rounded-md hover:bg-amber-600 transition-colors"
            >
              {t('backToSearch')}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Use service images from database or fallback to default images
  const galleryImages = service.images && service.images.length > 0 ? service.images.map(getImageUrl) : [
    "https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    "https://images.unsplash.com/photo-1544551763-77ef2d0cfc6c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
  ];

  // Carousel navigation functions
  const nextImage = () => {
    setSelectedImageIndex((prev) => (prev + 1) % galleryImages.length);
  };

  const prevImage = () => {
    setSelectedImageIndex(
      (prev) => (prev - 1 + galleryImages.length) % galleryImages.length
    );
  };

  // Touch/swipe support constants
  const minSwipeDistance = 50;

  const onTouchStart = (e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe) {
      nextImage();
    } else if (isRightSwipe) {
      prevImage();
    }
  };

  const pageName = "Service Details";

  return (
    <>
      <Head>
        <title>{pageName} - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      <div className="min-h-screen bg-gray-50">
        <Navbar currentPage="boats" />

        <div className="max-w-6xl mx-auto px-4 py-4 md:py-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-4 md:mb-6">
            <h1 className="text-xl md:text-2xl font-bold text-gray-900">
              {t('service')} ({service.serviceType?.name || 'Service'})
            </h1>
            <button
              onClick={handleBackToProvider}
              className="md:flex hidden items-center text-gray-600 hover:text-gray-900 mr-4 text-md"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-1" />
              {t('backToProvider')} {service.provider.displayName}
            </button>
          </div>

          {/* Section 1: Full Width - Service Header and Photo Gallery */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-4 sm:mb-6">
            <div className="flex items-start justify-between">
              <div>
                {/* <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  {service.name}
                </h1> */}
                <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-4">
                  {service.serviceType?.name || 'Service'}
                </h2>

                {service.description && (
                  <p className="text-gray-700 leading-relaxed hidden sm:block">
                    {service.description}
                  </p>
                )}
              </div>
              <div className="text-right">
                <div className="flex items-baseline justify-end gap-1 text-amber-600">
                  <span className="text-sm sm:text-base font-medium">{t('fromService')}</span>
                  <span className="text-lg sm:text-2xl font-bold">
                    RM
                    {(() => {
                      const prices = [];
                      if (service.basePrice) prices.push(parseFloat(service.basePrice));
                      if (service.agePricing?.child) prices.push(parseFloat(service.agePricing.child));
                      if (service.agePricing?.adult) prices.push(parseFloat(service.agePricing.adult));
                      const lowestPrice = prices.length > 0 ? Math.min(...prices) : service.basePrice;
                      return lowestPrice.toFixed(0);
                    })()}
                  </span>
                </div>
                <div className="text-sm text-gray-500 mb-4">{t('perPerson')}</div>
                {/* <button
                  onClick={handleBookService}
                  className="px-8 py-3 bg-amber-500 text-white font-semibold rounded-md hover:bg-amber-600 transition-colors"
              >
                Book This Service
              </button> */}
              </div>
            </div>

            {/* Photo Gallery Carousel - No Header */}
            {galleryImages && galleryImages.length > 0 && (
              <div className="relative sm:mt-6">
                {/* Main Image Display */}
                <div className="relative h-96 bg-gray-200 rounded-lg overflow-hidden">
                  <img
                    src={galleryImages[selectedImageIndex]}
                    alt={`${service.name} - Image ${selectedImageIndex + 1}`}
                    className="w-full h-full object-cover"
                    onTouchStart={onTouchStart}
                    onTouchMove={onTouchMove}
                    onTouchEnd={onTouchEnd}
                  />

                  {/* Navigation Arrows */}
                  {galleryImages.length > 1 && (
                    <>
                      <button
                        onClick={prevImage}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                      >
                        <ChevronLeftIcon className="w-6 h-6" />
                      </button>
                      <button
                        onClick={nextImage}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                      >
                        <ChevronRightIcon className="w-6 h-6" />
                      </button>
                    </>
                  )}

                  {/* Image Counter */}
                  <div className="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
                    {selectedImageIndex + 1} / {galleryImages.length}
                  </div>
                </div>

                {/* Thumbnail Navigation */}
                {galleryImages.length > 1 && (
                  <div className="hidden sm:flex gap-2 mt-4 overflow-x-auto pb-2 ">
                    {galleryImages.map((photo, index) => (
                      <button
                        key={index}
                        onClick={() => setSelectedImageIndex(index)}
                        className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors ${selectedImageIndex === index
                          ? "border-amber-500"
                          : "border-gray-200 hover:border-amber-300"
                          }`}
                      >
                        <img
                          src={photo}
                          alt={`Thumbnail ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Section 2: Two-Column Grid Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
            {/* Left Column: Service Details and Itinerary */}
            <div className="space-y-4 sm:space-y-6">
              {/* Service Details */}
              <div className="bg-white rounded-lg shadow-sm p-6 mb-4 md:mb-6">
                <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-4">
                  {t('serviceDetails')}
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    {service.duration && (
                      <div className="flex items-center">
                        <ClockIcon className="w-5 h-5 text-amber-500 mr-3" />
                        <div>
                          <div className="font-medium text-gray-900">
                            {t('duration')}
                          </div>
                          <div className="text-gray-600">
                            {Math.floor(service.duration / 60)}h{" "}
                            {service.duration % 60}m
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="flex items-center">
                      <UsersIcon className="w-5 h-5 text-amber-500 mr-3" />
                      <div>
                        <div className="font-medium text-gray-900">
                          {t('maxCapacity')}
                        </div>
                        <div className="text-gray-600">
                          {service.calculatedCapacity || service.maxCapacity} {t('passengers')}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <BuildingOfficeIcon className="w-5 h-5 mr-3 text-amber-500" />
                      <div>
                        <div className="font-medium text-gray-900">{t('provider')}</div>
                        <div className="text-gray-600">
                          {service.provider.displayName}
                        </div>
                      </div>
                    </div>

                    {/* Destinations */}
                    {service.routes && service.routes.length > 0 && (
                      <div className="flex items-center">
                        <MapPinIcon className="w-5 h-5 text-amber-500 mr-3" />
                        <div>
                          <div className="font-medium text-gray-900">{t('destination')}</div>
                          <div className="text-gray-600">
                            {service.routes.map((route, index) => (
                              <span key={route.id}>
                                {route.destination.name}
                                {index < service.routes.length - 1 && ", "}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Included and Excluded Items */}
              {service.includedItems && service.includedItems.length > 0 && (
                <div className="bg-white rounded-lg shadow-sm p-6 mb-4 md:mb-6">
                  <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    {t('includedExcludedItems')}
                  </h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {/* Included Items - Left Column */}
                    <div className="space-y-2">
                      {service.includedItems.map((item, index) => (
                        <div key={index} className="flex items-center">
                          <CheckIcon className="w-4 h-4 text-emerald-600 font-medium mr-3 flex-shrink-0" />
                          <span className="text-gray-700">{item}</span>
                        </div>
                      ))}
                    </div>

                    {/* Excluded Items - Right Column */}
                    <div className="space-y-2">
                      {service.excludedItems.map((item, index) => (
                        <div key={index} className="flex items-center">
                          <XMarkIcon className="w-4 h-4 text-red-500 font-medium mr-3 flex-shrink-0" />
                          <span className="text-gray-700">{item}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Minimalist Custom Timeline for Itinerary */}
              <div className="bg-white rounded-lg shadow-sm p-5 mb-6">
                <h2 className="text-base sm:text-lg font-medium text-gray-900 mb-5 flex items-center">
                  <ClockIcon className="w-4 h-4 text-amber-500 mr-2" />
                  {t('itinerary')}
                </h2>

                {/* Minimalist Timeline Implementation */}
                <div className="relative pl-4">
                  {service.itinerary && Array.isArray(service.itinerary) && service.itinerary.length > 0 ? (
                    service.itinerary.map(item => ({
                      time: item.time,
                      activity: item.activity,
                      description: item.location ? `Location: ${item.location}` : item.description || '',
                      location: item.location
                    })).map((item, index, array) => (
                      <div key={index} className="relative pb-2 last:pb-0 pr-4"> 
                        {/* Timeline Line that connects to next dot */}
                        {index < array.length - 1 && (
                          <div 
                            className="absolute left-0 w-0.5 bg-amber-300 z-0"
                            style={{ 
                              top: '10px',
                              height: 'calc(100% + 4px)',
                              marginLeft: '5px'
                            }}
                          />
                        )}

                        {/* Timeline Dot - aligned with activity */}
                        <div className="absolute left-0 top-1 z-10">
                          <div className="w-3 h-3 bg-amber-500 rounded-full border border-white shadow-sm"></div>
                        </div>

                        {/* Content Container */}
                        <div className="ml-6">
                          {/* Header layout with activity and time */}
                          <div className="flex items-center justify-between mb-1">
                            <h3 className="font-medium text-gray-900 text-xs sm:text-sm leading-tight pr-3">
                              {item.activity}
                            </h3>
                            <span className="text-xs font-medium text-amber-600 ml-2 flex-shrink-0">
                              {item.time}
                            </span>
                          </div>
                          
                          {/* Description close to activity */}
                          {(item.description || item.location) && (
                            <p className="text-xs text-gray-500 leading-relaxed mb-4">
                              {item.description || item.location}
                            </p>
                          )}
                        </div>
                      </div>
                    ))
                  ) : (
                    /* Improved empty state spacing */
                    <div className="flex flex-col items-center justify-center py-10 px-6 rounded-lg bg-gray-50">
                      <div className="text-center">
                        <div className="w-12 h-12 mx-auto mb-4 bg-amber-100 rounded-full flex items-center justify-center">
                          <NotepadText className="w-6 h-6 text-amber-600" />
                        </div>
                        <h3 className="text-sm font-medium text-gray-900 mb-2">
                          {t('noItineraryAvailable')}
                        </h3>
                        <p className="text-xs text-gray-500">
                          {t('noItineraryMessage')}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Right Column: Pricing Tables */}
            <div className="space-y-4 sm:space-y-6">
              {/* Dynamic Pricing Section */}
              <DynamicPricingSection
                service={service}
                goToBookingPage={handleBookService}
              />

              {/* Special Instructions */}
              {service.specialInstruction && (
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-6 mb-4 md:mb-6">
                  <h2 className="text-lg sm:text-xl font-semibold text-amber-800 mb-4 flex items-center">
                    {t('specialInstructions')}
                  </h2>
                  <p className="text-amber-700 leading-relaxed">{service.specialInstruction}</p>
                </div>
              )}
            </div>
          </div>
        </div>
        <Footer />

        {/* Modal */}
        <StandardModal
          isOpen={showModal}
          type={modalConfig.type}
          title={modalConfig.title}
          message={modalConfig.message}
          details={modalConfig.details}
          onClose={() => {
            setShowModal(false);
            if (modalConfig.onClose) modalConfig.onClose();
          }}
        />
      </div>
    </>
  );
};

export default ServiceDetailPage;
