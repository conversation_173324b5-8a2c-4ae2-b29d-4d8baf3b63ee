import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Navbar from '../../components/Navbar';
import Footer from '../../components/Footer';
import StandardModal from '../../components/StandardModal';
import {
  UsersIcon,
  BuildingOfficeIcon,
  MagnifyingGlassIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  CheckIcon,
  XMarkIcon,
  ExclamationTriangleIcon,
  UserCircleIcon
} from '@heroicons/react/24/outline';
import { LayoutDashboard, Ship, Receipt, UserCheck, ShipWheel } from 'lucide-react'

/**
 * Admin Management Dashboard for GoSea Platform
 * Provides CRUD operations for Users, Providers, and Boats
 */

const AdminDashboard = () => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading } = useAuth();
  const { t } = useLanguage();

  // State management
  const [activeTab, setActiveTab] = useState('dashboard');
  const [dashboardStats, setDashboardStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Entity data states
  const [users, setUsers] = useState([]);
  const [providers, setProviders] = useState([]);
  const [boats, setBoats] = useState([]);
  const [pendingBoatOwners, setPendingBoatOwners] = useState([]);

  // Pagination and filtering
  const [pagination, setPagination] = useState({
    users: { page: 1, limit: 20, total: 0, pages: 0 },
    providers: { page: 1, limit: 20, total: 0, pages: 0 },
    boats: { page: 1, limit: 20, total: 0, pages: 0 },
    boatOwners: { page: 1, limit: 20, total: 0, pages: 0 }
  });

  const [filters, setFilters] = useState({
    users: { role: '', isActive: '', search: '' },
    providers: { isVerified: '', isActive: '', search: '' },
    boats: { status: '', isActive: '', search: '' },
    boatOwners: { search: '' }
  });

  // Authentication and authorization check
  useEffect(() => {
    if (!isLoading && (!isAuthenticated || user?.role !== 'ADMIN')) {
      router.push('/dashboard');
    }
  }, [isLoading, isAuthenticated, user, router]);

  // Load dashboard stats on mount
  useEffect(() => {
    if (isAuthenticated && user?.role === 'ADMIN') {
      loadDashboardStats();
    }
  }, [isAuthenticated, user]);

  // Load entity data when tab changes
  useEffect(() => {
    if (isAuthenticated && user?.role === 'ADMIN') {
      switch (activeTab) {
        case 'users':
          loadUsers();
          break;
        case 'providers':
          loadProviders();
          break;
        case 'boats':
          loadBoats();
          break;
        case 'boatOwners':
          loadPendingBoatOwners();
          break;
      }
    }
  }, [activeTab, isAuthenticated, user, pagination[activeTab]?.page, filters[activeTab]]);

  // API call helper
  const apiCall = async (endpoint, options = {}) => {
    // Get token from the correct localStorage location
    let token = null;
    try {
      const authData = localStorage.getItem('gosea_auth');
      if (authData) {
        const parsedAuth = JSON.parse(authData);
        token = parsedAuth.accessToken;
      }
    } catch (error) {
      console.error('Error getting auth token:', error);
      throw new Error('Authentication required');
    }
    
    if (!token) {
      throw new Error('No authentication token found');
    }
    
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/admin${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers
      },
      ...options
    });

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || 'API call failed');
    }
    
    return data;
  };

  // Load dashboard statistics
  const loadDashboardStats = async () => {
    try {
      setLoading(true);
      const data = await apiCall('/dashboard/stats');
      setDashboardStats(data.data);
    } catch (error) {
      console.error('Failed to load dashboard stats:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Load users data
  const loadUsers = async () => {
    try {
      const queryParams = new URLSearchParams({
        page: pagination.users.page,
        limit: pagination.users.limit,
        ...Object.fromEntries(Object.entries(filters.users).filter(([_, value]) => value))
      });

      const data = await apiCall(`/users?${queryParams}`);
      setUsers(data.data.users);
      setPagination(prev => ({
        ...prev,
        users: data.data.pagination
      }));
    } catch (error) {
      console.error('Failed to load users:', error);
      setError(error.message);
    }
  };

  // Load providers data
  const loadProviders = async () => {
    try {
      const queryParams = new URLSearchParams({
        page: pagination.providers.page,
        limit: pagination.providers.limit,
        ...Object.fromEntries(Object.entries(filters.providers).filter(([_, value]) => value))
      });

      const data = await apiCall(`/providers?${queryParams}`);
      setProviders(data.data.providers);
      setPagination(prev => ({
        ...prev,
        providers: data.data.pagination
      }));
    } catch (error) {
      console.error('Failed to load providers:', error);
      setError(error.message);
    }
  };

  // Load boats data
  const loadBoats = async () => {
    try {
      const queryParams = new URLSearchParams({
        page: pagination.boats.page,
        limit: pagination.boats.limit,
        ...Object.fromEntries(Object.entries(filters.boats).filter(([_, value]) => value))
      });

      const data = await apiCall(`/boats?${queryParams}`);
      setBoats(data.data.boats);
      setPagination(prev => ({
        ...prev,
        boats: data.data.pagination
      }));
    } catch (error) {
      console.error('Failed to load boats:', error);
      setError(error.message);
    }
  };

  // Load pending boat owners data
  const loadPendingBoatOwners = async () => {
    try {
      const queryParams = new URLSearchParams({
        page: pagination.boatOwners.page,
        limit: pagination.boatOwners.limit,
        ...Object.fromEntries(Object.entries(filters.boatOwners).filter(([_, value]) => value))
      });

      const data = await apiCall(`/boat-owners/pending?${queryParams}`);
      setPendingBoatOwners(data.data.pendingBoatOwners || []);
      setPagination(prev => ({
        ...prev,
        boatOwners: data.data.pagination || { page: 1, limit: 20, total: 0, pages: 0 }
      }));
    } catch (error) {
      console.error('Failed to load pending boat owners:', error);
      setError(error.message);
      setPendingBoatOwners([]);
    }
  };

  // Loading states for buttons
  const [userStatusLoading, setUserStatusLoading] = useState({});
  const [providerVerificationLoading, setProviderVerificationLoading] = useState({});
  const [boatOwnerApprovalLoading, setBoatOwnerApprovalLoading] = useState({});

  // Approve boat owner
  const approveBoatOwner = async (userId) => {
    try {
      setBoatOwnerApprovalLoading(prev => ({ ...prev, [userId]: true }));
      await apiCall(`/boat-owners/${userId}/approve`, {
        method: 'POST'
      });
      loadPendingBoatOwners(); // Reload pending boat owners
      showSuccessModal(
        t('success'),
        t('boatOwnerApprovedSuccessfully'),
        null
      );
    } catch (error) {
      console.error('Failed to approve boat owner:', error);
      showErrorModal(
        t('error'),
        t('failedToApproveBoatOwner') + ': ' + error.message,
        null
      );
    } finally {
      setBoatOwnerApprovalLoading(prev => ({ ...prev, [userId]: false }));
    }
  };

  // Reject boat owner
  const rejectBoatOwner = async (userId, reason) => {
    try {
      setBoatOwnerApprovalLoading(prev => ({ ...prev, [userId]: true }));
      await apiCall(`/boat-owners/${userId}/reject`, {
        method: 'POST',
        body: JSON.stringify({ rejectionReason: reason })
      });
      loadPendingBoatOwners(); // Reload pending boat owners
      showSuccessModal(
        t('success'),
        t('boatOwnerRejectedSuccessfully'),
        null
      );
    } catch (error) {
      console.error('Failed to reject boat owner:', error);
      showErrorModal(
        t('error'),
        t('failedToRejectBoatOwner') + ': ' + error.message,
        null
      );
    } finally {
      setBoatOwnerApprovalLoading(prev => ({ ...prev, [userId]: false }));
    }
  };

  // Modal states
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackModal, setFeedbackModal] = useState({
    type: "success",
    title: "",
    message: "",
    details: null,
  });

  // Rejection modal state
  const [showRejectionModal, setShowRejectionModal] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [currentRejectUserId, setCurrentRejectUserId] = useState(null);

  // Helper functions for showing modals
  const showSuccessModal = (title, message, details = null) => {
    setFeedbackModal({
      type: "success",
      title,
      message,
      details,
    });
    setShowFeedbackModal(true);
  };

  const showErrorModal = (title, message, details = null) => {
    setFeedbackModal({
      type: "error",
      title,
      message,
      details,
    });
    setShowFeedbackModal(true);
  };

  const closeFeedbackModal = () => {
    setShowFeedbackModal(false);
  };

  // Rejection modal functions
  const openRejectionModal = (userId) => {
    setCurrentRejectUserId(userId);
    setRejectionReason('');
    setShowRejectionModal(true);
  };

  const closeRejectionModal = () => {
    setShowRejectionModal(false);
    setCurrentRejectUserId(null);
    setRejectionReason('');
  };

  const handleRejectionSubmit = () => {
    if (rejectionReason.trim()) {
      rejectBoatOwner(currentRejectUserId, rejectionReason.trim());
      closeRejectionModal();
    }
  };

  // Handle filter changes
  const handleFilterChange = (entity, field, value) => {
    setFilters(prev => ({
      ...prev,
      [entity]: { ...prev[entity], [field]: value }
    }));
    // Reset to first page when filtering
    setPagination(prev => ({
      ...prev,
      [entity]: { ...prev[entity], page: 1 }
    }));
  };

  // Handle pagination
  const handlePageChange = (entity, newPage) => {
    setPagination(prev => ({
      ...prev,
      [entity]: { ...prev[entity], page: newPage }
    }));
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Status badge component
  const StatusBadge = ({ status, type = 'default' }) => {
    const getStatusStyle = () => {
      switch (type) {
        case 'user':
          return status 
            ? { backgroundColor: '#d1fae5', color: '#047857' }
            : { backgroundColor: '#fee2e2', color: '#b91c1c' };
        case 'provider':
          return status 
            ? { backgroundColor: '#d1fae5', color: '#047857' }
            : { backgroundColor: '#fee2e2', color: '#b91c1c' };
        case 'boat':
          switch (status) {
            case 'APPROVED': return { backgroundColor: 'var(--success)', color: 'var(--success-content)' };
            case 'PENDING_APPROVAL': return { backgroundColor: 'var(--warning)', color: 'var(--warning-content)' };
            case 'REJECTED': return { backgroundColor: 'var(--error)', color: 'var(--error-content)' };
            case 'DRAFT': return { backgroundColor: 'var(--neutral)', color: 'var(--neutral-content)' };
            case 'INACTIVE': return { backgroundColor: 'var(--error)', color: 'var(--error-content)' };
            case 'MAINTENANCE': return { backgroundColor: 'var(--secondary)', color: 'var(--secondary-content)' };
            default: return { backgroundColor: 'var(--neutral)', color: 'var(--neutral-content)' };
          }
        default:
          return { backgroundColor: 'var(--neutral)', color: 'var(--neutral-content)' };
      }
    };

    return (
      <span 
        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" 
        style={getStatusStyle()}
      >
        {typeof status === 'boolean' ? (status ? 'Active' : 'Inactive') : status}
      </span>
    );
  };

  // Pagination component
  const Pagination = ({ entity }) => {
    const pag = pagination[entity];
    
    return (
      <div 
        className="px-4 py-3 flex items-center justify-between border-t sm:px-6" 
        style={{ 
          backgroundColor: 'var(--base-100)', 
          borderColor: 'var(--base-300)' 
        }}
      >
        <div className="flex-1 flex justify-between sm:hidden">
            <button
            onClick={() => handlePageChange(entity, pag.page - 1)}
            disabled={pag.page === 1}
            className="relative inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
            style={{
              borderColor: 'var(--base-300)',
              backgroundColor: 'var(--base-100)',
              color: 'var(--base-content)'
            }}
            onMouseEnter={(e) => !e.target.disabled && (e.target.style.backgroundColor = 'var(--base-200)')}
            onMouseLeave={(e) => !e.target.disabled && (e.target.style.backgroundColor = 'var(--base-100)')}
          >
            {t('previous')}
          </button>
          <button
            onClick={() => handlePageChange(entity, pag.page + 1)}
            disabled={pag.page === pag.pages}
            className="ml-3 relative inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
            style={{
              borderColor: 'var(--base-300)',
              backgroundColor: 'var(--base-100)',
              color: 'var(--base-content)'
            }}
            onMouseEnter={(e) => !e.target.disabled && (e.target.style.backgroundColor = 'var(--base-200)')}
            onMouseLeave={(e) => !e.target.disabled && (e.target.style.backgroundColor = 'var(--base-100)')}
          >
            {t('next')}
          </button>
        </div>
        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p className="text-sm" style={{ color: 'var(--base-content)' }}>
              {t('showing')} <span className="font-medium">{(pag.page - 1) * pag.limit + 1}</span> {t('to')}{' '}
              <span className="font-medium">{Math.min(pag.page * pag.limit, pag.total)}</span> {t('of')}{' '}
              <span className="font-medium">{pag.total}</span> {t('results')}
            </p>
          </div>
          <div>
            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
              <button
                onClick={() => handlePageChange(entity, pag.page - 1)}
                disabled={pag.page === 1}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                style={{
                  borderColor: 'var(--base-300)',
                  backgroundColor: 'var(--base-100)',
                  color: 'var(--neutral)'
                }}
                onMouseEnter={(e) => !e.target.disabled && (e.target.style.backgroundColor = 'var(--base-200)')}
                onMouseLeave={(e) => !e.target.disabled && (e.target.style.backgroundColor = 'var(--base-100)')}
              >
                <ChevronLeftIcon className="h-5 w-5" />
              </button>
              
              {/* Page numbers */}
              {Array.from({ length: Math.min(5, pag.pages) }, (_, i) => {
                const pageNum = i + 1;
                const isActive = pageNum === pag.page;
                return (
                  <button
                    key={pageNum}
                    onClick={() => handlePageChange(entity, pageNum)}
                    className="relative inline-flex items-center px-4 py-2 border text-sm font-medium"
                    style={{
                      zIndex: isActive ? 10 : 'auto',
                      backgroundColor: isActive ? 'var(--secondary)' : 'var(--base-100)',
                      borderColor: isActive ? 'var(--secondary)' : 'var(--base-300)',
                      color: isActive ? 'var(--secondary-content)' : 'var(--neutral)'
                    }}
                    onMouseEnter={(e) => !isActive && (e.target.style.backgroundColor = 'var(--base-200)')}
                    onMouseLeave={(e) => !isActive && (e.target.style.backgroundColor = 'var(--base-100)')}
                  >
                    {pageNum}
                  </button>
                );
              })}
              
              <button
                onClick={() => handlePageChange(entity, pag.page + 1)}
                disabled={pag.page === pag.pages}
                className="relative inline-flex items-center px-2 py-2 rounded-r-md border text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                style={{
                  borderColor: 'var(--base-300)',
                  backgroundColor: 'var(--base-100)',
                  color: 'var(--neutral)'
                }}
                onMouseEnter={(e) => !e.target.disabled && (e.target.style.backgroundColor = 'var(--base-200)')}
                onMouseLeave={(e) => !e.target.disabled && (e.target.style.backgroundColor = 'var(--base-100)')}
              >
                <ChevronRightIcon className="h-5 w-5" />
              </button>
            </nav>
          </div>
        </div>
      </div>
    );
  };

  // Show loading state
  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: '#f8fafc' }}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4" style={{ borderColor: '#0ea5e9' }}></div>
          <p style={{ color: '#64748b' }}>{t('loadingAdminDashboard')}</p>
        </div>
      </div>
    );
  }

  // Don't render if not authenticated or not admin
  if (!isAuthenticated || user?.role !== 'ADMIN') {
    return null;
  }

  // Update user status
  const updateUserStatus = async (userId, isActive) => {
    try {
      setUserStatusLoading(prev => ({ ...prev, [userId]: true }));
      const response = await apiCall(`/users/${userId}/status`, {
        method: 'PATCH',
        body: JSON.stringify({ isActive })
      });

      loadUsers(); // Reload users data
      showSuccessModal(
        t('success'),
        `${t('user')} ${isActive ? t('activated') : t('deactivated')} ${t('successfully')}`,
        null
      );
    } catch (error) {
      console.error('Failed to update user status:', error);
      showErrorModal(
        t('error'),
        `${t('failedToUpdate')} ${t('userStatus')}: ${error.message}`,
        null
      );
    } finally {
      setUserStatusLoading(prev => ({ ...prev, [userId]: false }));
    }
  };

  // Update user role
  const updateUserRole = async (userId, role) => {
    try {
      setUserStatusLoading(prev => ({ ...prev, [userId]: true }));
      const response = await apiCall(`/users/${userId}/role`, {
        method: 'PATCH',
        body: JSON.stringify({ role })
      });

      loadUsers(); // Reload users data
      showSuccessModal(
        t('success'),
        `${t('userRole')} ${t('updated')} ${t('successfully')}`,
        null
      );
    } catch (error) {
      console.error('Failed to update user role:', error);
      showErrorModal(
        t('error'),
        `${t('failedToUpdate')} ${t('userRole')}: ${error.message}`,
        null
      );
    } finally {
      setUserStatusLoading(prev => ({ ...prev, [userId]: false }));
    }
  };

  // Update provider verification
  const updateProviderVerification = async (providerId, isVerified) => {
    try {
      setProviderVerificationLoading(prev => ({ ...prev, [providerId]: true }));
      const response = await apiCall(`/providers/${providerId}/verify`, {
        method: 'PATCH',
        body: JSON.stringify({ isVerified })
      });

      loadProviders(); // Reload providers data
      showSuccessModal(
        t('success'),
        `${t('provider')} ${isVerified ? t('verified') : t('unverified')} ${t('successfully')}`,
        null
      );
    } catch (error) {
      console.error('Failed to update provider verification:', error);
      showErrorModal(
        t('error'),
        `${t('failedToUpdate')} ${t('providerVerification')}: ${error.message}`,
        null
      );
    } finally {
      setProviderVerificationLoading(prev => ({ ...prev, [providerId]: false }));
    }
  };

  // Update boat status
  const updateBoatStatus = async (boatId, status) => {
    try {
      const response = await apiCall(`/boats/${boatId}/status`, {
        method: 'PATCH',
        body: JSON.stringify({ status })
      });

      loadBoats(); // Reload boats data
      showSuccessModal(
        t('success'),
        `${t('boatStatus')} ${t('updated')} ${t('successfully')}`,
        null
      );
    } catch (error) {
      console.error('Failed to update boat status:', error);
      showErrorModal(
        t('error'),
        `${t('failedToUpdate')} ${t('boatStatus')}: ${error.message}`,
        null
      );
    }
  };

  // Show loading state
  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: '#f8fafc' }}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4" style={{ borderColor: '#0ea5e9' }}></div>
          <p style={{ color: '#64748b' }}>{t('loadingAdminDashboard')}</p>
        </div>
      </div>
    );
  }

  // Don't render if not authenticated or not admin
  if (!isAuthenticated || user?.role !== 'ADMIN') {
    return null;
  }

  const pageName = "Admin Dashboard";

  return (
    <>
      <Head>
        <title>{pageName} - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen bg-slate-50">
        <Navbar currentPage="admin" />

        {/* Main Content */}
        <main className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          <div className="px-4 py-4 sm:py-8 sm:px-0">
            {/* Page Header */}
            <div className="mb-4 md:mb-6">
                <h1 className="text-xl md:text-2xl font-bold" style={{ color: "#0f172a" }}>
                    {t('adminDashboard')}
                </h1>
                <p className="text-sm sm:text-lg" style={{ color: "#64748b" }}>
                {t('manageUsersProvidersBoats')}
              </p>
            </div>

            {/* Error Display */}
            {error && (
              <div 
                className="mb-6 border px-4 py-3 rounded" 
                style={{ 
                  backgroundColor: '#fef2f2', 
                  borderColor: '#fca5a5', 
                  color: 'var(--error)' 
                }}
              >
                <div className="flex">
                  <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
                  <span>{error}</span>
                </div>
              </div>
            )}

            {/* Navigation Tabs */}
            <div className="mb-4 sm:mb-8">
              <div className="border-b" style={{ borderColor: 'var(--neutral-light)' }}>
                <nav className="-mb-px flex space-x-4 sm:space-x-8 overflow-x-auto scrollbar-hide">
                  {[
                    { id: 'dashboard', name: t('overview'), icon: LayoutDashboard },
                    { id: 'users', name: t('users'), icon: UsersIcon },
                    { id: 'providers', name: t('providers'), icon: BuildingOfficeIcon },
                    { id: 'boats', name: t('boats'), icon: Ship },
                    { id: 'boatOwners', name: t('boatOwnerApprovals'), icon: UserCheck }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex items-center py-4 px-2 sm:px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-all duration-200 ${
                        activeTab === tab.id
                          ? 'text-amber-500 border-amber-500'
                          : 'text-gray-500 border-transparent hover:text-amber-500 hover:border-amber-500'
                      }`}
                    >
                      <tab.icon className="h-5 w-5 mr-2" />
                      <span className="hidden sm:inline">{tab.name}</span>
                      <span className="sm:hidden">{tab.name.split(' ')[0]}</span>
                    </button>
                  ))}
                </nav>
              </div>
            </div>

            {/* Tab Content */}
            {activeTab === 'dashboard' && dashboardStats && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Users Stats */}
                <div className="rounded-lg shadow p-6" style={{ backgroundColor: 'var(--base-100)' }}>
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <UsersIcon className="h-8 w-8" style={{ color: 'var(--primary)' }} />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium truncate" style={{ color: 'var(--neutral)' }}>{t('totalUsers')}</dt>
                        <dd className="text-lg font-medium" style={{ color: 'var(--base-content)' }}>{dashboardStats.users.total}</dd>
                        <dd className="text-sm" style={{ color: 'var(--neutral)' }}>
                          {dashboardStats.users.active} {t('active')}, {dashboardStats.users.inactive} {t('inactive')}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>

                {/* Providers Stats */}
                <div className="rounded-lg shadow p-6" style={{ backgroundColor: 'var(--base-100)' }}>
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <BuildingOfficeIcon className="h-8 w-8" style={{ color: 'var(--success)' }} />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium truncate" style={{ color: 'var(--neutral)' }}>{t('totalProviders')}</dt>
                        <dd className="text-lg font-medium" style={{ color: 'var(--base-content)' }}>{dashboardStats.providers.total}</dd>
                        <dd className="text-sm" style={{ color: 'var(--neutral)' }}>
                          {dashboardStats.providers.verified} {t('verified')}, {dashboardStats.providers.unverified} {t('pending')}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>

                {/* Boats Stats */}
                <div className="rounded-lg shadow p-6" style={{ backgroundColor: 'var(--base-100)' }}>
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Ship className="h-8 w-8" style={{ color: 'var(--accent)' }} />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium truncate" style={{ color: 'var(--neutral)' }}>{t('totalBoats')}</dt>
                        <dd className="text-lg font-medium" style={{ color: 'var(--base-content)' }}>{dashboardStats.boats.total}</dd>
                        <dd className="text-sm" style={{ color: 'var(--neutral)' }}>
                          {dashboardStats.boats.approved} {t('approved')}, {dashboardStats.boats.pending} {t('pending')}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>

                {/* Bookings Stats */}
                <div className="rounded-lg shadow p-6" style={{ backgroundColor: 'var(--base-100)' }}>
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Receipt className="h-8 w-8" style={{ color: 'var(--secondary)' }} />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium truncate" style={{ color: 'var(--neutral)' }}>{t('totalBookings')}</dt>
                        <dd className="text-lg font-medium" style={{ color: 'var(--base-content)' }}>{dashboardStats.bookings.total}</dd>
                        <dd className="text-sm" style={{ color: 'var(--neutral)' }}>
                          {dashboardStats.bookings.recent} {t('inLast7Days')}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Users Management */}
            {activeTab === 'users' && (
              <div className="shadow overflow-hidden sm:rounded-md" style={{ backgroundColor: 'var(--base-100)' }}>
                {/* Filters */}
                <div className="px-4 py-5 sm:p-6 border-b" style={{ borderColor: 'var(--base-300)' }}>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium" style={{ color: 'var(--base-content)' }}>{t('role')}</label>
                      <select
                        value={filters.users.role}
                        onChange={(e) => handleFilterChange('users', 'role', e.target.value)}
                        className="mt-1 block w-full rounded-md shadow-sm focus:outline-none focus:ring-2 sm:text-sm"
                        style={{
                          borderColor: 'var(--base-300)',
                          backgroundColor: 'var(--base-100)',
                          color: 'var(--base-content)'
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = 'var(--primary)';
                          e.target.style.boxShadow = `0 0 0 1px var(--primary)`;
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = 'var(--base-300)';
                          e.target.style.boxShadow = 'none';
                        }}
                      >
                        <option value="">{t('allRoles')}</option>
                        <option value="CUSTOMER">{t('customer')}</option>
                        <option value="BOAT_OWNER">{t('boatOwner')}</option>
                        <option value="AFFILIATE_AGENT">{t('affiliateAgent')}</option>
                        <option value="ADMIN">{t('admin')}</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium" style={{ color: 'var(--base-content)' }}>{t('status')}</label>
                      <select
                        value={filters.users.isActive}
                        onChange={(e) => handleFilterChange('users', 'isActive', e.target.value)}
                        className="mt-1 block w-full rounded-md shadow-sm focus:outline-none focus:ring-2 sm:text-sm"
                        style={{
                          borderColor: 'var(--base-300)',
                          backgroundColor: 'var(--base-100)',
                          color: 'var(--base-content)'
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = 'var(--primary)';
                          e.target.style.boxShadow = `0 0 0 1px var(--primary)`;
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = 'var(--base-300)';
                          e.target.style.boxShadow = 'none';
                        }}
                      >
                        <option value="">{t('allStatus')}</option>
                        <option value="true">{t('active')}</option>
                        <option value="false">{t('inactive')}</option>
                      </select>
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium" style={{ color: 'var(--base-content)' }}>{t('search')}</label>
                      <div className="mt-1 relative rounded-md shadow-sm">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <MagnifyingGlassIcon className="h-5 w-5" style={{ color: 'var(--neutral)' }} />
                        </div>
                        <input
                          type="text"
                          value={filters.users.search}
                          onChange={(e) => handleFilterChange('users', 'search', e.target.value)}
                          className="block w-full pl-10 sm:text-sm rounded-md focus:outline-none focus:ring-2"
                          style={{
                            borderColor: 'var(--base-300)',
                            backgroundColor: 'var(--base-100)',
                            color: 'var(--base-content)'
                          }}
                          onFocus={(e) => {
                            e.target.style.borderColor = 'var(--primary)';
                            e.target.style.boxShadow = `0 0 0 1px var(--primary)`;
                          }}
                          onBlur={(e) => {
                            e.target.style.borderColor = 'var(--base-300)';
                            e.target.style.boxShadow = 'none';
                          }}
                          placeholder={t('searchByEmail')}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Users Table */}
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y" style={{ backgroundColor: 'var(--base-100)', borderColor: 'var(--base-300)' }}>
                    <thead class="bg-slate-50">
                      <tr>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('user')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('role')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('status')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('verified')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('joined')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('actions')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y" style={{ backgroundColor: 'var(--base-100)', borderColor: 'var(--base-300)' }}>
                      {users.map((user) => (
                        <tr key={user.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                <div className="h-10 w-10 rounded-full flex items-center justify-center bg-sky-50">
                                  <span className="text-sm font-medium" style={{ color: 'var(--base-content)' }}>
                                    {user.email.charAt(0).toUpperCase()}
                                  </span>
                                </div>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium" style={{ color: 'var(--base-content)' }}>
                                  {user.profile?.firstName && user.profile?.lastName
                                    ? `${user.profile.firstName} ${user.profile.lastName}`
                                    : user.email}
                                </div>
                                <div className="text-sm" style={{ color: 'var(--neutral)' }}>{user.email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <select
                              value={user.role}
                              onChange={(e) => updateUserRole(user.id, e.target.value)}
                              className="text-xs font-medium rounded border focus:outline-none focus:ring-2"
                              style={{
                                borderColor: 'var(--base-300)',
                                backgroundColor: 'var(--base-100)',
                                color: 'var(--base-content)',
                                padding: '0.25rem 0.5rem'
                              }}
                              onFocus={(e) => {
                                e.target.style.borderColor = 'var(--primary)';
                                e.target.style.boxShadow = `0 0 0 1px var(--primary)`;
                              }}
                              onBlur={(e) => {
                                e.target.style.borderColor = 'var(--base-300)';
                                e.target.style.boxShadow = 'none';
                              }}
                            >
                              <option value="CUSTOMER">{t('customer')}</option>
                              <option value="BOAT_OWNER">{t('boatOwner')}</option>
                              <option value="AFFILIATE_AGENT">{t('affiliateAgent')}</option>
                              <option value="ADMIN">{t('admin')}</option>
                            </select>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <StatusBadge status={user.isActive} type="user" />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <StatusBadge status={user.emailVerified} type="user" />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm" style={{ color: 'var(--neutral)' }}>
                            {formatDate(user.createdAt)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button
                              onClick={() => updateUserStatus(user.id, !user.isActive)}
                              disabled={userStatusLoading[user.id]}
                              className={`inline-flex items-center px-2.5 py-1.5 border text-xs font-medium rounded disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ${
                                user.isActive
                                  ? 'border-red-500 text-red-600 hover:bg-red-500 hover:text-white hover:border-red-500'
                                  : 'border-emerald-500 text-emerald-600 hover:bg-emerald-500 hover:text-white hover:border-emerald-500'
                              }`}
                            >
                              {userStatusLoading[user.id] ? (
                                <div className="animate-spin rounded-full h-3 w-3 border-b-2 mr-1" style={{ borderColor: user.isActive ? '#b91c1c' : '#047857' }}></div>
                              ) : user.isActive ? (
                                <XMarkIcon className="h-4 w-4 mr-1" />
                              ) : (
                                <CheckIcon className="h-4 w-4 mr-1" />
                              )}
                              {user.isActive ? t('deactivate') : t('activate')}
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                <Pagination entity="users" />
              </div>
            )}

            {/* Similar structure for Providers and Boats tabs would follow here */}
            {/* This is a comprehensive foundation - the remaining tabs would be implemented similarly */}

            {/* Providers Management */}
            {activeTab === 'providers' && (
              <div className="shadow overflow-hidden sm:rounded-md" style={{ backgroundColor: 'var(--base-100)' }}>
                {/* Filters */}
                <div className="px-4 py-5 sm:p-6 border-b" style={{ borderColor: 'var(--base-300)' }}>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium" style={{ color: 'var(--base-content)' }}>{t('verification')}</label>
                      <select
                        value={filters.providers.isVerified}
                        onChange={(e) => handleFilterChange('providers', 'isVerified', e.target.value)}
                        className="mt-1 block w-full rounded-md shadow-sm focus:outline-none focus:ring-2 sm:text-sm"
                        style={{
                          borderColor: 'var(--base-300)',
                          backgroundColor: 'var(--base-100)',
                          color: 'var(--base-content)'
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = 'var(--primary)';
                          e.target.style.boxShadow = `0 0 0 1px var(--primary)`;
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = 'var(--base-300)';
                          e.target.style.boxShadow = 'none';
                        }}
                      >
                        <option value="">{t('allVerificationStatus')}</option>
                        <option value="true">{t('verified')}</option>
                        <option value="false">{t('unverified')}</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium" style={{ color: 'var(--base-content)' }}>Status</label>
                      <select
                        value={filters.providers.isActive}
                        onChange={(e) => handleFilterChange('providers', 'isActive', e.target.value)}
                        className="mt-1 block w-full rounded-md shadow-sm focus:outline-none focus:ring-2 sm:text-sm"
                        style={{
                          borderColor: 'var(--base-300)',
                          backgroundColor: 'var(--base-100)',
                          color: 'var(--base-content)'
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = 'var(--primary)';
                          e.target.style.boxShadow = `0 0 0 1px var(--primary)`;
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = 'var(--base-300)';
                          e.target.style.boxShadow = 'none';
                        }}
                      >
                        <option value="">{t('allStatus')}</option>
                        <option value="true">{t('active')}</option>
                        <option value="false">{t('inactive')}</option>
                      </select>
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium" style={{ color: 'var(--base-content)' }}>Search</label>
                      <div className="mt-1 relative rounded-md shadow-sm">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <MagnifyingGlassIcon className="h-5 w-5" style={{ color: 'var(--neutral)' }} />
                        </div>
                        <input
                          type="text"
                          value={filters.providers.search}
                          onChange={(e) => handleFilterChange('providers', 'search', e.target.value)}
                          className="block w-full pl-10 sm:text-sm rounded-md focus:outline-none focus:ring-2"
                          style={{
                            borderColor: 'var(--base-300)',
                            backgroundColor: 'var(--base-100)',
                            color: 'var(--base-content)'
                          }}
                          onFocus={(e) => {
                            e.target.style.borderColor = 'var(--primary)';
                            e.target.style.boxShadow = `0 0 0 1px var(--primary)`;
                          }}
                          onBlur={(e) => {
                            e.target.style.borderColor = 'var(--base-300)';
                            e.target.style.boxShadow = 'none';
                          }}
                          placeholder={t('searchByCompany')}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Providers Table */}
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y" style={{ backgroundColor: 'var(--base-100)', borderColor: 'var(--base-300)' }}>
                    <thead className="bg-slate-50">
                      <tr>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('provider')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('owner')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('verification')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('status')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('services')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('created')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('actions')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y" style={{ backgroundColor: 'var(--base-100)', borderColor: 'var(--base-300)' }}>
                      {providers.map((provider) => (
                        <tr key={provider.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center justify-left">
                              <div className="flex-shrink-0 h-10 w-10">
                                <div className="h-10 w-10 rounded-full flex items-center justify-center bg-sky-50">
                                  <BuildingOfficeIcon className="h-6 w-6" style={{ color: 'var(--neutral)' }} />
                                </div>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium" style={{ color: 'var(--base-content)' }}>
                                  {provider.companyName}
                                </div>
                                <div className="text-sm" style={{ color: 'var(--neutral)' }}>{provider.displayName}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm" style={{ color: 'var(--base-content)' }}>
                              {provider.user.profile?.firstName && provider.user.profile?.lastName
                                ? `${provider.user.profile.firstName} ${provider.user.profile.lastName}`
                                : provider.user.email}
                            </div>
                            <div className="text-sm" style={{ color: 'var(--neutral)' }}>{provider.user.email}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <StatusBadge status={provider.isVerified} type="provider" />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <StatusBadge status={provider.isActive} type="user" />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm" style={{ color: 'var(--neutral)' }}>
                            {provider._count.services} {t('servicesCount')}, {provider._count.boats} {t('boatsCount')}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm" style={{ color: 'var(--neutral)' }}>
                            {formatDate(provider.createdAt)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button
                              onClick={() => updateProviderVerification(provider.id, !provider.isVerified)}
                              disabled={providerVerificationLoading[provider.id]}
                              className={`inline-flex items-center px-2.5 py-1.5 border text-xs font-medium rounded disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ${
                                provider.isVerified
                                  ? 'border-red-500 text-red-600 hover:bg-red-500 hover:text-white hover:border-red-500'
                                  : 'border-emerald-500 text-emerald-600 hover:bg-emerald-500 hover:text-white hover:border-emerald-500'
                              }`}
                            >
                              {providerVerificationLoading[provider.id] ? (
                                <div className="animate-spin rounded-full h-3 w-3 border-b-2 mr-1" style={{ borderColor: provider.isVerified ? '#b91c1c' : '#047857' }}></div>
                              ) : provider.isVerified ? (
                                <XMarkIcon className="h-4 w-4 mr-1" />
                              ) : (
                                <CheckIcon className="h-4 w-4 mr-1" />
                              )}
                              {provider.isVerified ? t('unverify') : t('verify')}
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                <Pagination entity="providers" />
              </div>
            )}

            {/* Boats Management */}
            {activeTab === 'boats' && (
              <div className="shadow overflow-hidden sm:rounded-md" style={{ backgroundColor: 'var(--base-100)' }}>
                {/* Filters */}
                <div className="px-4 py-5 sm:p-6 border-b" style={{ borderColor: 'var(--base-300)' }}>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium" style={{ color: 'var(--base-content)' }}>Status</label>
                      <select
                        value={filters.boats.status}
                        onChange={(e) => handleFilterChange('boats', 'status', e.target.value)}
                        className="mt-1 block w-full rounded-md shadow-sm focus:outline-none focus:ring-2 sm:text-sm"
                        style={{
                          borderColor: 'var(--base-300)',
                          backgroundColor: 'var(--base-100)',
                          color: 'var(--base-content)'
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = 'var(--primary)';
                          e.target.style.boxShadow = `0 0 0 1px var(--primary)`;
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = 'var(--base-300)';
                          e.target.style.boxShadow = 'none';
                        }}
                      >
                        <option value="">{t('allStatus')}</option>
                        <option value="DRAFT">{t('draft')}</option>
                        <option value="PENDING_APPROVAL">{t('pendingApproval')}</option>
                        <option value="APPROVED">{t('approved')}</option>
                        <option value="REJECTED">{t('rejected')}</option>
                        <option value="INACTIVE">{t('inactive')}</option>
                        <option value="MAINTENANCE">{t('maintenance')}</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium" style={{ color: 'var(--base-content)' }}>{t('activeStatus')}</label>
                      <select
                        value={filters.boats.isActive}
                        onChange={(e) => handleFilterChange('boats', 'isActive', e.target.value)}
                        className="mt-1 block w-full rounded-md shadow-sm focus:outline-none focus:ring-2 sm:text-sm"
                        style={{
                          borderColor: 'var(--base-300)',
                          backgroundColor: 'var(--base-100)',
                          color: 'var(--base-content)'
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = 'var(--primary)';
                          e.target.style.boxShadow = `0 0 0 1px var(--primary)`;
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = 'var(--base-300)';
                          e.target.style.boxShadow = 'none';
                        }}
                      >
                        <option value="">{t('allActiveStatus')}</option>
                        <option value="true">{t('active')}</option>
                        <option value="false">{t('inactive')}</option>
                      </select>
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium" style={{ color: 'var(--base-content)' }}>Search</label>
                      <div className="mt-1 relative rounded-md shadow-sm">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <MagnifyingGlassIcon className="h-5 w-5" style={{ color: 'var(--neutral)' }} />
                        </div>
                        <input
                          type="text"
                          value={filters.boats.search}
                          onChange={(e) => handleFilterChange('boats', 'search', e.target.value)}
                          className="block w-full pl-10 sm:text-sm rounded-md focus:outline-none focus:ring-2"
                          style={{
                            borderColor: 'var(--base-300)',
                            backgroundColor: 'var(--base-100)',
                            color: 'var(--base-content)'
                          }}
                          onFocus={(e) => {
                            e.target.style.borderColor = 'var(--primary)';
                            e.target.style.boxShadow = `0 0 0 1px var(--primary)`;
                          }}
                          onBlur={(e) => {
                            e.target.style.borderColor = 'var(--base-300)';
                            e.target.style.boxShadow = 'none';
                          }}
                          placeholder={t('searchByBoatName')}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Boats Table */}
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y" style={{ backgroundColor: 'var(--base-100)', borderColor: 'var(--base-300)' }}>
                    <thead className="bg-slate-50">
                      <tr>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('boat')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('owner')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('provider')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('status')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('capacity')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('services')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('created')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('actions')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y" style={{ backgroundColor: 'var(--base-100)', borderColor: 'var(--base-300)' }}>
                      {boats.map((boat) => (
                        <tr key={boat.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                <div className="h-10 w-10 rounded-full flex items-center justify-center bg-sky-50">
                                  <Ship className="h-6 w-6" style={{ color: 'var(--neutral)' }} />
                                </div>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium" style={{ color: 'var(--base-content)' }}>
                                  {boat.name}
                                </div>
                                <div className="text-sm" style={{ color: 'var(--neutral)' }}>
                                  {boat.registrationNumber || t('noRegistration')}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm" style={{ color: 'var(--base-content)' }}>
                              {boat.owner.profile?.firstName && boat.owner.profile?.lastName
                                ? `${boat.owner.profile.firstName} ${boat.owner.profile.lastName}`
                                : boat.owner.email}
                            </div>
                            <div className="text-sm" style={{ color: 'var(--neutral)' }}>{boat.owner.email}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {boat.provider ? (
                              <div>
                                <div className="text-sm" style={{ color: 'var(--base-content)' }}>{boat.provider.companyName}</div>
                                <StatusBadge status={boat.provider.isVerified} type="provider" />
                              </div>
                            ) : (
                              <span className="text-sm" style={{ color: 'var(--neutral)' }}>{t('noProvider')}</span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-xs">
                            <StatusBadge status={boat.status} type="boat" />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm" style={{ color: 'var(--neutral)' }}>
                            {boat.capacity} {t('people')}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm" style={{ color: 'var(--neutral)' }}>
                            {boat._count.serviceAssignments} {t('servicesCount')}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm" style={{ color: 'var(--neutral)' }}>
                            {formatDate(boat.createdAt)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <select
                              value={boat.status}
                              onChange={(e) => updateBoatStatus(boat.id, e.target.value)}
                              className="inline-flex items-center px-2.5 py-1.5 border text-xs font-medium rounded focus:outline-none focus:ring-2"
                              style={{
                                borderColor: 'var(--base-300)',
                                backgroundColor: 'var(--base-100)',
                                color: 'var(--base-content)'
                              }}
                              onFocus={(e) => {
                                e.target.style.borderColor = 'var(--primary)';
                                e.target.style.boxShadow = `0 0 0 1px var(--primary)`;
                              }}
                              onBlur={(e) => {
                                e.target.style.borderColor = 'var(--base-300)';
                                e.target.style.boxShadow = 'none';
                              }}
                            >
                              <option value="DRAFT">{t('draft')}</option>
                              <option value="PENDING_APPROVAL">{t('pendingApproval')}</option>
                              <option value="APPROVED">{t('approved')}</option>
                              <option value="REJECTED">{t('rejected')}</option>
                              <option value="INACTIVE">{t('inactive')}</option>
                              <option value="MAINTENANCE">{t('maintenance')}</option>
                            </select>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                <Pagination entity="boats" />
              </div>
            )}

            {/* Boat Owner Approvals */}
            {activeTab === 'boatOwners' && (
              <div className="shadow overflow-hidden sm:rounded-md" style={{ backgroundColor: 'var(--base-100)' }}>
                {/* Filters */}
                <div className="px-4 py-5 sm:p-6 border-b" style={{ borderColor: 'var(--base-300)' }}>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium" style={{ color: 'var(--base-content)' }}>{t('emailVerificationStatus')}</label>
                      <select
                        value={filters.boatOwners.emailVerified || ''}
                        onChange={(e) => handleFilterChange('boatOwners', 'emailVerified', e.target.value)}
                        className="mt-1 block w-full rounded-md shadow-sm focus:outline-none focus:ring-2 sm:text-sm"
                        style={{
                          borderColor: 'var(--base-300)',
                          backgroundColor: 'var(--base-100)',
                          color: 'var(--base-content)'
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = 'var(--primary)';
                          e.target.style.boxShadow = `0 0 0 1px var(--primary)`;
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = 'var(--base-300)';
                          e.target.style.boxShadow = 'none';
                        }}
                      >
                        <option value="">{t('allVerificationStatus')}</option>
                        <option value="true">{t('emailVerified')}</option>
                        <option value="false">{t('emailPending')}</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium" style={{ color: 'var(--base-content)' }}>{t('registrationDate')}</label>
                      <select
                        value={filters.boatOwners.dateRange || ''}
                        onChange={(e) => handleFilterChange('boatOwners', 'dateRange', e.target.value)}
                        className="mt-1 block w-full rounded-md shadow-sm focus:outline-none focus:ring-2 sm:text-sm"
                        style={{
                          borderColor: 'var(--base-300)',
                          backgroundColor: 'var(--base-100)',
                          color: 'var(--base-content)'
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = 'var(--primary)';
                          e.target.style.boxShadow = `0 0 0 1px var(--primary)`;
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = 'var(--base-300)';
                          e.target.style.boxShadow = 'none';
                        }}
                      >
                        <option value="">{t('allDates')}</option>
                        <option value="today">{t('today')}</option>
                        <option value="week">{t('thisWeek')}</option>
                        <option value="month">{t('thisMonth')}</option>
                      </select>
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium" style={{ color: 'var(--base-content)' }}>{t('search')}</label>
                      <div className="mt-1 relative rounded-md shadow-sm">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <MagnifyingGlassIcon className="h-5 w-5" style={{ color: 'var(--neutral)' }} />
                        </div>
                        <input
                          type="text"
                          value={filters.boatOwners.search}
                          onChange={(e) => handleFilterChange('boatOwners', 'search', e.target.value)}
                          className="block w-full pl-10 sm:text-sm rounded-md focus:outline-none focus:ring-2"
                          style={{
                            borderColor: 'var(--base-300)',
                            backgroundColor: 'var(--base-100)',
                            color: 'var(--base-content)'
                          }}
                          onFocus={(e) => {
                            e.target.style.borderColor = 'var(--primary)';
                            e.target.style.boxShadow = `0 0 0 1px var(--primary)`;
                          }}
                          onBlur={(e) => {
                            e.target.style.borderColor = 'var(--base-300)';
                            e.target.style.boxShadow = 'none';
                          }}
                          placeholder={t('searchByNameOrEmail')}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Boat Owners Table */}
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y" style={{ backgroundColor: 'var(--base-100)', borderColor: 'var(--base-300)' }}>
                    <thead className="bg-slate-50">
                      <tr>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('boatOwner')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('companyName')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('businessRegistrationNumber')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('contactPhone')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('emailVerificationStatus')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('registrationDate')}
                        </th>
                        <th className="px-6 py-3 text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--neutral)' }}>
                          {t('actions')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y" style={{ backgroundColor: 'var(--base-100)', borderColor: 'var(--base-300)' }}>
                      {pendingBoatOwners.map((owner) => (
                        <tr key={owner.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                <div className="h-10 w-10 rounded-full flex items-center justify-center bg-sky-50">
                                  <ShipWheel className="h-6 w-6" style={{ color: 'var(--neutral)' }} />
                                </div>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium" style={{ color: 'var(--base-content)' }}>
                                  {owner.profile?.firstName && owner.profile?.lastName
                                    ? `${owner.profile.firstName} ${owner.profile.lastName}`
                                    : t('nameNotProvided')}
                                </div>
                                <div className="text-sm" style={{ color: 'var(--neutral)' }}>{owner.email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm" style={{ color: 'var(--base-content)' }}>
                              {owner.provider?.companyName || owner.profile?.companyName || 'N/A'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm" style={{ color: 'var(--base-content)' }}>
                              {owner.provider?.brn || owner.profile?.brn || 'N/A'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm" style={{ color: 'var(--base-content)' }}>
                              {owner.provider?.contactPhone || owner.profile?.phone || 'N/A'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              owner.emailVerified
                                ? 'bg-emerald-100 text-emerald-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {owner.emailVerified ? t('emailVerified') : t('emailPending')}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm" style={{ color: 'var(--neutral)' }}>
                            {formatDate(owner.createdAt)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex flex-col space-y-2">
                              <button
                                onClick={() => openRejectionModal(owner.id)}
                                disabled={boatOwnerApprovalLoading[owner.id]}
                                className="inline-flex items-center px-2.5 py-1.5 border border-red-500 text-red-600 hover:bg-red-500 hover:text-white hover:border-red-500 text-xs font-medium rounded disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                              >
                                {boatOwnerApprovalLoading[owner.id] ? (
                                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current mr-1"></div>
                                ) : (
                                  <XMarkIcon className="h-4 w-4 mr-1" />
                                )}
                                {t('reject')}
                              </button>
                              <button
                                onClick={() => approveBoatOwner(owner.id)}
                                disabled={boatOwnerApprovalLoading[owner.id]}
                                className="inline-flex items-center px-2.5 py-1.5 border border-emerald-500 text-emerald-600 hover:bg-emerald-500 hover:text-white hover:border-emerald-500 text-xs font-medium rounded disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                              >
                                {boatOwnerApprovalLoading[owner.id] ? (
                                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current mr-1"></div>
                                ) : (
                                  <CheckIcon className="h-4 w-4 mr-1" />
                                )}
                                {t('approve')}
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>


                <Pagination entity="boatOwners" />
              </div>
            )}
          </div>
        </main>

        <Footer />
        
        {/* Feedback Modal */}
        <StandardModal
          isOpen={showFeedbackModal}
          onClose={closeFeedbackModal}
          type={feedbackModal.type}
          title={feedbackModal.title}
          message={feedbackModal.message}
          details={feedbackModal.details}
          autoClose={feedbackModal.type === 'success'}
          autoCloseDelay={2000}
        />
        
        {/* Rejection Reason Modal */}
        {showRejectionModal && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            {/* Backdrop */}
            <div 
              className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
              onClick={closeRejectionModal}
            />
            
            {/* Modal */}
            <div className="flex min-h-full items-center justify-center p-4">
              <div 
                className="relative w-full max-w-md transform overflow-hidden rounded-lg bg-white shadow-xl transition-all"
                style={{ backgroundColor: '#ffffff' }}
              >
                {/* Header */}
                <div 
                  className="px-6 py-4 border-b"
                  style={{ 
                    backgroundColor: '#f9fafb',
                    borderColor: '#e5e7eb',
                    borderBottomWidth: '1px'
                  }}
                >
                  <div className="flex items-center justify-between">
                    <h3 
                      className="text-lg font-semibold"
                      style={{ color: '#374151' }}
                    >
                      {t('reasonForRejection')}
                    </h3>
                    <button
                      onClick={closeRejectionModal}
                      className="rounded-full p-1 hover:bg-gray-100 transition-colors"
                      style={{ color: '#6b7280' }}
                    >
                      <XMarkIcon className="w-5 h-5" />
                    </button>
                  </div>
                </div>

                {/* Content */}
                <div className="px-6 py-4">
                  <p 
                    className="text-sm mb-4"
                    style={{ color: '#4b5563' }}
                  >
                    {t('pleaseProvideReasonForRejection')}
                  </p>
                  
                  <textarea
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    className="w-full px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                    style={{ 
                      borderColor: '#d1d5db',
                      backgroundColor: '#ffffff',
                      color: '#374151',
                      minHeight: '100px'
                    }}
                    placeholder={t('enterRejectionReason')}
                  />
                </div>

                {/* Actions */}
                <div 
                  className="px-6 py-4 border-t flex justify-end space-x-3"
                  style={{ 
                    backgroundColor: '#f9fafb',
                    borderColor: '#e5e7eb'
                  }}
                >
                  <button
                    onClick={closeRejectionModal}
                    className="px-4 py-2 text-sm font-medium rounded-lg transition-colors"
                    style={{
                      backgroundColor: '#f3f4f6',
                      color: '#374151'
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.backgroundColor = '#e5e7eb';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.backgroundColor = '#f3f4f6';
                    }}
                  >
                    {t('cancel')}
                  </button>
                  <button
                    onClick={handleRejectionSubmit}
                    disabled={!rejectionReason.trim()}
                    className="px-4 py-2 text-sm font-medium rounded-lg transition-colors disabled:opacity-50"
                    style={{
                      backgroundColor: '#ef4444',
                      color: '#ffffff'
                    }}
                    onMouseEnter={(e) => {
                      if (rejectionReason.trim()) {
                        e.target.style.backgroundColor = '#dc2626';
                      }
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.backgroundColor = '#ef4444';
                    }}
                  >
                    {t('rejectApplication')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default AdminDashboard;