import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import Link from "next/link";
import { useAuth } from "../contexts/AuthContext";
import { useLanguage } from "../contexts/LanguageContext";
import Navbar from "../components/Navbar";
import ProfileCompletionModal from "../components/ProfileCompletionModal";
import Footer from "../components/Footer";

/**
 * Dashboard Page for GoSea Platform
 * Protected route that requires authentication
 */

// Utility function to format names with proper capitalization
const formatName = (name) => {
  if (!name) return "";
  return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();
};

// Utility function to format dates
const formatDate = (dateString) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

// Utility function to format address
const formatAddress = (profile) => {
  if (!profile) return "";
  const parts = [];
  if (profile.address1) parts.push(profile.address1);
  if (profile.address2) parts.push(profile.address2);
  if (profile.city) parts.push(profile.city);
  if (profile.postcode) parts.push(profile.postcode);
  if (profile.state) parts.push(profile.state);
  return parts.join(", ");
};

const DashboardPage = () => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading } = useAuth();
  const { t } = useLanguage();
  const [showProfileCompletion, setShowProfileCompletion] = useState(false);

  // Redirect unauthenticated users (but not during logout)
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      // Check if we're in the middle of a logout process
      const isLoggingOut = sessionStorage.getItem("gosea_logging_out");
      if (isLoggingOut) {
        // Clear the logout flag and redirect to home
        sessionStorage.removeItem("gosea_logging_out");
        router.push("/");
      } else {
        // Normal redirect to home page with sign-in modal for unauthenticated access
        router.push("/?openSignIn=true&redirect=" + encodeURIComponent("/dashboard"));
      }
    }
  }, [isAuthenticated, isLoading, router]);

  // Track if this is a mandatory profile completion (from Google OAuth)
  const [isMandatoryProfileCompletion, setIsMandatoryProfileCompletion] = useState(false);

  // Check for first-time login and show profile completion modal
  useEffect(() => {
    if (isAuthenticated && user) {
      console.log('Dashboard useEffect triggered:', {
        userId: user.id,
        userEmail: user.email,
        userRole: user.role,
        userProfile: user.profile,
        isAuthenticated: isAuthenticated
      });

      // Create a more reliable localStorage key that works for both manual and OAuth users
      // For Google OAuth users, we'll use email as a fallback if ID is not consistent
      const localStorageKey = user.id 
        ? `gosea_profile_completion_${user.id}`
        : user.email 
          ? `gosea_profile_completion_${btoa(user.email)}` // base64 encode email to avoid special characters
          : `gosea_profile_completion_unknown`;

      // Check if this is the first time the user is accessing the dashboard
      const hasSeenProfileCompletion = localStorage.getItem(localStorageKey);
      
      console.log('localStorage check:', {
        localStorageKey: localStorageKey,
        hasSeenProfileCompletion: hasSeenProfileCompletion
      });

      // Check URL parameters for first-time Google users
      const urlParams = new URLSearchParams(window.location.search);
      const shouldShowProfileCompletion =
        urlParams.get("show_profile_completion") === "true";
        
      console.log('URL parameter check:', {
        showProfileCompletionParam: urlParams.get("show_profile_completion"),
        shouldShowProfileCompletion: shouldShowProfileCompletion
      });

      // Check if user has incomplete profile (only phone number is required)
      const hasIncompleteProfile = !user.profile?.phone;
      
      console.log('Profile completeness check:', {
        hasPhone: !!user.profile?.phone,
        phoneValue: user.profile?.phone,
        hasIncompleteProfile: hasIncompleteProfile
      });

      // Show profile completion if:
      // 1. User hasn't seen it before AND has incomplete profile fields, OR
      // 2. Explicitly requested via URL parameter AND hasn't completed profile AND hasn't seen the modal before
      const needsProfileCompletion =
        (!hasSeenProfileCompletion && hasIncompleteProfile) ||
        (shouldShowProfileCompletion && hasIncompleteProfile && !hasSeenProfileCompletion);

      console.log('Final decision logic:', {
        condition1: (!hasSeenProfileCompletion && hasIncompleteProfile),
        condition2: (shouldShowProfileCompletion && hasIncompleteProfile && !hasSeenProfileCompletion),
        needsProfileCompletion: needsProfileCompletion
      });

      if (needsProfileCompletion) {
        console.log('Showing profile completion modal');
        setShowProfileCompletion(true);
        // Set as mandatory if triggered by Google OAuth flow
        setIsMandatoryProfileCompletion(shouldShowProfileCompletion);

        // Clean up URL parameter if present
        if (shouldShowProfileCompletion) {
          console.log('Cleaning up URL parameter');
          const newUrl = window.location.pathname;
          window.history.replaceState({}, document.title, newUrl);
        }
      } else if (shouldShowProfileCompletion) {
        // If URL parameter is present but user doesn't need to see the modal,
        // clean up the URL parameter without showing the modal
        console.log('Cleaning up URL parameter without showing modal');
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
      } else {
        console.log('No profile completion modal needed');
      }
    }
  }, [isAuthenticated, user]);

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-base-100">
        <div className="loading loading-spinner loading-lg text-primary"></div>
      </div>
    );
  }

  // Don't render dashboard if user is not authenticated
  if (!isAuthenticated) {
    return null;
  }

  const pageName = "Dashboard";

  return (
    <>
      <Head>
        <title>{pageName} - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" type="image/png" href="gosea_favicon.png" />
      </Head>

      <div className="min-h-screen" style={{ backgroundColor: "#f8fafc" }}>
        {/* Unified Navigation */}
        <Navbar currentPage="dashboard" />

        {/* Main Content */}
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {/* Page Header */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold" style={{ color: "#0f172a" }}>
                {t("welcomeBack")},{" "}
                <span style={{ color: "#0ea5e9" }}>
                  {formatName(user?.profile?.firstName || user?.firstName) ||
                    user?.email?.split("@")[0]}
                  !
                </span>
              </h1>
              <p className="mt-2 text-lg" style={{ color: "#64748b" }}>
                {t("dashboardSubtitle")}
              </p>
            </div>

            {/* Dashboard Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
              {/* Account Information Card */}
              <div
                className="rounded-2xl p-8 hover:shadow-2xl transition-all duration-300 border"
                style={{
                  backgroundColor: "#ffffff", // base-100
                  borderColor: "#e2e8f0", // neutral-200
                  boxShadow: "0 10px 15px -3px rgb(0 0 0 / 0.1)", // shadow-lg
                }}
              >
                <div className="flex items-center mb-6">
                  <div
                    className="w-12 h-12 rounded-xl flex items-center justify-center"
                    style={{ backgroundColor: "rgba(14, 165, 233, 0.1)" }} // primary with opacity
                  >
                    <svg
                      className="w-6 h-6"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      style={{ color: "#0ea5e9" }} // primary
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                  </div>
                  <h3
                    className="text-xl font-bold ml-4"
                    style={{ color: "#0f172a" }} // base-content
                  >
                    {t("accountInformation")}
                  </h3>
                </div>
                <div className="space-y-4">
                  {/* Basic Information - Always Visible */}
                  <div>
                    <p className="text-sm" style={{ color: "#64748b" }}>
                      {t("fullName")}
                    </p>{" "}
                    {/* neutral */}
                    <div className="flex items-center space-x-3">
                      <p
                        className="text-lg font-medium"
                        style={{ color: "#0f172a" }}
                      >
                        {" "}
                        {/* base-content */}
                        {formatName(
                          user?.profile?.firstName || user?.firstName
                        )}{" "}
                        {formatName(user?.profile?.lastName || user?.lastName)}
                      </p>
                      <span
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        style={{
                          backgroundColor:
                            user?.role === "ADMIN"
                              ? "#fef2f2" // red-50
                              : user?.role === "BOAT_OWNER"
                                ? "#fffbeb" // amber-50
                                : user?.role === "AFFILIATE_AGENT"
                                  ? "#eff6ff" // blue-50
                                  : "#f0fdf4", // green-50
                          color:
                            user?.role === "ADMIN"
                              ? "#ef4444" // error
                              : user?.role === "BOAT_OWNER"
                                ? "#f59e0b" // secondary
                                : user?.role === "AFFILIATE_AGENT"
                                  ? "#0ea5e9" // primary
                                  : "#10b981", // success
                        }}
                      >
                        {t(
                          user?.role?.toLowerCase()?.replace("_", "") ||
                            "customer"
                        )}
                      </span>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm" style={{ color: "#64748b" }}>
                      {t("emailAddress")}
                    </p>{" "}
                    {/* neutral */}
                    <p
                      className="text-lg font-medium"
                      style={{ color: "#0f172a" }}
                    >
                      {user?.email}
                    </p>{" "}
                    {/* base-content */}
                  </div>
                  <div>
                    <p className="text-sm" style={{ color: "#64748b" }}>
                      {t("accountStatus")}
                    </p>{" "}
                    {/* neutral */}
                    <div className="flex items-center space-x-2">
                      <span
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        style={{
                          backgroundColor: user?.emailVerified
                            ? "#f0fdf4"
                            : "#fffbeb", // success-50 or warning-50
                          color: user?.emailVerified ? "#10b981" : "#f59e0b", // success or warning
                        }}
                      >
                        {user?.emailVerified
                          ? `✓ ${t("verified")}`
                          : `⏳ ${t("pendingVerification")}`}
                      </span>
                    </div>
                  </div>

                  {/* View Profile Button */}
                  <div className="pt-4 border-t border-gray-200">
                    <button
                      onClick={() => router.push("/profile")}
                      className="flex items-center space-x-2 text-sm font-medium transition-colors hover:opacity-80"
                      style={{ color: "#0ea5e9" }}
                    >
                      <span>{t("viewProfile")}</span>
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              {/* Quick Actions Card */}
              <div
                className="rounded-2xl p-8 hover:shadow-2xl transition-all duration-300 border"
                style={{
                  backgroundColor: "#ffffff", // base-100
                  borderColor: "#e2e8f0", // neutral-200
                  boxShadow: "0 10px 15px -3px rgb(0 0 0 / 0.1)", // shadow-lg
                }}
              >
                <div className="flex items-center mb-6">
                  <div
                    className="w-12 h-12 rounded-xl flex items-center justify-center"
                    style={{ backgroundColor: "rgba(6, 182, 212, 0.1)" }} // accent with opacity
                  >
                    <svg
                      className="w-6 h-6"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      style={{ color: "#06b6d4" }} // accent
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                  </div>
                  <h3
                    className="text-xl font-bold ml-4"
                    style={{ color: "#0f172a" }} // base-content
                  >
                    {t("quickActions")}
                  </h3>
                </div>
                <div className="space-y-3">
                  {user?.role === "CUSTOMER" && (
                    <>
                      <button
                        className="w-full py-3 px-4 rounded-xl font-medium transition-colors"
                        style={{
                          backgroundColor: "#0ea5e9", // primary
                          color: "#ffffff", // primary-content
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.backgroundColor = "#0284c7"; // primary-focus
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.backgroundColor = "#0ea5e9"; // primary
                        }}
                      >
                        🌊 {t("browseServices")}
                      </button>
                      <button
                        className="w-full py-3 px-4 rounded-xl font-medium transition-colors"
                        style={{
                          backgroundColor: "#f1f5f9", // neutral-100
                          color: "#475569", // neutral-focus
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.backgroundColor = "#e2e8f0"; // neutral-200
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.backgroundColor = "#f1f5f9"; // neutral-100
                        }}
                      >
                        📋 {t("myBookings")}
                      </button>
                    </>
                  )}
                  {user?.role === "BOAT_OWNER" && (
                    <>
                      <button
                        className="w-full py-3 px-4 rounded-xl font-medium transition-colors"
                        style={{
                          backgroundColor: "#0ea5e9", // primary
                          color: "#ffffff", // primary-content
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.backgroundColor = "#0284c7"; // primary-focus
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.backgroundColor = "#0ea5e9"; // primary
                        }}
                      >
                        🚤 {t("manageBoats")}
                      </button>
                      <button
                        className="w-full py-3 px-4 rounded-xl font-medium transition-colors"
                        style={{
                          backgroundColor: "#f1f5f9", // neutral-100
                          color: "#475569", // neutral-focus
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.backgroundColor = "#e2e8f0"; // neutral-200
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.backgroundColor = "#f1f5f9"; // neutral-100
                        }}
                      >
                        📊 {t("viewBookings")}
                      </button>
                    </>
                  )}
                  {user?.role === "AFFILIATE_AGENT" && (
                    <>
                      <button
                        className="w-full py-3 px-4 rounded-xl font-medium transition-colors"
                        style={{
                          backgroundColor: "#0ea5e9", // primary
                          color: "#ffffff", // primary-content
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.backgroundColor = "#0284c7"; // primary-focus
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.backgroundColor = "#0ea5e9"; // primary
                        }}
                      >
                        🔗 {t("referralLinks")}
                      </button>
                      <button
                        className="w-full py-3 px-4 rounded-xl font-medium transition-colors"
                        style={{
                          backgroundColor: "#f1f5f9", // neutral-100
                          color: "#475569", // neutral-focus
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.backgroundColor = "#e2e8f0"; // neutral-200
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.backgroundColor = "#f1f5f9"; // neutral-100
                        }}
                      >
                        💰 {t("commissionReport")}
                      </button>
                    </>
                  )}
                  <button
                    className="w-full py-3 px-4 rounded-xl font-medium transition-colors"
                    style={{
                      backgroundColor: "#f8fafc", // neutral-50
                      color: "#64748b", // neutral
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.backgroundColor = "#f1f5f9"; // neutral-100
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.backgroundColor = "#f8fafc"; // neutral-50
                    }}
                  >
                    ⚙️ {t("accountSettings")}
                  </button>
                </div>
              </div>

              {/* Recent Activity Card */}
              <div
                className="rounded-2xl p-8 hover:shadow-2xl transition-all duration-300 border"
                style={{
                  backgroundColor: "#ffffff", // base-100
                  borderColor: "#e2e8f0", // neutral-200
                  boxShadow: "0 10px 15px -3px rgb(0 0 0 / 0.1)", // shadow-lg
                }}
              >
                <div className="flex items-center mb-6">
                  <div
                    className="w-12 h-12 rounded-xl flex items-center justify-center"
                    style={{ backgroundColor: "rgba(16, 185, 129, 0.1)" }} // success with opacity
                  >
                    <svg
                      className="w-6 h-6"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      style={{ color: "#10b981" }} // success
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <h3
                    className="text-xl font-bold ml-4"
                    style={{ color: "#0f172a" }} // base-content
                  >
                    {t("recentActivity")}
                  </h3>
                </div>
                <div className="space-y-4">
                  <div className="text-center py-8">
                    <div
                      className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"
                      style={{ backgroundColor: "#f1f5f9" }} // neutral-100
                    >
                      <svg
                        className="w-8 h-8"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        style={{ color: "#94a3b8" }} // neutral-light
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                        />
                      </svg>
                    </div>
                    <p className="text-sm" style={{ color: "#64748b" }}>
                      {t("noRecentActivity")}
                    </p>{" "}
                    {/* neutral */}
                    <p className="text-xs mt-1" style={{ color: "#94a3b8" }}>
                      {t("startExploring")}
                    </p>{" "}
                    {/* neutral-light */}
                  </div>
                </div>
              </div>
            </div>

            
          </div>
        </main>
        {/* Footer */}
        <Footer />

        {/* Profile Completion Modal */}
        {showProfileCompletion && (
          <ProfileCompletionModal
            isOpen={showProfileCompletion}
            onClose={() => {
              setShowProfileCompletion(false);
              setIsMandatoryProfileCompletion(false);
              // Mark as seen so it doesn't show again
              // Use the same reliable key we used for checking
              const localStorageKey = user.id 
                ? `gosea_profile_completion_${user.id}`
                : user.email 
                  ? `gosea_profile_completion_${btoa(user.email)}`
                  : `gosea_profile_completion_unknown`;
              
              localStorage.setItem(localStorageKey, "true");
            }}
            user={user}
            mandatory={isMandatoryProfileCompletion}
          />
        )}
      </div>
    </>
  );
};

export default DashboardPage;
