import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import Head from 'next/head';

/**
 * Email Verification Page for GoSea Platform
 * Displays verification instructions and handles resend functionality
 */

const VerifyEmailPage = () => {
  const router = useRouter();
  const { email } = router.query;
  const [isResending, setIsResending] = useState(false);
  const [resendMessage, setResendMessage] = useState('');
  const [resendError, setResendError] = useState('');

  // Clear messages after a delay
  useEffect(() => {
    if (resendMessage || resendError) {
      const timer = setTimeout(() => {
        setResendMessage('');
        setResendError('');
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [resendMessage, resendError]);

  // Handle resend verification email
  const handleResendEmail = async () => {
    if (!email) {
      setResendError('Email address is required to resend verification');
      return;
    }

    setIsResending(true);
    setResendError('');
    setResendMessage('');

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001'}/api/auth/resend-verification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        setResendMessage('Verification email sent successfully! Please check your inbox.');
      } else {
        setResendError(data.message || 'Failed to resend verification email. Please try again.');
      }
    } catch (error) {
      console.error('Resend verification error:', error);
      setResendError('Network error. Please check your connection and try again.');
    } finally {
      setIsResending(false);
    }
  };

  const pageName = "Verify Your Email";

  return (
    <>
      <Head>
        <title>{pageName} - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-accent-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {/* Header */}
          <div className="text-center">
            <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-success text-white text-3xl">
              📧
            </div>
            <h2 className="mt-6 text-3xl font-extrabold text-neutral-900">
              Check Your Email
            </h2>
            <p className="mt-2 text-sm text-neutral-600">
              We've sent a verification link to your email address
            </p>
          </div>

          {/* Main Content */}
          <div className="bg-white rounded-lg shadow-md p-6 space-y-6">
            {/* Email Display */}
            {email ? (
              <div className="text-center">
                <p className="text-sm text-neutral-600 mb-2">
                  Verification email sent to:
                </p>
                <p className="text-lg font-medium text-neutral-900 bg-neutral-50 rounded-lg p-3 break-all">
                  {decodeURIComponent(email)}
                </p>
              </div>
            ) : (
              <div className="alert alert-warning">
                <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <span>No email address provided. Please register again.</span>
              </div>
            )}

            {/* Instructions */}
            <div className="space-y-4">
              <div className="bg-info-50 border border-info-200 rounded-lg p-4">
                <h3 className="font-medium text-info-900 mb-2">Next Steps:</h3>
                <ol className="list-decimal list-inside space-y-2 text-sm text-info-800">
                  <li>Check your email inbox for a verification message from GoSea</li>
                  <li>Click the verification link in the email to activate your account</li>
                  <li>Return to the login page to access your account</li>
                </ol>
              </div>

              <div className="bg-warning-50 border border-warning-200 rounded-lg p-4">
                <h3 className="font-medium text-warning-900 mb-2">Can't find the email?</h3>
                <ul className="list-disc list-inside space-y-1 text-sm text-warning-800">
                  <li>Check your spam or junk folder</li>
                  <li>Make sure you entered the correct email address</li>
                  <li>Wait a few minutes - emails can sometimes be delayed</li>
                  <li>Use the resend button below if needed</li>
                </ul>
              </div>
            </div>

            {/* Success/Error Messages */}
            {resendMessage && (
              <div className="alert alert-success">
                <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{resendMessage}</span>
              </div>
            )}

            {resendError && (
              <div className="alert alert-error">
                <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{resendError}</span>
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-3">
              {/* Resend Email Button */}
              {email && (
                <button
                  onClick={handleResendEmail}
                  disabled={isResending}
                  className="btn btn-outline btn-primary w-full"
                >
                  {isResending ? (
                    <>
                      <span className="loading loading-spinner loading-sm"></span>
                      Sending...
                    </>
                  ) : (
                    <>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                      Resend Verification Email
                    </>
                  )}
                </button>
              )}

              {/* Go to Login Button */}
              <Link href="/login" className="btn btn-primary w-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013 3v1" />
                </svg>
                Go to Login Page
              </Link>
            </div>
          </div>

          {/* Additional Help */}
          <div className="text-center">
            <p className="text-sm text-neutral-600">
              Need help?{' '}
              <Link href="/contact" className="font-medium text-primary hover:text-primary-focus">
                Contact Support
              </Link>
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default VerifyEmailPage;
