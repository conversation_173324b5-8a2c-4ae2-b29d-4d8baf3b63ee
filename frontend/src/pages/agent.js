import { useState } from 'react';
import Link from 'next/link';
import { useLanguage } from '../contexts/LanguageContext';

export default function AgentPage() {
  const { t } = useLanguage();
  const [showSignUp, setShowSignUp] = useState(false);
  const [showSignIn, setShowSignIn] = useState(false);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl">🚢</span>
              <span className="text-xl font-bold text-primary">GoSea</span>
            </Link>
            <Link href="/" className="text-gray-600 hover:text-primary transition-colors">
              ← {t('backToHome')}
            </Link>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center">
          <div className="text-6xl mb-6">🤝</div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {t('agentWelcome')}
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            {t('agentDescription')}
          </p>

          {/* Action Buttons */}
          {!showSignUp && !showSignIn && (
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => setShowSignUp(true)}
                className="btn btn-primary btn-lg px-8"
              >
                {t('signUpAsAgent')}
              </button>
              <button
                onClick={() => setShowSignIn(true)}
                className="btn btn-outline btn-lg px-8"
              >
                {t('signInAsAgent')}
              </button>
            </div>
          )}

          {/* Sign Up Options */}
          {showSignUp && (
            <div className="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                {t('signUpAsAgent')}
              </h2>
              <div className="space-y-4">
                <Link
                  href="/register/agent?method=google"
                  className="w-full btn btn-outline flex items-center justify-center space-x-2"
                >
                  <svg className="w-5 h-5" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  <span>{t('continueWithGoogle')}</span>
                </Link>
                <Link
                  href="/register/agent?method=email"
                  className="w-full btn btn-primary"
                >
                  {t('continueWithEmail')}
                </Link>
              </div>
              <button
                onClick={() => setShowSignUp(false)}
                className="mt-4 text-gray-500 hover:text-gray-700 transition-colors"
              >
                {t('cancel')}
              </button>
            </div>
          )}

          {/* Sign In Options */}
          {showSignIn && (
            <div className="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                {t('signInAsAgent')}
              </h2>
              <div className="space-y-4">
                <Link
                  href="/login/agent?method=google"
                  className="w-full btn btn-outline flex items-center justify-center space-x-2"
                >
                  <svg className="w-5 h-5" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  <span>{t('continueWithGoogle')}</span>
                </Link>
                <Link
                  href="/login/agent?method=email"
                  className="w-full btn btn-primary"
                >
                  {t('continueWithEmail')}
                </Link>
              </div>
              <button
                onClick={() => setShowSignIn(false)}
                className="mt-4 text-gray-500 hover:text-gray-700 transition-colors"
              >
                {t('cancel')}
              </button>
            </div>
          )}
        </div>

        {/* Features Section */}
        {!showSignUp && !showSignIn && (
          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-4xl mb-4">💼</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {t('earnCommission')}
              </h3>
              <p className="text-gray-600">
                {t('earnCommissionDescription')}
              </p>
            </div>
            <div className="text-center">
              <div className="text-4xl mb-4">🔗</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {t('affiliateLinks')}
              </h3>
              <p className="text-gray-600">
                {t('affiliateLinksDescription')}
              </p>
            </div>
            <div className="text-center">
              <div className="text-4xl mb-4">📊</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {t('trackPerformance')}
              </h3>
              <p className="text-gray-600">
                {t('trackPerformanceDescription')}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
