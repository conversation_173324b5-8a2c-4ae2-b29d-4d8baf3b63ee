import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { CheckCircleIcon, XCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';

export default function VerifyEmailConfirm() {
  const router = useRouter();
  const [status, setStatus] = useState('loading'); // loading, success, error, expired, invalid
  const [message, setMessage] = useState('');
  const [userEmail, setUserEmail] = useState('');

  useEffect(() => {
    const { token } = router.query;
    
    if (token) {
      verifyEmailToken(token);
    }
  }, [router.query]);

  const verifyEmailToken = async (token) => {
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/auth/verify-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      const data = await response.json();

      if (data.success) {
        setStatus('success');
        setMessage(data.message || 'Your email has been successfully verified!');
        setUserEmail(data.user?.email || '');
        
        // Check for stored return URL and redirect there, otherwise go to home page
        setTimeout(() => {
          const returnUrl = sessionStorage.getItem('gosea_return_url');
          if (returnUrl && returnUrl !== '/verify-email-confirm') {
            // Clear the return URL from session storage
            sessionStorage.removeItem('gosea_return_url');
            // Redirect to the original page with verification success flag
            const finalUrl = `${returnUrl}${returnUrl.includes('?') ? '&' : '?'}verified=true&openSignIn=true`;
            router.push(finalUrl);
          } else {
            // Default redirect to home page with sign-in modal trigger
            router.push('/?verified=true&openSignIn=true');
          }
        }, 3000);
      } else {
        if (data.code === 'TOKEN_EXPIRED') {
          setStatus('expired');
          setMessage(data.message || 'Your verification link has expired.');
          setUserEmail(data.email || '');
        } else if (data.code === 'TOKEN_INVALID' || data.code === 'TOKEN_NOT_FOUND') {
          setStatus('invalid');
          setMessage(data.message || 'Invalid verification link.');
        } else if (data.code === 'EMAIL_ALREADY_VERIFIED') {
          setStatus('success');
          setMessage(data.message || 'Your email is already verified.');
          setTimeout(() => {
            const returnUrl = sessionStorage.getItem('gosea_return_url');
            if (returnUrl && returnUrl !== '/verify-email-confirm') {
              // Clear the return URL from session storage
              sessionStorage.removeItem('gosea_return_url');
              // Redirect to the original page with verification success flag
              const finalUrl = `${returnUrl}${returnUrl.includes('?') ? '&' : '?'}verified=true&openSignIn=true`;
              router.push(finalUrl);
            } else {
              // Default redirect to home page with sign-in modal trigger
              router.push('/?verified=true&openSignIn=true');
            }
          }, 3000);
        } else {
          setStatus('error');
          setMessage(data.message || 'An error occurred during verification.');
        }
      }
    } catch (error) {
      console.error('Email verification error:', error);
      setStatus('error');
      setMessage('Network error. Please check your connection and try again.');
    }
  };

  const handleResendVerification = async () => {
    if (!userEmail) {
      router.push('/verify-email');
      return;
    }

    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/auth/resend-verification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: userEmail }),
      });

      const data = await response.json();
      
      if (data.success) {
        setStatus('loading');
        setMessage('A new verification email has been sent. Please check your inbox.');
      } else {
        setMessage(data.message || 'Failed to resend verification email.');
      }
    } catch (error) {
      console.error('Resend verification error:', error);
      setMessage('Failed to resend verification email. Please try again.');
    }
  };

  const renderContent = () => {
    switch (status) {
      case 'loading':
        return (
          <div className="text-center">
            <div className="loading loading-spinner loading-lg text-primary mb-4"></div>
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Verifying Your Email...
            </h1>
            <p className="text-gray-600">
              Please wait while we verify your email address.
            </p>
          </div>
        );

      case 'success':
        return (
          <div className="text-center">
            <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Email Verified Successfully! 🎉
            </h1>
            <p className="text-gray-600 mb-6">
              {message}
            </p>
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <p className="text-green-800 text-sm">
                Welcome to GoSea Platform! You can now access all features of your account.
              </p>
            </div>
            <p className="text-sm text-gray-500 mb-4">
              Redirecting you to login in 3 seconds...
            </p>
            <Link href="/login" className="btn btn-primary">
              Continue to Login
            </Link>
          </div>
        );

      case 'expired':
        return (
          <div className="text-center">
            <ExclamationTriangleIcon className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Verification Link Expired
            </h1>
            <p className="text-gray-600 mb-6">
              {message}
            </p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <p className="text-yellow-800 text-sm">
                Don't worry! You can request a new verification email below.
              </p>
            </div>
            <div className="space-y-3">
              <button 
                onClick={handleResendVerification}
                className="btn btn-primary w-full"
              >
                Send New Verification Email
              </button>
              <Link href="/verify-email" className="btn btn-outline w-full">
                Enter Different Email
              </Link>
            </div>
          </div>
        );

      case 'invalid':
        return (
          <div className="text-center">
            <XCircleIcon className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Invalid Verification Link
            </h1>
            <p className="text-gray-600 mb-6">
              {message}
            </p>
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <p className="text-red-800 text-sm">
                This verification link is not valid. Please request a new one.
              </p>
            </div>
            <div className="space-y-3">
              <Link href="/verify-email" className="btn btn-primary w-full">
                Request New Verification Email
              </Link>
              <Link href="/register" className="btn btn-outline w-full">
                Create New Account
              </Link>
            </div>
          </div>
        );

      case 'error':
        return (
          <div className="text-center">
            <XCircleIcon className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Verification Failed
            </h1>
            <p className="text-gray-600 mb-6">
              {message}
            </p>
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <p className="text-red-800 text-sm">
                Something went wrong during verification. Please try again or contact support.
              </p>
            </div>
            <div className="space-y-3">
              <button 
                onClick={() => window.location.reload()}
                className="btn btn-primary w-full"
              >
                Try Again
              </button>
              <Link href="/verify-email" className="btn btn-outline w-full">
                Request New Verification Email
              </Link>
              <Link href="/contact" className="btn btn-ghost w-full">
                Contact Support
              </Link>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center">
            <ExclamationTriangleIcon className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              No Verification Token
            </h1>
            <p className="text-gray-600 mb-6">
              No verification token was provided in the URL.
            </p>
            <div className="space-y-3">
              <Link href="/verify-email" className="btn btn-primary w-full">
                Request Verification Email
              </Link>
              <Link href="/register" className="btn btn-outline w-full">
                Create New Account
              </Link>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-cyan-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <div className="bg-white rounded-2xl shadow-xl p-8">
          {/* Logo */}
          <div className="text-center mb-8">
            <div className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600">
              🌊 GoSea
            </div>
            <p className="text-gray-500 text-sm mt-2">Maritime Platform</p>
          </div>

          {renderContent()}

          {/* Footer */}
          <div className="mt-8 pt-6 border-t border-gray-200 text-center">
            <p className="text-xs text-gray-500">
              Need help? <Link href="/contact" className="text-blue-600 hover:text-blue-700">Contact Support</Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
