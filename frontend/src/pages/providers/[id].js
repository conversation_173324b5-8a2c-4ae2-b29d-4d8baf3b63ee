import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useLanguage } from '../../contexts/LanguageContext';
import Navbar from '../../components/Navbar';
import StandardModal from '../../components/StandardModal';
import Footer from '../../components/Footer';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { MapPinIcon, PhoneIcon, EnvelopeIcon, StarIcon, ClockIcon } from '@heroicons/react/24/outline';
import Head from 'next/head';

export default function ProviderDetailsPage() {
  const { t } = useLanguage();
  const router = useRouter();
  const { id } = router.query;

  const [provider, setProvider] = useState(null);
  const [selectedService, setSelectedService] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Modal state
  const [showModal, setShowModal] = useState(false);
  const [modalConfig, setModalConfig] = useState({
    type: 'info',
    title: '',
    message: '',
    details: null
  });

  // Load provider details
  useEffect(() => {
    if (!id) return;

    const loadProviderDetails = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/providers/${id}`);
        const data = await response.json();

        if (data.success) {
          setProvider(data.data);
        } else {
          setError(data.message || t('failedLoadProviderDetails'));
        }
      } catch (error) {
        console.error('Error loading provider:', error);
        setError(t('errorLoadingProvider'));
      } finally {
        setIsLoading(false);
      }
    };

    loadProviderDetails();
  }, [id]);

  // Handle service selection
  const handleServiceSelect = (service) => {
    setSelectedService(service);

    // Navigate to booking page with provider and service context
    const searchParams = new URLSearchParams();
    searchParams.set('providerId', provider.id);
    searchParams.set('serviceId', service.id);

    router.push(`/booking?${searchParams.toString()}`);
  };

  // Show error modal
  const showErrorModal = (title, message, details = null) => {
    setModalConfig({ type: 'error', title, message, details });
    setShowModal(true);
  };

  // Handle back to search with filter preservation
  const handleBackToSearch = () => {
    // Get search filters from URL query parameters
    const { jetty, category, destination, date, datetime } = router.query;

    // Build search URL with preserved filters
    const searchParams = new URLSearchParams();
    if (jetty) searchParams.set('jettyId', jetty);
    if (category) searchParams.set('serviceCategory', category);
    if (destination) searchParams.set('destinationId', destination);
    if (date) searchParams.set('date', date);
    if (datetime) searchParams.set('datetime', datetime);

    const searchUrl = searchParams.toString()
      ? `/boats?${searchParams.toString()}`
      : '/boats';

    router.push(searchUrl);
  };

  // Determine pricing scenario for a service
  const getPricingScenario = (service) => {
    if (!service) return 'normal';

    const hasPackages = service.packages && service.packages.length > 0;
    const hasAgePricing = service.agePricing || (hasPackages && service.packages.some(pkg => pkg.agePricing));

    if (!hasPackages && !hasAgePricing) return 'normal'; // Scenario 1
    if (hasPackages && !hasAgePricing) return 'packages-only'; // Scenario 2
    if (!hasPackages && hasAgePricing) return 'age-only'; // Scenario 3
    return 'both-age-and-package'; // Scenario 4
  };

  // Check if we should show the "per person" price display
  const shouldShowPerPersonPrice = (service) => {
    const scenario = getPricingScenario(service);
    // Hide per person price only in Scenario 4 (both age and package-based pricing)
    return scenario !== 'both-age-and-package';
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar currentPage="boats" />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-500 mx-auto mb-4"></div>
            <p className="text-gray-600">{t('loadingProviderDetails')}</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (error || !provider) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar currentPage="boats" />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <div className="text-6xl mb-4">❌</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {t('providerNotFound')}
            </h3>
            <p className="text-gray-600 mb-6">
              {error || t('providerNotFoundMessage')}
            </p>
            <button
              onClick={() => router.push('/search')}
              className="px-6 py-3 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors"
            >
              {t('backToSearch')}
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  const pageName = "Provider Details";

  return (
    <>
      <Head>
        <title>{pageName} - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        <Navbar currentPage="boats" />

        <div className="max-w-7xl mx-auto px-4 py-4 md:py-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-4 md:mb-6">
            <h1 className="text-xl md:text-2xl font-bold text-gray-900">
              {t('provider')} ({provider.displayName || provider.companyName || t('providerNameNotAvailable')})
            </h1>
            <button
              onClick={handleBackToSearch}
              className="md:flex hidden items-center text-gray-600 hover:text-gray-900 mr-4 text-sm"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-1" />
              {t('backToSearch')}
            </button>
          </div>
          {/* Provider Header */}
          <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <div className="flex flex-col items-center md:flex-row md:items-start gap-6">
              {/* Provider Logo/Image */}
              <div className="flex-shrink-0">
                {provider.logoUrl ? (
                  <img
                    src={provider.logoUrl}
                    alt={provider.displayName || t('providerLogo')}
                    className="w-32 h-32 rounded-lg object-cover border"
                  />
                ) : (
                  <div className="w-32 h-32 bg-amber-100 rounded-lg flex items-center justify-center">
                    <span className="text-2xl font-bold text-amber-600">
                      {provider.displayName && provider.displayName.length > 0
                        ? provider.displayName.charAt(0).toUpperCase()
                        : 'P'}
                    </span>
                  </div>
                )}
              </div>

              {/* Provider Info */}
              <div className="flex-1">
                {/* <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  {provider.displayName || provider.companyName || t('providerNameNotAvailable')}
                </h1> */}

                {provider.description && (
                  <p className="text-gray-600 mb-4">
                    {provider.description}
                  </p>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  {/* Contact Info */}
                  <div className="space-y-2">
                    {provider.contactPhone && (
                      <div className="flex items-center text-gray-600">
                        <PhoneIcon className="h-4 w-4 mr-2" />
                        <span>{provider.contactPhone}</span>
                      </div>
                    )}

                    {provider.contactEmail && (
                      <div className="flex items-center text-gray-600">
                        <EnvelopeIcon className="h-4 w-4 mr-2" />
                        <span>{provider.contactEmail}</span>
                      </div>
                    )}

                    {/* Address from user profile */}
                    {provider.user?.profile && (provider.user.profile.address1 || provider.user.profile.city) && (
                      <div className="flex items-center text-gray-600">
                        <MapPinIcon className="h-4 w-4 mr-2" />
                        <span>
                          {[provider.user.profile.address1, provider.user.profile.address2, provider.user.profile.city, provider.user.profile.postcode, provider.user.profile.state]
                            .filter(Boolean)
                            .join(', ')}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Business Info */}
                  <div className="space-y-2">
                    <div className="flex items-center text-gray-600">
                      <ClockIcon className="h-4 w-4 mr-2" />
                      <span>{t('operatingSince')} {new Date(provider.createdAt).getFullYear()}</span>
                    </div>

                    {provider.rating && (
                      <div className="flex items-center text-gray-600">
                        <StarIcon className="h-4 w-4 mr-2" />
                        <span>{typeof provider.rating === 'number' ? provider.rating.toFixed(1) : '0.0'} {t('rating')}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Services Section */}
          <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <h2 className="text-lgsm:text-xl font-semibold text-gray-900 mb-4">
              {t('availableServices')}
            </h2>

            {provider.services && provider.services.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {provider.services.map((service) => (
                  <div
                    key={service.id}
                    className="border rounded-lg p-4 hover:border-amber-500 transition-colors cursor-pointer"
                    onClick={() => handleServiceSelect(service)}
                  >
                    <h3 className="font-semibold text-gray-900 mb-2">
                      {service.serviceType?.name || 'Service'}
                    </h3>

                    {service.description && (
                      <p className="text-sm text-gray-600 mb-3">
                        {service.description}
                      </p>
                    )}

                    <div className="space-y-1 text-sm">
                      {/* Conditionally show per person price based on pricing scenario */}
                      {shouldShowPerPersonPrice(service) && (
                        <div className="flex justify-between">
                          <span className="text-gray-500">Base Price:</span>
                          <span className="font-medium">RM {service.basePrice} per person</span>
                        </div>
                      )}

                      {(service.calculatedCapacity || service.maxCapacity) && (
                        <div className="flex justify-between">
                          <span className="text-gray-500">Max Capacity:</span>
                          <span className="font-medium">{service.calculatedCapacity || service.maxCapacity} people</span>
                        </div>
                      )}
                    </div>

                    {/* <div className="flex gap-2 mt-3"> */}
                    <div className="w-full mt-6 flex flex-col sm:flex-row sm:space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/services/${service.id}`);
                        }}
                        className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors w-full sm:w-1/2"
                      >
                        {t('viewDetails')}
                      </button>
                      <button
                        className="px-4 py-2 bg-amber-500 text-white rounded-md hover:bg-amber-600 transition-colors w-full sm:w-1/2 mt-2 sm:mt-0"
                      >
                        {t('bookNow')}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">{t('noServicesAvailable')}</p>
            )}
          </div>

          {/* Boats Section */}
          {provider.boats && provider.boats.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-4">
                {t('fleet')} ({provider.boats.length} {t('boats')})
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {provider.boats.map((boat) => (
                  <div key={boat.id} className="border rounded-lg p-4">
                    <h3 className="font-semibold text-gray-900 mb-2">
                      {boat.name}
                    </h3>

                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Capacity:</span>
                        <span className="font-medium">{boat.capacity} people</span>
                      </div>

                      <div className="flex justify-between">
                        <span className="text-gray-500">Type:</span>
                        <span className="font-medium">{boat.boatType}</span>
                      </div>

                      {boat.yearBuilt && (
                        <div className="flex justify-between">
                          <span className="text-gray-500">Year:</span>
                          <span className="font-medium">{boat.yearBuilt}</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <Footer />

        {/* Modal */}
        <StandardModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          type={modalConfig.type}
          title={modalConfig.title}
          message={modalConfig.message}
          details={modalConfig.details}
        />
      </div>
    </>
  );
}
