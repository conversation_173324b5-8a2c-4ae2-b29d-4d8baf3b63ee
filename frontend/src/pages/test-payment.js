import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import DummyPaymentForm from '../components/payment/DummyPaymentForm';

const TestPaymentPage = () => {
  const { user } = useAuth();
  const [paymentResult, setPaymentResult] = useState(null);
  const [error, setError] = useState(null);

  // Mock booking data for testing
  const mockBooking = {
    id: 'booking_test_123',
    amount: 150.00,
    depositAmount: 45.00, // 30% of 150
    currency: 'MYR',
    boatName: 'Test Boat',
    date: '2024-08-15',
    time: '10:00 AM'
  };

  const handlePaymentSuccess = (result) => {
    setPaymentResult(result);
    setError(null);
    console.log('Payment successful:', result);
  };

  const handlePaymentError = (errorMessage) => {
    setError(errorMessage);
    setPaymentResult(null);
    console.error('Payment error:', errorMessage);
  };

  const resetTest = () => {
    setPaymentResult(null);
    setError(null);
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Payment Test</h1>
          <p className="text-gray-600">Please sign in to test the payment flow.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Payment Gateway Test
          </h1>
          <p className="text-gray-600">
            Test the dummy payment gateway implementation
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Booking Summary */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900">
              Mock Booking Details
            </h2>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Booking ID:</span>
                <span className="font-medium">{mockBooking.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Boat:</span>
                <span className="font-medium">{mockBooking.boatName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Date:</span>
                <span className="font-medium">{mockBooking.date}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Time:</span>
                <span className="font-medium">{mockBooking.time}</span>
              </div>
              <hr className="my-4" />
              <div className="flex justify-between text-lg font-semibold">
                <span>Total Amount:</span>
                <span>{mockBooking.currency} {mockBooking.amount.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-sm text-gray-600">
                <span>Deposit (30%):</span>
                <span>{mockBooking.currency} {mockBooking.depositAmount.toFixed(2)}</span>
              </div>
            </div>
          </div>

          {/* Payment Forms */}
          <div className="space-y-6">
            {!paymentResult && !error && (
              <>
                {/* Full Payment */}
                <div>
                  <h3 className="text-lg font-semibold mb-3 text-gray-900">
                    Option 1: Full Payment
                  </h3>
                  <DummyPaymentForm
                    amount={mockBooking.amount}
                    currency={mockBooking.currency}
                    bookingId={mockBooking.id}
                    onPaymentSuccess={handlePaymentSuccess}
                    onPaymentError={handlePaymentError}
                    isDeposit={false}
                  />
                </div>

                {/* Deposit Payment */}
                <div>
                  <h3 className="text-lg font-semibold mb-3 text-gray-900">
                    Option 2: Deposit Payment
                  </h3>
                  <DummyPaymentForm
                    amount={mockBooking.depositAmount}
                    currency={mockBooking.currency}
                    bookingId={mockBooking.id}
                    onPaymentSuccess={handlePaymentSuccess}
                    onPaymentError={handlePaymentError}
                    isDeposit={true}
                  />
                </div>
              </>
            )}

            {/* Success Result */}
            {paymentResult && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <svg className="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="ml-3 flex-1">
                    <h3 className="text-lg font-medium text-green-800">
                      Payment Successful!
                    </h3>
                    <div className="mt-2 text-sm text-green-700">
                      <p className="mb-2">{paymentResult.message}</p>
                      <div className="bg-white rounded p-3 text-xs">
                        <strong>Payment Details:</strong>
                        <pre className="mt-1 whitespace-pre-wrap">
                          {JSON.stringify(paymentResult, null, 2)}
                        </pre>
                      </div>
                    </div>
                    <div className="mt-4">
                      <button
                        onClick={resetTest}
                        className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
                      >
                        Test Again
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Error Result */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <svg className="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="ml-3 flex-1">
                    <h3 className="text-lg font-medium text-red-800">
                      Payment Failed
                    </h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>{error}</p>
                    </div>
                    <div className="mt-4">
                      <button
                        onClick={resetTest}
                        className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
                      >
                        Try Again
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* API Information */}
        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-900">
            Dummy Payment Gateway Information
          </h2>
          <div className="prose text-gray-600">
            <p className="mb-4">
              This implementation replaces Stripe with a dummy payment gateway that simulates the payment flow.
              The following endpoints are available:
            </p>
            <ul className="list-disc pl-6 space-y-2">
              <li><code>POST /api/payments/create-intent</code> - Creates a dummy payment intent</li>
              <li><code>POST /api/payments/create-deposit-intent</code> - Creates a dummy deposit payment intent</li>
              <li><code>POST /api/payments/confirm</code> - Confirms and processes the payment</li>
              <li><code>POST /api/payments/validate-promo</code> - Validates discount codes (unchanged)</li>
            </ul>
            <p className="mt-4">
              <strong>Note:</strong> This is a development implementation. When you're ready to integrate 
              with a real Malaysian payment gateway (ToyyibPay, iPay88, etc.), the same API structure 
              can be maintained while replacing the internal implementation.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestPaymentPage;
