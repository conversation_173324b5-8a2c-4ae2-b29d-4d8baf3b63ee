import Head from 'next/head';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import Navbar from '../components/Navbar';
import SignInModal from '../components/SignInModal';
import SignUpModal from '../components/SignUpModal';
import ProfileCompletionModal from '../components/ProfileCompletionModal';
import BoatSearchForm from '../components/BoatSearchForm';
import ProviderCard from '../components/provider/ProviderCard';
import Footer from '../components/Footer';
import { TbScubaMask } from 'react-icons/tb'

import {
  Ship,
  Handshake,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

// Utility function to format names with proper capitalization
const formatName = (name) => {
  if (!name) return '';
  return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();
};

export default function Home() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const { t } = useLanguage();
  const [showSignInModal, setShowSignInModal] = useState(false);
  const [showSignUpModal, setShowSignUpModal] = useState(false);
  const [showProfileCompletion, setShowProfileCompletion] = useState(false);
  // Track if this is a mandatory profile completion (from Google OAuth)
  const [isMandatoryProfileCompletion, setIsMandatoryProfileCompletion] = useState(false);
  const [topProviders, setTopProviders] = useState([]);
  const [isLoadingProviders, setIsLoadingProviders] = useState(true);
  const [providersScrollRef, setProvidersScrollRef] = useState(null);
  const [showProvidersLeftArrow, setShowProvidersLeftArrow] = useState(false);
  const [showProvidersRightArrow, setShowProvidersRightArrow] = useState(false);
  const [roleScrollRef, setRoleScrollRef] = useState(null);
  const [showRoleLeftArrow, setShowRoleLeftArrow] = useState(false);
  const [showRoleRightArrow, setShowRoleRightArrow] = useState(false);

  // Check for URL parameters to auto-open sign-in modal (e.g., after email verification) or handle OAuth errors
  useEffect(() => {
    if (router.isReady) {
      const { verified, openSignIn, error, redirect } = router.query;

      // Handle email verification success or direct sign-in request
      if ((verified === 'true' && openSignIn === 'true') || openSignIn === 'true') {
        setShowSignInModal(true);
        // Store redirect URL if provided
        if (redirect) {
          sessionStorage.setItem('gosea_post_auth_redirect', redirect);
        }
        // Clean up URL parameters
        router.replace('/', undefined, { shallow: true });
      }

      // Handle OAuth errors
      if (error) {
        let errorMessage = 'Authentication failed. Please try again.';
        if (error === 'oauth_failed') {
          errorMessage = 'Google sign-in failed. Please try again.';
        } else if (error === 'oauth_callback_failed') {
          errorMessage = 'Authentication callback failed. Please try again.';
        }

        // Show error message (you can implement a toast notification here)
        console.error('OAuth Error:', errorMessage);
        alert(errorMessage); // Temporary - replace with proper notification

        // Open sign-in modal for retry
        setShowSignInModal(true);

        // Clean up URL parameters
        router.replace('/', undefined, { shallow: true });
      }
    }
  }, [router.isReady, router.query]);

  // Handle profile completion for first-time Google users
  useEffect(() => {
    if (router.isReady && router.query.show_profile_completion === 'true' && user) {
      // Create a more reliable localStorage key that works for both manual and OAuth users
      // For Google OAuth users, we'll use email as a fallback if ID is not consistent
      const localStorageKey = user.id 
        ? `gosea_profile_completion_${user.id}`
        : user.email 
          ? `gosea_profile_completion_${btoa(user.email)}` // base64 encode email to avoid special characters
          : `gosea_profile_completion_unknown`;
      
      // Check if this is the first time the user is accessing the dashboard
      const hasSeenProfileCompletion = localStorage.getItem(localStorageKey);
      
      // Check if user has incomplete profile (only phone number is required)
      const hasIncompleteProfile = !user.profile?.phone;

      console.log('Homepage profile completion check:', {
        userId: user.id,
        userEmail: user.email,
        hasSeenProfileCompletion,
        hasIncompleteProfile,
        hasPhone: !!user.profile?.phone
      });

      // Only show profile completion modal if user has incomplete profile and hasn't seen the modal before
      if (hasIncompleteProfile && !hasSeenProfileCompletion) {
        console.log('Showing profile completion modal in homepage');
        // Show mandatory profile completion modal for first-time Google users
        setShowProfileCompletion(true);
        setIsMandatoryProfileCompletion(true);
      }

      // Remove the query parameter from URL in all cases
      const newQuery = { ...router.query };
      delete newQuery.show_profile_completion;
      router.replace({
        pathname: router.pathname,
        query: newQuery
      }, undefined, { shallow: true });
    }
  }, [router.isReady, router.query.show_profile_completion, user]);

  // Fetch top providers data
  useEffect(() => {
    const fetchTopProviders = async () => {
      try {
        setIsLoadingProviders(true);
        const response = await fetch('/api/providers');
        const data = await response.json();

        if (data.success) {
          // Get top 3 providers based on service count and rating
          const sortedProviders = data.data
            .filter(provider => provider.serviceCount > 0) // Only providers with services
            .sort((a, b) => {
              // Sort by service count first, then by rating
              if (b.serviceCount !== a.serviceCount) {
                return b.serviceCount - a.serviceCount;
              }
              return (b.rating || 0) - (a.rating || 0);
            })
            .slice(0, 3); // Take top 3

          setTopProviders(sortedProviders);
        }
      } catch (error) {
        console.error('Error fetching providers:', error);
      } finally {
        setIsLoadingProviders(false);
      }
    };

    fetchTopProviders();
  }, []);

  // Initialize scroll buttons when providers data loads
  useEffect(() => {
    if (!isLoadingProviders && providersScrollRef) {
      checkProvidersScrollButtons();
    }
  }, [isLoadingProviders, providersScrollRef]);

  // Initialize role sections scroll buttons
  useEffect(() => {
    if (roleScrollRef) {
      checkRoleScrollButtons();
    }
  }, [roleScrollRef]);

  const switchToSignUp = () => {
    setShowSignInModal(false);
    setShowSignUpModal(true);
  };

  const switchToSignIn = () => {
    setShowSignUpModal(false);
    setShowSignInModal(true);
  };

  // Providers scroll handling functions
  const checkProvidersScrollButtons = () => {
    if (providersScrollRef) {
      const { scrollLeft, scrollWidth, clientWidth } = providersScrollRef;
      setShowProvidersLeftArrow(scrollLeft > 0);
      setShowProvidersRightArrow(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  const scrollProvidersLeft = () => {
    if (providersScrollRef) {
      providersScrollRef.scrollBy({ left: -320, behavior: 'smooth' });
    }
  };

  const scrollProvidersRight = () => {
    if (providersScrollRef) {
      providersScrollRef.scrollBy({ left: 320, behavior: 'smooth' });
    }
  };

  // Role sections scroll handling functions
  const checkRoleScrollButtons = () => {
    if (roleScrollRef) {
      const { scrollLeft, scrollWidth, clientWidth } = roleScrollRef;
      setShowRoleLeftArrow(scrollLeft > 0);
      setShowRoleRightArrow(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  const scrollRoleLeft = () => {
    if (roleScrollRef) {
      roleScrollRef.scrollBy({ left: -320, behavior: 'smooth' });
    }
  };

  const scrollRoleRight = () => {
    if (roleScrollRef) {
      roleScrollRef.scrollBy({ left: 320, behavior: 'smooth' });
    }
  };

  // Handle Book Adventure button clicks
  const handleBookAdventure = () => {
    router.push('/boats');
    // if (isAuthenticated) {
    //   // User is signed in, redirect to boats page
    //   router.push('/boats');
    // } else {
    //   // User is not signed in, scroll to user types section to choose their role
    //   const userTypesSection = document.getElementById('user-types');
    //   if (userTypesSection) {
    //     userTypesSection.scrollIntoView({
    //       behavior: 'smooth',
    //       block: 'start'
    //     });
    //   }
    // }
  };

  return (
    <>
      <Head>
        <title>Home - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" type="image/png" href="gosea_favicon.png" />
      </Head>

      <div className="min-h-screen bg-white overflow-x-hidden">
        {/* Unified Navigation */}
        <Navbar
          currentPage="home"
          onSignInClick={() => setShowSignInModal(true)}
          onSignUpClick={() => setShowSignUpModal(true)}
        />

        {/* Hero Section */}
        <main>
          {/* Hero Banner */}
          <section
            className="relative overflow-visible"
            style={{ backgroundColor: '#f0f9ff' }} // base-200
          >
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10 lg:py-16">
              <div className="text-center">
                <h1 className="text-3xl tracking-tight font-extrabold sm:text-5xl lg:text-6xl"
                    style={{ color: '#0f172a' }} // base-content
                >
                  <span className="block">{t('heroTitle')}</span>
                  <span className="block text-transparent bg-clip-text"
                        style={{
                          backgroundImage: 'linear-gradient(to right, #0ea5e9, #06b6d4)', // primary to accent
                          WebkitBackgroundClip: 'text',
                          backgroundClip: 'text',
                          lineHeight: '1.2'
                        }}
                  >
                    {t('heroSubtitle')}
                  </span>
                </h1>
                <p className="mt-4 sm:mt-6 max-w-3xl mx-auto text-md lg:text-xl leading-relaxed"
                   style={{ color: '#475569' }} // base-content-light
                >
                  {t('heroDescription')}
                </p>

                {/* Boat Search Form */}
                <div className="mt-6 sm:mt-10 max-w-6xl mx-auto px-4 md:px-3">
                  <BoatSearchForm variant="hero" />
                </div>


              </div>
            </div>

            {/* Background Decoration - Fixed z-index to prevent overlay issues */}
            <div className="absolute top-0 right-0 -mt-20 -mr-20 w-80 h-80 rounded-full blur-3xl -z-10"
                 style={{ backgroundColor: 'rgba(14, 165, 233, 0.1)' }} // primary with opacity
            ></div>
            <div className="absolute bottom-0 left-0 -mb-20 -ml-20 w-80 h-80 rounded-full blur-3xl -z-10"
                 style={{ backgroundColor: 'rgba(6, 182, 212, 0.1)' }} // accent with opacity
            ></div>
          </section>

          {/* Top 3 Boat Providers Section */}
          <section className="py-10 lg:py-16 bg-white">
            <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-8 md:mb-12">
                <h2 className="text-2xl font-bold sm:text-3xl"
                    style={{ color: '#0f172a' }}
                >
                  {t('topBoatProviders')}
                </h2>
                <p className="mt-3 text-md lg:text-lg max-w-2xl mx-auto"
                   style={{ color: '#64748b' }}
                >
                  {t('trustedProvidersDescription')}
                </p>
              </div>

              {/* Mobile: Horizontal scroll with arrows, Desktop: Grid */}
              <div className="relative">
                {isLoadingProviders ? (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="bg-white rounded-lg shadow-sm border animate-pulse">
                        <div className="w-full h-48 bg-gray-200 rounded-t-lg"></div>
                        <div className="p-4">
                          <div className="h-6 bg-gray-200 rounded mb-2"></div>
                          <div className="h-4 bg-gray-200 rounded mb-3"></div>
                          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <>
                    {/* Desktop Grid */}
                    <div className="hidden md:grid md:grid-cols-3 gap-6 md:gap-8">
                      {topProviders.map((provider) => (
                        <ProviderCard
                          key={provider.id}
                          provider={provider}
                          searchFilters={{}} // No search filters for homepage display
                        />
                      ))}
                    </div>

                    {/* Mobile Horizontal Scroll */}
                    <div className="md:hidden relative">
                      <div
                        ref={setProvidersScrollRef}
                        className="flex gap-4 overflow-x-auto pb-4 snap-x snap-mandatory scrollbar-hide"
                        onScroll={checkProvidersScrollButtons}
                      >
                        <style jsx>{`
                          .scrollbar-hide {
                            -ms-overflow-style: none;
                            scrollbar-width: none;
                          }
                          .scrollbar-hide::-webkit-scrollbar {
                            display: none;
                          }
                        `}</style>
                        {topProviders.map((provider) => (
                          <div key={provider.id} className="flex-shrink-0 w-80 snap-center">
                            <ProviderCard
                              provider={provider}
                              searchFilters={{}} // No search filters for homepage display
                            />
                          </div>
                        ))}
                      </div>

                      {/* Left Arrow */}
                      {showProvidersLeftArrow && (
                        <button
                          onClick={scrollProvidersLeft}
                          className="absolute left-2 top-1/2 -translate-y-1/2 -mt-12 z-10 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-lg border border-gray-200 hover:bg-white transition-all duration-200"
                          aria-label="Scroll left"
                        >
                          <ChevronLeft className="w-5 h-5 text-gray-600" />
                        </button>
                      )}

                      {/* Right Arrow */}
                      {showProvidersRightArrow && (
                        <button
                          onClick={scrollProvidersRight}
                          className="absolute right-2 top-1/2 -translate-y-1/2 -mt-12 z-10 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-lg border border-gray-200 hover:bg-white transition-all duration-200"
                          aria-label="Scroll right"
                        >
                          <ChevronRight className="w-5 h-5 text-gray-600" />
                        </button>
                      )}
                    </div>
                  </>
                )}
              </div>
            </div>
          </section>

          {/* Role-Specific Sections - Minimalist Design */}
          <section id="user-types" className="py-10 lg:py-16" style={{ backgroundColor: '#f0f9ff' }}> {/* Clean white background */}
            <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-6 md:mb-12">
                <h2 className="text-2xl font-bold sm:text-3xl"
                    style={{ color: '#0f172a' }}
                >
                  {t('perfectForEveryone')}
                </h2>
                <p className="mt-3 text-md lg:text-lg max-w-2xl mx-auto"
                   style={{ color: '#64748b' }}
                >
                  {t('featuresDescription')}
                </p>
              </div>

              {/* Mobile: Horizontal scroll with arrows, Desktop: Grid */}
              <div className="relative">
                <div className="md:grid md:grid-cols-3 md:gap-12">
                  <div
                    ref={setRoleScrollRef}
                    className="flex md:contents gap-4 md:gap-0 overflow-x-auto pb-4 md:pb-0 snap-x snap-mandatory scrollbar-hide"
                    onScroll={checkRoleScrollButtons}
                  >
                  <style jsx>{`
                    .scrollbar-hide {
                      -ms-overflow-style: none;
                      scrollbar-width: none;
                    }
                    .scrollbar-hide::-webkit-scrollbar {
                      display: none;
                    }
                  `}</style>
                  {/* For Customers */}
                  <div className="text-center group p-6 rounded-2xl bg-white shadow-sm border border-gray-100 hover:shadow-md transition-shadow flex-shrink-0 w-80 md:w-auto snap-center">
                  <div className="w-16 h-16 flex items-center justify-center mx-auto mb-3 transition-transform group-hover:scale-110 bg-sky-50 rounded-full">
                    <TbScubaMask className="w-8 h-8 text-sky-500" />
                  </div>
                  <h3 className="text-base font-semibold mb-2" style={{ color: '#0f172a' }}>
                    {t('adventureSeekers')}
                  </h3>
                  <p className="text-base mb-4" style={{ color: '#64748b' }}>
                    {t('customerDescription')}
                  </p>
                  <ul className="text-sm mb-8 space-y-1" style={{ color: '#64748b' }}>
                    <li className="flex items-center gap-1 justify-center">
                      <span style={{ color: '#10b981' }}>✓</span>
                      <span>{t('browseOperators')}</span>
                    </li>
                    <li className="flex items-center gap-1 justify-center">
                      <span style={{ color: '#10b981' }}>✓</span>
                      <span>{t('realTimeAvailability')}</span>
                    </li>
                    <li className="flex items-center gap-1 justify-center">
                      <span style={{ color: '#10b981' }}>✓</span>
                      <span>{t('securePayment')}</span>
                    </li>
                    <li className="flex items-center gap-1 justify-center">
                      <span style={{ color: '#10b981' }}>✓</span>
                      <span>{t('support247')}</span>
                    </li>
                  </ul>
                  <button
                    onClick={handleBookAdventure}
                    className="text-sm font-medium py-2 px-4 rounded-md transition-colors"
                    style={{
                      backgroundColor: '#f59e0b',
                      color: '#ffffff',
                      border: 'none'
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.backgroundColor = '#d97706';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.backgroundColor = '#f59e0b';
                    }}
                  >
                    {t('startAdventure')}
                  </button>
                  </div>

                  {/* For Boat Owners */}
                  <div className="text-center group p-6 rounded-2xl bg-white shadow-sm border border-gray-100 hover:shadow-md transition-shadow flex-shrink-0 w-80 md:w-auto snap-center">
                  <div className="w-16 h-16 flex items-center justify-center mx-auto mb-3 transition-transform group-hover:scale-110 bg-sky-50 rounded-full">
                    <Ship className="w-8 h-8 text-sky-500" />
                  </div>
                  <h3 className="text-base font-semibold mb-2" style={{ color: '#0f172a' }}>
                    {t('boatOwners')}
                  </h3>
                  <p className="text-base mb-4" style={{ color: '#64748b' }}>
                    {t('ownerDescription')}
                  </p>
                  <ul className="text-sm mb-8 space-y-1" style={{ color: '#64748b' }}>
                    <li className="flex items-center gap-1 justify-center">
                      <span style={{ color: '#10b981' }}>✓</span>
                      <span>{t('easyListing')}</span>
                    </li>
                    <li className="flex items-center gap-1 justify-center">
                      <span style={{ color: '#10b981' }}>✓</span>
                      <span>{t('automatedBooking')}</span>
                    </li>
                    <li className="flex items-center gap-1 justify-center">
                      <span style={{ color: '#10b981' }}>✓</span>
                      <span>{t('revenueTracking')}</span>
                    </li>
                    <li className="flex items-center gap-1 justify-center">
                      <span style={{ color: '#10b981' }}>✓</span>
                      <span>{t('marketingTools')}</span>
                    </li>
                  </ul>
                  <Link
                    href="/boat-owner/signup"
                    className="text-sm font-medium py-2 px-4 rounded-md transition-colors inline-flex items-center justify-center"
                    style={{
                      backgroundColor: '#f59e0b',
                      color: '#ffffff',
                      textDecoration: 'none'
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.backgroundColor = '#d97706';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.backgroundColor = '#f59e0b';
                    }}
                  >
                    {t('listYourBoat')}
                  </Link>
                  </div>

                  {/* For Affiliates */}
                  <div className="text-center group p-6 rounded-2xl bg-white shadow-sm border border-gray-100 hover:shadow-md transition-shadow flex-shrink-0 w-80 md:w-auto snap-center">
                  <div className="w-16 h-16 flex items-center justify-center mx-auto mb-3 transition-transform group-hover:scale-110 bg-sky-50 rounded-full">
                    <Handshake className="w-8 h-8 text-sky-500" />
                  </div>
                  <h3 className="text-base font-semibold mb-2" style={{ color: '#0f172a' }}>
                    {t('affiliatePartners')}
                  </h3>
                  <p className="text-base mb-4" style={{ color: '#64748b' }}>
                    {t('affiliateDescription')}
                  </p>
                  <ul className="text-sm mb-8 space-y-1" style={{ color: '#64748b' }}>
                    <li className="flex items-center gap-1 justify-center">
                      <span style={{ color: '#10b981' }}>✓</span>
                      <span>{t('competitiveRates')}</span>
                    </li>
                    <li className="flex items-center gap-1 justify-center">
                      <span style={{ color: '#10b981' }}>✓</span>
                      <span>{t('marketingMaterials')}</span>
                    </li>
                    <li className="flex items-center gap-1 justify-center">
                      <span style={{ color: '#10b981' }}>✓</span>
                      <span>{t('earningsTracking')}</span>
                    </li>
                    <li className="flex items-center gap-1 justify-center">
                      <span style={{ color: '#10b981' }}>✓</span>
                      <span>{t('partnerSupport')}</span>
                    </li>
                  </ul>
                  <Link
                    href="/agent"
                    className="text-sm font-medium py-2 px-4 rounded-md transition-colors inline-flex items-center justify-center"
                    style={{
                      backgroundColor: '#f59e0b',
                      color: '#ffffff',
                      textDecoration: 'none'
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.backgroundColor = '#d97706';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.backgroundColor = '#f59e0b';
                    }}
                  >
                    {t('becomePartner')}
                  </Link>
                  </div>
                </div>
              </div>

              {/* Mobile Arrow Navigation */}
                <div className="md:hidden">
                  {/* Left Arrow */}
                  {showRoleLeftArrow && (
                    <button
                      onClick={scrollRoleLeft}
                      className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-lg border border-gray-200 hover:bg-white transition-all duration-200"
                      aria-label="Scroll left"
                    >
                      <ChevronLeft className="w-5 h-5 text-gray-600" />
                    </button>
                  )}

                  {/* Right Arrow */}
                  {showRoleRightArrow && (
                    <button
                      onClick={scrollRoleRight}
                      className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-lg border border-gray-200 hover:bg-white transition-all duration-200"
                      aria-label="Scroll right"
                    >
                      <ChevronRight className="w-5 h-5 text-gray-600" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </section>




        </main>

        <Footer />

        {/* Authentication Modals */}
        <SignInModal
          isOpen={showSignInModal}
          onClose={() => setShowSignInModal(false)}
          onSwitchToSignUp={switchToSignUp}
          onLoginSuccess={() => {
            // Check if there's a post-auth redirect
            const redirectUrl = sessionStorage.getItem('gosea_post_auth_redirect');
            if (redirectUrl) {
              sessionStorage.removeItem('gosea_post_auth_redirect');
              router.push(redirectUrl);
            }
            // Otherwise user stays on home page
          }}
        />
        <SignUpModal
          isOpen={showSignUpModal}
          onClose={() => setShowSignUpModal(false)}
          onSwitchToSignIn={switchToSignIn}
          onSignUpSuccess={() => {
            // Check if there's a post-auth redirect
            const redirectUrl = sessionStorage.getItem('gosea_post_auth_redirect');
            if (redirectUrl) {
              sessionStorage.removeItem('gosea_post_auth_redirect');
              router.push(redirectUrl);
            }
            // Otherwise user stays on home page
          }}
        />

        {/* Profile Completion Modal */}
        {showProfileCompletion && (
          <ProfileCompletionModal
            isOpen={showProfileCompletion}
            onClose={() => {
              setShowProfileCompletion(false);
              setIsMandatoryProfileCompletion(false);
              // Mark as seen so it doesn't show again
              // Use the same reliable key we used for checking
              const localStorageKey = user.id 
                ? `gosea_profile_completion_${user.id}`
                : user.email 
                  ? `gosea_profile_completion_${btoa(user.email)}`
                  : `gosea_profile_completion_unknown`;
              
              localStorage.setItem(localStorageKey, "true");
            }}
            user={user}
            mandatory={isMandatoryProfileCompletion}
          />
        )}
      </div>
    </>
  );
}
