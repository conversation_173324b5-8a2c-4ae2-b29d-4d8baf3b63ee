import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import Link from "next/link";
import { useAuth } from "../contexts/AuthContext";
import { useLanguage } from "../contexts/LanguageContext";
import Navbar from "../components/Navbar";
import ProfileCompletionModal from "../components/ProfileCompletionModal";
import Footer from "../components/Footer";
import { Pencil } from "lucide-react";
import { PencilIcon } from '@heroicons/react/24/outline';

/**
 * Profile Page for GoSea Platform
 * Protected route that displays comprehensive user profile information
 */

// Utility function to construct proper image URL
const getImageUrl = (imagePath) => {
  if (!imagePath) return null;

  // If it's already a full URL (Google profile pictures), return as is
  if (imagePath.startsWith("http")) {
    return imagePath;
  }

  // If it's a relative path, construct the full URL
  if (imagePath.startsWith("/uploads/")) {
    return `${process.env.NEXT_PUBLIC_API_URL}${imagePath}`;
  }

  return imagePath;
};

// Utility function to format names with proper capitalization
const formatName = (name) => {
  if (!name) return "";
  return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();
};

// Utility function to format dates
const formatDate = (dateString) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

// Utility function to format dates with time
const formatDateTime = (dateString) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  return date.toLocaleString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });
};

// Utility function to format address
const formatAddress = (profile) => {
  if (!profile) return "";
  const parts = [];
  if (profile.personalAddress1) parts.push(profile.personalAddress1);
  if (profile.personalAddress2) parts.push(profile.personalAddress2);
  if (profile.personalCity) parts.push(profile.personalCity);
  if (profile.personalPostcode) parts.push(profile.personalPostcode);
  if (profile.personalState) parts.push(profile.personalState);
  return parts.join(", ");
};

// Utility function to display "Not provided" for empty values
const displayValue = (value) => {
  return value || "Not provided";
};

const ProfilePage = () => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading, getCurrentUser } = useAuth();
  const { t } = useLanguage();
  const [showProfileCompletion, setShowProfileCompletion] = useState(false);

  // Fetch latest user data when component mounts
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      console.log("Profile page: Fetching latest user data");
      getCurrentUser().then((userData) => {
        console.log("Profile page: Received user data:", userData);
      });
    }
  }, [isAuthenticated, isLoading, getCurrentUser]);

  // Redirect unauthenticated users (but not during logout)
  useEffect(() => {
    const isLoggingOut = sessionStorage.getItem("isLoggingOut") === "true";

    if (!isLoading && !isAuthenticated && !isLoggingOut) {
      router.push("/");
    }
  }, [isAuthenticated, isLoading, router]);

  // Show loading state
  if (isLoading) {
    return (
      <div
        className="min-h-screen flex items-center justify-center"
        style={{ backgroundColor: "#f8fafc" }}
      >
        <div className="text-center">
          <div
            className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4"
            style={{ borderColor: "#0ea5e9" }}
          ></div>
          <p style={{ color: "#64748b" }}>{t("loading")}</p>
        </div>
      </div>
    );
  }

  // Don't render if not authenticated
  if (!isAuthenticated || !user) {
    return null;
  }

  const handleLogout = async () => {
    sessionStorage.setItem("isLoggingOut", "true");
    await logout();
    sessionStorage.removeItem("isLoggingOut");
    router.push("/");
  };

  return (
    <>
      <Head>
        <title>Profile - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" type="image/png" href="gosea_favicon.png" />
      </Head>

      <div className="min-h-screen" style={{ backgroundColor: "#f8fafc" }}>
        {/* Unified Navigation */}
        <Navbar currentPage="profile" />

        {/* Main Content */}
        <main className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          <div className="px-4 py-4 sm:py-8 sm:px-0">
            {/* Page Header */}
            <div className="mb-4 md:mb-6">
              <div className="flex justify-between items-center">
                <div>
                  <h1 className="text-xl md:text-2xl font-bold" style={{ color: "#0f172a" }}>
                    {t("userProfile")}
                  </h1>
                </div>
                <button
                  onClick={() => router.push("/profile/edit")}
                  className="flex items-center justify-center px-3 py-2 text-white bg-sky-500 rounded-md hover:bg-sky-600 active:bg-sky-600 active:text-white transition-colors font-medium text-sm touch-manipulation w-auto"
                  style={{
                    WebkitTapHighlightColor: 'transparent',
                    touchAction: 'manipulation',
                    cursor: 'pointer',
                    userSelect: 'none'
                  }}
                >
                  <PencilIcon className="h-4 w-4 mr-1" />
                  {t('edit')}
                </button>
              </div>
            </div>

            {/* Profile Information Card */}
            <div
              className="rounded-2xl p-8 shadow-lg border pb-14 mb-8"
              style={{
                backgroundColor: "#ffffff",
                borderColor: "#e2e8f0",
              }}
            >
              <div className="flex items-center mb-8">
                {user?.profile?.profilePicture ? (
                  <img
                    src={getImageUrl(user.profile.profilePicture)}
                    alt="Profile"
                    className="w-16 h-16 rounded-full object-cover mr-6"
                    style={{ border: "2px solid #e2e8f0" }}
                  />
                ) : (
                  <div
                    className="w-16 h-16 rounded-full flex items-center justify-center text-xl font-bold mr-6"
                    style={{
                      backgroundColor: "#f59e0b",
                      color: "#ffffff",
                    }}
                  >
                    {user?.profile?.firstName && user?.profile?.lastName
                      ? `${user.profile.firstName.charAt(0).toUpperCase()}${user.profile.lastName.charAt(0).toUpperCase()}`
                      : user?.email?.charAt(0).toUpperCase() || "U"}
                  </div>
                )}
                <div>
                  <h2
                    className="text-2xl font-bold"
                    style={{ color: "#0f172a" }}
                  >
                    {user?.profile?.firstName && user?.profile?.lastName
                      ? `${formatName(user.profile.firstName)} ${formatName(user.profile.lastName)}`
                      : user?.email}
                  </h2>
                  <p className="text-lg mt-1" style={{ color: "#64748b" }}>
                    {user?.email}
                  </p>
                </div>
              </div>

              {/* Divider between profile header and information sections */}
              <div className="border-t border-gray-200 my-6"></div>

              {/* Profile Details Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Information */}
                <div className="space-y-6">
                  <h3
                    className="text-lg font-semibold"
                    style={{ color: "#0f172a" }}
                  >
                    {t("basicInformation")}
                  </h3>

                  <div>
                    <p
                      className="text-sm font-medium"
                      style={{ color: "#64748b" }}
                    >
                      {t("phoneNumber")}
                    </p>
                    <p className="text-lg" style={{ color: "#0f172a" }}>
                      {displayValue(user?.profile?.phone)}
                    </p>
                  </div>

                  <div>
                    <p
                      className="text-sm font-medium"
                      style={{ color: "#64748b" }}
                    >
                      {t("dateOfBirth")}
                    </p>
                    <p className="text-lg" style={{ color: "#0f172a" }}>
                      {user?.profile?.dateOfBirth ? formatDate(user.profile.dateOfBirth) : "Not provided"}
                    </p>
                  </div>

                  <div>
                    <p
                      className="text-sm font-medium"
                      style={{ color: "#64748b" }}
                    >
                      {t("address")}
                    </p>
                    <p className="text-lg" style={{ color: "#0f172a" }}>
                      {formatAddress(user?.profile) || "Not provided"}
                    </p>
                  </div>

                  <div>
                    <p
                      className="text-sm font-medium"
                      style={{ color: "#64748b" }}
                    >
                      {t("emergencyContact")}
                    </p>
                    <p className="text-lg" style={{ color: "#0f172a" }}>
                      {displayValue(user?.profile?.emergencyContact)}
                    </p>
                  </div>
                </div>

                {/* Account Information */}
                <div className="space-y-6">
                  <h3
                    className="text-lg font-semibold"
                    style={{ color: "#0f172a" }}
                  >
                    {t("accountInformation")}
                  </h3>

                  <div>
                    <p
                      className="text-sm font-medium"
                      style={{ color: "#64748b" }}
                    >
                      {t("role")}
                    </p>
                    <p className="text-lg" style={{ color: "#0f172a" }}>
                      {t(
                        user?.role?.toLowerCase()?.replace("_", "") ||
                          "customer"
                      )}
                    </p>
                  </div>

                  <div>
                    <p
                      className="text-sm font-medium"
                      style={{ color: "#64748b" }}
                    >
                      {t("verificationStatus")}
                    </p>
                    <p className={`text-lg ${user?.emailVerified ? 'text-emerald-500' : 'text-gray-900'}`}>
                      {user?.emailVerified
                        ? t("verified")
                        : t("pendingVerification")}
                    </p>
                  </div>

                  <div>
                    <p
                      className="text-sm font-medium"
                      style={{ color: "#64748b" }}
                    >
                      {t("memberSince")}
                    </p>
                    <p className="text-lg" style={{ color: "#0f172a" }}>
                      {formatDateTime(user?.createdAt)}
                    </p>
                  </div>

                  <div>
                    <p
                      className="text-sm font-medium"
                      style={{ color: "#64748b" }}
                    >
                      {t("lastLogin")}
                    </p>
                    <p className="text-lg" style={{ color: "#0f172a" }}>
                      {user?.lastLoginAt ? formatDateTime(user.lastLoginAt) : "Not provided"}
                    </p>
                  </div>

                  <div>
                    <p
                      className="text-sm font-medium"
                      style={{ color: "#64748b" }}
                    >
                      {t("language")}
                    </p>
                    <p className="text-lg" style={{ color: "#0f172a" }}>
                      {user?.profile?.language === "ms"
                        ? "Bahasa Melayu"
                        : "English"}
                    </p>
                  </div>

                  {/* Business Information for Boat Owners - Removed as requested */}

                  {/* Agency Information for Affiliate Agents */}
                  {user?.role === "AFFILIATE_AGENT" && (
                    <>
                      <h3
                        className="text-lg font-semibold mt-8"
                        style={{ color: "#0f172a" }}
                      >
                        {t("agencyInformation")}
                      </h3>

                      <div>
                        <p
                          className="text-sm font-medium"
                          style={{ color: "#64748b" }}
                        >
                          {t("agencyName")}
                        </p>
                        <p className="text-lg" style={{ color: "#0f172a" }}>
                          {displayValue(user?.profile?.agencyName)}
                        </p>
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Action Buttons - Removed as edit button is now inline with header */}
              {/* <div className="mt-8 pt-6 border-t border-gray-200 flex justify-end">
                <button
                  onClick={() => router.push("/profile/edit")}
                  className="px-6 py-3 rounded-lg font-medium transition-colors"
                  style={{
                    backgroundColor: "#0ea5e9",
                    color: "#ffffff",
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = "#0284c7";
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = "#0ea5e9";
                  }}
                >
                  {t("editProfile")}
                </button>
              </div> */}
            </div>
          </div>
        </main>

        {/* Footer */}
        <Footer />
      </div>

      {/* Profile Completion Modal */}
      {showProfileCompletion && (
        <ProfileCompletionModal
          isOpen={showProfileCompletion}
          onClose={() => setShowProfileCompletion(false)}
        />
      )}
    </>
  );
};

export default ProfilePage;
