import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import Link from "next/link";
import { useAuth } from "../../contexts/AuthContext";
import { useLanguage } from "../../contexts/LanguageContext";
import { validateMalaysianPhoneNumber } from "../../utils/validation";
import Navbar from "../../components/Navbar";
import CustomDatePicker from "../../components/CustomDatePicker";
import StandardModal from "../../components/StandardModal";
import { XMarkIcon } from "@heroicons/react/24/outline";
import Footer from "../../components/Footer";


/**
 * Dedicated Profile Update Page for GoSea Platform
 * Separate from ProfileCompletionModal - used for editing existing profiles
 */

// Utility function to construct proper image URL
const getImageUrl = (imagePath) => {
  if (!imagePath) return null;

  // If it's already a full URL (Google profile pictures), return as is
  if (imagePath.startsWith("http")) {
    return imagePath;
  }

  // If it's a relative path, construct the full URL
  if (imagePath.startsWith("/uploads/")) {
    return `${process.env.NEXT_PUBLIC_API_URL}${imagePath}`;
  }

  return imagePath;
};

const ProfileEditPage = () => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading, getCurrentUser, accessToken } =
    useAuth();
  const { t, language, changeLanguage } = useLanguage();

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    phone: "",
    dateOfBirth: "",
    address1: "",
    address2: "",
    postcode: "",
    city: "",
    state: "",
    emergencyContact: "",
    profilePicture: "",
    language: "en",
  });

  const [profileImage, setProfileImage] = useState(null);
  const [profileImagePreview, setProfileImagePreview] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});
  const [imageRefreshKey, setImageRefreshKey] = useState(0); // Force image refresh

  // Modal states
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackModal, setFeedbackModal] = useState({
    type: "success",
    title: "",
    message: "",
    details: null,
  });

  // Helper functions for showing modals
  const showSuccessModal = (title, message, details = null) => {
    setFeedbackModal({
      type: "success",
      title,
      message,
      details,
    });
    setShowFeedbackModal(true);
  };

  const showErrorModal = (title, message, details = null) => {
    setFeedbackModal({
      type: "error",
      title,
      message,
      details,
    });
    setShowFeedbackModal(true);
  };

  const closeFeedbackModal = () => {
    setShowFeedbackModal(false);
  };

  // Populate form with existing user data
  useEffect(() => {
    if (user && user.profile) {
      // Helper function to format date without timezone issues
      const formatDateForForm = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
      };

      // Convert dateOfBirth to string format if it exists
      let dateOfBirthValue = "";
      if (user.profile.dateOfBirth) {
        if (user.profile.dateOfBirth instanceof Date) {
          dateOfBirthValue = formatDateForForm(user.profile.dateOfBirth);
        } else if (typeof user.profile.dateOfBirth === "string") {
          // If it's already a string, try to parse and reformat to ensure consistency
          const parsedDate = new Date(user.profile.dateOfBirth);
          if (!isNaN(parsedDate.getTime())) {
            dateOfBirthValue = formatDateForForm(parsedDate);
          } else {
            dateOfBirthValue = user.profile.dateOfBirth;
          }
        }
      }

      setFormData({
        firstName: user.profile.firstName || "",
        lastName: user.profile.lastName || "",
        phone: user.profile.phone || "",
        dateOfBirth: dateOfBirthValue,
        address1: user.profile.personalAddress1 || "",
        address2: user.profile.personalAddress2 || "",
        postcode: user.profile.personalPostcode || "",
        city: user.profile.personalCity || "",
        state: user.profile.personalState || "",
        emergencyContact: user.profile.emergencyContact || "",
        profilePicture: user.profile.profilePicture || "",
        language: user.profile.language || language || "en",
      });

      // Set profile image preview if exists
      if (user.profile.profilePicture) {
        setProfileImagePreview(getImageUrl(user.profile.profilePicture));
      }
    }
  }, [user]);

  // Redirect unauthenticated users
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/");
    }
  }, [isAuthenticated, isLoading, router]);

  // Add a timeout ref for postcode lookup debouncing
  const postcodeLookupTimeout = React.useRef(null);
  
  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (postcodeLookupTimeout.current) {
        clearTimeout(postcodeLookupTimeout.current);
      }
    };
  }, []);
  
  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
    
    // Handle postcode auto-lookup
    if (name === "postcode" && value.length >= 4) {
      // Clear any existing timeout
      if (postcodeLookupTimeout.current) {
        clearTimeout(postcodeLookupTimeout.current);
      }
      
      // Set new timeout to debounce the lookup
      postcodeLookupTimeout.current = setTimeout(() => {
        lookupPostcode(value);
      }, 500);
    }
  };
  
  // Lookup postcode and auto-fill city and state
  const lookupPostcode = async (postcode) => {
    if (!postcode || postcode.length < 4) return;
    
    try {
      const response = await fetch(`/api/search/postcode/${postcode}`);
      const data = await response.json();
      
      if (data.success && data.data) {
        setFormData((prev) => ({
          ...prev,
          city: data.data.city || prev.city,
          state: data.data.state || prev.state,
        }));
      }
    } catch (error) {
      console.error("Postcode lookup error:", error);
      // Don't show error to user as this is a convenience feature
    }
  };

  // Handle profile image upload
  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      const validTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
      if (!validTypes.includes(file.type)) {
        setErrors((prev) => ({
          ...prev,
          profileImage: "Please select a valid image file (JPG, PNG, WebP)",
        }));
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setErrors((prev) => ({
          ...prev,
          profileImage: "Image size must be less than 5MB",
        }));
        return;
      }

      setProfileImage(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfileImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);

      // Clear any previous errors
      setErrors((prev) => ({
        ...prev,
        profileImage: "",
      }));
    }
  };

  // Remove profile image
  const removeProfileImage = () => {
    setProfileImage(null);
    setProfileImagePreview(getImageUrl(formData.profilePicture) || null);
    // Reset file input
    const fileInput = document.getElementById("profileImageInput");
    if (fileInput) {
      fileInput.value = "";
    }
  };

  // Form validation
  const validateForm = () => {
    const newErrors = {};

    // Required fields validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = t("firstNameRequired");
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = t("lastNameRequired");
    }

    // Phone number validation (optional but must be valid if provided)
    if (formData.phone && !validateMalaysianPhoneNumber(formData.phone)) {
      newErrors.phone = t("invalidPhoneNumber");
    }

    // Emergency contact validation (optional but must be valid if provided)
    if (
      formData.emergencyContact &&
      !validateMalaysianPhoneNumber(formData.emergencyContact)
    ) {
      newErrors.emergencyContact = t("invalidEmergencyContact");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    console.log("Form submission started");
    console.log("Form data:", formData);

    if (!validateForm()) {
      console.log("Form validation failed");
      return;
    }

    console.log("Form validation passed, submitting...");
    setIsSubmitting(true);

    try {
      // Strip trailing commas from address fields before saving
      const cleanFormData = {
        ...formData,
        address1: formData.address1?.replace(/,\s*$/, '') || '',
        address2: formData.address2?.replace(/,\s*$/, '') || ''
      };

      // Create FormData for file upload
      const submitData = new FormData();

      // Add form fields (including empty values to allow clearing fields)
      Object.keys(cleanFormData).forEach((key) => {
        if (key !== "profilePicture") {
          let value = cleanFormData[key] || "";

          // Special handling for dateOfBirth to ensure it's a string without timezone issues
          if (key === "dateOfBirth" && value) {
            if (typeof value === "string") {
              // If it's already in YYYY-MM-DD format, use it directly
              if (value.match(/^\d{4}-\d{2}-\d{2}$/)) {
                value = value;
              } else {
                // Parse and format to avoid timezone issues
                const dateParts = value.split("-");
                if (dateParts.length === 3) {
                  // Create date using local timezone
                  const date = new Date(
                    parseInt(dateParts[0]),
                    parseInt(dateParts[1]) - 1,
                    parseInt(dateParts[2])
                  );
                  if (!isNaN(date.getTime())) {
                    value = formatDateForForm(date);
                  } else {
                    console.warn("Invalid date string:", value);
                    value = "";
                  }
                } else {
                  console.warn("Invalid date format:", value);
                  value = "";
                }
              }
            } else if (value instanceof Date) {
              // For Date objects, format to YYYY-MM-DD in local timezone
              value = formatDateForForm(value);
            } else if (typeof value === "object") {
              console.warn(
                "dateOfBirth is an object, converting to empty string:",
                value
              );
              value = "";
            }
          }

          submitData.append(key, value);
        }
      });

      // Add profile image if uploaded
      if (profileImage) {
        submitData.append("profileImage", profileImage);
      }

      console.log("Making PUT request to /api/auth/profile");
      console.log("Access token:", accessToken ? "Present" : "Missing");

      const response = await fetch("/api/auth/profile", {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        body: submitData,
      });

      console.log("Response received:", response.status, response.statusText);

      // First check if the response is JSON
      const contentType = response.headers.get("content-type");
      if (!contentType || !contentType.includes("application/json")) {
        throw new Error("Server response was not in JSON format");
      }

      const data = await response.json();

      if (data.success) {
        // Refresh user data first to ensure context is updated
        await getCurrentUser();

        // Update form data with the response data
        if (data.profile) {
          setFormData((prev) => ({
            ...prev,
            profilePicture: data.profile.profilePicture || "",
          }));

          // Update profile image preview if a new image was uploaded
          if (data.profile.profilePicture) {
            setProfileImagePreview(getImageUrl(data.profile.profilePicture));
            setProfileImage(null); // Clear the file input state
            setImageRefreshKey((prev) => prev + 1); // Force image refresh across all components
          }
        }

        // Apply language preference change if it was updated (do this before showing modal)
        if (formData.language && formData.language !== language) {
          console.log(
            "Changing language from",
            language,
            "to",
            formData.language
          );
          changeLanguage(formData.language);

          // Wait a moment for language change to take effect
          await new Promise((resolve) => setTimeout(resolve, 100));
        }

        // Show success modal
        showSuccessModal(
          t("profileUpdatedSuccessfully"),
          "Your profile has been updated successfully. You will be redirected to your profile page.",
          null
        );

        // Redirect to profile page after a short delay
        setTimeout(() => {
          router.push("/profile");
        }, 3000);
      } else {
        showErrorModal(
          "Update Failed",
          data.message || t("updateProfileError"),
          data.error || null
        );
      }
    } catch (error) {
      console.error("Profile update error:", error);

      // Handle different types of errors
      let errorTitle = "Update Failed";
      let errorMessage = t("updateProfileError");

      if (error.message === "Server response was not in JSON format") {
        errorTitle = "Server Error";
        errorMessage = "Server error occurred. Please try again.";
      } else if (error instanceof SyntaxError) {
        errorTitle = "Response Error";
        errorMessage = "Invalid response from server. Please try again.";
      } else if (error.message === "Profile not found") {
        errorTitle = "Profile Not Found";
        errorMessage = "Profile not found. Please try refreshing the page.";
      }

      showErrorModal(errorTitle, errorMessage, error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    router.back();
  };

  // Show loading state
  if (isLoading) {
    return (
      <div
        className="min-h-screen flex items-center justify-center"
        style={{ backgroundColor: "#f8fafc" }}
      >
        <div className="text-center">
          <div
            className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4"
            style={{ borderColor: "#0ea5e9" }}
          ></div>
          <p style={{ color: "#64748b" }}>{t("loading")}</p>
        </div>
      </div>
    );
  }

  // Don't render if not authenticated
  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Edit Profile - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" type="image/png" href="gosea_favicon.png" />
      </Head>

      <div className="min-h-screen" style={{ backgroundColor: "#f8fafc" }}>
        <Navbar currentPage="profile-edit" />

        {/* Main Content */}
        <main className="max-w-4xl mx-auto sm:px-6 lg:px-8">
          <div className="px-4 py-4 sm:py-8 sm:px-0">
            {/* Page Header */}
            <div className="mb-4 md:mb-6">
                <h1 className="text-xl md:text-2xl font-bold" style={{ color: "#0f172a" }}>
                {t("editProfile")}
              </h1>
              {/* <p className="mt-2 text-lg" style={{ color: "#64748b" }}>
                {t("updateYourProfileInformation")}
              </p> */}
            </div>

            {/* Profile Edit Form */}
            <div
              className="rounded-2xl p-8 shadow-lg border"
              style={{ backgroundColor: "#ffffff", borderColor: "#e2e8f0" }}
            >
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Profile Image Section */}
                <div className="flex flex-col items-center space-y-4">
                  <div className="relative">
                    {profileImagePreview ? (
                      <div className="relative">
                        <img
                          src={profileImagePreview}
                          alt=""
                          className="w-32 h-32 rounded-full object-cover border-4"
                          style={{ borderColor: "#e2e8f0" }}
                        />
                        <button
                          type="button"
                          onClick={removeProfileImage}
                          className="absolute -top-2 -right-2 p-1 rounded-full shadow-lg"
                          style={{
                            backgroundColor: "#ef4444",
                            color: "#ffffff",
                          }}
                          title="Remove image"
                        >
                          <XMarkIcon className="w-4 h-4" />
                        </button>
                      </div>
                    ) : (
                      <div
                        className="w-32 h-32 rounded-full border-4 flex items-center justify-center text-4xl font-bold text-white cursor-pointer hover:opacity-80 transition-opacity"
                        style={{
                          backgroundColor: "#f59e0b",
                          borderColor: "#e2e8f0",
                        }}
                        onClick={() => fileInputRef.current?.click()}
                        title="Click to upload photo"
                      >
                        {user?.profile?.firstName && user?.profile?.lastName
                          ? `${user.profile.firstName.charAt(0).toUpperCase()}${user.profile.lastName.charAt(0).toUpperCase()}`
                          : user?.email?.charAt(0).toUpperCase() || "U"}
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col items-center space-y-2">
                    <label
                      htmlFor="profileImageInput"
                      className="px-4 py-2 rounded-lg font-medium cursor-pointer transition-colors"
                      style={{ backgroundColor: "#f1f5f9", color: "#475569" }}
                    >
                      {profileImagePreview
                        ? t("changePhoto")
                        : t("uploadPhoto")}
                    </label>
                    <input
                      id="profileImageInput"
                      type="file"
                      accept="image/jpeg,image/jpg,image/png,image/webp"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                    <p className="text-sm" style={{ color: "#64748b" }}>
                      {t("supportedFormats")}: JPG, PNG, WebP (Max 5MB)
                    </p>
                  </div>

                  {errors.profileImage && (
                    <p className="text-sm" style={{ color: "#dc2626" }}>
                      {errors.profileImage}
                    </p>
                  )}
                </div>

                {/* Personal Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* First Name */}
                  <div>
                    <label
                      className="block text-sm font-medium mb-2"
                      style={{ color: "#374151" }}
                    >
                      {t("firstName")}{" "}
                      <span style={{ color: "#dc2626" }}>*</span>
                    </label>
                    <input
                      type="text"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                      style={{
                        borderColor: errors.firstName ? "#dc2626" : "#d1d5db",
                        backgroundColor: "#ffffff",
                      }}
                      placeholder={t("enterFirstName")}
                    />
                    {errors.firstName && (
                      <p className="mt-1 text-sm" style={{ color: "#dc2626" }}>
                        {errors.firstName}
                      </p>
                    )}
                  </div>

                  {/* Last Name */}
                  <div>
                    <label
                      className="block text-sm font-medium mb-2"
                      style={{ color: "#374151" }}
                    >
                      {t("lastName")}{" "}
                      <span style={{ color: "#dc2626" }}>*</span>
                    </label>
                    <input
                      type="text"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                      style={{
                        borderColor: errors.lastName ? "#dc2626" : "#d1d5db",
                        backgroundColor: "#ffffff",
                      }}
                      placeholder={t("enterLastName")}
                    />
                    {errors.lastName && (
                      <p className="mt-1 text-sm" style={{ color: "#dc2626" }}>
                        {errors.lastName}
                      </p>
                    )}
                  </div>
                </div>

                {/* Contact Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Phone Number */}
                  <div>
                    <label
                      className="block text-sm font-medium mb-2"
                      style={{ color: "#374151" }}
                    >
                      {t("phoneNumber")}{" "}
                      <span
                        className="text-sm font-normal"
                        style={{ color: "#64748b" }}
                      >
                        ({t("optional")})
                      </span>
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                      style={{
                        borderColor: errors.phone ? "#dc2626" : "#d1d5db",
                        backgroundColor: "#ffffff",
                      }}
                      placeholder="+60123456789"
                    />
                    {errors.phone && (
                      <p className="mt-1 text-sm" style={{ color: "#dc2626" }}>
                        {errors.phone}
                      </p>
                    )}
                  </div>

                  {/* Date of Birth */}
                  <div>
                    <label
                      className="block text-sm font-medium mb-2"
                      style={{ color: "#374151" }}
                    >
                      {t("dateOfBirth")}{" "}
                      <span
                        className="text-sm font-normal"
                        style={{ color: "#64748b" }}
                      >
                        ({t("optional")})
                      </span>
                    </label>
                    <CustomDatePicker
                      name="dateOfBirth"
                      value={formData.dateOfBirth}
                      onChange={handleInputChange}
                      placeholder={t("selectDateOfBirth")}
                      error={!!errors.dateOfBirth}
                    />
                    {errors.dateOfBirth && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.dateOfBirth}
                      </p>
                    )}
                  </div>
                </div>

                {/* Address Information */}
                <div className="space-y-4">
                  <h3
                    className="text-lg font-medium"
                    style={{ color: "#374151" }}
                  >
                    {t("addressInformation")}
                  </h3>

                  {/* Address Line 1 */}
                  <div>
                    <label
                      className="block text-sm font-medium mb-2"
                      style={{ color: "#374151" }}
                    >
                      {t("addressLine1")}{" "}
                      <span
                        className="text-sm font-normal"
                        style={{ color: "#64748b" }}
                      >
                        ({t("optional")})
                      </span>
                    </label>
                    <input
                      type="text"
                      name="address1"
                      value={formData.address1}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                      style={{
                        borderColor: errors.address1 ? "#dc2626" : "#d1d5db",
                        backgroundColor: "#ffffff",
                      }}
                      placeholder={t("enterAddressLine1")}
                    />
                  </div>

                  {/* Address Line 2 */}
                  <div>
                    <label
                      className="block text-sm font-medium mb-2"
                      style={{ color: "#374151" }}
                    >
                      {t("addressLine2")}{" "}
                      <span
                        className="text-sm font-normal"
                        style={{ color: "#64748b" }}
                      >
                        ({t("optional")})
                      </span>
                    </label>
                    <input
                      type="text"
                      name="address2"
                      value={formData.address2}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                      style={{
                        borderColor: errors.address2 ? "#dc2626" : "#d1d5db",
                        backgroundColor: "#ffffff",
                      }}
                      placeholder={t("enterAddressLine2")}
                    />
                  </div>

                  {/* Postcode, City, State */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label
                        className="block text-sm font-medium mb-2"
                        style={{ color: "#374151" }}
                      >
                        {t("postcode")}{" "}
                        <span
                          className="text-sm font-normal"
                          style={{ color: "#64748b" }}
                        >
                          ({t("optional")})
                        </span>
                      </label>
                      <input
                        type="text"
                        name="postcode"
                        value={formData.postcode}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                        style={{
                          borderColor: errors.postcode ? "#dc2626" : "#d1d5db",
                          backgroundColor: "#ffffff",
                        }}
                        placeholder="12345"
                      />
                    </div>

                    <div>
                      <label
                        className="block text-sm font-medium mb-2"
                        style={{ color: "#374151" }}
                      >
                        {t("city")}{" "}
                        <span
                          className="text-sm font-normal"
                          style={{ color: "#64748b" }}
                        >
                          ({t("optional")})
                        </span>
                      </label>
                      <input
                        type="text"
                        name="city"
                        value={formData.city}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                        style={{
                          borderColor: errors.city ? "#dc2626" : "#d1d5db",
                          backgroundColor: "#ffffff",
                        }}
                        placeholder={t("enterCity")}
                      />
                    </div>

                    <div>
                      <label
                        className="block text-sm font-medium mb-2"
                        style={{ color: "#374151" }}
                      >
                        {t("state")}{" "}
                        <span
                          className="text-sm font-normal"
                          style={{ color: "#64748b" }}
                        >
                          ({t("optional")})
                        </span>
                      </label>
                      <input
                        type="text"
                        name="state"
                        value={formData.state}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                        style={{
                          borderColor: errors.state ? "#dc2626" : "#d1d5db",
                          backgroundColor: "#ffffff",
                        }}
                        placeholder={t("enterState")}
                      />
                    </div>
                  </div>
                </div>

                {/* Emergency Contact */}
                <div>
                  <label
                    className="block text-sm font-medium mb-2"
                    style={{ color: "#374151" }}
                  >
                    {t("emergencyContact")}{" "}
                    <span
                      className="text-sm font-normal"
                      style={{ color: "#64748b" }}
                    >
                      ({t("optional")})
                    </span>
                  </label>
                  <input
                    type="tel"
                    name="emergencyContact"
                    value={formData.emergencyContact}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                    style={{
                      borderColor: errors.emergencyContact
                        ? "#dc2626"
                        : "#d1d5db",
                      backgroundColor: "#ffffff",
                    }}
                    placeholder="+60123456789"
                  />
                  {errors.emergencyContact && (
                    <p className="mt-1 text-sm" style={{ color: "#dc2626" }}>
                      {errors.emergencyContact}
                    </p>
                  )}
                </div>

                {/* Language Preference */}
                <div>
                  <label
                    className="block text-sm font-medium mb-2"
                    style={{ color: "#374151" }}
                  >
                    {t("preferredLanguage")}
                  </label>
                  <select
                    name="language"
                    value={formData.language}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                    style={{
                      borderColor: "#d1d5db",
                      backgroundColor: "#ffffff",
                    }}
                  >
                    <option value="en">English</option>
                    <option value="ms">Bahasa Malaysia</option>
                  </select>
                  <p className="mt-1 text-sm" style={{ color: "#64748b" }}>
                    {t("languagePreferenceHelp")}
                  </p>
                </div>

                {/* Action Buttons */}
                <div
                  className="flex flex-col sm:flex-row gap-4 pt-6 border-t justify-end"
                  style={{ borderColor: "#e5e7eb" }}
                >
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="px-6 py-3 rounded-lg font-medium transition-colors"
                    style={{
                      backgroundColor: "#f1f5f9",
                      color: "#475569",
                    }}
                    disabled={isSubmitting}
                  >
                    {t("cancel")}
                  </button>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-6 py-3 rounded-lg font-medium transition-colors disabled:opacity-50"
                    style={{
                      backgroundColor: "#0ea5e9",
                      color: "#ffffff",
                    }}
                  >
                    {isSubmitting ? t("saving") : t("saveChanges")}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </main>
        {/* Footer */}
        <Footer />
      </div>

      {/* Feedback Modal */}
      <StandardModal
        isOpen={showFeedbackModal}
        onClose={closeFeedbackModal}
        type={feedbackModal.type}
        title={feedbackModal.title}
        message={feedbackModal.message}
        details={feedbackModal.details}
        autoClose={feedbackModal.type === 'success'}
        autoCloseDelay={2000}
      />
    </>
  );
};

export default ProfileEditPage;
