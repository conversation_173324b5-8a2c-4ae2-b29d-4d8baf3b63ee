import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useAuth } from '../../../contexts/AuthContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import Navbar from '../../../components/Navbar';
import Footer from '../../../components/Footer';
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  TrendingUpIcon,
  TrendingDownIcon
} from '@heroicons/react/24/outline';
import { BarChart3, PieChart, TrendingUp, Users, DollarSign, Calendar } from 'lucide-react';

const BoatOwnerAnalytics = () => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading, accessToken } = useAuth();
  const { t } = useLanguage();

  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedPeriod, setSelectedPeriod] = useState('30d');

  // Redirect if not authenticated or not a boat owner
  useEffect(() => {
    if (!isLoading && (!isAuthenticated || user?.role !== 'BOAT_OWNER' || !user?.isApproved)) {
      router.push('/');
      return;
    }
  }, [isAuthenticated, user, isLoading, router]);

  // Fetch analytics data
  useEffect(() => {
    const fetchAnalytics = async () => {
      if (!accessToken) return;

      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/analytics/bookings?period=${selectedPeriod}`,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (response.ok) {
          const data = await response.json();
          setAnalytics(data.data);
        } else {
          setError('Failed to load analytics');
        }
      } catch (error) {
        console.error('Analytics fetch error:', error);
        setError('Failed to load analytics');
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated && user?.role === 'BOAT_OWNER' && accessToken) {
      fetchAnalytics();
    }
  }, [isAuthenticated, user, accessToken, selectedPeriod]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-MY', {
      style: 'currency',
      currency: 'MYR'
    }).format(amount || 0);
  };

  const formatPercentage = (value) => {
    return `${parseFloat(value || 0).toFixed(1)}%`;
  };

  const getPeriodLabel = (period) => {
    switch (period) {
      case '7d':
        return t('last7Days');
      case '30d':
        return t('last30Days');
      case '90d':
        return t('last90Days');
      case '1y':
        return t('lastYear');
      default:
        return t('last30Days');
    }
  };

  if (isLoading || loading) {
    const pageName = "Analytics";

    return (
      <>
        <Head>
          <title>{pageName} - GoSea</title>
          <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
        </Head>
        <div className="min-h-screen bg-slate-50">
          <Navbar currentPage="boat-owner" />
          <main className="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-4 sm:py-8 sm:px-0">
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </>
    );
  }

  if (error) {
    const pageName = "Analytics";

    return (
      <>
        <Head>
          <title>{pageName} - GoSea</title>
          <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
        </Head>
        <div className="min-h-screen bg-slate-50">
          <Navbar currentPage="boat-owner" />
          <main className="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-4 sm:py-8 sm:px-0">
              <div className="text-center py-12">
                <p className="text-red-600">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {t('retry')}
                </button>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </>
    );
  }

  const stats = analytics?.stats || {};
  const trends = analytics?.trends || [];

  const keyMetrics = [
    {
      name: t('totalBookings'),
      value: stats.totalBookings || 0,
      icon: Calendar,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+12%',
      changeType: 'increase'
    },
    {
      name: t('confirmedBookings'),
      value: stats.confirmedBookings || 0,
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+8%',
      changeType: 'increase'
    },
    {
      name: t('totalRevenue'),
      value: formatCurrency(stats.totalRevenue),
      icon: DollarSign,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+15%',
      changeType: 'increase'
    },
    {
      name: t('conversionRate'),
      value: formatPercentage(stats.conversionRate),
      icon: TrendingUp,
      color: 'text-amber-600',
      bgColor: 'bg-amber-50',
      change: '+2.3%',
      changeType: 'increase'
    }
  ];

  const pageName = "Analytics";

  return (
    <>
      <Head>
        <title>{pageName} - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen bg-slate-50">
        <Navbar currentPage="boat-owner" />

        <main className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          <div className="px-4 py-4 sm:py-8 sm:px-0">
            {/* Page Header */}
            <div className="mb-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
                    {t('analytics')}
                  </h1>
                  <p className="text-gray-600 mt-2">
                    {t('viewYourBusinessAnalytics')}
                  </p>
                </div>
                <div className="mt-4 sm:mt-0">
                  <select
                    value={selectedPeriod}
                    onChange={(e) => setSelectedPeriod(e.target.value)}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="7d">{t('last7Days')}</option>
                    <option value="30d">{t('last30Days')}</option>
                    <option value="90d">{t('last90Days')}</option>
                    <option value="1y">{t('lastYear')}</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {keyMetrics.map((metric) => {
                  const Icon = metric.icon;
                  return (
                    <div key={metric.name} className="bg-white rounded-lg shadow p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-600 mb-1">
                            {metric.name}
                          </p>
                          <p className="text-2xl font-bold text-gray-900">
                            {metric.value}
                          </p>
                          <div className="flex items-center mt-2">
                            <TrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                            <span className="text-sm font-medium text-green-600">
                              {metric.change}
                            </span>
                            <span className="text-sm text-gray-500 ml-1">
                              {getPeriodLabel(selectedPeriod)}
                            </span>
                          </div>
                        </div>
                        <div className={`p-3 rounded-full ${metric.bgColor}`}>
                          <Icon className={`h-6 w-6 ${metric.color}`} />
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Booking Status Breakdown */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white rounded-lg shadow">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-900">
                      {t('bookingStatusBreakdown')}
                    </h2>
                  </div>
                  <div className="p-6">
                    {trends.length > 0 ? (
                      <div className="space-y-4">
                        {trends.map((trend) => {
                          const percentage = stats.totalBookings > 0 
                            ? ((trend._count.id / stats.totalBookings) * 100).toFixed(1)
                            : 0;
                          
                          return (
                            <div key={trend.status} className="flex items-center justify-between">
                              <div className="flex items-center">
                                <div className={`w-3 h-3 rounded-full mr-3 ${
                                  trend.status === 'CONFIRMED' ? 'bg-green-500' :
                                  trend.status === 'PENDING' ? 'bg-yellow-500' :
                                  trend.status === 'CANCELLED' ? 'bg-red-500' :
                                  'bg-blue-500'
                                }`}></div>
                                <span className="text-sm font-medium text-gray-700">
                                  {t(trend.status?.toLowerCase() || 'unknown')}
                                </span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm text-gray-600">
                                  {trend._count.id}
                                </span>
                                <span className="text-sm font-medium text-gray-900">
                                  {percentage}%
                                </span>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <PieChart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500">{t('noDataAvailable')}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Performance Summary */}
                <div className="bg-white rounded-lg shadow">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-900">
                      {t('performanceSummary')}
                    </h2>
                  </div>
                  <div className="p-6">
                    <div className="space-y-6">
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium text-gray-700">
                            {t('bookingConversionRate')}
                          </span>
                          <span className="text-sm font-bold text-gray-900">
                            {formatPercentage(stats.conversionRate)}
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${Math.min(stats.conversionRate || 0, 100)}%` }}
                          ></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium text-gray-700">
                            {t('averageBookingValue')}
                          </span>
                          <span className="text-sm font-bold text-gray-900">
                            {formatCurrency(
                              stats.confirmedBookings > 0 
                                ? stats.totalRevenue / stats.confirmedBookings 
                                : 0
                            )}
                          </span>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium text-gray-700">
                            {t('cancellationRate')}
                          </span>
                          <span className="text-sm font-bold text-gray-900">
                            {formatPercentage(
                              stats.totalBookings > 0 
                                ? (stats.cancelledBookings / stats.totalBookings) * 100 
                                : 0
                            )}
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-red-500 h-2 rounded-full" 
                            style={{ 
                              width: `${Math.min(
                                stats.totalBookings > 0 
                                  ? (stats.cancelledBookings / stats.totalBookings) * 100 
                                  : 0, 
                                100
                              )}%` 
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Revenue Insights */}
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">
                    {t('revenueInsights')}
                  </h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {formatCurrency(stats.totalRevenue)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {t('totalRevenue')} ({getPeriodLabel(selectedPeriod)})
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {formatCurrency(
                          stats.confirmedBookings > 0 
                            ? stats.totalRevenue / stats.confirmedBookings 
                            : 0
                        )}
                      </div>
                      <div className="text-sm text-gray-500">
                        {t('averageBookingValue')}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {formatCurrency(
                          selectedPeriod === '7d' ? stats.totalRevenue / 7 :
                          selectedPeriod === '30d' ? stats.totalRevenue / 30 :
                          selectedPeriod === '90d' ? stats.totalRevenue / 90 :
                          stats.totalRevenue / 365
                        )}
                      </div>
                      <div className="text-sm text-gray-500">
                        {t('dailyAverageRevenue')}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recommendations */}
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">
                    {t('recommendations')}
                  </h2>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {stats.conversionRate < 50 && (
                      <div className="flex items-start space-x-3 p-4 bg-yellow-50 rounded-lg">
                        <TrendingUpIcon className="h-5 w-5 text-yellow-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-yellow-800">
                            {t('improveConversionRate')}
                          </h4>
                          <p className="text-sm text-yellow-700 mt-1">
                            {t('conversionRateRecommendation')}
                          </p>
                        </div>
                      </div>
                    )}
                    
                    {stats.totalBookings === 0 && (
                      <div className="flex items-start space-x-3 p-4 bg-blue-50 rounded-lg">
                        <ChartBarIcon className="h-5 w-5 text-blue-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-blue-800">
                            {t('getYourFirstBooking')}
                          </h4>
                          <p className="text-sm text-blue-700 mt-1">
                            {t('firstBookingRecommendation')}
                          </p>
                        </div>
                      </div>
                    )}

                    {stats.totalBookings > 0 && stats.conversionRate >= 70 && (
                      <div className="flex items-start space-x-3 p-4 bg-green-50 rounded-lg">
                        <TrendingUpIcon className="h-5 w-5 text-green-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-green-800">
                            {t('excellentPerformance')}
                          </h4>
                          <p className="text-sm text-green-700 mt-1">
                            {t('excellentPerformanceMessage')}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>

        <Footer />
      </div>
    </>
  );
};

export default BoatOwnerAnalytics;
