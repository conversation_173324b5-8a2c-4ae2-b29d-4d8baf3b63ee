import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Navbar from '../../components/Navbar';
import Footer from '../../components/Footer';
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  UserGroupIcon,
  PlusIcon,
  EyeIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { Anchor, TrendingUp, Users, DollarSign } from 'lucide-react';

const BoatOwnerDashboard = () => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading, accessToken } = useAuth();
  const { t } = useLanguage();

  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Redirect if not authenticated or not a boat owner
  useEffect(() => {
    if (!isLoading && (!isAuthenticated || user?.role !== 'BOAT_OWNER' || !user?.isApproved)) {
      router.push('/');
      return;
    }
  }, [isAuthenticated, user, isLoading, router]);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!accessToken) return;

      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/profile`, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          setDashboardData(data.data);
        } else {
          setError('Failed to load dashboard data');
        }
      } catch (error) {
        console.error('Dashboard fetch error:', error);
        setError('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated && user?.role === 'BOAT_OWNER' && accessToken) {
      fetchDashboardData();
    }
  }, [isAuthenticated, user, accessToken]);

  if (isLoading || loading) {
    return (
      <>
        <Head>
          <title>Boat Owner Dashboard - GoSea</title>
        </Head>
        <div className="min-h-screen bg-slate-50">
          <Navbar currentPage="boat-owner" />
          <main className="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-4 sm:py-8 sm:px-0">
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <Head>
          <title>Boat Owner Dashboard - GoSea</title>
        </Head>
        <div className="min-h-screen bg-slate-50">
          <Navbar currentPage="boat-owner" />
          <main className="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-4 sm:py-8 sm:px-0">
              <div className="text-center py-12">
                <p className="text-red-600">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {t('retry')}
                </button>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </>
    );
  }

  const stats = [
    {
      name: t('totalBoats'),
      value: dashboardData?.provider?.stats?.totalBoats || 0,
      icon: Anchor,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+12%',
      changeType: 'increase'
    },
    {
      name: t('activeServices'),
      value: dashboardData?.provider?.stats?.totalServices || 0,
      icon: Cog6ToothIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+8%',
      changeType: 'increase'
    },
    {
      name: t('recentBookings'),
      value: dashboardData?.provider?.stats?.recentBookings || 0,
      icon: CalendarIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+23%',
      changeType: 'increase'
    },
    {
      name: t('monthlyRevenue'),
      value: 'RM 12,450',
      icon: DollarSign,
      color: 'text-amber-600',
      bgColor: 'bg-amber-50',
      change: '+15%',
      changeType: 'increase'
    }
  ];

  const quickActions = [
    {
      name: t('addNewBoat'),
      description: t('addBoatToFleet'),
      href: '/boat-owner/boats/create',
      icon: PlusIcon,
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      name: t('createService'),
      description: t('createNewService'),
      href: '/boat-owner/services/create',
      icon: PlusIcon,
      color: 'bg-green-600 hover:bg-green-700'
    },
    {
      name: t('viewBookings'),
      description: t('manageBookings'),
      href: '/boat-owner/bookings',
      icon: EyeIcon,
      color: 'bg-purple-600 hover:bg-purple-700'
    },
    {
      name: t('viewAnalytics'),
      description: t('businessInsights'),
      href: '/boat-owner/analytics',
      icon: ChartBarIcon,
      color: 'bg-amber-600 hover:bg-amber-700'
    }
  ];

  const pageName = "Boat Owner Dashboard";

  return (
    <>
      <Head>
        <title>{pageName} - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen bg-slate-50">
        <Navbar currentPage="boat-owner" />

        <main className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          <div className="px-4 py-4 sm:py-8 sm:px-0">
            {/* Page Header */}
            <div className="mb-6">
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
                {t('welcomeBack')}, {dashboardData?.user?.profile?.firstName || dashboardData?.provider?.companyName}!
              </h1>
              <p className="text-gray-600 mt-2">
                {t('hereIsYourBusinessOverview')}
              </p>
            </div>

            <div className="space-y-6">
              {/* Stats Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {stats.map((stat) => {
                  const Icon = stat.icon;
                  return (
                    <div key={stat.name} className="bg-white rounded-lg shadow p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-600 mb-1">
                            {stat.name}
                          </p>
                          <p className="text-2xl font-bold text-gray-900">
                            {stat.value}
                          </p>
                          <div className="flex items-center mt-2">
                            <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                            <span className="text-sm font-medium text-green-600">
                              {stat.change}
                            </span>
                            <span className="text-sm text-gray-500 ml-1">
                              {t('fromLastMonth')}
                            </span>
                          </div>
                        </div>
                        <div className={`p-3 rounded-full ${stat.bgColor}`}>
                          <Icon className={`h-6 w-6 ${stat.color}`} />
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Quick Actions */}
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">
                    {t('quickActions')}
                  </h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {quickActions.map((action) => {
                      const Icon = action.icon;
                      return (
                        <Link
                          key={action.name}
                          href={action.href}
                          className="group p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all duration-200"
                        >
                          <div className={`inline-flex items-center justify-center p-2 rounded-lg text-white ${action.color} mb-3`}>
                            <Icon className="h-5 w-5" />
                          </div>
                          <h3 className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                            {action.name}
                          </h3>
                          <p className="text-sm text-gray-500 mt-1">
                            {action.description}
                          </p>
                        </Link>
                      );
                    })}
                  </div>
                </div>
              </div>

              {/* Recent Activity */}
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">
                    {t('recentActivity')}
                  </h2>
                </div>
                <div className="p-6">
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">
                      {t('noRecentActivity')}
                    </p>
                    <p className="text-sm text-gray-400 mt-1">
                      {t('activityWillAppearHere')}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>

        <Footer />
      </div>
    </>
  );
};

export default BoatOwnerDashboard;
