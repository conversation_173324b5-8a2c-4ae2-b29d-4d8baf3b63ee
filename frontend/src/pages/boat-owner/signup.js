import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';
import Navbar from '../../components/Navbar';
import Footer from '../../components/Footer';
import StandardModal from '../../components/StandardModal';
import ProfileCompletionModal from '../../components/ProfileCompletionModal';
import { ChevronLeftIcon } from '@heroicons/react/24/outline';
import {
  Building,
  User,
  Lock,
  Mail,
  ShieldQuestionMark,
  Check,
  Eye,
  EyeOff,
  Info,
  SquarePen
} from 'lucide-react';
import { useModal } from '../../contexts/ModalContext';

const BoatOwnerSignUp = () => {
  const { t } = useLanguage();
  const router = useRouter();
  const fileInputRef = useRef(null);
  const { user, isAuthenticated, accessToken } = useAuth();

  // Sign In Modal
  const { openSignInModal } = useModal();

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    companyName: '',
    brn: '',
    operatorType: 'individual' // 'individual' or 'business'
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);
  const [showEmailVerifiedSuccess, setShowEmailVerifiedSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [passwordValidation, setPasswordValidation] = useState({
    length: false,
    uppercase: false,
    specialChar: false
  });

  // Profile completion modal state
  const [showProfileCompletion, setShowProfileCompletion] = useState(false);
  const [isMandatoryProfileCompletion, setIsMandatoryProfileCompletion] = useState(false);

  // Check for verified parameter in URL
  useEffect(() => {
    if (router.isReady) {
      const { verified } = router.query;
      if (verified === 'true') {
        setShowEmailVerifiedSuccess(true);
        // Remove the query parameter from URL
        const newQuery = { ...router.query };
        delete newQuery.verified;
        router.replace({
          pathname: router.pathname,
          query: newQuery
        }, undefined, { shallow: true });
      }
    }
  }, [router.isReady, router.query]);

  // Handle profile completion for first-time Google users
  useEffect(() => {
    if (router.isReady && router.query.show_profile_completion === 'true') {
      console.log('Boat owner signup: show_profile_completion parameter detected');
      console.log('Authentication status:', { isAuthenticated, user: user ? 'present' : 'null' });

      if (isAuthenticated && user) {
        // Create a more reliable localStorage key that works for both manual and OAuth users
        // For Google OAuth users, we'll use email as a fallback if ID is not consistent
        const localStorageKey = user.id
          ? `gosea_profile_completion_${user.id}`
          : user.email
            ? `gosea_profile_completion_${btoa(user.email)}` // base64 encode email to avoid special characters
            : `gosea_profile_completion_unknown`;

        // Check if this is the first time the user is accessing the dashboard
        const hasSeenProfileCompletion = localStorage.getItem(localStorageKey);

        // Check if user has incomplete profile (only phone number is required)
        const hasIncompleteProfile = !user.profile?.phone;

        console.log('Boat owner signup profile completion check:', {
          userId: user.id,
          userEmail: user.email,
          localStorageKey,
          hasSeenProfileCompletion,
          hasIncompleteProfile,
          hasPhone: !!user.profile?.phone,
          userProfile: user.profile
        });

        // Only show profile completion modal if user has incomplete profile and hasn't seen the modal before
        if (hasIncompleteProfile && !hasSeenProfileCompletion) {
          console.log('Showing profile completion modal in boat owner signup');
          // Show mandatory profile completion modal for first-time Google users
          setShowProfileCompletion(true);
          setIsMandatoryProfileCompletion(true);
        } else {
          console.log('Not showing profile completion modal:', {
            hasIncompleteProfile,
            hasSeenProfileCompletion
          });
        }
      } else {
        console.log('User not authenticated, skipping profile completion modal');
      }

      // Remove the query parameter from URL in all cases
      const newQuery = { ...router.query };
      delete newQuery.show_profile_completion;
      router.replace({
        pathname: router.pathname,
        query: newQuery
      }, undefined, { shallow: true });
    }
  }, [router.isReady, router.query.show_profile_completion, isAuthenticated, user]);

  // Modal states
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackModal, setFeedbackModal] = useState({
    type: 'success',
    title: '',
    message: '',
    details: null,
  });

  // Helper functions for showing modals
  const showSuccessModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'success',
      title,
      message,
      details,
    });
    setShowFeedbackModal(true);
  };

  const showErrorModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'error',
      title,
      message,
      details,
    });
    setShowFeedbackModal(true);
  };

  const closeFeedbackModal = () => {
    setShowFeedbackModal(false);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Real-time password validation
    if (name === 'password') {
      setPasswordValidation({
        length: value.length >= 8,
        uppercase: /[A-Z]/.test(value),
        specialChar: /[!@#$%^&*(),.?":{}|<>]/.test(value)
      });
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Client-side validation
    if (formData.password !== formData.confirmPassword) {
      setError(t('passwordsDoNotMatch'));
      setIsLoading(false);
      return;
    }

    // Check password strength requirements
    if (formData.password.length < 8) {
      setError(t('passwordMinLengthError'));
      setIsLoading(false);
      return;
    }

    const hasUppercase = /[A-Z]/.test(formData.password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(formData.password);

    if (!hasUppercase || !hasSpecialChar) {
      setError(t('passwordRequirementsError'));
      setIsLoading(false);
      return;
    }

    // Malaysia phone number validation
    const phoneRegex = /^(\+?6?01[0-46-9][-\s]?\d{7,8}|(\+?6?0)?(1[0-46-9][-\s]?\d{7,8}))$/;
    if (!phoneRegex.test(formData.phone.replace(/[-\s]/g, ''))) {
      setError(t('invalidPhoneNumberFormat'));
      setIsLoading(false);
      return;
    }

    // Business fields validation (only required if operating as business)
    if (formData.operatorType === 'business') {
      if (!formData.companyName.trim()) {
        setError(t('companyNameRequired'));
        setIsLoading(false);
        return;
      }

      if (!formData.brn.trim()) {
        setError(t('brnRequired'));
        setIsLoading(false);
        return;
      }
    }

    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';

      // Prepare registration data - only include business fields if operating as business
      const registrationData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        password: formData.password,
        phone: formData.phone
      };

      // Add business fields only if operating as business
      if (formData.operatorType === 'business') {
        registrationData.companyName = formData.companyName;
        registrationData.brn = formData.brn;
      }

      const response = await fetch(`${apiUrl}/api/auth/boat-owner/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(registrationData),
      });

      const data = await response.json();

      if (data.success) {
        setShowSuccess(true);
      } else {
        // Handle specific error codes
        switch (data.code) {
          case 'USER_ALREADY_EXISTS':
            setError(t('accountAlreadyExists'));
            break;
          case 'WEAK_PASSWORD':
            setError(data.message);
            break;
          case 'VALIDATION_ERROR':
            if (data.errors && data.errors.length > 0) {
              setError(data.errors.map(err => err.msg).join(', '));
            } else {
              setError(data.message);
            }
            break;
          default:
            setError(data.message || t('registrationFailed'));
        }
      }
    } catch (err) {
      console.error('Registration error:', err);
      setError(t('networkError'));
    } finally {
      setIsLoading(false);
    }
  };

  if (showSuccess || showEmailVerifiedSuccess) {

    return (
      <>
        <Head>
          <title>Registration Successful - Gosea</title>
          <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
        </Head>

        <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-md w-full space-y-8">
            <div className="flex items-center justify-center space-x-4 mb-4">
              <div className="flex-shrink-0 h-8 sm:h-12 w-8 sm:w-12 rounded-full bg-emerald-100 flex items-center justify-center">
                <Check className="h-4 sm:h-6 w-4 sm:w-6 text-emerald-600" />
              </div>
              <h2 className="text-lg sm:text-2xl font-bold text-gray-900">
                {showEmailVerifiedSuccess ? t('emailVerified') : t('registrationSuccessful')}
              </h2>
            </div>
            <div className="text-center border">
              <div className="bg-white shadow rounded-lg p-6">
                <p className="text-gray-600 mb-4">
                  {showEmailVerifiedSuccess
                    ? t('emailVerificationSuccessful')
                    : t('thankYouForRegistering')}
                </p>
                <div className="bg-blue-50 border border-sky-200 rounded-md p-4 mb-4">
                  <h3 className="text-sm font-medium text-sky-800 mb-2 flex items-center">
                    <Mail className="w-4 h-4 mr-2" />
                    {t('nextSteps')}
                  </h3>
                  <ol className="text-xs sm:text-sm text-sky-700 space-y-1 text-left">
                    <li>1. {t('checkEmailVerification')}</li>
                    <li>2. {t('clickVerificationLink')}</li>
                    <li>3. {t('adminReviewApplication')}</li>
                    <li>4. {t('receiveApprovalNotification')}</li>
                  </ol>
                </div>
                <p className="text-sm text-gray-500 mb-6">
                  {t('onceApprovedAccess')}
                </p>
                <div className="space-y-3">
                  <Link href="/" className="w-full bg-amber-500 text-white py-2 px-4 rounded-md hover:bg-amber-600 transition-colors text-center block">
                    {t('returnToHomepage')}
                  </Link>
                  {/* <Link href="/boat-owner" className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors text-center block">
                    {t('learnMoreBoatOwnerFeatures')}
                  </Link> */}
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Boat Owner Registration - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen bg-slate-50">
        <Navbar currentPage="profile-edit" />

        {/* Main Content */}
        <main className="max-w-4xl mx-auto sm:px-6 lg:px-8">
          <div className="px-4 py-4 sm:py-8 sm:px-0">
            {/* Page Header */}
            <div className="mb-4 md:mb-6">
              <h1 className="text-xl md:text-2xl font-bold" style={{ color: "#0f172a" }}>
                {t('boatOwnerRegistration')}
              </h1>
            </div>

            {/* Registration Form */}
            <div
              className="rounded-2xl p-8 shadow-lg border"
              style={{ backgroundColor: "#ffffff", borderColor: "#e2e8f0" }}
            >
              <form onSubmit={handleSubmit} className="space-y-6">
                {error && (
                  <div className="px-4 py-3 rounded-md" style={{ backgroundColor: '#fef2f2', borderColor: '#fecaca', color: '#ef4444', border: '1px solid' }}>
                    {error}
                  </div>
                )}

                {/* Operator Type Selection */}
                <div>
                  <h3 className="text-lg font-medium mb-4 flex items-center" style={{ color: "#374151" }}>
                    <ShieldQuestionMark className="w-5 h-5 mr-2" />
                    {t('operatorType')}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div
                      className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                        formData.operatorType === 'individual'
                          ? 'border-amber-500 bg-amber-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setFormData(prev => ({ ...prev, operatorType: 'individual', companyName: '', brn: '' }))}
                    >
                      <div className="flex items-center">
                        <input
                          type="radio"
                          name="operatorType"
                          value="individual"
                          checked={formData.operatorType === 'individual'}
                          onChange={handleInputChange}
                          className="mr-3 text-amber-600"
                        />
                        <div>
                          <h4 className="font-medium text-gray-900">{t('individualOperator')}</h4>
                          <p className="text-sm text-gray-600">{t('individualOperatorDescription')}</p>
                        </div>
                      </div>
                    </div>
                    <div
                      className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                        formData.operatorType === 'business'
                          ? 'border-amber-500 bg-amber-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setFormData(prev => ({ ...prev, operatorType: 'business' }))}
                    >
                      <div className="flex items-center">
                        <input
                          type="radio"
                          name="operatorType"
                          value="business"
                          checked={formData.operatorType === 'business'}
                          onChange={handleInputChange}
                          className="mr-3 text-amber-600"
                        />
                        <div>
                          <h4 className="font-medium text-gray-900">{t('businessOperator')}</h4>
                          <p className="text-sm text-gray-600">{t('businessOperatorDescription')}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Business Information Section - Only show if business operator */}
                {formData.operatorType === 'business' && (
                  <div>
                    <h3 className="text-lg font-medium mb-4 flex items-center" style={{ color: "#374151" }}>
                      <Building className="w-5 h-5 mr-2" />
                      {t('businessInformation')}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium mb-2" style={{ color: "#374151" }}>
                          {t('companyName')} <span style={{ color: "#dc2626" }}>*</span>
                        </label>
                        <input
                          type="text"
                          name="companyName"
                          value={formData.companyName}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                          style={{
                            borderColor: errors.companyName ? "#dc2626" : "#d1d5db",
                            backgroundColor: "#ffffff",
                          }}
                          placeholder={t('enterCompanyName')}
                          required={formData.operatorType === 'business'}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2" style={{ color: "#374151" }}>
                          {t('businessRegistrationNumber')} <span style={{ color: "#dc2626" }}>*</span>
                        </label>
                        <input
                          type="text"
                          name="brn"
                          value={formData.brn}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                          style={{
                            borderColor: errors.brn ? "#dc2626" : "#d1d5db",
                            backgroundColor: "#ffffff",
                          }}
                          placeholder={t('enterBRN')}
                          required={formData.operatorType === 'business'}
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* Personal Information Section */}
                <div>
                  <h3 className="text-lg font-medium mb-4 flex items-center" style={{ color: "#374151" }}>
                    <User className="w-5 h-5 mr-2" />
                    {t('personalInformation')}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: "#374151" }}>
                        {t('firstName')} <span style={{ color: "#dc2626" }}>*</span>
                      </label>
                      <input
                        type="text"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                        style={{
                          borderColor: errors.firstName ? "#dc2626" : "#d1d5db",
                          backgroundColor: "#ffffff",
                        }}
                        placeholder={t('enterFirstName')}
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: "#374151" }}>
                        {t('lastName')} <span style={{ color: "#dc2626" }}>*</span>
                      </label>
                      <input
                        type="text"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                        style={{
                          borderColor: errors.lastName ? "#dc2626" : "#d1d5db",
                          backgroundColor: "#ffffff",
                        }}
                        placeholder={t('enterLastName')}
                        required
                      />
                    </div>
                  </div>
                </div>

                {/* Contact Information Section */}
                <div>
                  {/* <h2 className="text-xl font-semibold mb-4 flex items-center" style={{ color: "#0f172a" }}>
                    <Phone className="w-5 h-5 mr-2" />
                    {t('contactInformation')}
                  </h2> */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: "#374151" }}>
                        {t('businessEmailAddress')} <span style={{ color: "#dc2626" }}>*</span>
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                        style={{
                          borderColor: errors.email ? "#dc2626" : "#d1d5db",
                          backgroundColor: "#ffffff",
                        }}
                        placeholder={t('enterBusinessEmail')}
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: "#374151" }}>
                        {t('phoneNumber')} <span style={{ color: "#dc2626" }}>*</span>
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                        style={{
                          borderColor: errors.phone ? "#dc2626" : "#d1d5db",
                          backgroundColor: "#ffffff",
                        }}
                        placeholder={t('enterPhoneNumber')}
                        required
                      />
                      <p className="mt-1 text-xs" style={{ color: "#64748b" }}>
                        {t('malaysiaPhoneFormat')}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Security Section */}
                <div>
                  <h3 className="text-lg font-medium mb-4 flex items-center" style={{ color: "#374151" }}>
                    <Lock className="w-5 h-5 mr-2" />
                    {t('accountSecurity')}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-1" style={{ color: "#374151" }}>
                        {t('password')} <span style={{ color: "#dc2626" }}>*</span>
                      </label>
                      <div className="relative">
                        <input
                          type={showPassword ? 'text' : 'password'}
                          name="password"
                          value={formData.password}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 pr-10 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                          style={{
                            borderColor: errors.password ? "#dc2626" : "#d1d5db",
                            backgroundColor: "#ffffff",
                          }}
                          placeholder={t('createStrongPassword')}
                          required
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center transition-colors"
                          style={{ color: "#94a3b8" }}
                          onMouseEnter={(e) => e.target.style.color = "#64748b"}
                          onMouseLeave={(e) => e.target.style.color = "#94a3b8"}
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOff className="w-5 h-5" />
                          ) : (
                            <Eye className="w-5 h-5" />
                          )}
                        </button>
                      </div>
                      {/* Real-time password validation */}
                      <div className="text-xs mt-2 space-y-1">
                        <div className="flex items-center space-x-2">
                          <span className={`w-2 h-2 rounded-full ${passwordValidation.length ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                          <span style={{ color: passwordValidation.length ? '#10b981' : '#64748b' }}>
                            {t('passwordMinLength')}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`w-2 h-2 rounded-full ${passwordValidation.uppercase ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                          <span style={{ color: passwordValidation.uppercase ? '#10b981' : '#64748b' }}>
                            {t('passwordUppercaseSimple')}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`w-2 h-2 rounded-full ${passwordValidation.specialChar ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                          <span style={{ color: passwordValidation.specialChar ? '#10b981' : '#64748b' }}>
                            {t('passwordSpecialCharSimple')}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1" style={{ color: "#374151" }}>
                        {t('confirmPassword')} <span style={{ color: "#dc2626" }}>*</span>
                      </label>
                      <div className="relative">
                        <input
                          type={showConfirmPassword ? 'text' : 'password'}
                          name="confirmPassword"
                          value={formData.confirmPassword}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 pr-10 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                          style={{
                            borderColor: errors.confirmPassword ? "#dc2626" : "#d1d5db",
                            backgroundColor: "#ffffff",
                          }}
                          placeholder={t('confirmYourPassword')}
                          required
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center transition-colors"
                          style={{ color: "#94a3b8" }}
                          onMouseEnter={(e) => e.target.style.color = "#64748b"}
                          onMouseLeave={(e) => e.target.style.color = "#94a3b8"}
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                          {showConfirmPassword ? (
                            <EyeOff className="w-5 h-5" />
                          ) : (
                            <Eye className="w-5 h-5" />
                          )}
                        </button>
                      </div>
                      {formData.confirmPassword && formData.password !== formData.confirmPassword && (
                        <p className="mt-1 text-sm" style={{ color: "#dc2626" }}>{t('passwordsDoNotMatch')}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full px-6 py-3 rounded-lg font-medium transition-colors disabled:opacity-50"
                  style={{
                    backgroundColor: isLoading ? "#94a3b8" : "#0ea5e9",
                    color: "#ffffff",
                  }}
                  onMouseEnter={(e) => !isLoading && (e.target.style.backgroundColor = "#0284c7")}
                  onMouseLeave={(e) => !isLoading && (e.target.style.backgroundColor = "#0ea5e9")}
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {t('creatingAccount')}
                    </div>
                  ) : (
                    t('createBoatOwnerAccount')
                  )}
                </button>

                {/* Already have account */}
                <div className="text-center pt-4">
                  <p style={{ color: "#64748b" }}>
                    {t('alreadyHaveAccount')}{' '}
                    <button
                      type="button"
                      className="font-medium transition-colors text-amber-500 hover:text-amber-600"
                      onClick={() => {
                        openSignInModal();
                      }}
                    >
                      {t('signIn')}
                    </button>
                  </p>
                </div>
              </form>
            </div>

            {/* Information Footer */}
            <div className="mt-8 bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
                <Info className="w-5 h-5 mr-2" />
                {t('whatHappensAfterRegistration')}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="bg-amber-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <Mail className="w-6 h-6 text-amber-600" />
                  </div>
                  <h4 className="font-medium text-gray-900 mb-2">{t('emailVerification')}</h4>
                  <p className="text-sm text-gray-600">
                    {t('checkInboxVerifyEmail')}
                  </p>
                </div>
                <div className="text-center">
                  <div className="bg-amber-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <Check className="w-6 h-6 text-amber-600" />
                  </div>
                  <h4 className="font-medium text-gray-900 mb-2">{t('adminReview')}</h4>
                  <p className="text-sm text-gray-600">
                    {t('teamReviewsApplication')}
                  </p>
                </div>
                <div className="text-center">
                  <div className="bg-amber-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <SquarePen className="w-6 h-6 text-amber-600" />
                  </div>
                  <h4 className="font-medium text-gray-900 mb-2">{t('startListing')}</h4>
                  <p className="text-sm text-gray-600">
                    {t('addBoatsServices')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </main>

        <Footer />
      </div>

      {/* Feedback Modal */}
      <StandardModal
        isOpen={showFeedbackModal}
        onClose={closeFeedbackModal}
        type={feedbackModal.type}
        title={feedbackModal.title}
        message={feedbackModal.message}
        details={feedbackModal.details}
        autoClose={feedbackModal.type === 'success'}
        autoCloseDelay={2000}
      />

      {/* Profile Completion Modal */}
      {showProfileCompletion && (
        <ProfileCompletionModal
          isOpen={showProfileCompletion}
          onClose={() => {
            setShowProfileCompletion(false);
            setIsMandatoryProfileCompletion(false);
            // Mark as seen so it doesn't show again
            // Use the same reliable key we used for checking
            const localStorageKey = user?.id
              ? `gosea_profile_completion_${user.id}`
              : user?.email
                ? `gosea_profile_completion_${btoa(user.email)}`
                : `gosea_profile_completion_unknown`;

            localStorage.setItem(localStorageKey, "true");
          }}
          user={user}
          mandatory={isMandatoryProfileCompletion}
        />
      )}
    </>
  );
};

export default BoatOwnerSignUp;