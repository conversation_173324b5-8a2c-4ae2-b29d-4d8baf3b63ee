import { useState, useRef } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useLanguage } from '../../contexts/LanguageContext';
import Navbar from '../../components/Navbar';
import Footer from '../../components/Footer';
import StandardModal from '../../components/StandardModal';
import { ChevronLeftIcon } from '@heroicons/react/24/outline';
import { 
  Anchor, 
  Building, 
  User, 
  Phone, 
  Lock, 
  Mail, 
  CheckCircle, 
  Check,
  Rocket, 
  Eye, 
  EyeOff,
  Info,
  SquarePen
} from 'lucide-react';

const BoatOwnerSignUp = () => {
  const { t } = useLanguage();
  const router = useRouter();
  const fileInputRef = useRef(null);
  
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    companyName: '',
    brn: ''
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [passwordValidation, setPasswordValidation] = useState({
    length: false,
    uppercase: false,
    specialChar: false
  });

  // Modal states
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackModal, setFeedbackModal] = useState({
    type: 'success',
    title: '',
    message: '',
    details: null,
  });

  // Helper functions for showing modals
  const showSuccessModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'success',
      title,
      message,
      details,
    });
    setShowFeedbackModal(true);
  };

  const showErrorModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'error',
      title,
      message,
      details,
    });
    setShowFeedbackModal(true);
  };

  const closeFeedbackModal = () => {
    setShowFeedbackModal(false);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Real-time password validation
    if (name === 'password') {
      setPasswordValidation({
        length: value.length >= 8,
        uppercase: /[A-Z]/.test(value),
        specialChar: /[!@#$%^&*(),.?":{}|<>]/.test(value)
      });
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Client-side validation
    if (formData.password !== formData.confirmPassword) {
      setError(t('passwordsDoNotMatch'));
      setIsLoading(false);
      return;
    }

    // Check password strength requirements
    if (formData.password.length < 8) {
      setError(t('passwordMinLengthError'));
      setIsLoading(false);
      return;
    }

    const hasUppercase = /[A-Z]/.test(formData.password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(formData.password);

    if (!hasUppercase || !hasSpecialChar) {
      setError(t('passwordRequirementsError'));
      setIsLoading(false);
      return;
    }

    // Malaysia phone number validation
    const phoneRegex = /^(\+?6?01[0-46-9][-\s]?\d{7,8}|(\+?6?0)?(1[0-46-9][-\s]?\d{7,8}))$/;
    if (!phoneRegex.test(formData.phone.replace(/[-\s]/g, ''))) {
      setError(t('invalidPhoneNumberFormat'));
      setIsLoading(false);
      return;
    }

    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/auth/boat-owner/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          password: formData.password,
          phone: formData.phone,
          companyName: formData.companyName,
          brn: formData.brn
        }),
      });

      const data = await response.json();

      if (data.success) {
        setShowSuccess(true);
      } else {
        // Handle specific error codes
        switch (data.code) {
          case 'USER_ALREADY_EXISTS':
            setError(t('accountAlreadyExists'));
            break;
          case 'WEAK_PASSWORD':
            setError(data.message);
            break;
          case 'VALIDATION_ERROR':
            if (data.errors && data.errors.length > 0) {
              setError(data.errors.map(err => err.msg).join(', '));
            } else {
              setError(data.message);
            }
            break;
          default:
            setError(data.message || t('registrationFailed'));
        }
      }
    } catch (err) {
      console.error('Registration error:', err);
      setError(t('networkError'));
    } finally {
      setIsLoading(false);
    }
  };

  if (showSuccess) {
    const pageName = "Registration Successful";

    return (
      <>
        <Head>
          <title>{pageName} - GoSea</title>
          <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
        </Head>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-md w-full space-y-8">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {t('registrationSuccessful')}
              </h2>
              <div className="bg-white shadow rounded-lg p-6">
                <p className="text-gray-600 mb-4">
                  {t('thankYouForRegistering')}
                </p>
                <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
                  <h3 className="text-sm font-medium text-blue-800 mb-2 flex items-center">
                    <Mail className="w-4 h-4 mr-2" />
                    {t('nextSteps')}
                  </h3>
                  <ol className="text-sm text-blue-700 space-y-1">
                    <li>1. {t('checkEmailVerification')}</li>
                    <li>2. {t('clickVerificationLink')}</li>
                    <li>3. {t('adminReviewApplication')}</li>
                    <li>4. {t('receiveApprovalNotification')}</li>
                  </ol>
                </div>
                <p className="text-sm text-gray-500 mb-6">
                  {t('onceApprovedAccess')}
                </p>
                <div className="space-y-3">
                  <Link href="/" className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-center block">
                    {t('returnToHomepage')}
                  </Link>
                  <Link href="/boat-owner" className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors text-center block">
                    {t('learnMoreBoatOwnerFeatures')}
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  const pageName = "Boat Owner Registration";

  return (
    <>
      <Head>
        <title>{pageName} - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      
      <div className="min-h-screen" style={{ backgroundColor: "#f8fafc" }}>
        <Navbar />
        
        {/* Main Content */}
        <main className="max-w-4xl mx-auto sm:px-6 lg:px-8">
          <div className="px-4 py-4 sm:py-8 sm:px-0">
            {/* Page Header */}
            <div className="mb-4 md:mb-6">
                <h1 className="text-xl md:text-2xl font-bold" style={{ color: "#0f172a" }}>
                {t('boatOwnerRegistration')}
              </h1>
            </div>

            {/* Registration Form */}
            <div
              className="rounded-2xl p-8 shadow-lg border"
              style={{ backgroundColor: "#ffffff", borderColor: "#e2e8f0" }}
            >
              <form onSubmit={handleSubmit} className="space-y-6">
                {error && (
                  <div className="px-4 py-3 rounded-md" style={{ backgroundColor: '#fef2f2', borderColor: '#fecaca', color: '#ef4444', border: '1px solid' }}>
                    {error}
                  </div>
                )}

                {/* Business Information Section */}
                <div>
                  <h3 className="text-lg font-medium mb-4 flex items-center" style={{ color: "#374151" }}>
                    <Building className="w-5 h-5 mr-2" />
                    {t('businessInformation')}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: "#374151" }}>
                        {t('companyName')} <span style={{ color: "#dc2626" }}>*</span>
                      </label>
                      <input
                        type="text"
                        name="companyName"
                        value={formData.companyName}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                        style={{
                          borderColor: errors.companyName ? "#dc2626" : "#d1d5db",
                          backgroundColor: "#ffffff",
                        }}
                        placeholder={t('enterCompanyName')}
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: "#374151" }}>
                        {t('businessRegistrationNumber')} <span style={{ color: "#dc2626" }}>*</span>
                      </label>
                      <input
                        type="text"
                        name="brn"
                        value={formData.brn}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                        style={{
                          borderColor: errors.brn ? "#dc2626" : "#d1d5db",
                          backgroundColor: "#ffffff",
                        }}
                        placeholder={t('enterBRN')}
                        required
                      />
                    </div>
                  </div>
                </div>

                {/* Personal Information Section */}
                <div>
                  <h3 className="text-lg font-medium mb-4 flex items-center" style={{ color: "#374151" }}>
                    <User className="w-5 h-5 mr-2" />
                    {t('personalInformation')}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: "#374151" }}>
                        {t('firstName')} <span style={{ color: "#dc2626" }}>*</span>
                      </label>
                      <input
                        type="text"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                        style={{
                          borderColor: errors.firstName ? "#dc2626" : "#d1d5db",
                          backgroundColor: "#ffffff",
                        }}
                        placeholder={t('enterFirstName')}
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: "#374151" }}>
                        {t('lastName')} <span style={{ color: "#dc2626" }}>*</span>
                      </label>
                      <input
                        type="text"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                        style={{
                          borderColor: errors.lastName ? "#dc2626" : "#d1d5db",
                          backgroundColor: "#ffffff",
                        }}
                        placeholder={t('enterLastName')}
                        required
                      />
                    </div>
                  </div>
                </div>

                {/* Contact Information Section */}
                <div>
                  {/* <h2 className="text-xl font-semibold mb-4 flex items-center" style={{ color: "#0f172a" }}>
                    <Phone className="w-5 h-5 mr-2" />
                    {t('contactInformation')}
                  </h2> */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: "#374151" }}>
                        {t('businessEmailAddress')} <span style={{ color: "#dc2626" }}>*</span>
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                        style={{
                          borderColor: errors.email ? "#dc2626" : "#d1d5db",
                          backgroundColor: "#ffffff",
                        }}
                        placeholder={t('enterBusinessEmail')}
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: "#374151" }}>
                        {t('phoneNumber')} <span style={{ color: "#dc2626" }}>*</span>
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                        style={{
                          borderColor: errors.phone ? "#dc2626" : "#d1d5db",
                          backgroundColor: "#ffffff",
                        }}
                        placeholder={t('enterPhoneNumber')}
                        required
                      />
                      <p className="mt-1 text-xs" style={{ color: "#64748b" }}>
                        {t('malaysiaPhoneFormat')}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Security Section */}
                <div>
                  <h3 className="text-lg font-medium mb-4 flex items-center" style={{ color: "#374151" }}>
                    <Lock className="w-5 h-5 mr-2" />
                    {t('accountSecurity')}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-1" style={{ color: "#374151" }}>
                        {t('password')} <span style={{ color: "#dc2626" }}>*</span>
                      </label>
                      <div className="relative">
                        <input
                          type={showPassword ? 'text' : 'password'}
                          name="password"
                          value={formData.password}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 pr-10 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                          style={{
                            borderColor: errors.password ? "#dc2626" : "#d1d5db",
                            backgroundColor: "#ffffff",
                          }}
                          placeholder={t('createStrongPassword')}
                          required
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center transition-colors"
                          style={{ color: "#94a3b8" }}
                          onMouseEnter={(e) => e.target.style.color = "#64748b"}
                          onMouseLeave={(e) => e.target.style.color = "#94a3b8"}
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOff className="w-5 h-5" />
                          ) : (
                            <Eye className="w-5 h-5" />
                          )}
                        </button>
                      </div>
                      {/* Real-time password validation */}
                      <div className="text-xs mt-2 space-y-1">
                        <div className="flex items-center space-x-2">
                          <span className={`w-2 h-2 rounded-full ${passwordValidation.length ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                          <span style={{ color: passwordValidation.length ? '#10b981' : '#64748b' }}>
                            {t('passwordMinLength')}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`w-2 h-2 rounded-full ${passwordValidation.uppercase ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                          <span style={{ color: passwordValidation.uppercase ? '#10b981' : '#64748b' }}>
                            {t('passwordUppercaseSimple')}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`w-2 h-2 rounded-full ${passwordValidation.specialChar ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                          <span style={{ color: passwordValidation.specialChar ? '#10b981' : '#64748b' }}>
                            {t('passwordSpecialCharSimple')}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1" style={{ color: "#374151" }}>
                        {t('confirmPassword')} <span style={{ color: "#dc2626" }}>*</span>
                      </label>
                      <div className="relative">
                        <input
                          type={showConfirmPassword ? 'text' : 'password'}
                          name="confirmPassword"
                          value={formData.confirmPassword}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 pr-10 rounded-lg border focus:ring-1 focus:ring-amber-500 focus:border-transparent"
                          style={{
                            borderColor: errors.confirmPassword ? "#dc2626" : "#d1d5db",
                            backgroundColor: "#ffffff",
                          }}
                          placeholder={t('confirmYourPassword')}
                          required
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center transition-colors"
                          style={{ color: "#94a3b8" }}
                          onMouseEnter={(e) => e.target.style.color = "#64748b"}
                          onMouseLeave={(e) => e.target.style.color = "#94a3b8"}
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                          {showConfirmPassword ? (
                            <EyeOff className="w-5 h-5" />
                          ) : (
                            <Eye className="w-5 h-5" />
                          )}
                        </button>
                      </div>
                      {formData.confirmPassword && formData.password !== formData.confirmPassword && (
                        <p className="mt-1 text-sm" style={{ color: "#dc2626" }}>{t('passwordsDoNotMatch')}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full px-6 py-3 rounded-lg font-medium transition-colors disabled:opacity-50"
                  style={{
                    backgroundColor: isLoading ? "#94a3b8" : "#0ea5e9",
                    color: "#ffffff",
                  }}
                  onMouseEnter={(e) => !isLoading && (e.target.style.backgroundColor = "#0284c7")}
                  onMouseLeave={(e) => !isLoading && (e.target.style.backgroundColor = "#0ea5e9")}
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {t('creatingAccount')}
                    </div>
                  ) : (
                    t('createBoatOwnerAccount')
                  )}
                </button>

                {/* Already have account */}
                <div className="text-center pt-4">
                  <p className="text-sm" style={{ color: "#64748b" }}>
                    {t('alreadyHaveAccount')}{' '}
                    <Link href="/boat-owner" className="font-medium transition-colors" style={{ color: "#0ea5e9" }} onMouseEnter={(e) => e.target.style.color = "#0284c7"} onMouseLeave={(e) => e.target.style.color = "#0ea5e9"}>
                      {t('signInHere')}
                    </Link>
                  </p>
                </div>
              </form>
            </div>

            {/* Information Footer */}
            <div className="mt-8 bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
                <Info className="w-5 h-5 mr-2" />
                {t('whatHappensAfterRegistration')}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="bg-amber-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <Mail className="w-6 h-6 text-amber-600" />
                  </div>
                  <h4 className="font-medium text-gray-900 mb-2">{t('emailVerification')}</h4>
                  <p className="text-sm text-gray-600">
                    {t('checkInboxVerifyEmail')}
                  </p>
                </div>
                <div className="text-center">
                  <div className="bg-amber-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <Check className="w-6 h-6 text-amber-600" />
                  </div>
                  <h4 className="font-medium text-gray-900 mb-2">{t('adminReview')}</h4>
                  <p className="text-sm text-gray-600">
                    {t('teamReviewsApplication')}
                  </p>
                </div>
                <div className="text-center">
                  <div className="bg-amber-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <SquarePen className="w-6 h-6 text-amber-600" />
                  </div>
                  <h4 className="font-medium text-gray-900 mb-2">{t('startListing')}</h4>
                  <p className="text-sm text-gray-600">
                    {t('addBoatsServices')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </main>
        
        <Footer />
      </div>

      {/* Feedback Modal */}
      <StandardModal
        isOpen={showFeedbackModal}
        onClose={closeFeedbackModal}
        type={feedbackModal.type}
        title={feedbackModal.title}
        message={feedbackModal.message}
        details={feedbackModal.details}
        autoClose={feedbackModal.type === 'success'}
        autoCloseDelay={2000}
      />
    </>
  );
};

export default BoatOwnerSignUp;