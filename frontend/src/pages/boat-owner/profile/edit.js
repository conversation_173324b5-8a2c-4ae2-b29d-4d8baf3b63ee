import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useAuth } from '../../../contexts/AuthContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import { validateMalaysianPhoneNumber } from '../../../utils/validation';
import Navbar from '../../../components/Navbar';
import Footer from '../../../components/Footer';
import StandardModal from '../../../components/StandardModal';
import CustomDatePicker from '../../../components/CustomDatePicker';
import { UserIcon, BuildingOfficeIcon } from '@heroicons/react/24/outline';

const BoatOwnerProfileEdit = () => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading, accessToken, getCurrentUser } = useAuth();
  const { t } = useLanguage();
  const fileInputRef = useRef(null);

  // Add a timeout ref for postcode lookup debouncing
  const postcodeLookupTimeout = useRef(null);

  const [formData, setFormData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    phone: '',
    dateOfBirth: '',
    emergencyContact: '',
    language: 'en',
    timezone: 'Asia/Kuala_Lumpur',
    personalAddress1: '',
    personalAddress2: '',
    personalCity: '',
    personalPostcode: '',
    personalState: '',

    // Business Information
    companyName: '',
    displayName: '',
    description: '',
    brn: '',
    operatingLicense: '',
    businessPhone: '',
    businessEmail: '',
    websiteUrl: '',
    agencyName: '',
    licenseExpiryDate: '',
    businessAddress1: '',
    businessAddress2: '',
    businessCity: '',
    businessPostcode: '',
    businessState: '',

    businessHours: {
      monday: { open: '09:00', close: '17:00', closed: false },
      tuesday: { open: '09:00', close: '17:00', closed: false },
      wednesday: { open: '09:00', close: '17:00', closed: false },
      thursday: { open: '09:00', close: '17:00', closed: false },
      friday: { open: '09:00', close: '17:00', closed: false },
      saturday: { open: '09:00', close: '17:00', closed: false },
      sunday: { open: '09:00', close: '17:00', closed: true }
    }
  });

  const [logoFile, setLogoFile] = useState(null);
  const [coverImageFile, setCoverImageFile] = useState(null);
  const [logoPreview, setLogoPreview] = useState('');
  const [coverImagePreview, setCoverImagePreview] = useState('');

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [errors, setErrors] = useState({});
  // Feedback modal state
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackModal, setFeedbackModal] = useState({
    type: 'success',
    title: '',
    message: '',
    details: null
  });

  // Helper functions for feedback modals
  const showSuccessModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'success',
      title,
      message,
      details
    });
    setShowFeedbackModal(true);
  };

  const showErrorModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'error',
      title,
      message,
      details
    });
    setShowFeedbackModal(true);
  };
  const [selectedFiles, setSelectedFiles] = useState([]);

  // Redirect if not authenticated or not a boat owner
  useEffect(() => {
    if (!isLoading && (!isAuthenticated || user?.role !== 'BOAT_OWNER' || !user?.isApproved)) {
      router.push('/');
      return;
    }
  }, [isAuthenticated, user, isLoading, router]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (postcodeLookupTimeout.current) {
        clearTimeout(postcodeLookupTimeout.current);
      }
    };
  }, []);

  // Fetch profile data
  useEffect(() => {
    const fetchProfileData = async () => {
      if (!accessToken) return;

      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/profile`, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          const { user: userData, provider } = data.data;

          setFormData({
            // Personal Information
            firstName: userData.profile?.firstName || '',
            lastName: userData.profile?.lastName || '',
            phone: userData.profile?.phone || '',
            dateOfBirth: userData.profile?.dateOfBirth ? userData.profile.dateOfBirth.split('T')[0] : '',
            emergencyContact: userData.profile?.emergencyContact || '',
            language: userData.profile?.language || 'en',
            timezone: userData.profile?.timezone || 'Asia/Kuala_Lumpur',
            personalAddress1: userData.profile?.personalAddress1 || '',
            personalAddress2: userData.profile?.personalAddress2 || '',
            personalCity: userData.profile?.personalCity || '',
            personalPostcode: userData.profile?.personalPostcode || '',
            personalState: userData.profile?.personalState || '',

            // Business Information
            companyName: provider?.companyName || '',
            displayName: provider?.displayName || '',
            description: provider?.description || '',
            brn: provider?.brn || '',
            operatingLicense: provider?.operatingLicense || '',
            businessPhone: provider?.businessPhone || '',
            businessEmail: provider?.businessEmail || '',
            websiteUrl: provider?.websiteUrl || '',
            agencyName: provider?.agencyName || '',
            licenseExpiryDate: provider?.licenseExpiryDate ? provider.licenseExpiryDate.split('T')[0] : '',
            businessAddress1: provider?.businessAddress1 || '',
            businessAddress2: provider?.businessAddress2 || '',
            businessCity: provider?.businessCity || '',
            businessPostcode: provider?.businessPostcode || '',
            businessState: provider?.businessState || '',

            businessHours: provider?.businessHours || formData.businessHours
          });

          // Set logo and cover image previews if they exist
          if (provider?.logoUrl) {
            setLogoPreview(`${process.env.NEXT_PUBLIC_API_URL}${provider.logoUrl}`);
          }
          if (provider?.coverImageUrl) {
            setCoverImagePreview(`${process.env.NEXT_PUBLIC_API_URL}${provider.coverImageUrl}`);
          }
        } else {
          setError('Failed to load profile data');
        }
      } catch (error) {
        console.error('Profile fetch error:', error);
        setError('Failed to load profile data');
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated && user?.role === 'BOAT_OWNER' && accessToken) {
      fetchProfileData();
    }
  }, [isAuthenticated, user, accessToken]);

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
    
    // Handle postcode auto-lookup for personal address
    if (name === "personalPostcode" && value.length >= 4) {
      // Clear any existing timeout
      if (postcodeLookupTimeout.current) {
        clearTimeout(postcodeLookupTimeout.current);
      }
      
      // Set new timeout to debounce the lookup
      postcodeLookupTimeout.current = setTimeout(() => {
        lookupPostcode(value, 'personal');
      }, 500);
    }
    
    // Handle postcode auto-lookup for business address
    if (name === "businessPostcode" && value.length >= 4) {
      // Clear any existing timeout
      if (postcodeLookupTimeout.current) {
        clearTimeout(postcodeLookupTimeout.current);
      }
      
      // Set new timeout to debounce the lookup
      postcodeLookupTimeout.current = setTimeout(() => {
        lookupPostcode(value, 'business');
      }, 500);
    }
  };

  // Lookup postcode and auto-fill city and state
  const lookupPostcode = async (postcode, addressType) => {
    if (!postcode || postcode.length < 4) return;
    
    try {
      const response = await fetch(`/api/search/postcode/${postcode}`);
      const data = await response.json();
      
      if (data.success && data.data) {
        if (addressType === 'personal') {
          setFormData(prev => ({
            ...prev,
            personalCity: data.data.city || prev.personalCity,
            personalState: data.data.state || prev.personalState,
          }));
        } else if (addressType === 'business') {
          setFormData(prev => ({
            ...prev,
            businessCity: data.data.city || prev.businessCity,
            businessState: data.data.state || prev.businessState,
          }));
        }
      }
    } catch (error) {
      console.error("Postcode lookup error:", error);
      // Don't show error to user as this is a convenience feature
    }
  };

  const handleBusinessHoursChange = (day, field, value) => {
    setFormData(prev => ({
      ...prev,
      businessHours: {
        ...prev.businessHours,
        [day]: {
          ...prev.businessHours[day],
          [field]: value
        }
      }
    }));
  };

  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    setSelectedFiles(files);
  };

  // Handle logo upload
  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setErrors(prev => ({ ...prev, logo: t('invalidImageFormat') }));
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setErrors(prev => ({ ...prev, logo: t('imageSizeLimit') }));
        return;
      }

      setLogoFile(file);
      setErrors(prev => ({ ...prev, logo: '' }));

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle cover image upload
  const handleCoverImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setErrors(prev => ({ ...prev, coverImage: t('invalidImageFormat') }));
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setErrors(prev => ({ ...prev, coverImage: t('imageSizeLimit') }));
        return;
      }

      setCoverImageFile(file);
      setErrors(prev => ({ ...prev, coverImage: '' }));

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setCoverImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Remove logo
  const removeLogo = () => {
    setLogoFile(null);
    setLogoPreview('');
    // Reset file input
    const fileInput = document.getElementById('logo');
    if (fileInput) {
      fileInput.value = '';
    }
  };

  // Remove cover image
  const removeCoverImage = () => {
    setCoverImageFile(null);
    setCoverImagePreview('');
    // Reset file input
    const fileInput = document.getElementById('coverImage');
    if (fileInput) {
      fileInput.value = '';
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Personal Information Validation - Commented out to prevent validation errors
    /*
    if (!formData.firstName.trim()) {
      newErrors.firstName = t('firstNameRequired');
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = t('lastNameRequired');
    }

    if (!formData.phone.trim()) {
      newErrors.phone = t('phoneRequired');
    } else if (!validateMalaysianPhoneNumber(formData.phone)) {
      newErrors.phone = t('invalidPhoneNumber');
    }
    */

    // Business Information Validation
    // Company Name is no longer required
    // if (!formData.companyName.trim()) {
    //   newErrors.companyName = t('companyNameRequired');
    // }

    if (!formData.displayName.trim()) {
      newErrors.displayName = t('displayNameRequired');
    }

    // Email validation for business email if provided
    if (formData.businessEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.businessEmail)) {
      newErrors.businessEmail = t('invalidEmailFormat');
    }

    // Phone validation for business phone if provided
    if (formData.businessPhone && !validateMalaysianPhoneNumber(formData.businessPhone)) {
      newErrors.businessPhone = t('invalidPhoneNumber');
    }

    // URL validation for website if provided
    if (formData.websiteUrl && !/^https?:\/\/.+/.test(formData.websiteUrl)) {
      newErrors.websiteUrl = t('invalidUrlFormat');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setSaving(true);
    setError('');

    try {
      // Strip trailing commas from address fields before saving
      const cleanFormData = {
        ...formData,
        personalAddress1: formData.personalAddress1?.replace(/,\s*$/, '') || '',
        personalAddress2: formData.personalAddress2?.replace(/,\s*$/, '') || '',
        businessAddress1: formData.businessAddress1?.replace(/,\s*$/, '') || '',
        businessAddress2: formData.businessAddress2?.replace(/,\s*$/, '') || ''
      };

      // Update profile
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/profile`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(cleanFormData)
      });

      if (response.ok) {
        // Upload logo if selected
        if (logoFile) {
          const logoFormData = new FormData();
          logoFormData.append('logo', logoFile);

          const logoResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/profile/logo`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${accessToken}`
            },
            body: logoFormData
          });

          if (!logoResponse.ok) {
            const errorData = await logoResponse.json();
            throw new Error(errorData.message || t('failedToUploadLogo'));
          }
        }

        // Upload cover image if selected
        if (coverImageFile) {
          const coverFormData = new FormData();
          coverFormData.append('cover', coverImageFile);

          const coverResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/profile/cover`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${accessToken}`
            },
            body: coverFormData
          });

          if (!coverResponse.ok) {
            const errorData = await coverResponse.json();
            throw new Error(errorData.message || t('failedToUploadCoverImage'));
          }
        }

        // Upload documents if any
        if (selectedFiles.length > 0) {
          const formDataFiles = new FormData();
          selectedFiles.forEach(file => {
            formDataFiles.append('documents', file);
          });

          await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/documents/upload`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${accessToken}`
            },
            body: formDataFiles
          });
        }

        // Refresh user data
        await getCurrentUser();
        
        showSuccessModal(
          t('success'),
          t('profileUpdatedSuccessfully')
        );
        
        // Redirect after a delay
        setTimeout(() => {
          router.push('/boat-owner/profile');
        }, 2000);
      } else {
        const errorData = await response.json();
        setError(errorData.message || t('failedToUpdateProfile'));
      }
    } catch (error) {
      console.error('Update profile error:', error);
      setError(error.message || t('failedToUpdateProfile'));
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  if (isLoading || loading) {
  

    return (
      <>
        <Head>
          <title>Edit Business Profile - GoSea</title>
          <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
        </Head>
        <div className="min-h-screen bg-slate-50">
          <Navbar currentPage="boat-owner" />
          <main className="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-4 sm:py-8 sm:px-0">
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sky-600"></div>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </>
    );
  }



  return (
    <>
      <Head>
        <title>Edit Business Profile - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen bg-slate-50">
        <Navbar currentPage="boat-owner" />

        <main className="max-w-4xl mx-auto sm:px-6 lg:px-8">
          <div className="px-4 py-4 sm:py-8 sm:px-0">
            {/* Page Header */}
            <div className="mb-6">
              <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                <span onClick={() => router.push('/boat-owner/profile')} className="hover:text-sky-600 cursor-pointer">
                  {t('businessProfile')}
                </span>
                <span>/</span>
                <span className="text-gray-900">{t('edit')}</span>
              </nav>
              <h1 className="text-2xl font-bold text-gray-900">
                {t('editBusinessProfile')}
              </h1>
              {/* <p className="text-gray-600 mt-2">
                {t('updateYourBusinessInformation')}
              </p> */}
            </div>

            {/* Error Message */}
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Personal Information - Commented out as per requirements */}
              {/* 
              <div className="bg-white rounded-lg shadow">
                <div className="p-6">
                  <h3 className="text-lg font-medium mb-4 flex items-center text-gray-900">
                    <UserIcon className="w-5 h-5 mr-2" />
                    {t('personalInformation')}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('firstName')} <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="firstName"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${
                          errors.firstName ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder={t('enterFirstName')}
                      />
                      {errors.firstName && (
                        <p className="mt-1 text-sm text-red-600">{errors.firstName}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('lastName')} <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="lastName"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${
                          errors.lastName ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder={t('enterLastName')}
                      />
                      {errors.lastName && (
                        <p className="mt-1 text-sm text-red-600">{errors.lastName}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('personalPhone')} <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${
                          errors.phone ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder={t('malaysiaPhoneNumberPlaceholder')}
                      />
                      {errors.phone && (
                        <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="dateOfBirth" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('dateOfBirth')}
                      </label>
                      <CustomDatePicker
                        name="dateOfBirth"
                        value={formData.dateOfBirth}
                        onChange={handleInputChange}
                        placeholder={t('selectDateOfBirth')}
                        error={!!errors.dateOfBirth}
                      />
                      {errors.dateOfBirth && (
                        <p className="mt-1 text-sm text-red-600">{errors.dateOfBirth}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="emergencyContact" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('emergencyContact')}
                      </label>
                      <input
                        type="tel"
                        id="emergencyContact"
                        name="emergencyContact"
                        value={formData.emergencyContact}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                        placeholder={t('malaysiaPhoneNumberPlaceholder')}
                      />
                    </div>

                    <div>
                      <label htmlFor="language" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('preferredLanguage')}
                      </label>
                      <select
                        id="language"
                        name="language"
                        value={formData.language}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                      >
                        <option value="en">English</option>
                        <option value="ms">Bahasa Malaysia</option>
                      </select>
                    </div>

                    <div>
                      <label htmlFor="timezone" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('timezone')}
                      </label>
                      <select
                        id="timezone"
                        name="timezone"
                        value={formData.timezone}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                      >
                        <option value="Asia/Kuala_Lumpur">{t('asiaKualaLumpur')}</option>
                        <option value="Asia/Kuching">{t('asiaKuching')}</option>
                        <option value="Asia/Singapore">{t('asiaSingapore')}</option>
                      </select>
                    </div>

                    <div className="md:col-span-2">
                      <h3 className="text-md font-medium text-gray-900 mb-4">{t('personalAddress')}</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label htmlFor="personalAddress1" className="block text-sm font-medium text-gray-700 mb-2">
                            {t('addressLine1')}
                          </label>
                          <input
                            type="text"
                            id="personalAddress1"
                            name="personalAddress1"
                            value={formData.personalAddress1}
                            onChange={handleInputChange}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                            placeholder={t('enterAddressLine1')}
                          />
                        </div>
                        <div>
                          <label htmlFor="personalAddress2" className="block text-sm font-medium text-gray-700 mb-2">
                            {t('addressLine2')}
                          </label>
                          <input
                            type="text"
                            id="personalAddress2"
                            name="personalAddress2"
                            value={formData.personalAddress2}
                            onChange={handleInputChange}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                            placeholder={t('enterAddressLine2')}
                          />
                        </div>
                        <div>
                          <label htmlFor="personalPostcode" className="block text-sm font-medium text-gray-700 mb-2">
                            {t('postcode')}
                          </label>
                          <input
                            type="text"
                            id="personalPostcode"
                            name="personalPostcode"
                            value={formData.personalPostcode}
                            onChange={handleInputChange}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                            placeholder={t('enterPostcode')}
                          />
                        </div>
                        <div>
                          <label htmlFor="personalCity" className="block text-sm font-medium text-gray-700 mb-2">
                            {t('city')}
                          </label>
                          <input
                            type="text"
                            id="personalCity"
                            name="personalCity"
                            value={formData.personalCity}
                            onChange={handleInputChange}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                            placeholder={t('enterCity')}
                          />
                        </div>
                        <div>
                          <label htmlFor="personalState" className="block text-sm font-medium text-gray-700 mb-2">
                            {t('state')}
                          </label>
                          <input
                            type="text"
                            id="personalState"
                            name="personalState"
                            value={formData.personalState}
                            onChange={handleInputChange}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                            placeholder={t('enterState')}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              */}

              {/* Business Information */}
              <div className="bg-white rounded-lg shadow">
                <div className="p-6">
                  <h3 className="text-lg font-medium mb-4 flex items-center text-gray-900">
                    <BuildingOfficeIcon className="w-5 h-5 mr-2" />
                    {t('businessInformation')}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="companyName" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('companyName')} {/* <span className="text-red-500">*</span> */}
                      </label>
                      <input
                        type="text"
                        id="companyName"
                        name="companyName"
                        value={formData.companyName}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${
                          errors.companyName ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder={t('enterCompanyName')}
                      />
                      {errors.companyName && (
                        <p className="mt-1 text-sm text-red-600">{errors.companyName}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="displayName" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('displayName')} <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="displayName"
                        name="displayName"
                        value={formData.displayName}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${
                          errors.displayName ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder={t('enterDisplayName')}
                      />
                      {errors.displayName && (
                        <p className="mt-1 text-sm text-red-600">{errors.displayName}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="brn" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('businessRegistrationNumber')}
                      </label>
                      <input
                        type="text"
                        id="brn"
                        name="brn"
                        value={formData.brn}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                        placeholder={t('enterBRN')}
                      />
                    </div>

                    <div>
                      <label htmlFor="operatingLicense" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('operatingLicense')}
                      </label>
                      <input
                        type="text"
                        id="operatingLicense"
                        name="operatingLicense"
                        value={formData.operatingLicense}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                        placeholder={t('enterOperatingLicense')}
                      />
                    </div>

                    <div>
                      <label htmlFor="businessPhone" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('businessPhone')}
                      </label>
                      <input
                        type="tel"
                        id="businessPhone"
                        name="businessPhone"
                        value={formData.businessPhone}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${
                          errors.businessPhone ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder={t('malaysiaPhoneNumberPlaceholder')}
                      />
                      {errors.businessPhone && (
                        <p className="mt-1 text-sm text-red-600">{errors.businessPhone}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="businessEmail" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('businessEmail')}
                      </label>
                      <input
                        type="email"
                        id="businessEmail"
                        name="businessEmail"
                        value={formData.businessEmail}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${
                          errors.businessEmail ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder={t('enterBusinessEmail')}
                      />
                      {errors.businessEmail && (
                        <p className="mt-1 text-sm text-red-600">{errors.businessEmail}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="websiteUrl" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('websiteUrl')}
                      </label>
                      <input
                        type="url"
                        id="websiteUrl"
                        name="websiteUrl"
                        value={formData.websiteUrl}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${
                          errors.websiteUrl ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder={t('websiteUrlPlaceholder')}
                      />
                      {errors.websiteUrl && (
                        <p className="mt-1 text-sm text-red-600">{errors.websiteUrl}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="agencyName" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('agencyName')}
                      </label>
                      <input
                        type="text"
                        id="agencyName"
                        name="agencyName"
                        value={formData.agencyName}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                        placeholder={t('enterAgencyName')}
                      />
                    </div>

                    <div>
                      <label htmlFor="licenseExpiryDate" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('licenseExpiryDate')}
                      </label>
                      <CustomDatePicker
                        name="licenseExpiryDate"
                        value={formData.licenseExpiryDate}
                        onChange={handleInputChange}
                        placeholder={t('selectLicenseExpiryDate')}
                        error={!!errors.licenseExpiryDate}
                      />
                      {errors.licenseExpiryDate && (
                        <p className="mt-1 text-sm text-red-600">{errors.licenseExpiryDate}</p>
                      )}
                    </div>

                    {/* Logo and Cover Image Upload - Placed inline in a single row */}
                    <div className="md:col-span-2">
                      <h3 className="text-md font-medium text-gray-900 mb-4">{t('uploadImages')}</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            {t('logo')}
                          </label>
                          <div className="flex items-center">
                            <input
                              type="file"
                              id="logo"
                              name="logo"
                              accept="image/*"
                              onChange={handleLogoUpload}
                              className="hidden"
                            />
                            <label
                              htmlFor="logo"
                              className="px-4 py-3 border border-gray-300 text-gray-700 rounded-lg cursor-pointer hover:border-2 hover:border-amber-500 transition-colors font-medium"
                            >
                              {t('uploadLogo')}
                            </label>
                            {logoPreview && (
                              <div className="relative ml-4">
                                <img
                                  src={logoPreview}
                                  alt="Logo preview"
                                  className="w-16 h-16 object-contain rounded"
                                />
                                <button
                                  type="button"
                                  onClick={removeLogo}
                                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                                >
                                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                                  </svg>
                                </button>
                              </div>
                            )}
                          </div>
                          {errors.logo && (
                            <p className="mt-1 text-sm text-red-600">{errors.logo}</p>
                          )}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            {t('coverImage')}
                          </label>
                          <div className="flex items-center">
                            <input
                              type="file"
                              id="coverImage"
                              name="coverImage"
                              accept="image/*"
                              onChange={handleCoverImageUpload}
                              className="hidden"
                            />
                            <label
                              htmlFor="coverImage"
                              className="px-4 py-3 border border-gray-300 text-gray-700 rounded-lg cursor-pointer hover:border-2 hover:border-amber-500 transition-colors font-medium"
                            >
                              {t('uploadCoverImage')}
                            </label>
                            {coverImagePreview && (
                              <div className="relative ml-4">
                                <img
                                  src={coverImagePreview}
                                  alt="Cover image preview"
                                  className="w-16 h-16 object-cover rounded"
                                />
                                <button
                                  type="button"
                                  onClick={removeCoverImage}
                                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                                >
                                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                                  </svg>
                                </button>
                              </div>
                            )}
                          </div>
                          {errors.coverImage && (
                            <p className="mt-1 text-sm text-red-600">{errors.coverImage}</p>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="md:col-span-2">
                      <h3 className="text-md font-medium text-gray-900 mb-4">{t('businessAddress')}</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label htmlFor="businessAddress1" className="block text-sm font-medium text-gray-700 mb-2">
                            {t('addressLine1')}
                          </label>
                          <input
                            type="text"
                            id="businessAddress1"
                            name="businessAddress1"
                            value={formData.businessAddress1}
                            onChange={handleInputChange}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                            placeholder={t('enterAddressLine1')}
                          />
                        </div>
                        <div>
                          <label htmlFor="businessAddress2" className="block text-sm font-medium text-gray-700 mb-2">
                            {t('addressLine2')}
                          </label>
                          <input
                            type="text"
                            id="businessAddress2"
                            name="businessAddress2"
                            value={formData.businessAddress2}
                            onChange={handleInputChange}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                            placeholder={t('enterAddressLine2')}
                          />
                        </div>
                        <div>
                          <label htmlFor="businessPostcode" className="block text-sm font-medium text-gray-700 mb-2">
                            {t('postcode')}
                          </label>
                          <input
                            type="text"
                            id="businessPostcode"
                            name="businessPostcode"
                            value={formData.businessPostcode}
                            onChange={handleInputChange}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                            placeholder={t('enterPostcode')}
                          />
                        </div>
                        <div>
                          <label htmlFor="businessCity" className="block text-sm font-medium text-gray-700 mb-2">
                            {t('city')}
                          </label>
                          <input
                            type="text"
                            id="businessCity"
                            name="businessCity"
                            value={formData.businessCity}
                            onChange={handleInputChange}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                            placeholder={t('enterCity')}
                          />
                        </div>
                        <div>
                          <label htmlFor="businessState" className="block text-sm font-medium text-gray-700 mb-2">
                            {t('state')}
                          </label>
                          <input
                            type="text"
                            id="businessState"
                            name="businessState"
                            value={formData.businessState}
                            onChange={handleInputChange}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                            placeholder={t('enterState')}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="md:col-span-2">
                      <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('businessDescription')}
                      </label>
                      <textarea
                        id="description"
                        name="description"
                        rows={4}
                        value={formData.description}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                        placeholder={t('describeYourBusiness')}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row sm:justify-end sm:space-x-4 space-y-3 sm:space-y-0">
                <button
                  type="button"
                  onClick={handleCancel}
                  className="w-full sm:w-auto px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                >
                  {t('cancel')}
                </button>
                <button
                  type="submit"
                  disabled={saving}
                  className="w-full sm:w-auto px-6 py-3 bg-sky-500 text-white rounded-lg hover:bg-sky-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
                >
                  {saving ? t('saving') : t('saveChanges')}
                </button>
              </div>
            </form>
          </div>
        </main>

        <Footer />
      </div>

      {/* Feedback Modal */}
      <StandardModal
        isOpen={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
        type={feedbackModal.type}
        title={feedbackModal.title}
        message={feedbackModal.message}
        details={feedbackModal.details}
        autoClose={feedbackModal.type === 'success'}
        autoCloseDelay={2000}
      />
    </>
  );
};

export default BoatOwnerProfileEdit;