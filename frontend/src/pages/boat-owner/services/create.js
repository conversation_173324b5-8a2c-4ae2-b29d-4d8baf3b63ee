import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useAuth } from '../../../contexts/AuthContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import Navbar from '../../../components/Navbar';
import Footer from '../../../components/Footer';
import StandardModal from '../../../components/StandardModal';
import { CloudArrowUpIcon, XMarkIcon, PlusIcon, MinusIcon } from '@heroicons/react/24/outline';

const CreateService = () => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading, accessToken } = useAuth();
  const { t } = useLanguage();
  const fileInputRef = useRef(null);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: '',
    pricing: {
      adult: '',
      child: '',
      infant: ''
    },
    packages: [],
    duration: '',
    maxCapacity: '',
    minCapacity: '1',
    includedItems: [],
    excludedItems: [],
    specialInstructions: '',
    cancellationPolicy: '',
    termsConditions: '',
    routes: [],
    boatIds: []
  });

  const [selectedImages, setSelectedImages] = useState([]);
  const [imagePreviews, setImagePreviews] = useState([]);
  const [availableBoats, setAvailableBoats] = useState([]);
  const [availableJetties, setAvailableJetties] = useState([]);
  const [availableDestinations, setAvailableDestinations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  // Feedback modal state
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackModal, setFeedbackModal] = useState({
    type: 'success',
    title: '',
    message: '',
    details: null
  });

  // Helper functions for feedback modals
  const showSuccessModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'success',
      title,
      message,
      details
    });
    setShowFeedbackModal(true);
  };

  const showErrorModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'error',
      title,
      message,
      details
    });
    setShowFeedbackModal(true);
  };

  // Redirect if not authenticated or not a boat owner
  useEffect(() => {
    if (!isLoading && (!isAuthenticated || user?.role !== 'BOAT_OWNER' || !user?.isApproved)) {
      router.push('/');
      return;
    }
  }, [isAuthenticated, user, isLoading, router]);

  // Fetch available boats and jetties
  useEffect(() => {
    const fetchData = async () => {
      if (!accessToken) return;

      try {
        // Fetch boats
        const boatsResponse = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/boats?limit=100`,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (boatsResponse.ok) {
          const boatsData = await boatsResponse.json();
          setAvailableBoats(boatsData.data);
        }

        // Fetch jetties and destinations
        const jettiesResponse = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/jetties`,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (jettiesResponse.ok) {
          const jettiesData = await jettiesResponse.json();
          setAvailableJetties(jettiesData.data);
          setAvailableDestinations(jettiesData.data); // Assuming destinations are also jetties
        }
      } catch (error) {
        console.error('Fetch data error:', error);
      }
    };

    if (isAuthenticated && user?.role === 'BOAT_OWNER' && accessToken) {
      fetchData();
    }
  }, [isAuthenticated, user, accessToken]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === 'checkbox' ? checked : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleIncludedItemsChange = (e) => {
    const { value, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      includedItems: checked
        ? [...prev.includedItems, value]
        : prev.includedItems.filter(item => item !== value)
    }));
  };

  const handleBoatSelection = (e) => {
    const { value, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      boatIds: checked
        ? [...prev.boatIds, value]
        : prev.boatIds.filter(id => id !== value)
    }));
  };

  const handleImageSelect = (e) => {
    const files = Array.from(e.target.files);
    if (files.length + selectedImages.length > 10) {
      alert(t('maxImagesAllowed', { max: 10 }));
      return;
    }

    setSelectedImages(prev => [...prev, ...files]);

    // Create previews
    files.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreviews(prev => [...prev, {
          file,
          url: e.target.result
        }]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  const addRoute = () => {
    setFormData(prev => ({
      ...prev,
      routes: [...prev.routes, {
        departureJettyId: '',
        destinationId: '',
        duration: '',
        price: ''
      }]
    }));
  };

  const removeRoute = (index) => {
    setFormData(prev => ({
      ...prev,
      routes: prev.routes.filter((_, i) => i !== index)
    }));
  };

  const updateRoute = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      routes: prev.routes.map((route, i) => 
        i === index ? { ...route, [field]: value } : route
      )
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = t('serviceNameRequired');
    }

    if (!formData.description.trim()) {
      newErrors.description = t('descriptionRequired');
    }

    if (!formData.category) {
      newErrors.category = t('categoryRequired');
    }

    if (!formData.pricing.adult || formData.pricing.adult < 0) {
      newErrors['pricing.adult'] = t('adultPriceRequired');
    }

    if (!formData.maxCapacity || formData.maxCapacity < 1) {
      newErrors.maxCapacity = t('maxCapacityRequired');
    }

    if (formData.boatIds.length === 0) {
      newErrors.boatIds = t('selectAtLeastOneBoat');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const formDataToSend = new FormData();
      
      // Add form fields
      Object.keys(formData).forEach(key => {
        if (typeof formData[key] === 'object' && formData[key] !== null) {
          formDataToSend.append(key, JSON.stringify(formData[key]));
        } else {
          formDataToSend.append(key, formData[key]);
        }
      });

      // Add images
      selectedImages.forEach(image => {
        formDataToSend.append('images', image);
      });

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/services`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`
        },
        body: formDataToSend
      });

      if (response.ok) {
        showSuccessModal(
          t('success'),
          t('serviceCreatedSuccessfully')
        );

        setTimeout(() => {
          router.push('/boat-owner/services');
        }, 2000);
      } else {
        const errorData = await response.json();
        showErrorModal(
          t('error'),
          errorData.message || t('failedToCreateService'),
          errorData.details || null
        );
      }
    } catch (error) {
      console.error('Create service error:', error);
      showErrorModal(
        t('error'),
        t('failedToCreateService'),
        error.message
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  if (isLoading) {
    return (
      <>
        <Head>
          <title>Create New Service - GoSea</title>
        </Head>
        <div className="min-h-screen bg-slate-50">
          <Navbar currentPage="boat-owner" />
          <main className="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-4 sm:py-8 sm:px-0">
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </>
    );
  }

  const categories = [
    'dayTrip',
    'snorkeling',
    'fishing',
    'islandHopping',
    'sunset',
    'diving',
    'watersports',
    'charter'
  ];

  const includedItemOptions = [
    'lifeJackets',
    'snorkelingGear',
    'fishingEquipment',
    'refreshments',
    'towels',
    'sunscreen',
    'firstAidKit',
    'waterSports'
  ];

  const pageName = "Create New Service";

  return (
    <>
      <Head>
        <title>{pageName} - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen bg-slate-50">
        <Navbar currentPage="boat-owner" />

        <main className="max-w-4xl mx-auto sm:px-6 lg:px-8">
          <div className="px-4 py-4 sm:py-8 sm:px-0">
            {/* Page Header */}
            <div className="mb-6">
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
                {t('createNewService')}
              </h1>
              <p className="text-gray-600 mt-2">
                {t('createNewServiceForYourBusiness')}
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">
                    {t('basicInformation')}
                  </h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('serviceName')} <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors.name ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder={t('enterServiceName')}
                      />
                      {errors.name && (
                        <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('category')} <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="category"
                        name="category"
                        value={formData.category}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors.category ? 'border-red-300' : 'border-gray-300'
                        }`}
                      >
                        <option value="">{t('selectCategory')}</option>
                        {categories.map(category => (
                          <option key={category} value={category}>
                            {t(category)}
                          </option>
                        ))}
                      </select>
                      {errors.category && (
                        <p className="mt-1 text-sm text-red-600">{errors.category}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('duration')} ({t('hours')})
                      </label>
                      <input
                        type="number"
                        id="duration"
                        name="duration"
                        min="0.5"
                        step="0.5"
                        value={formData.duration}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        placeholder={t('serviceDuration')}
                      />
                    </div>

                    <div>
                      <label htmlFor="maxCapacity" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('maxCapacity')} <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="number"
                        id="maxCapacity"
                        name="maxCapacity"
                        min="1"
                        value={formData.maxCapacity}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors.maxCapacity ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder={t('maximumPassengers')}
                      />
                      {errors.maxCapacity && (
                        <p className="mt-1 text-sm text-red-600">{errors.maxCapacity}</p>
                      )}
                    </div>

                    <div className="md:col-span-2">
                      <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('description')} <span className="text-red-500">*</span>
                      </label>
                      <textarea
                        id="description"
                        name="description"
                        rows={4}
                        value={formData.description}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors.description ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder={t('describeYourService')}
                      />
                      {errors.description && (
                        <p className="mt-1 text-sm text-red-600">{errors.description}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Pricing */}
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">
                    {t('pricing')}
                  </h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label htmlFor="pricing.adult" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('adultPrice')} (RM) <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="number"
                        id="pricing.adult"
                        name="pricing.adult"
                        min="0"
                        step="0.01"
                        value={formData.pricing.adult}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors['pricing.adult'] ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="0.00"
                      />
                      {errors['pricing.adult'] && (
                        <p className="mt-1 text-sm text-red-600">{errors['pricing.adult']}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="pricing.child" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('childPrice')} (RM)
                      </label>
                      <input
                        type="number"
                        id="pricing.child"
                        name="pricing.child"
                        min="0"
                        step="0.01"
                        value={formData.pricing.child}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        placeholder="0.00"
                      />
                    </div>

                    <div>
                      <label htmlFor="pricing.infant" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('infantPrice')} (RM)
                      </label>
                      <input
                        type="number"
                        id="pricing.infant"
                        name="pricing.infant"
                        min="0"
                        step="0.01"
                        value={formData.pricing.infant}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Boat Assignment */}
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">
                    {t('assignBoats')} <span className="text-red-500">*</span>
                  </h2>
                </div>
                <div className="p-6">
                  {availableBoats.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {availableBoats.map(boat => (
                        <label key={boat.id} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                          <input
                            type="checkbox"
                            value={boat.id}
                            checked={formData.boatIds.includes(boat.id)}
                            onChange={handleBoatSelection}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <div className="flex-1">
                            <p className="font-medium text-gray-900">{boat.name}</p>
                            <p className="text-sm text-gray-500">
                              {t('capacity')}: {boat.capacity} {t('passengers')}
                            </p>
                          </div>
                        </label>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-500">{t('noBoatsAvailable')}</p>
                      <p className="text-sm text-gray-400 mt-1">
                        {t('addBoatsFirst')}
                      </p>
                    </div>
                  )}
                  {errors.boatIds && (
                    <p className="mt-2 text-sm text-red-600">{errors.boatIds}</p>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row sm:justify-end sm:space-x-4 space-y-3 sm:space-y-0">
                <button
                  type="button"
                  onClick={handleCancel}
                  className="w-full sm:w-auto px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                >
                  {t('cancel')}
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full sm:w-auto px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
                >
                  {loading ? t('creating') : t('createService')}
                </button>
              </div>
            </form>
          </div>
        </main>

        <Footer />
      </div>

      {/* Feedback Modal */}
      <StandardModal
        isOpen={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
        type={feedbackModal.type}
        title={feedbackModal.title}
        message={feedbackModal.message}
        details={feedbackModal.details}
        autoClose={feedbackModal.type === 'success'}
        autoCloseDelay={2000}
      />
    </>
  );
};

export default CreateService;
