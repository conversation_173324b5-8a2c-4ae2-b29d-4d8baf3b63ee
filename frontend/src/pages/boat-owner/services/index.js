import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '../../../contexts/AuthContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import Navbar from '../../../components/Navbar';
import StandardModal from '../../../components/StandardModal';
import Footer from '../../../components/Footer';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  EyeIcon,
  TrashIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { MapPin, Clock, Users, DollarSign } from 'lucide-react';

const BoatOwnerServices = () => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading, accessToken } = useAuth();
  const { t } = useLanguage();

  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [categories, setCategories] = useState([]);

  // Feedback modal state
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackModal, setFeedbackModal] = useState({
    type: 'success',
    title: '',
    message: '',
    details: null
  });

  // Helper functions for feedback modals
  const showSuccessModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'success',
      title,
      message,
      details
    });
    setShowFeedbackModal(true);
  };

  const showErrorModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'error',
      title,
      message,
      details
    });
    setShowFeedbackModal(true);
  };

  // Redirect if not authenticated or not a boat owner
  useEffect(() => {
    if (!isLoading && (!isAuthenticated || user?.role !== 'BOAT_OWNER' || !user?.isApproved)) {
      router.push('/');
      return;
    }
  }, [isAuthenticated, user, isLoading, router]);

  // Fetch service categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/services/categories`
        );

        if (response.ok) {
          const data = await response.json();
          setCategories(data.data);
        } else {
          console.error('Failed to fetch service categories');
        }
      } catch (error) {
        console.error('Error fetching service categories:', error);
      }
    };

    fetchCategories();
  }, []);

  // Fetch services data
  useEffect(() => {
    const fetchServices = async () => {
      if (!accessToken) return;

      try {
        // Only add parameters that have values
        const params = new URLSearchParams({
          page: currentPage.toString(),
          limit: '12'
        });
        
        // Conditionally add search parameter
        if (searchTerm) {
          params.append('search', searchTerm);
        }
        
        // Conditionally add status parameter
        if (statusFilter) {
          params.append('status', statusFilter);
        }
        
        // Conditionally add category parameter
        if (categoryFilter) {
          params.append('category', categoryFilter);
        }

        const url = `${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/services?${params.toString()}`;
        console.log('Fetching services from:', url);
        
        const response = await fetch(url, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        });

        console.log('Services response status:', response.status);
        
        if (response.ok) {
          const data = await response.json();
          console.log('Services data received:', data);
          setServices(data.data);
          setTotalPages(data.pagination.pages);
        } else {
          const errorText = await response.text();
          console.error('Services fetch error response text:', errorText);
          try {
            const errorData = JSON.parse(errorText);
            setError(errorData.message || 'Failed to load services');
          } catch (e) {
            setError(`Failed to load services (${response.status}): ${errorText}`);
          }
        }
      } catch (error) {
        console.error('Services fetch error:', error);
        setError('Failed to load services: ' + error.message);
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated && user?.role === 'BOAT_OWNER' && accessToken) {
      fetchServices();
    }
  }, [isAuthenticated, user, accessToken, currentPage, searchTerm, statusFilter, categoryFilter]);

  const handleDeleteService = async (serviceId) => {
    if (!confirm(t('confirmDeleteService'))) return;

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/services/${serviceId}`,
        {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.ok) {
        setServices(services.filter(service => service.id !== serviceId));
        showSuccessModal(
          t('success'),
          t('serviceDeletedSuccessfully')
        );
      } else {
        const errorData = await response.json();
        showErrorModal(
          t('error'),
          errorData.message || t('failedToDeleteService'),
          errorData.details || null
        );
      }
    } catch (error) {
      console.error('Delete service error:', error);
      showErrorModal(
        t('error'),
        t('failedToDeleteService'),
        error.message
      );
    }
  };

  const getImageUrl = (imagePath) => {
    if (!imagePath) return '/images/service-placeholder.jpg';
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/uploads/')) {
      return `${process.env.NEXT_PUBLIC_API_URL}${imagePath}`;
    }
    return imagePath;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'INACTIVE':
        return 'bg-gray-100 text-gray-800';
      case 'DRAFT':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatPrice = (pricing) => {
    if (!pricing || typeof pricing !== 'object') return 'N/A';
    
    // Handle different pricing structures
    if (pricing.adult) {
      return `RM ${pricing.adult}`;
    }
    if (pricing.base) {
      return `RM ${pricing.base}`;
    }
    if (pricing.price) {
      return `RM ${pricing.price}`;
    }
    
    return 'N/A';
  };

  if (isLoading || loading) {
    const pageName = "My Services";

    return (
      <>
        <Head>
          <title>My Services - GoSea</title>
          <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
        </Head>
        <div className="min-h-screen bg-slate-50">
          <Navbar currentPage="boat-owner" />
          <main className="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-4 sm:py-8 sm:px-0">
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sky-600"></div>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>My Services - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen bg-slate-50">
        <Navbar currentPage="boat-owner" />

        <main className="max-w-7xl mx-auto px-4 py-4 md:py-6">
          {/* Page Header */}
          <div className="flex justify-between items-center mb-4 md:mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {t('myServices')}
              </h1>
              {/* <p className="text-gray-600 mt-1 text-sm">
                {t('manageYourServices')}
              </p> */}
            </div>
            <Link
              href="/boat-owner/services/create"
              className="flex items-center justify-center px-3 py-2 text-white bg-sky-500 rounded-md hover:bg-sky-600 active:bg-sky-600 active:text-white transition-colors font-medium text-sm touch-manipulation w-auto"
              style={{
                WebkitTapHighlightColor: 'transparent',
                touchAction: 'manipulation',
                cursor: 'pointer',
                userSelect: 'none'
              }}
            >
              <PlusIcon className="h-4 w-4 mr-1" />
              {t('createService')}
            </Link>
          </div>

          {/* Search and Filters */}
          <div className="bg-white sm:rounded-md shadow p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <label className="block text-sm font-medium mb-2" style={{ color: 'var(--base-content)' }}>{t('searchServices')}</label>
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder={t('searchServices')}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                  />
                </div>
              </div>
              <div className="sm:w-48">
                <label className="block text-sm font-medium mb-2" style={{ color: 'var(--base-content)' }}>{t('status')}</label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                >
                  <option value="">{t('allStatuses')}</option>
                  <option value="ACTIVE">{t('active')}</option>
                  <option value="INACTIVE">{t('inactive')}</option>
                  <option value="DRAFT">{t('draft')}</option>
                </select>
              </div>
              <div className="sm:w-48">
                <label className="block text-sm font-medium mb-2" style={{ color: 'var(--base-content)' }}>{t('category')}</label>
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                >
                  <option value="">{t('allCategories')}</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.code}>
                      {t(category.name)}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            {/* Services Grid or Empty State */}
            {services.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
                {services.map((service) => (
                  <div key={service.id} className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
                    {/* Service Image */}
                    <div className="relative h-48 rounded-t-lg overflow-hidden">
                      <img
                        src={getImageUrl(service.images?.[0])}
                        alt={service.name}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute top-4 right-4">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(service.status)}`}>
                          {t(service.status?.toLowerCase() || 'inactive')}
                        </span>
                      </div>
                      {service.category && (
                        <div className="absolute top-4 left-4">
                          <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                            {t(service.category)}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Service Info */}
                    <div className="p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {service.name}
                      </h3>
                      <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                        {service.description || t('noDescriptionAvailable')}
                      </p>

                      {/* Service Stats */}
                      <div className="grid grid-cols-2 gap-4 text-sm text-gray-500 mb-4">
                        <div className="flex items-center">
                          <DollarSign className="h-4 w-4 mr-1" />
                          <span>{formatPrice(service.pricing)}</span>
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          <span>{service.duration ? `${service.duration}h` : 'N/A'}</span>
                        </div>
                        <div className="flex items-center">
                          <Users className="h-4 w-4 mr-1" />
                          <span>{service.maxCapacity || 'N/A'} {t('max')}</span>
                        </div>
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1" />
                          <span>{service.serviceRoutes?.length || 0} {t('routes')}</span>
                        </div>
                      </div>

                      {/* Assigned Boats */}
                      {service.serviceAssignments && service.serviceAssignments.length > 0 && (
                        <div className="mb-4">
                          <p className="text-xs text-gray-500 mb-1">{t('assignedBoats')}:</p>
                          <div className="flex flex-wrap gap-1">
                            {service.serviceAssignments.slice(0, 2).map((assignment) => (
                              <span key={assignment.id} className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
                                {assignment.boat.name}
                              </span>
                            ))}
                            {service.serviceAssignments.length > 2 && (
                              <span className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
                                +{service.serviceAssignments.length - 2} {t('more')}
                              </span>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="flex space-x-2">
                        <Link
                          href={`/boat-owner/services/${service.id}`}
                          className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                        >
                          <EyeIcon className="h-4 w-4 mr-1" />
                          {t('view')}
                        </Link>
                        <Link
                          href={`/boat-owner/services/${service.id}/edit`}
                          className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-sky-500 hover:bg-sky-600 transition-colors"
                        >
                          <PencilIcon className="h-4 w-4 mr-1" />
                          {t('edit')}
                        </Link>
                        <button
                          onClick={() => handleDeleteService(service.id)}
                          className="px-3 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-amber-500 hover:bg-amber-600 transition-colors"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 bg-gray-50 mt-6 rounded-lg">
                <div className="w-20 h-20 flex items-center justify-center rounded-full bg-amber-100 text-amber-600 mx-auto mb-4">
                  <Cog6ToothIcon className="h-10 w-10" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {t('noServicesFound')}
                </h3>
                <p className="text-gray-500 mb-2">
                  {searchTerm || statusFilter || categoryFilter ? t('noServicesMatchFilters') : t('startByCreatingYourFirstService')}
                </p>
                <Link
                  href="/boat-owner/services/create"
                  className="inline-flex items-center justify-center space-x-1 px-3 py-3 text-sky-500 hover:text-sky-600 active:text-sky-700 transition-colors font-medium text-md touch-manipulation"
                  style={{
                    WebkitTapHighlightColor: 'transparent',
                    touchAction: 'manipulation',
                    cursor: 'pointer',
                    userSelect: 'none'
                  }}
                >
                  {t('createYourFirstService')}
                </Link>
              </div>
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && services.length > 0 && (
            <div className="mt-8 flex justify-center">
              <nav className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {t('previous')}
                </button>
                
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`px-3 py-2 border rounded-lg text-sm font-medium ${
                      currentPage === page
                        ? 'border-sky-500 bg-sky-50 text-sky-600'
                        : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </button>
                ))}
                
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {t('next')}
                </button>
              </nav>
            </div>
          )}
        </main>

        <Footer />
      </div>

      {/* Feedback Modal */}
      <StandardModal
        isOpen={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
        type={feedbackModal.type}
        title={feedbackModal.title}
        message={feedbackModal.message}
        details={feedbackModal.details}
        autoClose={feedbackModal.type === 'success'}
        autoCloseDelay={2000}
      />
    </>
  );
};

export default BoatOwnerServices;