import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '../../../contexts/AuthContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import Navbar from '../../../components/Navbar';
import Footer from '../../../components/Footer';
import {
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  WrenchScrewdriverIcon,
  PhotoIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import { Users, Anchor, Calendar, Settings } from 'lucide-react';

const BoatDetail = () => {
  const router = useRouter();
  const { id } = router.query;
  const { user, isAuthenticated, isLoading, accessToken } = useAuth();
  const { t } = useLanguage();

  const [boat, setBoat] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  // Redirect if not authenticated or not a boat owner
  useEffect(() => {
    if (!isLoading && (!isAuthenticated || user?.role !== 'BOAT_OWNER' || !user?.isApproved)) {
      router.push('/');
      return;
    }
  }, [isAuthenticated, user, isLoading, router]);

  // Fetch boat data
  useEffect(() => {
    const fetchBoat = async () => {
      if (!accessToken || !id) return;

      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/boats/${id}`,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (response.ok) {
          const data = await response.json();
          setBoat(data.data);
        } else if (response.status === 404) {
          setError(t('boatNotFound'));
        } else {
          setError(t('failedToLoadBoat'));
        }
      } catch (error) {
        console.error('Boat fetch error:', error);
        setError(t('failedToLoadBoat'));
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated && user?.role === 'BOAT_OWNER' && accessToken && id) {
      fetchBoat();
    }
  }, [isAuthenticated, user, accessToken, id, t]);

  const handleDeleteBoat = async () => {
    if (!confirm(t('confirmDeleteBoat'))) return;

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/boats/${id}`,
        {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.ok) {
        router.push('/boat-owner/boats');
      } else {
        const errorData = await response.json();
        alert(errorData.message || t('failedToDeleteBoat'));
      }
    } catch (error) {
      console.error('Delete boat error:', error);
      alert(t('failedToDeleteBoat'));
    }
  };

  const getImageUrl = (imagePath) => {
    if (!imagePath) return '/images/boat-placeholder.jpg';
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/uploads/')) {
      return `${process.env.NEXT_PUBLIC_API_URL}${imagePath}`;
    }
    return imagePath;
  };

  // Carousel navigation functions
  const nextImage = () => {
    if (boat && boat.images && boat.images.length > 0) {
      setSelectedImageIndex((prev) => (prev + 1) % boat.images.length);
    }
  };

  const prevImage = () => {
    if (boat && boat.images && boat.images.length > 0) {
      setSelectedImageIndex((prev) => (prev - 1 + boat.images.length) % boat.images.length);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'APPROVED':
        return 'bg-emerald-100 text-emerald-600';
      case 'PENDING_APPROVAL':
        return 'bg-amber-100 text-amber-600';
      case 'MAINTENANCE':
        return 'bg-sky-100 text-sky-600';
      case 'REJECTED':
        return 'bg-red-100 text-red-600';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'ACTIVE':
        return <CheckCircleIcon className="h-5 w-5" />;
      case 'INACTIVE':
        return <XCircleIcon className="h-5 w-5" />;
      case 'MAINTENANCE':
        return <WrenchScrewdriverIcon className="h-5 w-5" />;
      default:
        return <XCircleIcon className="h-5 w-5" />;
    }
  };

  if (isLoading || loading) {

    return (
      <>
        <Head>
          <title>Boat Details - GoSea</title>
          <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
        </Head>
        <div className="min-h-screen bg-slate-50">
          <Navbar currentPage="boat-owner" />
          <main className="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-4 sm:py-8 sm:px-0">
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sky-500"></div>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </>
    );
  }

  if (error || !boat) {

    return (
      <>
        <Head>
          <title>Boat Details - GoSea</title>
          <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
        </Head>
        <div className="min-h-screen bg-slate-50">
          <Navbar currentPage="boat-owner" />
          <main className="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-4 sm:py-8 sm:px-0">
              <div className="text-center py-12">
                <Anchor className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {error || t('boatNotFound')}
                </h3>
                <Link
                  href="/boat-owner/boats"
                  className="text-sky-500 hover:text-sky-600 font-medium"
                >
                  {t('backToBoats')}
                </Link>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Boat Details - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen bg-slate-50">
        <Navbar currentPage="boat-owner" />

        <main className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          <div className="px-4 py-4 sm:py-8 sm:px-0">
            {/* Page Header */}
            <div className="mb-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                    <Link href="/boat-owner/boats" className="hover:text-sky-500">
                      {t('myBoats')}
                    </Link>
                    <span>/</span>
                    <span className="text-gray-900">{boat.name}</span>
                  </nav>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {boat.name}
                  </h1>
                </div>
                <div className="flex space-x-3 mt-4 sm:mt-0">
                  <Link
                    href={`/boat-owner/boats/${boat.id}/edit`}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-sky-500 hover:bg-sky-600 transition-colors"
                  >
                    <PencilIcon className="h-4 w-4 mr-2" />
                    {t('edit')}
                  </Link>
                  <button
                    onClick={handleDeleteBoat}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-amber-500 hover:bg-amber-600 transition-colors"
                  >
                    <TrashIcon className="h-4 w-4 mr-2" />
                    {t('delete')}
                  </button>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Main Content */}
              <div className="lg:col-span-2 space-y-6">
                {/* Images */}
                <div className="bg-white rounded-lg shadow">
                  <div className="p-6">
                    {boat.images && boat.images.length > 0 ? (
                      <div className="relative">
                        {/* Main Image Display */}
                        <div className="relative h-96 bg-gray-200 rounded-lg overflow-hidden">
                          <img
                            src={getImageUrl(boat.images[selectedImageIndex])}
                            alt={boat.name}
                            className="w-full h-full object-cover"
                          />

                          {/* Navigation Arrows */}
                          {boat.images.length > 1 && (
                            <>
                              <button
                                onClick={prevImage}
                                className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                              >
                                <ChevronLeftIcon className="w-6 h-6" />
                              </button>
                              <button
                                onClick={nextImage}
                                className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                              >
                                <ChevronRightIcon className="w-6 h-6" />
                              </button>
                            </>
                          )}

                          {/* Image Counter */}
                          <div className="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
                            {selectedImageIndex + 1} / {boat.images.length}
                          </div>
                        </div>

                        {/* Thumbnail Navigation */}
                        {boat.images.length > 1 && (
                          <div className="flex gap-2 mt-4 overflow-x-auto pb-2">
                            {boat.images.map((image, index) => (
                              <button
                                key={index}
                                onClick={() => setSelectedImageIndex(index)}
                                className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors ${
                                  selectedImageIndex === index
                                    ? "border-sky-500"
                                    : "border-gray-200 hover:border-sky-300"
                                }`}
                              >
                                <img
                                  src={getImageUrl(image)}
                                  alt={`${boat.name} ${index + 1}`}
                                  className="w-full h-full object-cover"
                                />
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg">
                        <div className="bg-amber-100 rounded-full p-4 mb-4">
                          <PhotoIcon className="h-12 w-12 text-amber-500" />
                        </div>
                        <p className="text-gray-500">{t('noImagesAvailable')}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Description */}
                <div className="bg-white rounded-lg shadow">
                  <div className="px-6 pt-4">
                    <h2 className="text-lg font-semibold text-gray-900">
                      {t('description')}
                    </h2>
                  </div>
                  <div className="pb-6 px-6 pt-4">
                    <p className="text-gray-700 leading-relaxed">
                      {boat.description || t('noDescriptionAvailable')}
                    </p>
                  </div>
                </div>

                {/* Amenities */}
                {boat.amenities && Array.isArray(boat.amenities) && boat.amenities.length > 0 && (
                  <div className="bg-white rounded-lg shadow">
                    <div className="px-6 pt-4">
                      <h2 className="text-lg font-semibold text-gray-900">
                        {t('amenities')}
                      </h2>
                    </div>
                    <div className="pb-6 px-6 pt-4">
                      <div className="flex flex-wrap gap-2">
                        {boat.amenities.map((amenity, index) => (
                          <span 
                            key={index} 
                            className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-sky-100 text-sky-600"
                          >
                            {amenity}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Technical Specifications */}
                {boat.specifications && Object.keys(boat.specifications).length > 0 && (
                  <div className="bg-white rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-gray-200">
                      <h2 className="text-lg font-semibold text-gray-900">
                        {t('technicalSpecifications')}
                      </h2>
                    </div>
                    <div className="p-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {Object.entries(boat.specifications).map(([key, value]) => (
                          value && (
                            <div key={key} className="flex justify-between py-2 border-b border-gray-100 last:border-b-0">
                              <span className="font-medium text-gray-700">{t(key)}:</span>
                              <span className="text-gray-900">{value}</span>
                            </div>
                          )
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Basic Info */}
                <div className="bg-white rounded-lg shadow">
                  <div className="px-6 pt-4">
                    <h2 className="text-lg font-semibold text-gray-900">
                      {t('basicInformation')}
                    </h2>
                  </div>
                  <div className="pt-4 pb-6 px-6 space-y-4">
                    <div className="flex justify-between">
                      <span className="font-medium text-gray-700">{t('status')}:</span>
                      <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(boat.status)}`}>
                        {/* {getStatusIcon(boat.status)} */}
                        <span>{t(boat.status === 'PENDING_APPROVAL' ? 'PENDING  APPROVAL' : boat.status || 'INACTIVE')}</span>
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium text-gray-700">{t('capacity')}:</span>
                      <span className="text-gray-900 flex items-center space-x-1">
                        <Users className="h-4 w-4 text-gray-500" />
                        <span>{boat.capacity} {t('passengers')}</span>
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium text-gray-700">{t('registrationNumber')}:</span>
                      <span className="text-gray-900">{boat.registrationNumber}</span>
                    </div>
                    {boat.yearBuilt && (
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-700">{t('yearBuilt')}:</span>
                        <span className="text-gray-900">{boat.yearBuilt}</span>
                      </div>
                    )}
                    {boat.length && (
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-700">{t('length')}:</span>
                        <span className="text-gray-900">{boat.length}m</span>
                      </div>
                    )}
                    {boat.safetyRating && (
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-700">{t('safetyRating')}:</span>
                        <span className="text-gray-900">{boat.safetyRating}</span>
                      </div>
                    )}
                    {boat.serviceType && (
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-700">{t('serviceType')}:</span>
                        <span className="text-gray-900">{boat.serviceType}</span>
                      </div>
                    )}
                    {boat.location && (
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-700">{t('location')}:</span>
                        <span className="text-gray-900">
                          {typeof boat.location === 'object' 
                            ? boat.location.name 
                            : 'No location specified'}
                        </span>
                      </div>
                    )}
                    {boat.basePrice && (
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-700">{t('basePrice')}:</span>
                        <span className="text-gray-900">RM {boat.basePrice}</span>
                      </div>
                    )}
                    {boat.rejectionReason && (
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-700">{t('rejectionReason')}:</span>
                        <span className="text-gray-900">{boat.rejectionReason}</span>
                      </div>
                    )}
                    {boat.approvedBy && (
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-700">{t('approvedBy')}:</span>
                        <span className="text-gray-900">{boat.approvedBy}</span>
                      </div>
                    )}
                    {boat.approvedAt && (
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-700">{t('approvedAt')}:</span>
                        <span className="text-gray-900">
                          {new Date(boat.approvedAt).toLocaleDateString()}
                        </span>
                      </div>
                    )}
                    {boat.engineType && (
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-700">{t('engineType')}:</span>
                        <span className="text-gray-900">{boat.engineType}</span>
                      </div>
                    )}
                    {boat.enginePower && (
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-700">{t('enginePower')}:</span>
                        <span className="text-gray-900">{boat.enginePower}</span>
                      </div>
                    )}
                    {boat.material && (
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-700">{t('material')}:</span>
                        <span className="text-gray-900">{boat.material}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Service Assignments */}
                <div className="bg-white rounded-lg shadow">
                  <div className="px-6 pt-4">
                    <h2 className="text-lg font-semibold text-gray-900">
                      {t('assignedServices')}
                    </h2>
                  </div>
                  <div className="pb-6 px-6 pt-4">
                    {boat.serviceAssignments && boat.serviceAssignments.length > 0 ? (
                      <div className="space-y-3">
                        {boat.serviceAssignments.map((assignment) => (
                          <div key={assignment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                              <p className="font-medium text-gray-900">
                                {assignment.service.name}
                              </p>
                              <p className="text-sm text-gray-500">
                                {t(assignment.service.status?.toLowerCase() || 'inactive')}
                              </p>
                            </div>
                            <Link
                              href={`/boat-owner/services/${assignment.service.id}`}
                              className="text-sky-500 hover:text-sky-600 text-sm font-medium"
                            >
                              {t('view')}
                            </Link>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 bg-gray-50 rounded-lg">
                        <div className="bg-amber-100 rounded-full p-3 mx-auto mb-4 w-16 h-16 flex items-center justify-center">
                          <Settings className="h-10 w-10 text-amber-500" />
                        </div>
                        <p className="text-gray-500">{t('noServicesAssigned')}</p>
                        <Link
                          href="/boat-owner/services/create"
                          className="mt-2 text-sky-500 hover:text-sky-600 text-sm font-medium"
                        >
                          {t('createService')}
                        </Link>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>

        <Footer />
      </div>
    </>
  );
};

export default BoatDetail;