import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useAuth } from '../../../../contexts/AuthContext';
import { useLanguage } from '../../../../contexts/LanguageContext';
import Navbar from '../../../../components/Navbar';
import Footer from '../../../../components/Footer';
import StandardModal from '../../../../components/StandardModal';
import { CloudArrowUpIcon, XMarkIcon, PhotoIcon } from '@heroicons/react/24/outline';
import { Baseline, Camera, Cog, SquarePlus } from 'lucide-react';

const EditBoat = () => {
  const router = useRouter();
  const { id } = router.query;
  const { user, isAuthenticated, isLoading, accessToken } = useAuth();
  const { t } = useLanguage();
  const fileInputRef = useRef(null);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    capacity: '',
    registrationNumber: '',
    yearBuilt: '',
    engineType: '',
    length: '',
    safetyRating: 'A',
    serviceType: '',
    jettyId: '',
    amenities: [],
    specifications: {
      fuelCapacity: '',
      enginePower: '',
      material: ''
    },
    enginePower: '',
    material: ''
  });

  const [existingImages, setExistingImages] = useState([]);
  const [selectedImages, setSelectedImages] = useState([]);
  const [imagePreviews, setImagePreviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState({});
  // Feedback modal state
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackModal, setFeedbackModal] = useState({
    type: 'success',
    title: '',
    message: '',
    details: null
  });

  // Helper functions for feedback modals
  const showSuccessModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'success',
      title,
      message,
      details
    });
    setShowFeedbackModal(true);
  };

  const showErrorModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'error',
      title,
      message,
      details
    });
    setShowFeedbackModal(true);
  };

  // Jetties data for location dropdown
  const [jetties, setJetties] = useState([]);
  const [jettiesLoading, setJettiesLoading] = useState(false);
  const [jettiesError, setJettiesError] = useState('');

  // Service categories data for service type dropdown
  const [serviceCategories, setServiceCategories] = useState([]);
  const [serviceCategoriesLoading, setServiceCategoriesLoading] = useState(false);
  const [serviceCategoriesError, setServiceCategoriesError] = useState('');

  // Amenities searchable dropdown state
  const [amenitiesDropdownOpen, setAmenitiesDropdownOpen] = useState(false);
  const [amenitiesSearchTerm, setAmenitiesSearchTerm] = useState('');

  // Redirect if not authenticated or not a boat owner
  useEffect(() => {
    if (!isLoading && (!isAuthenticated || user?.role !== 'BOAT_OWNER' || !user?.isApproved)) {
      router.push('/');
      return;
    }
  }, [isAuthenticated, user, isLoading, router]);

  // Fetch jetties and service categories data
  useEffect(() => {
    const fetchData = async () => {
      // Fetch jetties
      setJettiesLoading(true);
      setJettiesError('');

      try {
        const jettiesResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/jetties`);

        if (jettiesResponse.ok) {
          const jettiesData = await jettiesResponse.json();
          setJetties(jettiesData.data || []);
        } else {
          setJettiesError('Failed to load locations');
        }
      } catch (error) {
        console.error('Error fetching jetties:', error);
        setJettiesError('Failed to load locations');
      } finally {
        setJettiesLoading(false);
      }

      // Fetch service categories
      setServiceCategoriesLoading(true);
      setServiceCategoriesError('');

      try {
        const categoriesResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/services/categories`);

        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json();
          setServiceCategories(categoriesData.data || []);
        } else {
          setServiceCategoriesError('Failed to load service types');
        }
      } catch (error) {
        console.error('Error fetching service categories:', error);
        setServiceCategoriesError('Failed to load service types');
      } finally {
        setServiceCategoriesLoading(false);
      }
    };

    fetchData();
  }, []);

  // Fetch boat data
  useEffect(() => {
    const fetchBoat = async () => {
      if (!accessToken || !id) return;

      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/boats/${id}`,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (response.ok) {
          const data = await response.json();
          const boat = data.data;
          
          setFormData({
            name: boat.name || '',
            description: boat.description || '',
            capacity: boat.capacity?.toString() || '',
            registrationNumber: boat.registrationNumber || '',
            yearBuilt: boat.yearBuilt?.toString() || '',
            engineType: boat.engineType || '',
            length: boat.length?.toString() || '',
            safetyRating: boat.safetyRating || 'A',
            serviceType: boat.serviceType || '',
            jettyId: boat.location?.id || '', // Extract jetty ID if location is an object
            amenities: Array.isArray(boat.amenities) ? boat.amenities : [],
            specifications: {
              fuelCapacity: boat.specifications?.fuelCapacity || '',
              enginePower: boat.specifications?.enginePower || '',
              material: boat.specifications?.material || ''
            },
            enginePower: boat.enginePower || '',
            material: boat.material || ''
          });
          
          setExistingImages(boat.galleryImages || []);
        } else if (response.status === 404) {
          router.push('/boat-owner/boats');
        } else {
          showErrorModal(
            t('error'),
            t('failedToLoadBoat')
          );
        }
      } catch (error) {
        console.error('Boat fetch error:', error);
        showErrorModal(
          t('error'),
          t('failedToLoadBoat'),
          error.message
        );
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated && user?.role === 'BOAT_OWNER' && accessToken && id) {
      fetchBoat();
    }
  }, [isAuthenticated, user, accessToken, id, t, router]);

  // Predefined amenities list
  const predefinedAmenities = [
    'Air Conditioning',
    'WiFi',
    'Restroom',
    'Kitchen',
    'Sound System',
    'Fishing Equipment',
    'Snorkeling Gear',
    'Life Jackets',
    'Cooler Box',
    'Shade/Canopy',
    'Seating Cushions',
    'Storage Compartments',
    'Ladder',
    'Safety Equipment',
    'First Aid Kit',
    'GPS Navigation',
    'Radio Communication',
    'Dive Platform',
    'Underwater Lights',
    'Fresh Water Shower'
  ];

  // Filter amenities based on search term
  const filteredAmenities = predefinedAmenities.filter(amenity =>
    amenity.toLowerCase().includes(amenitiesSearchTerm.toLowerCase()) &&
    !formData.amenities.includes(amenity)
  );

  // Image compression function
  const compressImage = (file, maxWidth = 1200, maxHeight = 800, quality = 0.8) => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;

        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx.drawImage(img, 0, 0, width, height);

        canvas.toBlob(
          (blob) => {
            // Create a new File object with the compressed blob
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            });
            resolve(compressedFile);
          },
          file.type,
          quality
        );
      };

      img.src = URL.createObjectURL(file);
    });
  };

  // Handle amenity selection
  const handleAmenitySelect = (amenity) => {
    if (!formData.amenities.includes(amenity)) {
      setFormData(prev => ({
        ...prev,
        amenities: [...prev.amenities, amenity]
      }));
    }
    setAmenitiesSearchTerm('');
    setAmenitiesDropdownOpen(false);
  };

  // Handle amenity removal
  const handleAmenityRemove = (amenityToRemove) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.filter(amenity => amenity !== amenityToRemove)
    }));
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === 'checkbox' ? checked : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleImageSelect = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length + selectedImages.length + existingImages.length > 10) {
      alert(t('imageLimitExceeded', { max: 10 }));
      return;
    }

    // Compress images before adding them
    const compressedFiles = [];
    for (const file of files) {
      if (file.type.startsWith('image/')) {
        try {
          const compressedFile = await compressImage(file);
          compressedFiles.push(compressedFile);
        } catch (error) {
          console.error('Error compressing image:', error);
          // If compression fails, use original file
          compressedFiles.push(file);
        }
      } else {
        compressedFiles.push(file);
      }
    }

    setSelectedImages(prev => [...prev, ...compressedFiles]);

    // Create previews
    compressedFiles.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreviews(prev => [...prev, {
          file,
          url: e.target.result
        }]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeExistingImage = (index) => {
    setExistingImages(prev => prev.filter((_, i) => i !== index));
  };

  const removeNewImage = (index) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  const getImageUrl = (imagePath) => {
    if (!imagePath) return '';
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/uploads/')) {
      return `${process.env.NEXT_PUBLIC_API_URL}${imagePath}`;
    }
    return imagePath;
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = t('boatNameRequired');
    }

    if (!formData.description.trim()) {
      newErrors.description = t('descriptionRequired');
    }

    if (!formData.capacity || formData.capacity < 1) {
      newErrors.capacity = t('validCapacityRequired');
    }

    if (!formData.registrationNumber.trim()) {
      newErrors.registrationNumber = t('registrationNumberRequired');
    }

    // Check for jettyId (new field) or location (old field) for backward compatibility
    if (!formData.jettyId && !formData.location) {
      newErrors.jettyId = t('locationRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setSaving(true);

    try {
      const formDataToSend = new FormData();
      
      // Add form fields, converting location field to jettyId if needed
      Object.keys(formData).forEach(key => {
        // Convert location field to jettyId for backward compatibility
        if (key === 'location' && formData.location) {
          formDataToSend.append('jettyId', formData.location);
        } 
        // Skip the old location field if we have jettyId
        else if (key === 'location' && formData.jettyId) {
          // Don't add the old location field
        }
        // Add other fields normally
        else if (typeof formData[key] === 'object' && formData[key] !== null) {
          formDataToSend.append(key, JSON.stringify(formData[key]));
        } else {
          formDataToSend.append(key, formData[key]);
        }
      });

      // Add existing images to keep
      formDataToSend.append('existingImages', JSON.stringify(existingImages));

      // Add new images
      selectedImages.forEach(image => {
        formDataToSend.append('images', image);
      });

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/boats/${id}`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${accessToken}`
          },
          body: formDataToSend
        }
      );

      if (response.ok) {
        showSuccessModal(
          t('success'),
          t('boatUpdatedSuccessfully')
        );

        setTimeout(() => {
          router.push(`/boat-owner/boats/${id}`);
        }, 2000);
      } else {
        const errorData = await response.json();
        showErrorModal(
          t('error'),
          errorData.message || t('failedToUpdateBoat'),
          errorData.details || null
        );
      }
    } catch (error) {
      console.error('Update boat error:', error);
      showErrorModal(
        t('error'),
        t('failedToUpdateBoat'),
        error.message
      );
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  if (isLoading || loading) {

    return (
      <>
        <Head>
          <title>Edit Boat - GoSea</title>
          <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
        </Head>
        <div className="min-h-screen bg-slate-50">
          <Navbar currentPage="boat-owner" />
          <main className="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-4 sm:py-8 sm:px-0">
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sky-600"></div>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </>
    );
  }

  const pageName = "Edit Boat";

  return (
    <>
      <Head>
        <title>Edit Boat - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen bg-slate-50">
        <Navbar currentPage="boat-owner" />

        <main className="max-w-4xl mx-auto sm:px-6 lg:px-8">
          <div className="px-4 py-4 sm:py-8 sm:px-0">
            {/* Page Header */}
            <div className="mb-6">
              <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                <span onClick={() => router.push('/boat-owner/boats')} className="hover:text-sky-600 cursor-pointer">
                  {t('myBoats')}
                </span>
                <span>/</span>
                <span onClick={() => router.push(`/boat-owner/boats/${id}`)} className="hover:text-sky-600 cursor-pointer">
                  {formData.name || t('boatDetails')}
                </span>
                <span>/</span>
                <span className="text-gray-900">{t('edit')}</span>
              </nav>
              <h1 className="text-2xl font-bold text-gray-900">
                {t('editBoat')}
              </h1>
              {/* <p className="text-gray-600 mt-2">
                {t('editYourBoatDetails')}
              </p> */}
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="bg-white rounded-lg shadow">
                <div className="p-6">
                  {/* Basic Information */}
                  <h3 className="text-lg font-medium mb-4 flex items-center text-gray-900">
                    <Baseline className="w-5 h-5 mr-2" />
                    {t('basicInformation')}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('boatName')} <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${errors.name ? 'border-red-300' : 'border-gray-300'
                          }`}
                        placeholder={t('enterBoatName')}
                      />
                      {errors.name && (
                        <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="capacity" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('capacity')} <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="number"
                        id="capacity"
                        name="capacity"
                        min="1"
                        max="100"
                        value={formData.capacity}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${errors.capacity ? 'border-red-300' : 'border-gray-300'
                          }`}
                        placeholder={t('maxPassengers')}
                      />
                      {errors.capacity && (
                        <p className="mt-1 text-sm text-red-600">{errors.capacity}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="registrationNumber" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('registrationNumber')} <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="registrationNumber"
                        name="registrationNumber"
                        value={formData.registrationNumber}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${errors.registrationNumber ? 'border-red-300' : 'border-gray-300'
                          }`}
                        placeholder={t('enterRegistrationNumber')}
                      />
                      {errors.registrationNumber && (
                        <p className="mt-1 text-sm text-red-600">{errors.registrationNumber}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="jettyId" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('location')} <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="jettyId"
                        name="jettyId"
                        value={formData.jettyId || formData.location || ''}  // Support both field names for backward compatibility
                        onChange={handleInputChange}
                        disabled={jettiesLoading}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${errors.jettyId || errors.location ? 'border-red-300' : 'border-gray-300'
                          } ${jettiesLoading ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}`}
                      >
                        <option value="">
                          {jettiesLoading ? t('loadingLocations') || 'Loading locations...' : t('selectLocation') || 'Select a location'}
                        </option>
                        {jetties.map((jetty) => (
                          <option key={jetty.id} value={jetty.id}>
                            {jetty.name} - {jetty.city}, {jetty.state}
                          </option>
                        ))}
                      </select>
                      {jettiesError && (
                        <p className="mt-1 text-sm text-red-600">{jettiesError}</p>
                      )}
                      {(errors.jettyId || errors.location) && (
                        <p className="mt-1 text-sm text-red-600">{errors.jettyId || errors.location}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="yearBuilt" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('yearBuilt')}
                      </label>
                      <input
                        type="number"
                        id="yearBuilt"
                        name="yearBuilt"
                        min="1950"
                        max={new Date().getFullYear()}
                        value={formData.yearBuilt}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                        placeholder={t('enterYearBuilt')}
                      />
                    </div>

                    <div>
                      <label htmlFor="serviceType" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('serviceType')}
                      </label>
                      <select
                        id="serviceType"
                        name="serviceType"
                        value={formData.serviceType}
                        onChange={handleInputChange}
                        disabled={serviceCategoriesLoading}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${serviceCategoriesLoading ? 'bg-gray-100 cursor-not-allowed border-gray-300' : 'bg-white border-gray-300'
                          }`}
                      >
                        <option value="">
                          {serviceCategoriesLoading ? t('loadingServiceTypes') || 'Loading service types...' : t('selectServiceType') || 'Select a service type'}
                        </option>
                        {serviceCategories.map((category) => (
                          <option key={category.id} value={category.id}>
                            {category.name}
                          </option>
                        ))}
                      </select>
                      {serviceCategoriesError && (
                        <p className="mt-1 text-sm text-red-600">{serviceCategoriesError}</p>
                      )}
                    </div>

                    <div className="md:col-span-2">
                      <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('description')} <span className="text-red-500">*</span>
                      </label>
                      <textarea
                        id="description"
                        name="description"
                        rows={4}
                        value={formData.description}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${errors.description ? 'border-red-300' : 'border-gray-300'
                          }`}
                        placeholder={t('describeYourBoat')}
                      />
                      {errors.description && (
                        <p className="mt-1 text-sm text-red-600">{errors.description}</p>
                      )}
                    </div>
                  </div>

                  <div className="border-t border-dotted border-gray-200 my-5"></div>

                  {/* Images Upload */}
                  <h3 className="text-lg font-medium mb-4 flex items-center text-gray-900">
                    <Camera className="w-5 h-5 mr-2" />
                    {t('boatImages')}
                  </h3>
                  <div className="mb-4">
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      className="w-full border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors"
                    >
                      <CloudArrowUpIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-sm text-gray-600">
                        {t('clickToUploadImages')}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        {t('maxImagesAllowed', { max: 10 })}
                      </p>
                    </button>
                    <input
                      ref={fileInputRef}
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handleImageSelect}
                      className="hidden"
                    />
                  </div>

                  {/* Image Previews */}
                  {(existingImages.length > 0 || imagePreviews.length > 0) && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                      {/* Existing Images */}
                      {existingImages.map((image, index) => (
                        <div key={`existing-${index}`} className="relative">
                          <img
                            src={getImageUrl(image)}
                            alt={`Existing ${index + 1}`}
                            className="w-full h-24 object-cover rounded-lg"
                          />
                          <button
                            type="button"
                            onClick={() => removeExistingImage(index)}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                          >
                            <XMarkIcon className="h-4 w-4" />
                          </button>
                        </div>
                      ))}

                      {/* New Image Previews */}
                      {imagePreviews.map((preview, index) => (
                        <div key={`preview-${index}`} className="relative">
                          <img
                            src={preview.url}
                            alt={`Preview ${index + 1}`}
                            className="w-full h-24 object-cover rounded-lg"
                          />
                          <button
                            type="button"
                            onClick={() => removeNewImage(index)}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                          >
                            <XMarkIcon className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="border-t border-dotted border-gray-200 my-5"></div>

                  {/* Technical Specifications */}
                  <h3 className="text-lg font-medium flex items-center text-gray-900 mb-4">
                    <Cog className="w-5 h-5 mr-2" />
                    {t('technicalSpecifications')}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label htmlFor="length" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('length')} (m)
                      </label>
                      <input
                        type="number"
                        id="length"
                        name="length"
                        step="0.1"
                        value={formData.length}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                        placeholder={t('boatLength')}
                      />
                    </div>

                    <div>
                      <label htmlFor="engineType" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('engineType')}
                      </label>
                      <input
                        type="text"
                        id="engineType"
                        name="engineType"
                        value={formData.engineType}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                        placeholder={t('engineType')}
                      />
                    </div>



                    <div>
                      <label htmlFor="enginePower" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('enginePower')} (HP)
                      </label>
                      <input
                        type="text"
                        id="enginePower"
                        name="enginePower"
                        value={formData.enginePower}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                        placeholder={t('enginePower')}
                      />
                    </div>

                    <div>
                      <label htmlFor="material" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('material')}
                      </label>
                      <input
                        type="text"
                        id="material"
                        name="material"
                        value={formData.material}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                        placeholder={t('material')}
                      />
                    </div>

                    <div>
                      <label htmlFor="safetyRating" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('safetyRating')}
                      </label>
                      <select
                        id="safetyRating"
                        name="safetyRating"
                        value={formData.safetyRating}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                      >
                        <option value="A">{t('safetyRatingA')}</option>
                        <option value="B">{t('safetyRatingB')}</option>
                        <option value="C">{t('safetyRatingC')}</option>
                      </select>
                    </div>


                  </div>

                  <div className="border-t border-dotted border-gray-200 my-5"></div>

                  <div className='mb-6'>
                    {/* Amenities */}
                    <h3 className="text-lg font-medium mb-4 flex items-center text-gray-900">
                      <SquarePlus className="w-5 h-5 mr-2" />
                      {t('amenities')}
                    </h3>
                    {/* Searchable Dropdown */}
                    <div className="relative">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Add Amenities
                      </label>
                      <div className="relative">
                        <input
                          type="text"
                          value={amenitiesSearchTerm}
                          onChange={(e) => {
                            setAmenitiesSearchTerm(e.target.value);
                            setAmenitiesDropdownOpen(true);
                          }}
                          onFocus={() => setAmenitiesDropdownOpen(true)}
                          placeholder="Search for amenities or type to add custom..."
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                        />

                        {amenitiesDropdownOpen && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                            {/* Predefined Amenities */}
                            {filteredAmenities.length > 0 && (
                              <div>
                                <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50">
                                  Suggested Amenities
                                </div>
                                {filteredAmenities.map((amenity, index) => (
                                  <button
                                    key={index}
                                    type="button"
                                    onClick={() => handleAmenitySelect(amenity)}
                                    className="w-full text-left px-4 py-2 hover:bg-sky-50 hover:text-sky-700 transition-colors"
                                  >
                                    {amenity}
                                  </button>
                                ))}
                              </div>
                            )}

                            {/* Custom Amenity Option */}
                            {amenitiesSearchTerm.trim() && !predefinedAmenities.includes(amenitiesSearchTerm.trim()) && (
                              <div>
                                {filteredAmenities.length > 0 && <div className="border-t border-gray-200"></div>}
                                <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50">
                                  Add Custom
                                </div>
                                <button
                                  type="button"
                                  onClick={() => handleAmenitySelect(amenitiesSearchTerm.trim())}
                                  className="w-full text-left px-4 py-2 hover:bg-green-50 hover:text-green-700 transition-colors"
                                >
                                  Add "{amenitiesSearchTerm.trim()}"
                                </button>
                              </div>
                            )}

                            {/* No results */}
                            {filteredAmenities.length === 0 && !amenitiesSearchTerm.trim() && (
                              <div className="px-4 py-3 text-sm text-gray-500">
                                Start typing to search amenities...
                              </div>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Close dropdown when clicking outside */}
                      {amenitiesDropdownOpen && (
                        <div
                          className="fixed inset-0 z-0"
                          onClick={() => setAmenitiesDropdownOpen(false)}
                        ></div>
                      )}

                      {/* Selected Amenities */}
                      {formData.amenities.length > 0 && (
                        <div className="m-4">
                          <h3 className="text-sm font-medium text-gray-700 mb-2">Selected Amenities:</h3>
                          <div className="flex flex-wrap gap-2">
                            {formData.amenities.map((amenity, index) => (
                              <span
                                key={index}
                                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-sky-100 text-sky-800"
                              >
                                {amenity}
                                <button
                                  type="button"
                                  onClick={() => handleAmenityRemove(amenity)}
                                  className="ml-2 text-sky-600 hover:text-sky-800"
                                >
                                  <XMarkIcon className="h-4 w-4" />
                                </button>
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row sm:justify-end sm:space-x-4 space-y-3 sm:space-y-0">
                <button
                  type="button"
                  onClick={handleCancel}
                  className="w-full sm:w-auto px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                >
                  {t('cancel')}
                </button>
                <button
                  type="submit"
                  disabled={saving}
                  className="w-full sm:w-auto px-6 py-3 bg-sky-500 text-white rounded-lg hover:bg-sky-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
                >
                  {saving ? t('saving') : t('saveChanges')}
                </button>
              </div>
            </form>
          </div>
        </main>

        <Footer />

        {/* Feedback Modal */}
        <StandardModal
          isOpen={showFeedbackModal}
          onClose={() => setShowFeedbackModal(false)}
          type={feedbackModal.type}
          title={feedbackModal.title}
          message={feedbackModal.message}
          details={feedbackModal.details}
          autoClose={feedbackModal.type === 'success'}
          autoCloseDelay={2000}
        />
      </div>
    </>
  );
};

export default EditBoat;