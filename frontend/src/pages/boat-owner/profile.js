import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Navbar from '../../components/Navbar';
import Footer from '../../components/Footer';
import StandardModal from '../../components/StandardModal';
import {
  BuildingOfficeIcon,
  PencilIcon,
  ClockIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

const BoatOwnerProfile = () => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading, accessToken } = useAuth();
  const { t } = useLanguage();

  // Utility function to format dates (matching profile.js format)
  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Utility function to format dates with time (matching profile.js format)
  const formatDateTime = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  const [profileData, setProfileData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  // Feedback modal state
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackModal, setFeedbackModal] = useState({
    type: 'success',
    title: '',
    message: '',
    details: null
  });

  // Helper functions for feedback modals
  const showSuccessModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'success',
      title,
      message,
      details
    });
    setShowFeedbackModal(true);
  };

  const showErrorModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'error',
      title,
      message,
      details
    });
    setShowFeedbackModal(true);
  };

  // Redirect if not authenticated or not a boat owner
  useEffect(() => {
    if (!isLoading && (!isAuthenticated || user?.role !== 'BOAT_OWNER' || !user?.isApproved)) {
      router.push('/');
      return;
    }
  }, [isAuthenticated, user, isLoading, router]);

  // Fetch profile data
  useEffect(() => {
    const fetchProfileData = async () => {
      if (!accessToken) return;

      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/profile`, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          setProfileData(data.data);
        } else {
          setError('Failed to load profile data');
        }
      } catch (error) {
        console.error('Profile fetch error:', error);
        setError('Failed to load profile data');
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated && user?.role === 'BOAT_OWNER' && accessToken) {
      fetchProfileData();
    }
  }, [isAuthenticated, user, accessToken]);

  if (isLoading || loading) {

    return (
      <>
        <Head>
          <title>Business Profile - GoSea</title>
          <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
        </Head>
        <div className="min-h-screen bg-slate-50">
          <Navbar currentPage="boat-owner" />
          <main className="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-4 sm:py-8 sm:px-0">
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </>
    );
  }

  if (error) {

    return (
      <>
        <Head>
          <title>Business Profile - GoSea</title>
          <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
        </Head>
        <div className="min-h-screen bg-slate-50">
          <Navbar currentPage="boat-owner" />
          <main className="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-4 sm:py-8 sm:px-0">
              <div className="text-center py-12">
                <p className="text-red-600">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {t('retry')}
                </button>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </>
    );
  }

  const getImageUrl = (imagePath) => {
    if (!imagePath) return null;
    if (imagePath.startsWith("http")) return imagePath;
    if (imagePath.startsWith("/uploads/")) {
      return `${process.env.NEXT_PUBLIC_API_URL}${imagePath}`;
    }
    return imagePath;
  };

  const formatName = (firstName, lastName) => {
    const first = firstName ? firstName.charAt(0).toUpperCase() + firstName.slice(1).toLowerCase() : '';
    const last = lastName ? lastName.charAt(0).toUpperCase() + lastName.slice(1).toLowerCase() : '';
    return `${first} ${last}`.trim();
  };

  // Utility function to format business address
  const formatBusinessAddress = (provider) => {
    if (!provider) return "";
    const parts = [];
    if (provider.businessAddress1) parts.push(provider.businessAddress1);
    if (provider.businessAddress2) parts.push(provider.businessAddress2);
    if (provider.businessCity) parts.push(provider.businessCity);
    if (provider.businessPostcode) parts.push(provider.businessPostcode);
    if (provider.businessState) parts.push(provider.businessState);
    return parts.join(", ");
  };

  // Utility function to display "Not provided" for empty values
  const displayValue = (value) => {
    return value || t('notProvided');
  };

  return (
    <>
      <Head>
        <title>Business Profile - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen" style={{ backgroundColor: "#f8fafc" }}>
        <Navbar currentPage="boat-owner" />

        <main className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          <div className="px-4 py-4 sm:py-8 sm:px-0">
            {/* Page Header */}
            <div className="mb-4 md:mb-6">
              <div className="flex justify-between items-center">
                <div>
                  <h1 className="text-xl md:text-2xl font-bold" style={{ color: "#0f172a" }}>
                    {t('businessProfile')}
                  </h1>
                </div>
                <Link
                  href="/boat-owner/profile/edit"
                  className="flex items-center justify-center px-3 py-2 text-white bg-sky-500 rounded-md hover:bg-sky-600 active:bg-sky-600 active:text-white transition-colors font-medium text-sm touch-manipulation w-auto"
                  style={{
                    WebkitTapHighlightColor: 'transparent',
                    touchAction: 'manipulation',
                    cursor: 'pointer',
                    userSelect: 'none'
                  }}
                >
                  <PencilIcon className="h-4 w-4 mr-1" />
                  {t('edit')}
                </Link>
              </div>
            </div>

            {/* Profile Information Card */}
            <div
              className="rounded-2xl p-8 shadow-lg border mb-8"
              style={{
                backgroundColor: "#ffffff",
                borderColor: "#e2e8f0",
              }}
            >
              <div className="flex items-center mb-8">
                {profileData?.provider?.logoUrl ? (
                  <img
                    src={getImageUrl(profileData.provider.logoUrl)}
                    alt="Business Logo"
                    className="w-16 h-16 rounded-full object-cover mr-6"
                    style={{ border: "2px solid #e2e8f0" }}
                  />
                ) : (
                  <div
                    className="w-16 h-16 rounded-full flex items-center justify-center text-xl font-bold mr-6"
                    style={{
                      backgroundColor: "#f59e0b",
                      color: "#ffffff",
                    }}
                  >
                    <BuildingOfficeIcon className="h-8 w-8" />
                  </div>
                )}
                <div>
                  <h2
                    className="text-2xl font-bold"
                    style={{ color: "#0f172a" }}
                  >
                    {profileData?.provider?.companyName || profileData?.provider?.displayName}
                  </h2>
                  <p className="text-lg mt-1" style={{ color: "#64748b" }}>
                    {profileData?.user?.email}
                  </p>
                </div>
              </div>

              {/* Divider between profile header and information sections */}
              <div className="border-t border-gray-200 my-6"></div>

              {/* Profile Details - Changed to top-bottom layout with 2-column sections */}
              <div className="space-y-8 mb-8">
                {/* Account Information - Moved to top */}
                <div>
                  <h3
                    className="text-lg font-semibold mb-4"
                    style={{ color: "#0f172a" }}
                  >
                    {t("accountInformation")}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium" style={{ color: "#64748b" }}>
                        {t("accountStatus")}
                      </p>
                      <p className={`text-base ${profileData?.user?.isApproved ? 'text-emerald-500' : 'text-gray-900'}`}>
                        {profileData?.user?.isApproved
                          ? t("approved")
                          : t("pendingApproval")}
                      </p>
                    </div>

                    {profileData?.user?.approvedAt && (
                      <div>
                        <p className="text-sm font-medium" style={{ color: "#64748b" }}>
                          {t("approvedOn")}
                        </p>
                        <p className="text-base" style={{ color: "#0f172a" }}>
                          {formatDate(profileData.user.approvedAt)}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Business Information - Moved to below account info */}
                <div className="mb-10">
                  <h3
                    className="text-lg font-semibold mb-4"
                    style={{ color: "#0f172a" }}
                  >
                    {t("businessInformation")}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium" style={{ color: "#64748b" }}>
                        {t("companyName")}
                      </p>
                      <p className="text-base" style={{ color: "#0f172a" }}>
                        {displayValue(profileData?.provider?.companyName)}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm font-medium" style={{ color: "#64748b" }}>
                        {t("displayName")}
                      </p>
                      <p className="text-base" style={{ color: "#0f172a" }}>
                        {displayValue(profileData?.provider?.displayName)}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm font-medium" style={{ color: "#64748b" }}>
                        {t("businessRegistrationNumber")}
                      </p>
                      <p className="text-base" style={{ color: "#0f172a" }}>
                        {displayValue(profileData?.provider?.brn)}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm font-medium" style={{ color: "#64748b" }}>
                        {t("operatingLicense")}
                      </p>
                      <p className="text-base" style={{ color: "#0f172a" }}>
                        {displayValue(profileData?.provider?.operatingLicense)}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm font-medium" style={{ color: "#64748b" }}>
                        {t("licenseExpiryDate")}
                      </p>
                      <p className="text-base" style={{ color: "#0f172a" }}>
                        {profileData?.provider?.licenseExpiryDate ? formatDate(profileData.provider.licenseExpiryDate) : t('notProvided')}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm font-medium" style={{ color: "#64748b" }}>
                        {t("businessPhone")}
                      </p>
                      <p className="text-base" style={{ color: "#0f172a" }}>
                        {displayValue(profileData?.provider?.businessPhone)}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm font-medium" style={{ color: "#64748b" }}>
                        {t("businessEmail")}
                      </p>
                      <p className="text-base" style={{ color: "#0f172a" }}>
                        {displayValue(profileData?.provider?.businessEmail)}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm font-medium" style={{ color: "#64748b" }}>
                        {t("websiteUrl")}
                      </p>
                      <p className="text-base" style={{ color: "#0f172a" }}>
                        {profileData?.provider?.websiteUrl ? (
                          <a
                            href={profileData.provider.websiteUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 underline"
                          >
                            {profileData.provider.websiteUrl}
                          </a>
                        ) : (
                          <span>{t('notProvided')}</span>
                        )}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm font-medium" style={{ color: "#64748b" }}>
                        {t("agencyName")}
                      </p>
                      <p className="text-base" style={{ color: "#0f172a" }}>
                        {displayValue(profileData?.provider?.agencyName)}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm font-medium" style={{ color: "#64748b" }}>
                        {t("businessAddress")}
                      </p>
                      <p className="text-base" style={{ color: "#0f172a" }}>
                        {formatBusinessAddress(profileData?.provider) || t('notProvided')}
                      </p>
                    </div>

                    {/* Description placed at the last left row */}
                    <div className="md:col-span-2">
                      <p className="text-sm font-medium" style={{ color: "#64748b" }}>
                        {t("description")}
                      </p>
                      <p className="text-base" style={{ color: "#0f172a" }}>
                        {displayValue(profileData?.provider?.description)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Business Images */}
              {(profileData?.provider?.logoUrl || profileData?.provider?.coverImageUrl) && (
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <h3
                    className="text-lg font-semibold mb-4"
                    style={{ color: "#0f172a" }}
                  >
                    {t("businessImages")}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {profileData?.provider?.logoUrl && (
                      <div>
                        <p
                          className="text-sm font-medium mb-2"
                          style={{ color: "#64748b" }}
                        >
                          {t("businessLogo")}
                        </p>
                        <img
                          src={getImageUrl(profileData.provider.logoUrl)}
                          alt={t('businessLogo')}
                          className="w-32 h-32 object-cover rounded-lg border border-gray-200"
                        />
                      </div>
                    )}
                    {profileData?.provider?.coverImageUrl && (
                      <div>
                        <p
                          className="text-sm font-medium mb-2"
                          style={{ color: "#64748b" }}
                        >
                          {t("coverImage")}
                        </p>
                        <img
                          src={getImageUrl(profileData.provider.coverImageUrl)}
                          alt={t('coverImage')}
                          className="w-full h-64 object-cover rounded-lg border border-gray-200"
                        />
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Certifications */}
              {profileData?.provider?.certifications && (
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <h3
                    className="text-lg font-semibold mb-4"
                    style={{ color: "#0f172a" }}
                  >
                    {t("certifications")}
                  </h3>
                  <div className="space-y-3">
                    {Array.isArray(profileData.provider.certifications) ?
                      profileData.provider.certifications.map((cert, index) => (
                        <div key={index} className="p-3 border border-gray-200 rounded-lg">
                          <p className="font-medium text-gray-900">
                            {cert.name || cert.title || `${t('certification')} ${index + 1}`}
                          </p>
                          {cert.issuer && (
                            <p className="text-sm text-gray-600">
                              {t('issuedBy')}: {cert.issuer}
                            </p>
                          )}
                          {cert.expiryDate && (
                            <p className="text-sm text-gray-600">
                              {t('expiresOn')}: {formatDate(cert.expiryDate)}
                            </p>
                          )}
                        </div>
                      )) : (
                        <div className="p-3 border border-gray-200 rounded-lg">
                          <p className="text-gray-900">
                            {JSON.stringify(profileData.provider.certifications)}
                          </p>
                        </div>
                      )
                    }
                  </div>
                </div>
              )}

              {/* 
              Documents section temporarily hidden
              <div className="mt-8 pt-6 border-t border-gray-200">
                <h3
                  className="text-lg font-semibold mb-4"
                  style={{ color: "#0f172a" }}
                >
                  {t("businessDocuments")}
                </h3>
                <div className="p-6 bg-gray-50 rounded-lg">
                  {profileData?.provider?.documents && profileData.provider.documents.length > 0 ? (
                    <div className="space-y-3">
                      {profileData.provider.documents.map((doc, index) => (
                        <div key={index} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg bg-white">
                          <DocumentTextIcon className="h-5 w-5 text-gray-400" />
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">
                              {doc.originalName}
                            </p>
                            <p className="text-xs text-gray-500">
                              {t('uploadedOn')} {new Date(doc.uploadedAt).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">
                        {t('noDocumentsUploaded')}
                      </p>
                      <Link
                        href="/boat-owner/profile/edit"
                        className="mt-2 text-blue-600 hover:text-blue-700 text-sm font-medium"
                      >
                        {t('uploadDocuments')}
                      </Link>
                    </div>
                  )}
                </div>
              </div>
              */}
            </div>
          </div>
        </main>

        <Footer />
      </div>

      {/* Feedback Modal */}
      <StandardModal
        isOpen={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
        type={feedbackModal.type}
        title={feedbackModal.title}
        message={feedbackModal.message}
        details={feedbackModal.details}
        autoClose={feedbackModal.type === 'success'}
        autoCloseDelay={2000}
      />
    </>
  );
};

export default BoatOwnerProfile;