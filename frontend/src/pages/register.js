import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import RegisterForm from '../components/auth/RegisterForm';
import { useAuth } from '../contexts/AuthContext';

/**
 * Registration Page for GoSea Platform
 * Redirects authenticated users to dashboard
 */

const RegisterPage = () => {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();

  // Redirect authenticated users
  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      const redirectTo = router.query.redirect || '/dashboard';
      router.push(redirectTo);
    }
  }, [isAuthenticated, isLoading, router]);

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-accent-50">
        <div className="loading loading-spinner loading-lg text-primary"></div>
      </div>
    );
  }

  // Don't render register form if user is authenticated
  if (isAuthenticated) {
    return null;
  }

  const pageName = "Sign Up";

  return (
    <>
      <Head>
        <title>{pageName} - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" type="image/png" href="gosea_favicon.png" />
      </Head>
      
      <RegisterForm />
    </>
  );
};

export default RegisterPage;
