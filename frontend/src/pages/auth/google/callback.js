import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../../../contexts/AuthContext';

/**
 * Google OAuth callback page for handling Google OAuth redirects
 * This page processes the tokens and user data from the backend OAuth flow
 */
export default function GoogleOAuthCallback() {
  const router = useRouter();
  const { login } = useAuth();
  const [status, setStatus] = useState('processing');
  const [error, setError] = useState(null);
  const [processed, setProcessed] = useState(false);
  const [redirecting, setRedirecting] = useState(false);
  const [redirectAttempted, setRedirectAttempted] = useState(false);

  useEffect(() => {
    const handleOAuthCallback = async () => {
      try {
        // Prevent re-execution
        if (processed || redirectAttempted) {
          console.log('OAuth callback already processed or redirect attempted, skipping');
          return;
        }

        // Debug: Log all query parameters
        console.log('All query parameters:', router.query);
        console.log('URL search params:', window.location.search);

        const { token, refreshToken, sessionToken, user, error: urlError, returnUrl: queryReturnUrl } = router.query;

        if (urlError) {
          console.error('OAuth error from URL:', urlError);
          setError(getErrorMessage(urlError));
          setStatus('error');
          return;
        }

        if (!token || !refreshToken || !user) {
          console.error('Missing OAuth data:', {
            token: !!token,
            refreshToken: !!refreshToken,
            sessionToken: !!sessionToken,
            user: !!user,
            allParams: router.query
          });
          setError(`Invalid OAuth response. Missing authentication data. Received: ${JSON.stringify(router.query)}`);
          setStatus('error');
          return;
        }

        // Log sessionToken availability for debugging
        if (!sessionToken) {
          console.warn('SessionToken not provided in OAuth callback - logout may not work properly');
        } else {
          console.log('SessionToken received in OAuth callback');
        }

        // Parse user data
        let userData;
        try {
          userData = JSON.parse(user);
        } catch (parseError) {
          console.error('Failed to parse user data:', parseError);
          setError('Invalid user data format.');
          setStatus('error');
          return;
        }

        console.log('Processing Google OAuth callback for user:', userData.email);

        // Store tokens and user data using AuthContext
        await login({
          accessToken: token,
          refreshToken: refreshToken,
          sessionToken: sessionToken || null,
          user: userData
        });

        setProcessed(true);
        setStatus('success');

        // Check if this is a first-time Google user
        const isFirstTimeUser = userData.isFirstTimeGoogleUser || router.query.first_time === 'true';

        // Get the return URL - prioritize query parameter from backend, then storage
        let returnUrl;
        try {
          // First, check if backend provided return URL in query parameter
          returnUrl = queryReturnUrl;

          // If not available from backend, try session storage
          if (!returnUrl) {
            returnUrl = sessionStorage.getItem('gosea_return_url');
          }

          // If sessionStorage is empty, try localStorage backup
          if (!returnUrl) {
            returnUrl = localStorage.getItem('gosea_return_url_backup');
            console.log('🔄 Using localStorage backup for return URL');
          }

          console.log('🔍 OAuth Callback Debug:');
          console.log('  - Return URL from backend query param:', queryReturnUrl);
          console.log('  - Retrieved return URL from sessionStorage:', sessionStorage.getItem('gosea_return_url'));
          console.log('  - Retrieved return URL from localStorage backup:', localStorage.getItem('gosea_return_url_backup'));
          console.log('  - Final return URL:', returnUrl);
          console.log('  - Current window location:', window.location.href);
          console.log('  - SessionStorage keys:', Object.keys(sessionStorage));
        } catch (error) {
          console.error('Failed to retrieve return URL from storage:', error);
          returnUrl = null;
        }

        // Prevent multiple redirects
        if (redirecting) {
          console.log('Already redirecting, skipping');
          return;
        }
        setRedirecting(true);
        setRedirectAttempted(true);

        if (isFirstTimeUser && (!returnUrl || returnUrl === '/')) {
          console.log('First-time Google user with no specific return URL, redirecting to homepage with profile completion');
          // Redirect first-time users to homepage with profile completion modal
          setTimeout(() => {
            if (router.isReady) {
              try {
                router.replace('/?show_profile_completion=true');
              } catch (redirectError) {
                console.error('Homepage redirect error:', redirectError);
                window.location.replace('/?show_profile_completion=true');
              }
            } else {
              window.location.replace('/?show_profile_completion=true');
            }
          }, 1500);
        } else {
          // Always redirect back to the original page to preserve form data
          const redirectUrl = returnUrl && returnUrl !== '/auth/google/callback' ? returnUrl : '/';
          console.log('Redirecting to:', redirectUrl);

          // Clear the return URL from both storage mechanisms
          try {
            sessionStorage.removeItem('gosea_return_url');
            localStorage.removeItem('gosea_return_url_backup');
            console.log('Cleared return URL from both sessionStorage and localStorage');
          } catch (error) {
            console.error('Failed to clear return URL from storage:', error);
          }

          // If it's a first-time user but they have a return URL, add profile completion flag as query param
          const finalUrl = isFirstTimeUser && returnUrl && returnUrl !== '/'
            ? `${redirectUrl}${redirectUrl.includes('?') ? '&' : '?'}show_profile_completion=true`
            : redirectUrl;

          console.log('Final redirect URL:', finalUrl);

          // Use replace instead of push to avoid navigation conflicts
          setTimeout(() => {
            if (router.isReady) {
              try {
                console.log('Attempting to redirect to:', finalUrl);
                // Use window.location.replace for more reliable redirect
                window.location.replace(finalUrl);
              } catch (redirectError) {
                console.error('Redirect error:', redirectError);
                // Fallback to window.location.href if replace fails
                window.location.href = finalUrl;
              }
            } else {
              // If router is not ready, use window.location directly
              console.log('Router not ready, using window.location for redirect to:', finalUrl);
              window.location.replace(finalUrl);
            }
          }, 1500);
        }

      } catch (error) {
        console.error('Google OAuth callback processing error:', error);
        setError('Failed to process authentication. Please try again.');
        setStatus('error');
      }
    };

    // Only process if we have query parameters
    if (router.isReady && !processed) {
      handleOAuthCallback();
    }
  }, [router.isReady, processed, login]);

  const getErrorMessage = (errorCode) => {
    switch (errorCode) {
      case 'oauth_failed':
        return 'Google authentication failed. Please try again.';
      case 'oauth_callback_failed':
        return 'Authentication callback failed. Please try again.';
      default:
        return 'Authentication error occurred. Please try again.';
    }
  };

  const handleRetry = () => {
    router.push('/login');
  };

  const handleGoHome = () => {
    router.push('/');
  };

  if (status === 'processing') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 to-accent-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-neutral-focus mb-2">
              Completing Google Authentication
            </h2>
            <p className="text-neutral-500">
              Please wait while we process your Google login...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (status === 'success') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 to-accent-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="text-success text-6xl mb-4">✓</div>
            <h2 className="text-xl font-semibold text-neutral-focus mb-2">
              Authentication Successful!
            </h2>
            <p className="text-neutral-500 mb-4">
              Welcome to GoSea! Redirecting to your dashboard...
            </p>
            <div className="loading loading-dots loading-md text-primary"></div>
          </div>
        </div>
      </div>
    );
  }

  if (status === 'error') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 to-accent-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="text-error text-6xl mb-4">✗</div>
            <h2 className="text-xl font-semibold text-neutral-focus mb-2">
              Authentication Failed
            </h2>
            <p className="text-neutral-500 mb-6">
              {error || 'An unexpected error occurred during authentication.'}
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                onClick={handleRetry}
                className="btn btn-primary"
              >
                Try Again
              </button>
              <button
                onClick={handleGoHome}
                className="btn btn-outline"
              >
                Go Home
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
}
