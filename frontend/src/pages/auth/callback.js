import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';

/**
 * OAuth callback page for handling Google OAuth redirects
 * This page processes the tokens and user data from the backend OAuth flow
 */
export default function AuthCallback() {
  const router = useRouter();
  const { login } = useAuth();
  const [status, setStatus] = useState('processing');
  const [error, setError] = useState(null);

  useEffect(() => {
    const handleOAuthCallback = async () => {
      try {
        const { token, refreshToken, user, error: urlError } = router.query;

        if (urlError) {
          console.error('OAuth error from URL:', urlError);
          setError(getErrorMessage(urlError));
          setStatus('error');
          return;
        }

        if (!token || !refreshToken || !user) {
          console.error('Missing OAuth data:', { token: !!token, refreshToken: !!refreshToken, user: !!user });
          setError('Invalid OAuth response. Missing authentication data.');
          setStatus('error');
          return;
        }

        // Parse user data
        let userData;
        try {
          userData = JSON.parse(user);
        } catch (parseError) {
          console.error('Failed to parse user data:', parseError);
          setError('Invalid user data format.');
          setStatus('error');
          return;
        }

        console.log('Processing OAuth callback for user:', userData.email);

        // Store tokens and user data using AuthContext
        await login({
          accessToken: token,
          refreshToken: refreshToken,
          user: userData
        });

        setStatus('success');

        // Get the return URL from session storage or stay on home page
        const returnUrl = sessionStorage.getItem('gosea_return_url');
        const redirectUrl = returnUrl && returnUrl !== '/auth/callback' ? returnUrl : '/';

        // Clear the return URL from session storage
        sessionStorage.removeItem('gosea_return_url');

        // Redirect back to the original page or home page
        setTimeout(() => {
          if (router.isReady) {
            try {
              router.replace(redirectUrl);
            } catch (redirectError) {
              console.error('Redirect error:', redirectError);
              window.location.replace(redirectUrl);
            }
          } else {
            window.location.replace(redirectUrl);
          }
        }, 1500);

      } catch (error) {
        console.error('OAuth callback processing error:', error);
        setError('Failed to process authentication. Please try again.');
        setStatus('error');
      }
    };

    // Only process if we have query parameters
    if (router.isReady) {
      handleOAuthCallback();
    }
  }, [router.isReady, router.query, login]);

  const getErrorMessage = (errorCode) => {
    switch (errorCode) {
      case 'oauth_failed':
        return 'Google authentication failed. Please try again.';
      case 'oauth_callback_failed':
        return 'Authentication callback failed. Please try again.';
      default:
        return 'Authentication error occurred. Please try again.';
    }
  };

  const handleRetry = () => {
    router.push('/login');
  };

  const handleGoHome = () => {
    router.push('/');
  };

  if (status === 'processing') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 to-accent-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-neutral-focus mb-2">
              Completing Authentication
            </h2>
            <p className="text-neutral">
              Please wait while we process your Google authentication...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (status === 'success') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 to-accent-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="w-12 h-12 bg-success rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-neutral-focus mb-2">
              Authentication Successful!
            </h2>
            <p className="text-neutral mb-4">
              Welcome to GoSea! Redirecting you to your dashboard...
            </p>
            <div className="animate-pulse text-primary">
              Redirecting in 2 seconds...
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (status === 'error') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 to-accent-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="w-12 h-12 bg-error rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-neutral-focus mb-2">
              Authentication Failed
            </h2>
            <p className="text-neutral mb-6">
              {error}
            </p>
            <div className="space-y-3">
              <button
                onClick={handleRetry}
                className="btn btn-primary w-full"
              >
                Try Again
              </button>
              <button
                onClick={handleGoHome}
                className="btn btn-outline btn-neutral w-full"
              >
                Go to Homepage
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
}
