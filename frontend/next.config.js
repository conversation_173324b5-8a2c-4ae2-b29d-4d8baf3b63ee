/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  
  // Environment variables
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // API configuration
  async rewrites() {
    // Use internal API URL for server-side requests (within Docker network)
    // and public API URL for client-side requests
    const apiUrl = process.env.INTERNAL_API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

    // Only add rewrites if we have a valid API URL
    if (!apiUrl || apiUrl === 'undefined') {
      console.warn('No API URL configured, skipping API rewrites');
      return [];
    }

    return [
      {
        source: '/api/:path*',
        destination: `${apiUrl}/api/:path*`,
      },
    ];
  },

  // Image optimization
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'localhost',
        port: '',
        pathname: '/**',
      }
    ],
    formats: ['image/webp', 'image/avif'],
  },



  // Headers for security
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },

  // Experimental features
  experimental: {
    serverComponentsExternalPackages: ['sharp'],
  },

  // Development-specific configuration
  ...(process.env.NODE_ENV === 'development' && {
    // Disable static optimization in development for better Docker compatibility
    staticPageGenerationTimeout: 1000,
    // Improve development server performance
    onDemandEntries: {
      maxInactiveAge: 25 * 1000,
      pagesBufferLength: 2,
    },
  }),

  // Webpack configuration for development
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer) {
      // Enable polling for hot reload in Docker
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
      };

      // Fix for Docker development static file serving
      config.output = {
        ...config.output,
        hotUpdateMainFilename: 'static/webpack/[fullhash].[runtime].hot-update.json',
        hotUpdateChunkFilename: 'static/webpack/[id].[fullhash].hot-update.js',
      };
    }
    return config;
  },

  // Output configuration
  output: 'standalone',
};

module.exports = nextConfig;
