version: '3.8'

services:
  # Development service
  gosea-frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: gosea-frontend-dev
    ports:
      - "3002:3000"
    volumes:
      # Mount source code for hot reloading
      - .:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
      - WATCHPACK_POLLING=true
    networks:
      - gosea-network
    restart: unless-stopped
    profiles:
      - dev

  # Production service
  gosea-frontend-prod:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: gosea-frontend-prod
    ports:
      - "3003:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
    networks:
      - gosea-network
    restart: unless-stopped
    profiles:
      - prod

networks:
  gosea-network:
    driver: bridge
