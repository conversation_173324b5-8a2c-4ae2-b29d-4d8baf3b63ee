version: '3.8'

services:
  gosea-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: gosea-frontend-dev
    ports:
      - "3002:3000"
    volumes:
      # Mount source code for hot reloading
      - .:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
      - WATCHPACK_POLLING=true
      - CHOKIDAR_USEPOLLING=true
    networks:
      - gosea-network
    restart: unless-stopped
    stdin_open: true
    tty: true

networks:
  gosea-network:
    driver: bridge
