#### Maximum Capacity ####
max capacity is based on physical seats data in boat table (column = capacity)
to overide this value, is by editing the service assignment table (column = maxCapacityOverride)

#### Service Date Avauilability ####
service date availability is based on the service schedule table (column = dayOfWeek & departureTime)
if dayOfWeek is null, then the service is available for all days
to disable a service for a specific day, set the service availability date, timeslot and isActive to false

#### Admin Account ####
"email": "<EMAIL>"
"password": "TestAdmin123!"

#### Reset and Re-seed Database ####
docker exec gosea-backend npx prisma migrate reset --force