# Docker Development Requirements - GoSea Platform

## Overview
The GoSea platform requires a fully containerized development environment using <PERSON><PERSON> and Docker Compose to ensure consistency across development teams and environments.

## Container Architecture

### Service Overview
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Database      │
│   (Next.js)     │    │  (Express.js)   │    │ (PostgreSQL)    │
│   Port: 3000    │    │   Port: 5000    │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────┐    ┌─────────────────┐
         │     SMTP        │    │     Redis       │
         │   (MailHog)     │    │   (Optional)    │
         │ Port: 1025/8025 │    │   Port: 6379    │
         └─────────────────┘    └─────────────────┘
```

## Required Docker Files

### 1. Root Level Configuration

#### docker-compose.yml
```yaml
version: '3.8'
services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://backend:5000
    depends_on:
      - backend
    networks:
      - gosea-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    ports:
      - "5000:5000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - DATABASE_URL=*****************************************/gosea_db
      - SMTP_HOST=mailhog
      - SMTP_PORT=1025
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - mailhog
    networks:
      - gosea-network

  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=gosea_db
      - POSTGRES_USER=gosea
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/prisma/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - gosea-network

  mailhog:
    image: mailhog/mailhog:latest
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - gosea-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - gosea-network

volumes:
  postgres_data:
  redis_data:

networks:
  gosea-network:
    driver: bridge
```

### 2. Frontend Container Configuration

#### frontend/Dockerfile.dev
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Install pnpm
RUN npm install -g pnpm

# Copy package files
COPY package*.json pnpm-lock.yaml ./

# Install dependencies
RUN pnpm install

# Copy source code
COPY . .

# Expose port
EXPOSE 3000

# Start development server
CMD ["pnpm", "dev"]
```

#### frontend/.dockerignore
```
node_modules
.next
.git
.gitignore
README.md
Dockerfile*
.dockerignore
.env.local
.env.*.local
```

### 3. Backend Container Configuration

#### backend/Dockerfile.dev
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Install pnpm
RUN npm install -g pnpm

# Copy package files
COPY package*.json pnpm-lock.yaml ./

# Install dependencies
RUN pnpm install

# Copy source code
COPY . .

# Expose port
EXPOSE 5000

# Start development server with nodemon
CMD ["pnpm", "dev"]
```

#### backend/.dockerignore
```
node_modules
.git
.gitignore
README.md
Dockerfile*
.dockerignore
.env.local
.env.*.local
uploads/
logs/
```

## Development Workflow

### 1. Initial Setup
```bash
# Clone repository
git clone <repository-url>
cd gosea-project

# Create environment files
cp .env.example .env.local

# Start all services
docker-compose up -d

# Run database migrations
docker-compose exec backend pnpm prisma migrate dev

# Seed database (if needed)
docker-compose exec backend pnpm prisma db seed
```

### 2. Daily Development
```bash
# Start development environment
docker-compose up -d

# View logs
docker-compose logs -f frontend
docker-compose logs -f backend

# Install new dependencies
docker-compose exec frontend pnpm add <package>
docker-compose exec backend pnpm add <package>

# Run tests
docker-compose exec frontend pnpm test
docker-compose exec backend pnpm test

# Access container shell
docker-compose exec frontend sh
docker-compose exec backend sh

# Stop environment
docker-compose down
```

### 3. Database Operations
```bash
# Run migrations
docker-compose exec backend pnpm prisma migrate dev

# Reset database
docker-compose exec backend pnpm prisma migrate reset

# Generate Prisma client
docker-compose exec backend pnpm prisma generate

# View database
docker-compose exec backend pnpm prisma studio
```

## Environment Variables

### .env.example
```env
# Database
DATABASE_URL=*****************************************/gosea_db

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:5000
API_PORT=5000

# Email Configuration
SMTP_HOST=mailhog
SMTP_PORT=1025
SMTP_USER=
SMTP_PASS=

# Redis Configuration
REDIS_URL=redis://redis:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# File Upload
UPLOAD_PATH=/app/uploads
MAX_FILE_SIZE=10MB

# Development
NODE_ENV=development
```

## Performance Considerations

### 1. Volume Mounting Strategy
- Source code mounted for hot reload
- node_modules excluded to prevent conflicts
- Build artifacts (.next) excluded for performance

### 2. Container Optimization
- Alpine Linux base images for smaller size
- Multi-stage builds for production
- Proper layer caching for faster rebuilds

### 3. Development Features
- Hot reload for both frontend and backend
- Automatic restart on file changes
- Live database connection
- Email testing interface

## Troubleshooting

### Common Issues
1. **Port conflicts**: Ensure ports 3000, 5000, 5432, 8025 are available
2. **Permission issues**: Use proper user mapping in containers
3. **Volume mounting**: Ensure proper path mapping for hot reload
4. **Network connectivity**: Verify container networking configuration

### Debugging Commands
```bash
# Check container status
docker-compose ps

# View container logs
docker-compose logs [service-name]

# Restart specific service
docker-compose restart [service-name]

# Rebuild containers
docker-compose build --no-cache

# Clean up
docker-compose down -v
docker system prune -a
```

## Production Considerations
- Separate docker-compose.prod.yml for production
- Multi-stage builds for optimized images
- Health checks and restart policies
- Secrets management
- Container orchestration (Kubernetes/Docker Swarm)
