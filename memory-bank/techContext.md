# Technical Context - GoSea Platform

## Frontend Stack
### Core Framework
- Framework: Next.js (Latest stable version)
- Justification:
  - Server-side rendering for better SEO and performance
  - Built-in API routes for backend integration
  - File-based routing simplifies navigation
  - Built-in image optimization for boat photos
  - JavaScript for simpler development

### UI Layer
- CSS Framework: Tailwind CSS
  - Utility-first approach for rapid development
  - Highly customizable for branded design
  - Responsive design out of the box
  
- Component Library: DaisyUI
  - Tailwind CSS based components
  - Pre-built elements for common UI patterns
  - Customizable theme system
  
- Icons: Heroicons
  - Consistent design language
  - SVG-based for scalability
  - Optimized for Tailwind CSS

### State Management
- React Context + Hooks for local state
- SWR for server state and caching
  - Optimistic UI updates
  - Automatic revalidation
  - Built-in error handling

## Backend Stack
### Core Framework
- Framework: Express.js
- Language: JavaScript
- Justification:
  - Lightweight and flexible
  - Large ecosystem of middleware
  - Easy integration with frontend
  - Simple and straightforward development

### Database
- ORM: Prisma
  - Type-safe database queries
  - Auto-generated migrations
  - Built-in connection pooling
  
- Database: PostgreSQL
  - Relational database for complex relationships
  - JSONB support for flexible data
  - Strong transactional support

### Development Features
- Authentication: JWT-based (local implementation)
  - Custom user authentication flow
  - Role-based access control
  - Session management

- File Handling (Local Development):
  - Local file system storage
  - Structured uploads directory
  - Basic image processing

- Email (Local Development):
  - Local SMTP server for testing
  - Email template system
  - Queue management for notifications

## Development Environment
### Containerization Strategy
- **Full Docker Development Environment**
  - Frontend containerized with Next.js dev server
  - Backend containerized with Express.js and nodemon
  - All services orchestrated via Docker Compose
  - Hot reload support for both frontend and backend
  - Consistent development environment across team

### Tools
- Package Manager: pnpm
  - Faster than npm
  - Disk space efficient
  - Strict dependency resolution

- Version Control:
  - Git
  - Conventional commits
  - Branch strategy: feature/bugfix/hotfix

- Containerization:
  - Docker & Docker Compose
  - Multi-stage builds for optimization
  - Development and production configurations
  - Volume mounting for hot reload

### Quality Assurance
- ESLint: Code linting
- Prettier: Code formatting
- Jest: Unit testing (containerized)
- Cypress: E2E testing (containerized)

### Docker Services
- **Frontend Container**:
  - Next.js development server
  - Hot reload with volume mounting
  - Port 3000 exposed
  - Node.js Alpine base image

- **Backend Container**:
  - Express.js with nodemon
  - Hot reload with volume mounting
  - Port 5000 exposed
  - Node.js Alpine base image

- **Database Container**:
  - PostgreSQL latest
  - Persistent volume for data
  - Port 5432 exposed
  - Environment-based configuration

- **SMTP Container**:
  - MailHog for local email testing
  - Web interface on port 8025
  - SMTP server on port 1025

- **Redis Container** (Optional):
  - Redis latest for caching
  - Port 6379 exposed
  - Persistent volume for data

## Future Considerations
### Payment Integration (To be decided)
- Options to evaluate:
  - Stripe
  - Local payment gateways
  - FPX integration

### Cloud Services (To be decided)
- File Storage
- Email Service
- Deployment Platform
- CDN Integration

## Development Workflow
1. **Containerized Local Development**:
   ```bash
   # Start all services
   docker-compose up -d

   # View logs
   docker-compose logs -f [service-name]

   # Stop all services
   docker-compose down
   ```

   - Frontend: http://localhost:3000 (Next.js with hot reload)
   - Backend: http://localhost:5000 (Express.js with nodemon)
   - Database: localhost:5432 (PostgreSQL)
   - Email UI: http://localhost:8025 (MailHog interface)
   - Redis: localhost:6379 (if enabled)

2. **Development Commands**:
   ```bash
   # Install dependencies (runs in container)
   docker-compose exec frontend pnpm install
   docker-compose exec backend pnpm install

   # Run database migrations
   docker-compose exec backend pnpm prisma migrate dev

   # Access container shell
   docker-compose exec frontend sh
   docker-compose exec backend sh
   ```

3. **Testing in Containers**:
   ```bash
   # Unit tests
   docker-compose exec frontend pnpm test
   docker-compose exec backend pnpm test

   # E2E tests
   docker-compose run --rm cypress

   # API testing with Postman collections
   docker-compose exec backend pnpm test:api
   ```

4. **Code Quality**:
   - Pre-commit hooks (runs in containers)
   - Automated testing (containerized CI/CD)
   - Code review process
   - Linting and formatting in containers

## Docker Configuration Files Required

### Root Level
- `docker-compose.yml` - Main orchestration file
- `docker-compose.override.yml` - Development overrides
- `.dockerignore` - Exclude unnecessary files

### Frontend
- `frontend/Dockerfile` - Multi-stage build
- `frontend/Dockerfile.dev` - Development optimized
- `frontend/.dockerignore` - Frontend specific exclusions

### Backend
- `backend/Dockerfile` - Multi-stage build
- `backend/Dockerfile.dev` - Development optimized
- `backend/.dockerignore` - Backend specific exclusions

## Environment Variables
- `.env.example` - Template for environment variables
- `.env.local` - Local development overrides
- Container-specific environment files for each service
