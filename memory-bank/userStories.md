# User Stories - GoSea Platform

## Epic: Customer Registration & Profile Management

### Feature: Sign-Up with Google OAuth
As a Customer User
I want to sign up using my Google account after clicking a Sign Up button 
So that I can quickly create an account without manually entering my details

#### Acceptance Criteria
- [ ] "Sign Up" button visible on homepage (at top right corner next to "Sign In" button)
- [ ] Upon click on "Sign Up" button, a sign modal is launched with two options: "Continue with Google" button and a form for registration via normal email
- [ ] Google OAuth redirect and authentication flow
- [ ] Auto-populate profile with Google email, first and last name
- [ ] Prompt for required fields (First Name, Last Name, Contact Phone Number and Profile picture)
- [ ] Malaysian phone format validation (+60 followed by 9-10 digits)
- [ ] Unmasked phone number in UI, masked in database
- [ ] Optional fields available but skippable
- [ ] Successful redirect to homepage after profile completion

### Feature: Sign-Up with Email and Token Verification
As a Customer User
I want to sign up using my email with OTP verification
So that I can securely create an account without a Google account

#### Acceptance Criteria
- [ ] "Sign Up" button visible on homepage (at top right corner next to "Sign In" button)
- [ ] Upon click on "Sign Up" button, there is a form with First Name (required), Last Name (required,) Email (required), Contact Phone Number (required), password (required) and confirm password (required) fields for option registration using normal email and a button "Continue with Google"
- [ ] Send verification link to user's email address upon succsseful form submission
- [ ] Required fields validation (First Name, Last Name, Contact Phone Number, Email, Password, Confirm Password). Password verification (at least 8 characters, 1 uppercase and 1 special character) is real time upon user type in
- [ ] Malaysian phone format validation
- [ ] Phone number masking in database
- [ ] Successful login and homepage redirect

### Feature: Password Recovery
As a Customer User who signed up with standard email
I want to recover my password using an OTP or reset link
So that I can regain access to my account if I forget my password

#### Acceptance Criteria
- [ ] Password recovery option visible in Sign In modal
- [ ] Send reset link to user's registered email
- [ ] Email delivery within 1 minute
- [ ] New password setup functionality
- [ ] Redirect to sign-in page after reset
- [ ] Option completely disabled for Google OAuth users

### Feature: Profile Management
As a Customer User
I want to complete my profile with required and optional fields
So that I can provide necessary information for bookings and personalize my experience

#### Acceptance Criteria
- [ ] Profile completion flow after first time sign in/sign up via "Complete Your Profile" modal
  - [ ] Required fields:
    - [ ] Contact Phone Number (Malaysian format)
  - [ ] Optional fields:
    - [ ] Date of Birth
    - [ ] Street Address 1
    - [ ] Street Address 2
    - [ ] Postcode
    - [ ] City
    - [ ] State
    - [ ] Emergency Contact (Malaysian phone format)

- [ ] Profile edit flow after next sign-in via Profile Edit Page
  - [ ] All fields are auto-populated and editable
  - [ ] Malaysian phone format validation for Contact Phone Number and Emergency Contact
  - [ ] Phone number masking in database
  - [ ] Successful save and profile page redirect

### Feature: Sign-In with Google OAuth
As a Customer User
I want to sign in using my Google account
So that I can access my account quickly and securely

#### Acceptance Criteria
- [ ] "Sign In" button visible on homepage (at top right corner before "Sign Up" button)
- [ ] Email and Password fields with Sign In button and another option: "Continue with Google"
- [ ] Google OAuth authentication flow
- [ ] Successful redirect to homepage after sign in
- [ ] User profile data loaded after sign in
- [ ] Remember me option available
- [ ] Session management and token handling

### Feature: Sign-In with Email
As a Customer User
I want to sign in using my email and password
So that I can access my account securely

#### Acceptance Criteria
- [ ] Email and password input fields
- [ ] Password field with show/hide toggle
- [ ] Input validation for email format
- [ ] Secure password handling
- [ ] Error messages for invalid credentials
- [ ] Remember me option available
- [ ] "Forgot Password" link visible
- [ ] Session management and token handling
- [ ] Successful redirect to homepage after sign in

## Epic: Boat Search & Booking

### Feature: Boat Search
As a Customer User
I want to search for boats using advanced filters
So that I can find a boat that matches my needs for a trip

#### Acceptance Criteria
- [ ] Advanced search filters accessible from homepage
- [ ] Service Type dropdown (Snorkeling, Passenger Boat)
- [ ] Location dropdown (Redang Island, Perhentian Island)
- [ ] Custom Date and Time picker integration
- [ ] Filter results display
- [ ] Clear filters functionality

### Feature: View Boat Group Details
As a Customer User
I want to view detailed information about a group of boats
So that I can decide if the group meets my requirements before booking

#### Acceptance Criteria
- [ ] High-quality photo gallery with zoom
- [ ] Package details display
- [ ] Included items list
- [ ] Itinerary information
- [ ] Age-based pricing display
- [ ] Navigation back to search results

### Feature: Book a Boat
As a Customer User
I want to book a boat with a streamlined process
So that I can secure my trip efficiently

#### Acceptance Criteria
- [ ] Service Type selection requirement
- [ ] Interactive calendar with availability indication
- [ ] Capacity validation against boat limits
- [ ] Primary contact information auto-population
- [ ] Malaysian phone format validation
- [ ] Phone number masking in database
- [ ] Booking summary display
- [ ] Edit capabilities before confirmation

### Feature: Payment Processing
As a Customer User
I want to pay for my booking using multiple payment methods
So that I can complete my reservation securely

#### Acceptance Criteria
- [ ] Payment option selection (Full Payment/Deposit)
- [ ] Deposit option rules (3+ days before service)
- [ ] Multiple payment method support
- [ ] Secure payment processing
- [ ] Deposit payment flow (30% initial, 70% later)
- [ ] Payment reminders for deposits
- [ ] Full payment confirmation
- [ ] PDF receipt generation
- [ ] Email/in-app notifications

### Feature: Apply Discount Code
As a Customer User
I want to apply a promo/discount code during booking
So that I can reduce the cost of my trip

#### Acceptance Criteria
- [ ] Promo/discount code input field on payment page
- [ ] Real-time price adjustment when valid code is applied
- [ ] Display updated total after discount
- [ ] Error message for invalid codes
- [ ] Option to remove code and revert to original price

### Feature: Cancel Booking and Process Refund
As a Customer User
I want to cancel my booking and receive a refund
So that I can manage changes to my plans

#### Acceptance Criteria
- [ ] "Cancel Booking" option for upcoming bookings
- [ ] Display cancellation policy with refund terms
- [ ] Email and in-app cancellation confirmation
- [ ] Automated refund processing based on timing:
  - [ ] Full refund if canceled 48+ hours prior
  - [ ] 50% refund if canceled within 24-48 hours
- [ ] Refund status tracking in booking history

### Feature: Manage Upcoming Bookings
As a Customer User
I want to view and manage my upcoming bookings
So that I can stay organized and make changes if needed

#### Acceptance Criteria
- [ ] Dashboard list of upcoming bookings with details
- [ ] Full booking details view with PDF receipt download
- [ ] Booking modification options (date/time changes)
- [ ] Cancellation flow with refund details
- [ ] Status updates for all booking changes

### Feature: Submit Trip Feedback
As a Customer User
I want to submit a review and rating for a completed trip
So that I can share my experience with others

#### Acceptance Criteria
- [ ] "Submit Review" option for completed trips only
- [ ] 1-5 star rating system
- [ ] Optional comments field
- [ ] Disabled review option for incomplete trips
- [ ] Review history in user profile

### Feature: Booking Notifications
As a Customer User
I want to receive notifications about my bookings
So that I can stay informed about my trip status

#### Acceptance Criteria
- [ ] Immediate booking confirmation notifications
- [ ] Payment reminder notifications for deposits:
  - [ ] 48 hours before deadline
  - [ ] 24 hours before deadline
- [ ] 24-hour trip reminder
- [ ] Cancellation and refund update notifications
- [ ] Both email and in-app notification delivery

### Feature: Support Ticket Management
As a Customer User
I want to submit a support ticket for issues
So that I can get help with booking or payment problems

#### Acceptance Criteria
- [ ] Support ticket submission form with categories
- [ ] Ticket ID generation and confirmation
- [ ] Status tracking (Open, In Progress, Resolved)
- [ ] Support contact information display
- [ ] Ticket history view in dashboard

## Epic: Boat Owner Operations

### Feature: Sign-Up as a Boat Owner
As a Boat Owner
I want to sign up using my Google account or email with OTP verification after selecting the Boat Owner menu
So that I can securely create an account to manage my boats after admin approval

#### Acceptance Criteria
- [ ] "Boat Owner" menu option on homepage
- [ ] Dedicated sign-up page with two options:
  - [ ] Continue with Google
  - [ ] Continue with Email
- [ ] Google OAuth authentication flow with user type "Boat Owner"
- [ ] Email OTP verification flow:
  - [ ] Email input and validation
  - [ ] OTP delivery within 1 minute
  - [ ] OTP validation with error handling
  - [ ] New OTP request option
- [ ] Required profile fields:
  - [ ] First Name
  - [ ] Last Name
  - [ ] Contact Phone Number (Malaysian format)
  - [ ] Company Name
  - [ ] BRN
  - [ ] Address
- [ ] Optional Profile Picture upload
- [ ] Phone number masking in database
- [ ] Admin approval workflow:
  - [ ] Account marked as "Pending Approval"
  - [ ] Notification of pending review
  - [ ] Email and in-app notifications for approval/rejection
- [ ] Redirect to owner dashboard upon approval

### Feature: Password Recovery for Boat Owner
As a Boat Owner who signed up with email
I want to recover my password using an OTP or reset link
So that I can regain access to my account if I forget my password

#### Acceptance Criteria
- [ ] Password recovery option on sign-in page
- [ ] Email submission for recovery
- [ ] OTP/reset link delivery within 1 minute
- [ ] New password setup functionality
- [ ] Sign-in with new password
- [ ] Option disabled for Google OAuth users

### Feature: Add Boat to Fleet
As a Boat Owner
I want to add a boat to my fleet with its details
So that I can make it available for rental

#### Acceptance Criteria
- [ ] "Add Boat" functionality in owner dashboard
- [ ] Required fields:
  - [ ] Boat Name
  - [ ] Service Type (Snorkeling/Passenger Boat)
  - [ ] Location (Redang/Perhentian Island)
  - [ ] Capacity
  - [ ] Base Price
  - [ ] Boat Pictures (up to 5)
  - [ ] Package details
  - [ ] Itinerary schedule
- [ ] Optional fields:
  - [ ] Amenities
  - [ ] Description
  - [ ] Availability Schedule
- [ ] Admin approval workflow
- [ ] Search result visibility after approval

### Feature: Update Boat Availability
As a Boat Owner
I want to update the availability of my boats
So that I can manage when they are rentable

#### Acceptance Criteria
- [ ] Interactive availability calendar
- [ ] Color-coded status (green/red)
- [ ] Real-time updates
- [ ] Changes reflected in search within 5 minutes
- [ ] Recurring availability patterns
- [ ] Fleet-wide availability view

### Feature: Booking Management
As a Boat Owner
I want to view and manage customer bookings for my boats
So that I can confirm or cancel them as needed

#### Acceptance Criteria
- [ ] Booking list with details:
  - [ ] Date and time
  - [ ] Service type
  - [ ] Customer information
  - [ ] Payment status
- [ ] Booking confirmation workflow
- [ ] Cancellation workflow with reason
- [ ] Customer notifications
- [ ] Support request for overdue payments

### Feature: Earnings Management
As a Boat Owner
I want to view and manage my earnings
So that I can track my income and receive payments

#### Acceptance Criteria
- [ ] Earnings dashboard with:
  - [ ] Total earnings
  - [ ] Breakdown by boat
  - [ ] Payment status filters
  - [ ] Date range filters
- [ ] PDF report generation
- [ ] Payout system:
  - [ ] 7-day holding period
  - [ ] Bank transfer setup
  - [ ] Payout notifications
  - [ ] Transaction history

### Feature: Booking Notifications
As a Boat Owner
I want to receive notifications about bookings
So that I can stay informed and respond promptly

#### Acceptance Criteria
- [ ] New booking notifications
- [ ] Booking status change notifications
- [ ] Payment update notifications
- [ ] Both email and in-app delivery
- [ ] Real-time notification system

### Feature: Owner Support
As a Boat Owner
I want to submit support tickets
So that I can get help with managing my boats or payments

#### Acceptance Criteria
- [ ] Support ticket system:
  - [ ] Category selection
  - [ ] Issue description
  - [ ] Ticket ID generation
- [ ] Status tracking (Open/In Progress/Resolved)
- [ ] Support contact information
- [ ] Ticket history view

## Epic: Boat Owner Operations

### Feature: Sign-Up as an Affiliate Agent
As an Affiliate Agent
I want to sign up using my Google account or email with OTP verification after selecting the Agent menu
So that I can securely create an account to promote boat rentals after admin approval

#### Acceptance Criteria
- [ ] "Agent" menu option on homepage
- [ ] Dedicated sign-up page with two options:
  - [ ] Continue with Google
  - [ ] Continue with Email
- [ ] Google OAuth authentication flow with user type "Affiliate Agent"
- [ ] Email OTP verification flow:
  - [ ] Email input and validation
  - [ ] OTP delivery within 1 minute
  - [ ] OTP validation with error handling
  - [ ] New OTP request option
- [ ] Required profile fields:
  - [ ] First Name
  - [ ] Last Name
  - [ ] Contact Phone Number (Malaysian format)
  - [ ] Agency Name
- [ ] Optional fields:
  - [ ] Profile Picture
  - [ ] Address
- [ ] Phone number masking in database
- [ ] Admin approval workflow:
  - [ ] Account marked as "Pending Approval"
  - [ ] Notification of pending review
  - [ ] Email and in-app notifications for approval/rejection

### Feature: Password Recovery for Affiliate Agent
As an Affiliate Agent who signed up with email
I want to recover my password using an OTP or reset link
So that I can regain access to my account if I forget my password

#### Acceptance Criteria
- [ ] Password recovery option on sign-in page
- [ ] Email submission for recovery
- [ ] OTP/reset link delivery within 1 minute
- [ ] New password setup functionality
- [ ] Sign-in with new password

### Feature: Sign-In as an Affiliate Agent
As an Affiliate Agent
I want to sign in using my Google account or email credentials after selecting the Agent menu
So that I can access my affiliate dashboard securely

#### Acceptance Criteria
- [ ] "Agent" menu with sign-in option
- [ ] Two sign-in options:
  - [ ] Continue with Google
  - [ ] Continue with Email
- [ ] Authentication validation
- [ ] Error handling for invalid credentials
- [ ] Account status validation:
  - [ ] Approved access to dashboard
  - [ ] Pending approval message
  - [ ] Rejection message with support option
- [ ] Redirect to affiliate dashboard upon success

### Feature: Generate Affiliate Link
As an Affiliate Agent
I want to generate a unique affiliate link
So that I can promote boat rentals and earn commissions

#### Acceptance Criteria
- [ ] Generate unique URL with affiliate ID
- [ ] Customizable parameters:
  - [ ] Service Type filter
  - [ ] Location filter
- [ ] Link tracking system
- [ ] Copy to clipboard functionality
- [ ] Booking attribution tracking

### Feature: Book on Behalf of Customer
As an Affiliate Agent
I want to book a boat on behalf of a customer
So that I can facilitate their reservation and earn a commission

#### Acceptance Criteria
- [ ] "Book on Behalf" functionality
- [ ] Customer details collection:
  - [ ] First Name
  - [ ] Last Name
  - [ ] Contact Phone Number (Malaysian format)
  - [ ] Email
- [ ] Booking details:
  - [ ] Service Type
  - [ ] Location
  - [ ] Date and Time
  - [ ] Capacity validation
- [ ] Payment options:
  - [ ] Agent Pays:
    - [ ] Full payment or deposit option
    - [ ] Payment processing
    - [ ] Customer reimbursement tracking
  - [ ] Customer Pays:
    - [ ] Payment link generation
    - [ ] 24-hour payment window
    - [ ] Automatic cancellation
- [ ] Commission tracking (10% after 7-day hold)
- [ ] Notifications for all parties
- [ ] Phone number masking in database

### Feature: Commission Management
As an Affiliate Agent
I want to view and manage my commission earnings
So that I can track my income

#### Acceptance Criteria
- [ ] Commission dashboard with:
  - [ ] Total earnings
  - [ ] Booking-wise breakdown
  - [ ] Payment status filters
  - [ ] Date range filters
- [ ] PDF report generation
- [ ] Payout system:
  - [ ] 7-day holding period
  - [ ] Bank transfer setup
  - [ ] Payout notifications
  - [ ] Transaction history

### Feature: Affiliate Notifications
As an Affiliate Agent
I want to receive notifications about bookings and commissions
So that I can stay informed and follow up with customers

#### Acceptance Criteria
- [ ] New booking notifications
- [ ] Booking status updates
- [ ] Payment confirmations
- [ ] Commission updates
- [ ] Both email and in-app delivery

### Feature: Affiliate Support
As an Affiliate Agent
I want to submit support tickets
So that I can get help with my affiliate account or payments

#### Acceptance Criteria
- [ ] Support ticket system:
  - [ ] Category selection
  - [ ] Issue description
  - [ ] Ticket ID generation
- [ ] Status tracking (Open/In Progress/Resolved)
- [ ] Support contact information
- [ ] Ticket history view

## Priority Matrix

| Story ID | User Type | Priority | Complexity | Status |
|----------|-----------|----------|------------|---------|
| US-001   | Customer  | High     | Medium     | Todo    |
| US-002   | Customer  | High     | High       | Todo    |
| US-003   | Owner     | High     | High       | Todo    |
| US-004   | Owner     | High     | Medium     | Todo    |
| US-005   | Affiliate | High     | High       | Todo    |
| US-006   | Affiliate | High     | High       | Todo    |
| US-007   | Affiliate | High     | Medium     | Todo    |
| US-008   | Affiliate | High     | Medium     | Todo    |
| US-009   | All       | High     | High       | Todo    |
| US-010   | All       | Medium   | Low        | Todo    |

Story IDs:
- US-001: Customer Registration & Authentication
- US-002: Boat Search & Booking
- US-003: Boat Owner Registration & Fleet Management
- US-004: Booking Management for Owners
- US-005: Affiliate Agent Registration & Authentication
- US-006: Affiliate Booking & Commission Management
- US-007: Affiliate Link Generation & Tracking
- US-008: Affiliate Payout System
- US-009: Payment Processing & Refunds
- US-010: Support System & Notifications

## Technical Notes
- Integration with payment gateway required for payment processing
- Real-time availability system needed for booking management
- Secure file storage required for document uploads
- Email service integration needed for notifications
- Mobile-responsive design required for all features
