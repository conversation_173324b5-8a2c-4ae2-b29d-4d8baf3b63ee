# System Requirements - GoSea Platform

## Overview
GoSea is a comprehensive web application for managing and renting boats for services such as snorkeling and passenger transportation, supporting three distinct user roles: Customers, Boat Owners, and Affiliate Agents.

## Functional Requirements

### 1. User Management
#### 1.1 Registration & Authentication
- FR1.1.1: Support multiple registration methods (Standard email and Google OAuth)
- FR1.1.2: Implement OTP verification via email for standard email registrations only
- FR1.1.3: Provide role-specific registration flows (Customer, Boat Owner and Affiliate Agent)
- FR1.1.4: Enable password recovery via email (OTP or reset link) for standard email registration only
- FR1.1.5: Implement admin approval access for Boat Owners and Affiliate Agents

#### 1.2 Profile Management
- FR1.2.1: Manage user profiles with required fields (name and contact)
- FR1.2.2: Support optional profile fields (picture, DOB, address and emergency contact)
- FR1.2.3: Support language selection (Bahasa and English)
- FR1.2.4: Maintain booking and review history
- FR1.2.5: Generate downloadable trip receipts (PDF)

### 2. Boat Management
#### 2.1 Listing Management
- FR2.1.1: Create and edit boat listings with detailed specifications
- FR2.1.2: Support multiple photo uploads with gallery view
- FR2.1.3: Manage boat availability via interactive calendar
- FR2.1.4: Set pricing and service options (i.e. included item lists and itinerary)
- FR2.1.5: Enable draft and publish functionality
- FR2.1.6: Implement admin approval workflow

#### 2.2 Search & Discovery
- FR2.2.1: Provide advanced search filters (service type, location, date)
- FR2.2.2: Support geolocation-based searches
- FR2.2.3: Display real-time availability
- FR2.2.4: Show detailed boat specifications and amenities
- FR2.2.5: Present pricing breakdowns

### 3. Booking System
#### 3.1 Booking Process
- FR3.1.1: Implement real-time availability checking
- FR3.1.2: Collect passenger information and validate capacity
- FR3.1.3: Support booking modifications and cancellations
- FR3.1.4: Generate booking confirmations and reminders
- FR3.1.5: Enable affiliate-based bookings

#### 3.2 Payment Processing
- FR3.2.1: Support multiple payment methods (FPX, credit/debit cards)
- FR3.2.2: Handle full and deposit payment models
- FR3.2.3: Process refunds based on cancellation policy
- FR3.2.4: Apply promotional discounts
- FR3.2.5: Generate payment receipts

### 4. Affiliate System
#### 4.1 Affiliate Management
- FR4.1.1: Generate unique affiliate links/codes
- FR4.1.2: Track referral performance
- FR4.1.3: Manage commission structures
- FR4.1.4: Process affiliate payouts
- FR4.1.5: Provide marketing materials

## Non-Functional Requirements

### 1. Performance
- NFR1.1: Page load time under 3 seconds
- NFR1.2: Support concurrent user sessions
- NFR1.3: Real-time availability updates
- NFR1.4: Responsive design for all devices

### 2. Security
- NFR2.1: Implement RBAC (Role-Based Access Control)
- NFR2.2: Enable 2FA for enhanced security
- NFR2.3: Secure payment processing (PCI compliance)
- NFR2.4: Data encryption at rest and in transit
- NFR2.5: PII data masking

### 3. Scalability
- NFR3.1: Horizontal scaling capability
- NFR3.2: Load balancing support
- NFR3.3: Database partitioning strategy
- NFR3.4: CDN integration for media delivery

### 4. Reliability
- NFR4.1: 99.9% system uptime
- NFR4.2: Automated backup systems
- NFR4.3: Disaster recovery plan
- NFR4.4: Error logging and monitoring

### 5. Integration
- NFR5.1: RESTful API architecture
- NFR5.2: Third-party payment gateway integration
- NFR5.3: OAuth provider integration
- NFR5.4: Email service integration
- NFR5.5: Calendar system integration

## Technical Requirements

### 1. Platform
- TR1.1: Web-based application
- TR1.2: Mobile-responsive design
- TR1.3: Cross-browser compatibility
- TR1.4: PWA capabilities

### 2. Infrastructure
- TR2.1: Cloud-based hosting
- TR2.2: Fully containerized development and deployment
- TR2.3: Docker Compose orchestration for local development
- TR2.4: Multi-stage Docker builds for optimization
- TR2.5: CI/CD pipeline with containerized testing
- TR2.6: Monitoring and logging systems

### 3. Data Management
- TR3.1: Relational database for transactional data
- TR3.2: Cache layer for performance optimization
- TR3.3: File storage for media content
- TR3.4: Backup and recovery systems
- TR3.5: Containerized database with persistent volumes
- TR3.6: Database migrations in containerized environment

### 4. Containerization Requirements
- TR4.1: Frontend application containerized with Next.js dev server
- TR4.2: Backend application containerized with Express.js and nodemon
- TR4.3: PostgreSQL database containerized with persistent storage
- TR4.4: SMTP server containerized for email testing (MailHog)
- TR4.5: Redis containerized for caching (optional)
- TR4.6: Docker Compose orchestration for all services
- TR4.7: Hot reload support for development containers
- TR4.8: Volume mounting for source code changes
- TR4.9: Environment variable management across containers
- TR4.10: Container health checks and restart policies
- TR4.11: Multi-stage builds for production optimization
- TR4.12: Container networking for service communication

## Constraints
### 1. Business Constraints
- BC1.1: Must comply with local maritime regulations
- BC1.2: Payment processing restrictions by region
- BC1.3: Commission structure limitations

### 2. Technical Constraints
- TC1.1: Browser compatibility requirements
- TC1.2: Mobile device support requirements
- TC1.3: Internet connectivity requirements
- TC1.4: Data storage limitations
- TC1.5: Docker and Docker Compose required for development
- TC1.6: Container resource allocation requirements
- TC1.7: Development environment consistency across team

### 3. Regulatory Constraints
- RC1.1: Data privacy compliance (GDPR, PDPA)
- RC1.2: Financial regulations compliance
- RC1.3: Maritime safety regulations
- RC1.4: Local tourism board requirements
