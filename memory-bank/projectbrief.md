# GoSea Project Brief

## Project Structure
The project is organized into two main directories:

- `frontend/`: Directory for the client-side application
- `backend/`: Directory for the server-side application

## Initial Setup
- Created basic project structure on 11/06/2025
- Established memory-bank for project documentation

## Core Requirements
See [System Requirements Document](systemRequirements.md) for detailed requirements including:
- Functional Requirements (User Management, Boat Management, Booking System, Affiliate System)
- Non-Functional Requirements (Performance, Security, Scalability)
- Technical Requirements
- System Constraints

## Project Goals
1. Create a comprehensive boat rental and service platform
2. Support three user roles: Customers, Boat Owners, and Affiliate Agents
3. Enable secure and efficient booking management
4. Facilitate seamless payment processing
5. Provide robust reporting and analytics

## Documentation Structure
- [System Requirements](systemRequirements.md): Detailed technical and functional requirements
- [User Stories](userStories.md): Comprehensive user stories and acceptance criteria
- [Technical Context](techContext.md): Technology stack and development approach
- [Docker Requirements](dockerRequirements.md): Containerized development environment setup
- [Development Plan](developmentPlan.md): Comprehensive 12-module development strategy
- [Development Summary](developmentSummary.md): Executive overview of development approach
- Additional documentation will be added as the project evolves

Note: See individual documents for detailed specifications and requirements.
