# GoSea Platform

A comprehensive boat rental and service platform supporting three user roles: Customers, Boat Owners, and Affiliate Agents. Built with Next.js, Express.js, and PostgreSQL in a fully containerized Docker environment.

## 🚢 Project Overview

GoSea enables users to:
- **Customers**: Search, book, and manage boat rentals for snorkeling and passenger transportation
- **Boat Owners**: List and manage their boat fleet with availability and pricing
- **Affiliate Agents**: Generate referral links and earn commissions from bookings

## 🏗️ Architecture

### Technology Stack
- **Frontend**: Next.js with Tailwind CSS and DaisyUI
- **Backend**: Express.js with Prisma ORM
- **Database**: PostgreSQL
- **Cache**: Redis
- **Email**: MailHog (development)
- **Package Manager**: pnpm

### Container Services
```
Frontend (Next.js)     ←→ Backend (Express.js)    ←→ Database (PostgreSQL)
Port: 3000                Port: 5001                 Port: 5432
                              ↓
                         Redis (Cache)           ←→ MailHog (SMTP)
                         Port: 6379                 Port: 1025/8025
```

## 🚀 Quick Start

### Prerequisites
- <PERSON><PERSON> and <PERSON>er Compose
- Git

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd gosea-project
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

3. **Start the development environment**
   ```bash
   docker-compose up -d
   ```

4. **Run database migrations**
   ```bash
   docker-compose exec backend pnpm prisma migrate dev
   ```

5. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5001
   - Email UI: http://localhost:8025
   - Database: localhost:5432

## 🛠️ Development Workflow

### Daily Development
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f frontend
docker-compose logs -f backend

# Install dependencies
docker-compose exec frontend pnpm add <package>
docker-compose exec backend pnpm add <package>

# Run tests
docker-compose exec frontend pnpm test
docker-compose exec backend pnpm test

# Access container shell
docker-compose exec frontend sh
docker-compose exec backend sh

# Stop services
docker-compose down
```

### Database Operations
```bash
# Run migrations
docker-compose exec backend pnpm prisma migrate dev

# Reset database
docker-compose exec backend pnpm prisma migrate reset

# Generate Prisma client
docker-compose exec backend pnpm prisma generate

# Open Prisma Studio
docker-compose exec backend pnpm prisma studio
```

## 📁 Project Structure

```
gosea-project/
├── frontend/                 # Next.js application
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── pages/          # Next.js pages
│   │   ├── styles/         # CSS and styling
│   │   └── utils/          # Utility functions
│   ├── public/             # Static assets
│   ├── Dockerfile.dev      # Development Docker config
│   └── package.json
├── backend/                  # Express.js application
│   ├── src/
│   │   ├── controllers/    # Route controllers
│   │   ├── middleware/     # Express middleware
│   │   ├── models/         # Database models
│   │   ├── routes/         # API routes
│   │   └── utils/          # Utility functions
│   ├── prisma/             # Database schema and migrations
│   ├── Dockerfile.dev      # Development Docker config
│   └── package.json
├── memory-bank/              # Project documentation
│   ├── systemRequirements.md
│   ├── userStories.md
│   ├── developmentPlan.md
│   └── ...
├── docker-compose.yml        # Docker orchestration
├── .env.example             # Environment template
└── README.md
```

## 🧪 Testing

### Running Tests
```bash
# Frontend tests
docker-compose exec frontend pnpm test:unit
docker-compose exec frontend pnpm test:e2e

# Backend tests
docker-compose exec backend pnpm test:unit
docker-compose exec backend pnpm test:integration

# All tests
docker-compose exec frontend pnpm test
docker-compose exec backend pnpm test
```

## 📋 Development Phases

### Phase 1: Foundation (Weeks 1-4)
- [x] Docker Infrastructure Setup
- [ ] Authentication & User Management
- [ ] Database Foundation

### Phase 2: Core Business (Weeks 5-10)
- [ ] Boat Management System
- [ ] Search & Discovery Engine
- [ ] Booking System Core

### Phase 3: Advanced Features (Weeks 11-16)
- [ ] Payment Processing Integration
- [ ] Affiliate System
- [ ] Notification System

### Phase 4: Production (Weeks 17-20)
- [ ] Analytics & Reporting
- [ ] Admin Panel
- [ ] Production Deployment

## 🤝 Contributing

1. Create a feature branch from `main`
2. Make your changes
3. Run tests: `docker-compose exec frontend pnpm test && docker-compose exec backend pnpm test`
4. Submit a pull request

## 📚 Documentation

- [System Requirements](memory-bank/systemRequirements.md)
- [User Stories](memory-bank/userStories.md)
- [Development Plan](memory-bank/developmentPlan.md)
- [Docker Requirements](memory-bank/dockerRequirements.md)
- [Technical Context](memory-bank/techContext.md)

## 🔧 Troubleshooting

### Common Issues

**Port conflicts**
```bash
# Check if ports are in use
lsof -i :3000 -i :5000 -i :5432 -i :8025

# Stop conflicting services
docker-compose down
```

**Container issues**
```bash
# Rebuild containers
docker-compose build --no-cache

# View container logs
docker-compose logs [service-name]

# Restart specific service
docker-compose restart [service-name]
```

**Database issues**
```bash
# Reset database
docker-compose exec backend pnpm prisma migrate reset

# Check database connection
docker-compose exec postgres psql -U gosea -d gosea_db
```

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation in the `memory-bank/` directory
- Review the troubleshooting section above
