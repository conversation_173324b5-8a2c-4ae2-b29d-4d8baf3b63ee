version: '3.8'

services:
  # Frontend Service - Next.js Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: gosea-frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
      - INTERNAL_API_URL=http://gosea-backend:5000
      - WATCHPACK_POLLING=true
      - NEXT_TELEMETRY_DISABLED=1
      - CHOKIDAR_USEPOLLING=true
    depends_on:
      - backend
    networks:
      - gosea-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://127.0.0.1:3000/api/health', (res) => process.exit(res.statusCode === 200 ? 0 : 1)).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend Service - Express.js Application
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: gosea-backend
    ports:
      - "5001:5000"
    volumes:
      - ./backend:/app
      - /app/node_modules
      - backend_uploads:/app/uploads
    env_file:
      - .env
    environment:
      - NODE_ENV=development
      - DATABASE_URL=*****************************************/gosea_db
      - REDIS_URL=redis://redis:6379
      - USE_REAL_EMAIL=${USE_REAL_EMAIL}
      - GMAIL_USER=${GMAIL_USER}
      - GMAIL_APP_PASSWORD=${GMAIL_APP_PASSWORD}
      - FROM_EMAIL=${FROM_EMAIL}
      - JWT_SECRET=dev-jwt-secret-key
      - JWT_REFRESH_SECRET=dev-jwt-refresh-secret-key
      - API_PORT=5000
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - gosea-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://127.0.0.1:5000/api/health', (res) => process.exit(res.statusCode === 200 ? 0 : 1)).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s


  # Database Service - PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: gosea-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=gosea_db
      - POSTGRES_USER=gosea
      - POSTGRES_PASSWORD=password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/prisma/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - gosea-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U gosea -d gosea_db"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Cache Service - Redis
  redis:
    image: redis:7-alpine
    container_name: gosea-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    networks:
      - gosea-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Email Service - MailHog (Development SMTP)
  # mailhog:
  #   image: mailhog/mailhog:latest
  #   container_name: gosea-mailhog
  #   ports:
  #     - "1025:1025"  # SMTP port
  #     - "8025:8025"  # Web UI port
  #   networks:
  #     - gosea-network
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8025"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

  # Database Administration - pgAdmin (Optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: gosea-pgadmin
    ports:
      - "8080:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
      - PGADMIN_CONFIG_SERVER_MODE=False
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - gosea-network
    restart: unless-stopped
    profiles:
      - tools

# Named Volumes for Persistent Data
volumes:
  postgres_data:
    driver: local
    name: gosea_postgres_data
  redis_data:
    driver: local
    name: gosea_redis_data
  backend_uploads:
    driver: local
    name: gosea_backend_uploads
  pgadmin_data:
    driver: local
    name: gosea_pgadmin_data

# Custom Network for Service Communication
networks:
  gosea-network:
    driver: bridge
    name: gosea_network
    ipam:
      config:
        - subnet: **********/16
